<template>
  <div pb-30>
    <div w-full h-500 class="bg-[url(/find_bg.jpg)] bg-no-repeat bg-center" text-center pt-117 flex-col>
      <div flex-center text="40 #fff" mt-20 max-md="flex-wrap text-30 flex-col gap-10">
        <div>国标行业标准零部件一件不漏</div>
        <div ml-37 max-md="ml-0">机械电气模组整机一网打尽</div>
      </div>
      <div w-813 mx-auto mt-30 px-12 max-md="w-full px-10">
        <search-bar w-813 size="large"></search-bar>
      </div>
    </div>

    <div max-w-1300 px-20 mt-130 mx-auto max-md="w-full overflow-hidden">
      <TextItem :item="item" v-for="(item, i) in promotionList" :reverse="i % 2 == 1" :key="i" sibling="mt-40">
        <div flex-center>
          <img :src="item.pic" max-lg="w-full h-auto" h-320 alt="" />
        </div>
      </TextItem>
    </div>
    <go-register />
    <side-widgets />
  </div>
</template>

<script lang="ts" setup>
import TextItem from '../components/textItem.vue'

const promotionList = [
  {
    title: '料号全',
    content: '覆盖全网常用料号，零件信息不再难找',
    pic: '/料号全.png',
    link: '',
  },
  {
    title: '图纸多',
    content: '聚合超10亿3D模型，标准件资料、数据一键查询',
    pic: '/图纸多.png',
    link: '',
  },
  {
    title: '免费下',
    content: '全网模型、资料、图纸免费，注册即可下载',
    pic: '免费下.png',
    link: '',
  },
]
</script>
