<template>
  <div
    class="min-h-100vh bg-gradient-to-br from-#0f0f1e via-#1a1a2b to-#0f0f1e bg-fixed text-white flex flex-col relative overflow-x-hidden"
  >
    <!-- Background effects -->
    <div class="fixed top-0 left-0 right-0 bottom-0 pointer-events-none z-0 bg-radial-effects"></div>

    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 z-1000 border-b border-#f94c30/10 transition-all duration-300 header-bg">
      <div class="header-before"></div>
      <div class="max-w-5600 mx-auto px-48 h-88 flex items-center justify-between relative z-1">
        <div class="flex items-center gap-16">
          <img
            src="../../assets/images/yanxuan_logo.png"
            alt="研选工场"
            class="w-160 object-contain logo-shadow cursor-pointer transition-all duration-300 hover:transform hover:scale-105"
            @click="handleLogoClick"
          />
        </div>
        <div class="flex items-center">
          <a-button
            v-if="!isLoggedIn"
            type="primary"
            size="large"
            @click="handleLogin"
            class="h-44 px-28 font-semibold rounded-22 border-none transition-all duration-300 login-btn"
          >
            登录
          </a-button>
          <div v-else class="flex items-center gap-16">
            <a-dropdown v-if="isLoggedIn">
              <div class="flex items-center gap-16 cursor-pointer">
                <a-avatar size="large" :src="userInfo.avatar">
                  {{ userInfo.name?.charAt(0) }}
                </a-avatar>
                <div class="flex flex-col">
                  <span class="text-white font-medium text-16 user-name-shadow">{{ userInfo.name }}</span>
                  <span class="text-white/70 text-12">{{ company.merchantShortName }}</span>
                </div>
                <DownOutlined class="ml-4 text-white text-14" />
              </div>
              <template #overlay>
                <div class="user-dropdown-card">
                  <div class="user-card-overlay"></div>
                  <div class="user-card-content">
                    <div class="user-actions">
                      <div class="logout-btn" @click="logout">
                        <LogoutOutlined class="logout-icon" />
                        <span class="logout-text">退出登录</span>
                        <div class="logout-btn-overlay"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </a-dropdown>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="flex-1 mt-88 relative" style="z-index: auto">
      <!-- Hero Section -->
      <section class="py-120 px-48 text-center relative hero-bg" style="z-index: auto">
        <div class="hero-before"></div>
        <div class="max-w-900 mx-auto relative" style="z-index: 10001">
          <h1 class="text-64 font-extrabold text-white mb-60 leading-tight tracking-tight hero-title-gradient">
            研选零部件库
          </h1>
          <!-- Search Bar -->
          <HomeSearchBar @categoryClick="handleCategoryClick" />
        </div>
      </section>

      <!-- Brands Section -->
      <section class="py-100 px-48 relative overflow-hidden brands-bg" style="z-index: 1">
        <div class="brands-before"></div>
        <div class="text-center mb-80 relative z-1">
          <h3 class="text-42 font-extrabold text-white mb-20 tracking-tight brands-title-gradient">合作品牌</h3>
        </div>
        <div class="max-w-5600 mx-auto mb-80 grid gap-24 relative z-1 brands-grid">
          <div
            v-for="brand in displayBrands"
            :key="brand.id"
            class="bg-white/6 rounded-20 p-32 cursor-pointer transition-all duration-400 border border-white/10 flex flex-col items-center text-center relative min-h-200 justify-center overflow-hidden brand-card-bg hover:transform hover:-translate-y-8 hover:scale-102 hover:border-#f94c30/30 brand-card-shadow"
            @click="handleBrandClick(brand)"
          >
            <div class="brand-card-before"></div>
            <!-- 推荐标识 -->
            <div
              v-if="brand.isRecommended"
              class="absolute top-16 right-16 px-12 py-6 rounded-16 flex items-center gap-4 z-2 text-white font-bold text-11 uppercase tracking-wider recommended-badge"
            >
              <StarFilled />
              推荐
            </div>

            <div class="mb-20 flex-shrink-0 relative z-1">
              <img
                v-if="brand.shopLogo"
                :src="useImage(useObs(brand.shopLogo))"
                :alt="brand.name"
                class="w-88 h-88 rounded-16 object-contain bg-white/95 p-12 transition-all duration-300 brand-logo-shadow hover:transform hover:scale-105"
              />
              <div
                v-else
                class="w-88 h-88 rounded-16 flex items-center justify-center text-26 font-extrabold text-white uppercase tracking-tight transition-all duration-300 brand-initial-bg hover:transform hover:scale-105"
              >
                {{ getBrandInitial(brand.name) }}
              </div>
            </div>
            <div class="text-center relative z-1">
              <h4
                class="text-16 font-semibold text-white/95 m-0 leading-snug transition-colors duration-300 hover:text-white"
              >
                {{ brand.name }}
              </h4>
            </div>
          </div>
        </div>

        <!-- 品牌加入引导 -->
        <div class="max-w-900 mx-auto text-center relative z-1">
          <div
            class="bg-white/4 rounded-28 p-60 relative overflow-hidden border border-#f94c30/20 transition-all duration-400 join-content-bg hover:border-#f94c30/30 hover:transform hover:-translate-y-4 join-content-shadow"
          >
            <div class="join-content-before"></div>
            <h4 class="text-28 font-extrabold text-white m-0 mb-16 leading-tight tracking-tight relative z-1">
              想要成为我们的合作伙伴？
            </h4>
            <p class="text-18 text-white/80 m-0 mb-40 leading-relaxed max-w-500 mx-auto relative z-1">
              加入研选工场，让更多客户发现您的优质产品
            </p>
            <a-button
              type="primary"
              size="large"
              class="h-52 px-40 text-16 font-bold rounded-26 border-none transition-all duration-300 relative z-1 overflow-hidden join-btn-bg hover:transform hover:-translate-y-3"
              @click="handleJoinBrand"
            >
              <div class="join-btn-before"></div>
              了解详情
            </a-button>
          </div>
        </div>
      </section>
    </main>

    <!-- Footer -->
    <footer class="mt-100 relative border-t border-#f94c30/10 footer-bg">
      <div class="footer-before"></div>
      <div class="max-w-5600 mx-auto py-80 px-48 relative z-1">
        <div class="footer-content">
          <!-- 左侧：Logo和描述 -->
          <div class="footer-brand">
            <img src="../../assets/images/yanxuan_logo.png" alt="研选工场" class="footer-logo mb-24" />
            <p class="footer-description">
              我们致力于为自动化装备制造行业提供智能化、高效率的供应链解决方案，帮助企业降低成本，提升竞争力。
            </p>
          </div>

          <!-- 中间：协议政策 -->
          <div class="footer-policies">
            <h4 class="footer-title">协议政策</h4>
            <ul class="footer-links">
              <li>
                <a @click="handleFooterLink('/agreement?key=termOfService')" class="footer-link">用户协议</a>
              </li>
              <li>
                <a @click="handleFooterLink('/agreement?key=privacyPolicy')" class="footer-link">隐私政策</a>
              </li>
              <li>
                <a @click="handleFooterLink('/agreement?key=disclaimer')" class="footer-link">免责声明</a>
              </li>
            </ul>
          </div>

          <!-- 右侧：联系方式和二维码 -->
          <div class="footer-contact">
            <h4 class="footer-title">联系方式</h4>
            <div class="contact-and-qr">
              <div class="contact-info">
                <div class="contact-item">
                  <MailOutlined class="contact-icon" />
                  <span class="contact-text"><EMAIL></span>
                </div>
                <div class="contact-item">
                  <EnvironmentOutlined class="contact-icon" />
                  <span class="contact-text">江苏省苏州市工业园区启泰路66号国泰新点软件研发楼509A</span>
                </div>
              </div>
              <!-- 二维码区域 -->
              <div class="qr-code-section">
                <div class="qr-code-wrapper">
                  <img
                    src="https://www.yanxuan.cloud/_nuxt/qrcode.DGe3HObh.jpg"
                    alt="扫码关注我们"
                    class="qr-code-img"
                    onerror="this.style.display='none'"
                  />
                  <p class="qr-code-text">扫码关注我们</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部版权信息 -->
      <div class="footer-bottom">
        <p class="copyright-text">Copyright©2025 研选工场（苏州）网络有限公司 | 苏ICP备2024149956号</p>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { StarFilled, DownOutlined, LogoutOutlined, MailOutlined, EnvironmentOutlined } from '@ant-design/icons-vue'
import { userStore } from '~/store/user'
import { companyStore } from '~/store/company'
import { has } from '~/utils/util'
import OrderInfo from '~/layouts/components/OrderInfo.vue'
import HomeSearchBar from './components/HomeSearchBar.vue'

// 设置布局
definePageMeta({
  layout: 'home',
})

const router = useRouter()

const store = userStore()
const user = computed(() => store.user)
const { company } = companyStore()

// 获取环境变量
const config = useRuntimeConfig()
const yanxuanUrl = config.public.baseUrl.VITE_YANXUAN

// 用户状态
const isLoggedIn = computed(() => !!user.value.userId)
const userInfo = computed(() => ({
  name: user.value.nickName,
  avatar: user.value.pic,
}))

// 品牌数据
const recommends = ref([])
const brands = ref([])
const loading1 = ref(false)
const loading2 = ref(false)

// 合并品牌数据并添加推荐标识
const displayBrands = computed(() => {
  const recommendedBrands = recommends.value.map((brand) => ({
    ...brand,
    isRecommended: true,
  }))
  const otherBrands = brands.value.map((brand) => ({
    ...brand,
    isRecommended: false,
  }))
  return [...recommendedBrands, ...otherBrands]
})

// 获取品牌首字母
const getBrandInitial = (name) => {
  if (!name) return 'B'
  // 提取英文首字母或中文首字
  const match = name.match(/^([A-Za-z])|[\u4e00-\u9fa5]/)
  return match ? match[0].toUpperCase() : name.charAt(0).toUpperCase()
}

// 事件处理
const handleLogoClick = () => {
  // 跳转到平台主页
  if (yanxuanUrl) {
    window.location.href = yanxuanUrl
  }
}

const handleFooterLink = (path) => {
  if (yanxuanUrl) {
    window.location.href = yanxuanUrl + path
  }
}

const handleLogin = () => {
  // 使用现有的登录逻辑
  const goLogin = usegoLogin()
  goLogin()
}

const handleCategoryClick = () => {
  // 跳转到分类树页面
  router.push('/categoryTree')
}

const handleBrandClick = (brand) => {
  // 使用现有的品牌点击逻辑
  if (brand.shopId) {
    navigateTo({
      path: '/brand/' + brand.shopId,
    })
  } else {
    message.info('品牌馆即将上线，敬请期待！')
  }
}

const handleWorkspaceClick = () => {
  router.push('/workSpace/company-space')
}

const handleJoinBrand = () => {
  if (yanxuanUrl) {
    window.location.href = yanxuanUrl + '/supplier'
  }
}

const logout = useLogout()

// 获取品牌数据的函数 - 使用现有API
const getRecommendList = async () => {
  loading1.value = true
  const [err, res] = await try_http('/mall/shop/recommend-brands', {
    params: {
      limit: 10,
    },
  })
  loading1.value = false
  if (!err) {
    recommends.value = res.data || []
  }
}

const getBrandList = async () => {
  loading2.value = true
  const [err, res] = await try_http('/mall/shop/pinned-brands', {
    params: {
      limit: 999,
    },
  })
  loading2.value = false
  if (!err) {
    brands.value = res.data || []
  }
}

// 页面加载时获取数据
onMounted(() => {
  getRecommendList()
  getBrandList()
})
</script>

<style scoped>
/* 背景效果 */
.bg-radial-effects {
  background: radial-gradient(ellipse at 30% 20%, rgba(249, 76, 48, 0.03) 0%, transparent 50%),
    radial-gradient(ellipse at 70% 80%, rgba(249, 76, 48, 0.02) 0%, transparent 50%);
}

/* Header */
.header-bg {
  background: rgba(26, 26, 43, 0.85);
  backdrop-filter: blur(20px) saturate(180%);
}

.header-before {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(249, 76, 48, 0.02) 0%, transparent 100%);
  pointer-events: none;
}

.logo-shadow {
  filter: drop-shadow(0 0 8px rgba(249, 76, 48, 0.1));
}

.login-btn {
  background: linear-gradient(135deg, #f94c30 0%, #ff6b47 100%);
  box-shadow: 0 4px 20px rgba(249, 76, 48, 0.25);
}

.login-btn:hover {
  background: linear-gradient(135deg, #ff6b47 0%, #f94c30 100%);
  box-shadow: 0 8px 30px rgba(249, 76, 48, 0.4);
}

.workspace-btn {
  background: linear-gradient(135deg, #f94c30 0%, #ff6b47 100%);
  box-shadow: 0 4px 20px rgba(249, 76, 48, 0.25);
}

.workspace-btn:hover {
  background: linear-gradient(135deg, #ff6b47 0%, #f94c30 100%);
  box-shadow: 0 8px 30px rgba(249, 76, 48, 0.4);
}

.user-name-shadow {
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
}

/* 用户下拉卡片样式 */
.user-dropdown-card {
  background: rgba(26, 26, 43, 0.98);
  backdrop-filter: blur(24px) saturate(180%);
  border-radius: 20px;
  border: 1px solid rgba(249, 76, 48, 0.2);
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4), 0 12px 40px rgba(249, 76, 48, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  min-width: 280px;
  position: relative;
  overflow: hidden;
  animation: userCardSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes userCardSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.user-card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(249, 76, 48, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  pointer-events: none;
}

.user-card-content {
  position: relative;
  z-index: 1;
  padding: 20px;
}

.user-info-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.user-avatar-wrapper {
  position: relative;
  flex-shrink: 0;
}

.user-avatar {
  border: 2px solid rgba(249, 76, 48, 0.3) !important;
  background: rgba(249, 76, 48, 0.1) !important;
  color: #ffffff !important;
  font-weight: 600;
  position: relative;
  z-index: 2;
}

.avatar-glow {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  background: radial-gradient(circle, rgba(249, 76, 48, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  z-index: 1;
  animation: avatarPulse 2s ease-in-out infinite;
}

@keyframes avatarPulse {
  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

.user-details {
  flex: 1;
  overflow: hidden;
}

.user-name {
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
  letter-spacing: -0.02em;
}

.company-name {
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: color 0.3s ease;
}

.user-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
  margin: 16px 0;
}

.user-actions {
  display: flex;
  justify-content: flex-end;
}

.logout-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.logout-btn:hover {
  background: rgba(249, 76, 48, 0.15);
  border-color: rgba(249, 76, 48, 0.3);
  transform: translateY(-1px);
}

.logout-btn-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(249, 76, 48, 0.1) 0%, transparent 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.logout-btn:hover .logout-btn-overlay {
  opacity: 1;
}

.logout-icon {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.logout-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.logout-btn:hover .logout-icon,
.logout-btn:hover .logout-text {
  color: #ffffff;
}

/* Hero Section */
.hero-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center top, rgba(249, 76, 48, 0.08) 0%, transparent 60%);
  pointer-events: none;
}

.hero-title-gradient {
  background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 40px rgba(249, 76, 48, 0.1);
}

/* Brands Section */
.brands-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center, rgba(249, 76, 48, 0.03) 0%, transparent 70%);
  pointer-events: none;
}

.brands-title-gradient {
  background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.85) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brands-grid {
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
}

.brand-card-bg {
  backdrop-filter: blur(20px) saturate(180%);
}

.brand-card-shadow {
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

.brand-card-bg:hover {
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.25), 0 8px 30px rgba(249, 76, 48, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

.brand-card-before {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(249, 76, 48, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
}

.brand-card-bg:hover .brand-card-before {
  opacity: 1;
}

.recommended-badge {
  background: linear-gradient(135deg, #f94c30, #ff6b47);
  box-shadow: 0 4px 15px rgba(249, 76, 48, 0.3);
  backdrop-filter: blur(10px);
}

.brand-logo-shadow {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.brand-logo-shadow:hover {
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.brand-initial-bg {
  background: linear-gradient(135deg, #f94c30 0%, #ff6b47 100%);
  box-shadow: 0 8px 25px rgba(249, 76, 48, 0.25);
}

.brand-initial-bg:hover {
  box-shadow: 0 12px 35px rgba(249, 76, 48, 0.35);
}

/* 品牌加入引导 */
.join-content-bg {
  backdrop-filter: blur(20px) saturate(180%);
}

.join-content-shadow {
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

.join-content-bg:hover {
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.2), 0 8px 30px rgba(249, 76, 48, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.08) inset;
}

.join-content-before {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(249, 76, 48, 0.08) 0%, rgba(255, 255, 255, 0.02) 100%);
  pointer-events: none;
}

.join-btn-bg {
  background: linear-gradient(135deg, #f94c30 0%, #ff6b47 100%);
  box-shadow: 0 8px 25px rgba(249, 76, 48, 0.3);
}

.join-btn-before {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #ff6b47 0%, #f94c30 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.join-btn-bg:hover {
  box-shadow: 0 12px 35px rgba(249, 76, 48, 0.4);
}

.join-btn-bg:hover .join-btn-before {
  opacity: 1;
}

/* Footer */
.footer-bg {
  background: rgba(15, 15, 30, 0.8);
  backdrop-filter: blur(20px) saturate(180%);
}

.footer-before {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(249, 76, 48, 0.02) 0%, transparent 100%);
  pointer-events: none;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1.5fr;
  gap: 60px;
  align-items: start;
}

.footer-brand {
  max-width: 400px;
}

.footer-logo {
  width: 160px;
  height: auto;
  object-fit: contain;
  filter: brightness(1.1);
}

.footer-description {
  color: rgba(255, 255, 255, 0.7);
  font-size: 15px;
  line-height: 1.6;
  margin: 0;
  transition: color 0.3s ease;
}

.footer-description:hover {
  color: rgba(255, 255, 255, 0.9);
}

.footer-title {
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  letter-spacing: -0.02em;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 12px;
}

.footer-link {
  color: rgba(255, 255, 255, 0.7);
  font-size: 15px;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
  position: relative;
}

.footer-link:hover {
  color: #f94c30;
  transform: translateX(4px);
}

.footer-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background: #f94c30;
  transition: width 0.3s ease;
}

.footer-link:hover::after {
  width: 100%;
}

.contact-and-qr {
  display: flex;
  gap: 40px;
  align-items: flex-start;
}

.contact-info {
  flex: 1;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
}

.contact-icon {
  font-size: 16px;
  color: #f94c30;
  flex-shrink: 0;
  margin-top: 2px;
}

.contact-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 15px;
  line-height: 1.5;
  transition: color 0.3s ease;
}

.contact-text:hover {
  color: rgba(255, 255, 255, 0.9);
}

.qr-code-section {
  flex-shrink: 0;
}

.qr-code-wrapper {
  text-align: center;
}

.qr-code-img {
  width: 100px;
  height: 100px;
  border-radius: 8px;
  border: 2px solid rgba(249, 76, 48, 0.2);
  background: rgba(255, 255, 255, 0.05);
  padding: 8px;
  transition: all 0.3s ease;
}

.qr-code-img:hover {
  border-color: rgba(249, 76, 48, 0.4);
  transform: scale(1.05);
}

.qr-code-text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  margin-top: 8px;
  margin-bottom: 0;
}

.footer-bottom {
  text-align: center;
  padding: 30px 48px;
  border-top: 1px solid rgba(255, 255, 255, 0.08);
  position: relative;
  z-index: 1;
}

.copyright-text {
  color: rgba(255, 255, 255, 0.5);
  font-size: 14px;
  margin: 0;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .brands-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 50px;
  }

  .footer-brand {
    grid-column: 1 / -1;
    max-width: none;
    text-align: center;
    margin-bottom: 20px;
  }

  .contact-and-qr {
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .brands-grid {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .contact-and-qr {
    flex-direction: column;
    gap: 30px;
    align-items: center;
  }

  .qr-code-section {
    align-self: center;
  }
}

@media (max-width: 480px) {
  .brands-grid {
    grid-template-columns: 1fr;
  }
}
</style>
