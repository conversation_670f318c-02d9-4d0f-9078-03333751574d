<template>
  <div text-14>
    <div mt-150 flex justify-around items-center max-w-1440 overflow-x-hidden px-12 pb-60 mx-auto>
      <div w-596 shrink-0 max-md="w-full">
        <h1 text-48>在SolidWorks中使用研选工场</h1>
        <div text-24 mt-10 text-justify leading-32 text="#000000/88">
          研选工场 Plugin for SolidWorks
          让您在熟悉的设计环境中触达研选工场海量零部件，轻松完成零部件选型、图纸查看、模型下载编辑与上传的全流程，助您提升设计效率，专注于创新本身。
        </div>
        <div mt-30>下载最新版：</div>
        <div flex mt-10>
          <div
            v-if="!!src.filePath"
            text="16 #fff"
            bg="#f94c30"
            w-240
            h-40
            mr-20
            flex-center
            rounded-4
            cursor-pointer
            @click="download"
          >
            研选工场 Plugin for SolidWorks
          </div>
          <nuxt-link
            to="/plugin-history"
            flex-center
            w-240
            h-40
            rounded-4
            cursor-pointer
            bg="#f94c30/10"
            color="#f94c30/88"
            decoration-none
          >
            历史版本
          </nuxt-link>
        </div>
      </div>

      <img w-630 src="/plugin.png" alt="" />
    </div>
  </div>
</template>

<script setup>
import fileDownload from 'js-file-download'

const { data } = await usePlatApi('/shop/pluginVersion/page', {
  params: {
    status: 1,
  },
})
const src = computed(() => {
  const res = JSON.parse(data.value.data)
  return res.records[0] || {}
})

const download = () => {
  const { filePath, version } = src.value
  $fetch(useObs(filePath), {
    responseType: 'blob',
  }).then((res) => {
    const ext = filePath.split('.').at(-1)
    fileDownload(res, `YanXuanForSWInstaller_${version}.${ext}`)
  })
}
</script>
