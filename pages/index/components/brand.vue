<template>
  <card-skeleton :count="10" v-if="loading"></card-skeleton>
  <div class="grid grid-cols-[repeat(auto-fill,minmax(250px,1fr))] gap-x-40 gap-y-30" v-else>
    <div
      v-for="brand in brands"
      :key="brand.id"
      class="aspect-250/100 flex items-center justify-center bg-white hover:card-shadow transition-all duration-200"
      @click="emits('clickBrand', brand)"
    >
      <a href="javascript:void(0)">
        <img
          :src="useImage(useObs(brand.shopLogo))"
          :alt="brand.name"
          class="max-w-226 max-h-80 object-contain grayscale opacity-30 hover:grayscale-0 hover:opacity-100 transition-opacity duration-200"
        />
      </a>
    </div>
  </div>
</template>

<script setup>
defineProps({
  brands: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

const emits = defineEmits(['clickBrand'])

const onClickBrand = (brand) => {
  message.info('品牌馆即将上线，敬请期待！')
}
</script>
