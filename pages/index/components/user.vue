<template>
  <div class="w-full h-full flex flex-col items-center text-#333" relative bg-white>
    <template v-if="isLogin">
      <a-avatar :size="64" class="mt-36 mx-auto"></a-avatar>
      <div class="text-16 my-10 font-bold">{{ user.nickName }}</div>
      <div v-if="!!company.shopCompanyId" class="text-#7f7f7f text-14">{{ company.merchantName }}</div>

      <div flex w-full justify-around mt-20>
        <div class="text-center cursor-pointer" @click="go('/workSpace/company-space/procure-order?status=1')">
          <div class="text-primary text-20" font-bold>{{ orderInfo[1] }}</div>
          <div class="text-13">待支付</div>
        </div>
        <div class="text-center cursor-pointer" @click="go('/workSpace/company-space/procure-order?status=2')">
          <div class="text-primary text-20" font-bold>{{ orderInfo[2] }}</div>
          <div class="text-13">待发货</div>
        </div>
        <div class="text-center cursor-pointer" @click="go('/workSpace/company-space/procure-order?status=3')">
          <div class="text-primary text-20" font-bold>{{ orderInfo[3] }}</div>
          <div class="text-13">待收货</div>
        </div>
      </div>
    </template>
    <div v-else w-full px-12>
      <div class="text-16 mt-30 font-bold">您好，欢迎来到研选零部件库！</div>
      <a-button type="primary" class="w-full my-10" @click="goLogin">登录</a-button>
      <a-button w-full type="primary" ghost @click="goRegister">注册</a-button>
    </div>

    <div class="mt-36 w-full grid grid-cols-2 gap-y-20">
      <div
        class="text-center text-14 cursor-pointer transition-colors hover:text-primary"
        @click="go('/workSpace/company-space/ans-price')"
      >
        <transaction-outlined text-24 />
        <div mt-10>企业询价单</div>
      </div>
      <div
        class="text-center text-14 cursor-pointer transition-colors hover:text-primary"
        @click="go('/workSpace/company-space/procure-order')"
      >
        <file-text-outlined text-24 />
        <div mt-10>企业采购单</div>
      </div>
      <!-- <div class="text-center text-14 cursor-pointer transition-colors hover:text-primary" @click="go('/workSpace/company-space/selection-library')">
        <star-outlined text-24 />
        <div mt-10>收藏的商品</div>
      </div>
      <div
        class="text-center text-14 cursor-pointer transition-colors hover:text-primary"
        @click="
          () => {
            message.warn('品牌馆暂未开放')
          }
        "
      >
        <inbox-outlined text-24 />
        <div mt-10>关注的品牌</div>
      </div> -->
    </div>
    <template v-if="isLogin">
      <div
        v-if="specialCustomer"
        class="w-full absolute bottom-5 left-0 text-14 px-10 h-20 leading-20 my-10 text-#7f7f7f"
      >
        <div class="px-10">
          <span>企业专属客服：</span>
          <span class="text-primary mr-8">{{ specialCustomer.nickName }}</span>
          <a-tooltip placement="topLeft">
            <IdcardOutlined />
            <template #title>
              <div flex flex-col items-center>
                <a-avatar :size="64" :src="specialCustomer.pic" />
                <div class="text-14">{{ specialCustomer.nickName }}</div>
                <div class="text-14">{{ specialCustomer.userMobile }}</div>
                <div class="text-14">{{ specialCustomer.userMail }}</div>
              </div>
            </template>
          </a-tooltip>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { User, userStore } from '~/store/user'
import { companyStore } from '~/store/company'
const user = computed(() => userStore().user)
const company = computed(() => companyStore().company)
const isLogin = computed(() => user.value.userId)
const yanxuan = useRuntimeConfig().public.baseUrl.VITE_YANXUAN
const goLogin = () => {
  location.href = `${yanxuan}/login?redirect=${encodeURIComponent(location.href)}`
}
const goRegister = () => {
  location.href = `${yanxuan}/register`
}
const go = (path) => {
  if (!isLogin.value) {
    goLogin()
  } else {
    useWorkspace(() => {
      navigateTo(path)
    })
  }
}

// 获取企业订单详情
type Order = Record<number, number>
const orderInfo = ref<Order>({})
const getOrderInfo = async () => {
  const res = await http<Order>('/mall/p/myOrder/myOrderSearchConut', {
    params: {
      merchantId: company.value.shopCompanyId,
    },
  })
  useMall(res, () => {
    orderInfo.value = res.data
  })
}

// 获取企业专属客服
const specialCustomer = ref<User>()
const getSpecialCustomer = async () => {
  const res = await http<User>('/mall/p/merchant-user/customer-service/' + company.value.shopCompanyId)
  useMall(res, () => {
    specialCustomer.value = res.data
  })
}

const serviceInfo = ref<any>({})
const getServiceInfo = async () => {
  const res = await http<any>('/mall/p/merchant-user/customer-service/' + company.value.shopCompanyId)
  useMall(res, () => {
    serviceInfo.value = res.data
  })
}

onMounted(() => {
  if (isLogin.value && company.value.shopCompanyId) {
    getOrderInfo()
    getSpecialCustomer()
  }
})
</script>
