<template>
  <div class="max-w-720 mx-auto relative search-container" style="z-index: 10000;">
    <div class="search-container-glow"></div>
    <div class="home-search-wrapper">
      <!-- Category Button -->
      <div class="category-button" @click="handleCategoryClick">
        <UnorderedListOutlined class="category-icon" />
        <span class="category-text">全部分类</span>
        <div class="category-button-overlay"></div>
      </div>

      <!-- Search Input -->
      <div class="search-input-wrapper relative">
        <input v-model="searchValue" :placeholder="placeholder" class="search-input" @input="handleInputChange"
          @focus="onFocus" @keydown.enter="handleEnterSearch" @click.stop />

        <!-- Search Type Selector -->
        <div class="search-type-selector" @click="changeType">
          <span class="search-type-text">{{ currentType.name }}</span>
          <OrderedListOutlined class="search-type-icon" />
        </div>

        <!-- Search Button -->
        <div class="search-button" @click="handleSearchClick">
          <SearchOutlined class="search-button-icon" />
          <span class="search-button-text">搜索</span>
          <div class="search-button-overlay"></div>
        </div>
      </div>
    </div>

    <!-- Search Recommendations Dropdown - 移到搜索容器外部 -->
    <div v-show="showSearchRecommend" class="search-recommendations" @click.stop>
      <div class="recommendations-content">
        <div v-if="searchHistory.length === 0" class="recommendation-item">
          <div class="recommendation-content">
            <span class="recommendation-text text-gray-500">暂无搜索建议</span>
          </div>
        </div>

        <div v-for="(item, index) in searchHistory" :key="index" class="recommendation-item"
          @click.stop="onSelectionItemClick(item)">
          <div class="recommendation-content">
            <img v-if="item.brandLogo" class="recommendation-logo" :src="item.brandLogo" alt="" />
            <span class="recommendation-text">{{ item.keyword }}</span>
          </div>
          <a v-show="item.type == SearchType.history" class="recommendation-delete" href="javascript:void(0)"
            @click.stop="onDeleteHistoryClick(item)">
            删除
          </a>
        </div>
        <div v-if="!has(searchValue) && searchHistory.length" class="recommendations-clear" @click.stop>
          <a-button type="link" @click.stop="handleClearHistory" class="clear-button">
            清空搜索记录
          </a-button>
        </div>
      </div>
    </div>

    <!-- 自定义确认弹窗 -->
    <div v-show="showClearConfirm" class="custom-modal-overlay" @click="handleCancelClear">
      <div class="custom-modal" @click.stop>
        <div class="modal-header">
          <div class="modal-icon">
            <ExclamationCircleOutlined />
          </div>
          <h3 class="modal-title">确认删除</h3>
        </div>
        <div class="modal-content">
          <p class="modal-message">搜索记录将被删除，是否继续？</p>
        </div>
        <div class="modal-actions">
          <button class="modal-btn modal-btn-cancel" @click="handleCancelClear">
            取消
          </button>
          <button class="modal-btn modal-btn-confirm" @click="handleConfirmClear">
            确定
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { UnorderedListOutlined, SearchOutlined, OrderedListOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue'
import usePart, { SearchType } from '~/components/SearchBar/usePart'
import { has } from '~/utils/util'

// Props
defineProps({
  // 可以添加props来自定义搜索框行为
})

// Emits
const emit = defineEmits(['categoryClick'])

const router = useRouter()

// 搜索相关
const searchValue = ref('')

// 自定义确认弹窗状态
const showClearConfirm = ref(false)

// 使用现有的搜索逻辑
const {
  placeholder,
  onSearch,
  onFocus,
  onChange,
  currentType,
  changeType,
  showSearchRecommend,
  searchHistory,
  clearHistory,
  onSelectionItemClick,
  onDeleteHistoryClick,
} = usePart(searchValue)

// 事件处理
const handleCategoryClick = () => {
  emit('categoryClick')
  router.push('/categoryTree')
}

const handleEnterSearch = () => {
  onSearch(searchValue.value)
}

const handleSearchClick = () => {
  onSearch(searchValue.value)
}

const handleInputChange = () => {
  onChange()
}

// 自定义确认弹窗处理
const handleClearHistory = () => {
  showClearConfirm.value = true
}

const handleCancelClear = () => {
  showClearConfirm.value = false
}

const handleConfirmClear = () => {
  // 直接清空localStorage，避免使用原有的Modal.confirm
  localStorage.removeItem('searchHistory')
  // 触发搜索历史重新计算（通过输入变化）
  const currentValue = searchValue.value
  searchValue.value = ''
  searchValue.value = currentValue
  showClearConfirm.value = false
}
</script>

<style scoped>
/* 搜索容器光晕效果 */
.search-container-glow {
  position: absolute;
  top: -24px;
  left: -24px;
  right: -24px;
  bottom: -24px;
  background: radial-gradient(ellipse at center, rgba(249, 76, 48, 0.12) 0%, rgba(255, 255, 255, 0.03) 40%, transparent 70%);
  border-radius: 40px;
  filter: blur(24px);
  z-index: -1;
  animation: searchGlow 3s ease-in-out infinite alternate;
}

@keyframes searchGlow {
  0% {
    opacity: 0.6;
    transform: scale(1);
  }

  100% {
    opacity: 1;
    transform: scale(1.02);
  }
}

.home-search-wrapper {
  display: flex;
  align-items: stretch;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(24px) saturate(180%);
  border: 1px solid rgba(249, 76, 48, 0.2);
  border-radius: 28px;
  overflow: hidden;
  /* 恢复hidden保持圆角 */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15),
    0 8px 32px rgba(249, 76, 48, 0.08),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  position: relative;
}

.home-search-wrapper:hover {
  border-color: rgba(249, 76, 48, 0.4);
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.2),
    0 12px 40px rgba(249, 76, 48, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.15) inset;
  transform: translateY(-2px);
}

.home-search-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(249, 76, 48, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
}

.home-search-wrapper:hover::before {
  opacity: 1;
}

/* Category Button */
.category-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 24px;
  height: 56px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
  border-right: 1px solid rgba(255, 255, 255, 0.08);
}

.category-button:hover {
  background: rgba(249, 76, 48, 0.15);
  transform: translateY(-1px);
}

.category-button-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(249, 76, 48, 0.1) 0%, transparent 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.category-button:hover .category-button-overlay {
  opacity: 1;
}

.category-icon {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  transition: all 0.3s ease;
}

.category-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 15px;
  font-weight: 600;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.category-button:hover .category-icon,
.category-button:hover .category-text {
  color: #ffffff;
}

/* Search Input */
.search-input-wrapper {
  display: flex;
  align-items: center;
  flex: 1;
  position: relative;
  z-index: 10;
}

.search-input {
  flex: 1;
  height: 56px;
  padding: 0 24px;
  background: transparent;
  border: none;
  outline: none;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
  font-weight: 400;
}

.search-input:focus::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

/* Search Type Selector */
.search-type-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 1px solid rgba(255, 255, 255, 0.08);
  height: 40px;
  margin: 8px 0;
}

.search-type-selector:hover {
  background: rgba(255, 255, 255, 0.05);
}

.search-type-text {
  color: rgba(249, 76, 48, 0.9);
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.search-type-icon {
  color: rgba(249, 76, 48, 0.9);
  font-size: 14px;
}

/* Search Button */
.search-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 32px;
  height: 56px;
  background: linear-gradient(135deg, #f94c30 0%, #ff6b47 100%);
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.search-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(249, 76, 48, 0.4);
}

.search-button-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #ff6b47 0%, #f94c30 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.search-button:hover .search-button-overlay {
  opacity: 1;
}

.search-button-icon {
  color: #ffffff;
  font-size: 16px;
  z-index: 1;
  position: relative;
}

.search-button-text {
  color: #ffffff;
  font-size: 15px;
  font-weight: 600;
  white-space: nowrap;
  z-index: 1;
  position: relative;
}

/* Search Recommendations */
.search-recommendations {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 9999 !important;
  margin-top: 8px;
}

.recommendations-content {
  background: rgba(26, 26, 43, 0.98);
  backdrop-filter: blur(20px) saturate(180%);
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3),
    0 8px 32px rgba(249, 76, 48, 0.1),
    0 0 0 1px rgba(249, 76, 48, 0.2) inset;
  overflow: hidden;
  border: 1px solid rgba(249, 76, 48, 0.15);
  min-height: 40px;
  max-height: 400px;
  overflow-y: auto;
}

/* 自定义滚动条样式 */
.recommendations-content::-webkit-scrollbar {
  width: 6px;
}

.recommendations-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.recommendations-content::-webkit-scrollbar-thumb {
  background: rgba(249, 76, 48, 0.6);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.recommendations-content::-webkit-scrollbar-thumb:hover {
  background: rgba(249, 76, 48, 0.8);
}

.recommendation-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.recommendation-item:last-child {
  border-bottom: none;
}

.recommendation-item:hover {
  background: rgba(249, 76, 48, 0.1);
  transform: translateX(4px);
}

.recommendation-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.recommendation-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.recommendation-item:hover .recommendation-text {
  color: #ffffff;
}

.recommendation-logo {
  height: 24px;
  width: 24px;
  object-fit: contain;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  padding: 2px;
}

.recommendation-delete {
  color: rgba(249, 76, 48, 0.8);
  font-size: 13px;
  text-decoration: none;
  transition: all 0.3s ease;
  padding: 6px 12px;
  border-radius: 8px;
  background: rgba(249, 76, 48, 0.1);
}

.recommendation-delete:hover {
  background: rgba(249, 76, 48, 0.2);
  color: #ff6b47;
}

.recommendations-clear {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.08);
}

.clear-button {
  color: rgba(255, 255, 255, 0.7) !important;
  font-size: 13px;
  transition: all 0.3s ease;
}

.clear-button:hover {
  color: rgba(249, 76, 48, 0.9) !important;
}

/* 自定义确认弹窗样式 */
.custom-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: modalFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.custom-modal {
  background: rgba(26, 26, 43, 0.98);
  backdrop-filter: blur(24px) saturate(180%);
  border-radius: 20px;
  border: 1px solid rgba(249, 76, 48, 0.2);
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4),
    0 12px 40px rgba(249, 76, 48, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  min-width: 400px;
  max-width: 90vw;
  animation: modalSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.custom-modal::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(249, 76, 48, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  pointer-events: none;
}

.modal-header {
  padding: 24px 24px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  z-index: 1;
}

.modal-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(249, 76, 48, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(249, 76, 48, 0.9);
  font-size: 20px;
}

.modal-title {
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  letter-spacing: -0.02em;
}

.modal-content {
  padding: 0 24px 24px;
  position: relative;
  z-index: 1;
}

.modal-message {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.modal-actions {
  padding: 20px 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  border-top: 1px solid rgba(255, 255, 255, 0.08);
  position: relative;
  z-index: 1;
}

.modal-btn {
  padding: 10px 20px;
  border-radius: 10px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-width: 80px;
}

.modal-btn-cancel {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.modal-btn-cancel:hover {
  background: rgba(255, 255, 255, 0.15);
  color: #ffffff;
  transform: translateY(-1px);
}

.modal-btn-confirm {
  background: linear-gradient(135deg, #f94c30 0%, #ff6b47 100%);
  color: #ffffff;
  box-shadow: 0 4px 15px rgba(249, 76, 48, 0.3);
}

.modal-btn-confirm:hover {
  background: linear-gradient(135deg, #ff6b47 0%, #f94c30 100%);
  box-shadow: 0 6px 20px rgba(249, 76, 48, 0.4);
  transform: translateY(-1px);
}

.modal-btn-confirm:active {
  transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .custom-modal {
    min-width: 320px;
    margin: 20px;
  }

  .modal-actions {
    flex-direction: column;
  }

  .modal-btn {
    width: 100%;
  }
}
</style>
