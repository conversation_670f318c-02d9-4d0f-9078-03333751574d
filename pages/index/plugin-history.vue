<template>
  <div class="py-50">
    <div flex justify-around max-w-1440 overflow-x-hidden px-12 mx-auto>
      <div w-596 shrink-0 max-md="w-full">
        <h1 text-48>历史版本</h1>
        <div text-24 leading-32 mt-10 text-justify text="#000000/88">
          研选工场 Plugin for SolidWorks
          让您在熟悉的设计环境中触达研选工场海量零部件，轻松完成零部件选型、图纸查看、模型下载编辑与上传的全流程，助您提升设计效率，专注于创新本身。
        </div>
      </div>

      <img max-w-758 src="/plugin-history.png" alt="" />
    </div>

    <div max-w-1200 mt-73 mx-auto>
      <div max-w-900 mx-auto>
        <a-table
          :columns="columns"
          :data-source="dataSource"
          :pagination="{
            current: page.current,
            pageSize: page.size,
            total,
            onChange: changePage,
            hideOnSinglePage: true,
          }"
          :loading="loading"
        >
          <template #bodyCell="{ column, record }">
            <div v-if="column.dataIndex == 'action'">
              <a-button type="link" @click="showLog(record.updateLog)">更新日志</a-button>
              <a-button type="link" @click="download(record)">下载</a-button>
            </div>
          </template>
        </a-table>
      </div>
    </div>
  </div>

  <a-modal v-model:open="show" title="更新日志" :footer="null">
    <div v-html="log"></div>
  </a-modal>
</template>

<script setup>
import fileDownload from 'js-file-download'

const columns = ref([
  {
    title: '版本号',
    dataIndex: 'version',
  },
  {
    title: '时间',
    dataIndex: 'createdTime',
  },
  {
    title: '操作',
    dataIndex: 'action',
  },
])

const current = ref(1)
const size = 10

const page = computed(() => {
  return {
    current: current.value,
    size,
  }
})

const total = ref(0)
const dataSource = ref([])

const {
  data,
  execute,
  pending: loading,
} = await useAsyncData('historyList', () =>
  useNuxtApp().$platApi('/shop/pluginVersion/page', {
    params: {
      ...page.value,
      status: 1,
    },
  }),
)

watchEffect(() => {
  const ret = JSON.parse(data.value.data)
  dataSource.value = ret.records
  total.value = ret.total
})

const changePage = (_current) => {
  current.value = _current
  execute()
}

const log = ref('')
const show = ref(false)
const showLog = (_log) => {
  log.value = _log
  show.value = true
}

const download = (record) => {
  $fetch(useObs(record.filePath), {
    responseType: 'blob',
  }).then((res) => {
    const ext = record.filePath.split('.').at(-1)
    fileDownload(res, `YIDAOForSWInstaller_${record.version}.${ext}`)
  })
}
</script>
