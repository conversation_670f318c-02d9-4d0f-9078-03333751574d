<template>
  <div
    id="sview_1"
    text-16
    style="
      width: 100%;
      height: 100%;
      position: absolute;
      background-color: aliceblue;
      z-index: 0;
      user-select: none;
      overflow: hidden;
    "
  >
    <canvas
      id="gesture_canvas"
      style="display: none; position: fixed; top: 0; left: 0; z-index: -1"
      langid="warning.browserNotSupportCanvas"
    >
      您的浏览器不支持 canvas 标签
    </canvas>
  </div>
</template>

<script setup>
definePageMeta({
  layout: false,
})
useHead({
  link: [
    { rel: 'stylesheet', href: 'css/SView-badge.css' },
    { rel: 'stylesheet', href: 'css/SView-bottomMenu.css' },
    { rel: 'stylesheet', href: 'css/SView-button.css' },
    { rel: 'stylesheet', href: 'css/SView-checkbox.css' },
    { rel: 'stylesheet', href: 'css/SView-collapse.css' },
    { rel: 'stylesheet', href: 'css/SView-common.css' },
    { rel: 'stylesheet', href: 'css/SView-dialog.css' },
    { rel: 'stylesheet', href: 'css/SView-info.css' },
    { rel: 'stylesheet', href: 'css/SView-input.css' },
    { rel: 'stylesheet', href: 'css/SView-meeting.css' },
    { rel: 'stylesheet', href: 'css/SView-message.css' },
    { rel: 'stylesheet', href: 'css/SView-popover.css' },
    { rel: 'stylesheet', href: 'css/SView-popoverSelect.css' },
    { rel: 'stylesheet', href: 'css/SView-rightClickMenu.css' },
    { rel: 'stylesheet', href: 'css/SView-rightMenu.css' },
    { rel: 'stylesheet', href: 'css/SView-scrollbar.css' },
    { rel: 'stylesheet', href: 'css/SView-select.css' },
    { rel: 'stylesheet', href: 'css/SView-slider.css' },
    { rel: 'stylesheet', href: 'css/SView-switch.css' },
    { rel: 'stylesheet', href: 'css/SView-list.css' },
    { rel: 'stylesheet', href: 'css/SView-table.css' },
    { rel: 'stylesheet', href: 'css/SView-tabs.css' },
    { rel: 'stylesheet', href: 'css/SView-tree.css' },
    { rel: 'stylesheet', href: 'css/SView-treeDialog.css' },
    { rel: 'stylesheet', href: 'css/SView-viewMenu.css' },
    { rel: 'stylesheet', href: 'css/SView-joystick.css' },
  ],

  script: [
    { src: 'js/sview/sview.base.min.js' },
    { src: 'js/sview/SView.Action.js' },
    { src: 'js/sview/SViewFrame.js' },
    { src: 'js/sview/SViewControls.js' },
    { src: 'js/sview/SViewCommand.js' },
    { src: 'js/sview/SView.Service.js' },
    { src: 'js/sview/SView.Synergy.js' },

    { src: 'js/sview.html5.js' },
    { src: 'js/Components/rightMenu.js' },
    { src: 'js/Components/normolDialog.js' },
    { src: 'js/Components/attributeDialog.js' },
    { src: 'js/Components/assemblyDialog.js' },
    { src: 'js/Components/messageDialog.js' },
    { src: 'js/Components/bottomMenu.js' },
    { src: 'js/Components/nationDialog.js' },
    { src: 'js/Components/viewMenu.js' },
    { src: 'js/Components/rightClickMenu.js' },
    { src: 'js/Components/meetingDialog.js' },
    { src: 'js/Components/settingDialog.js' },
    { src: 'js/Components/colorDialog.js' },
    { src: 'js/Components/resultDialog.js' },
    { src: 'js/colorpicker.js' },
    { src: 'js/SView-tree.js' },
    { src: 'js/jszip/jszip.min.js' },
    { src: 'js/jszip/jszip-utils.min.js' },
    { src: 'js/crypto/CryptoJS.min.js' },

    { src: 'js/ClickFunction/clickFun.js' },
    { src: 'js/ClickFunction/assemblyClick.js' },
    { src: 'js/ClickFunction/RightMenu.js' },
    { src: 'js/ClickFunction/BottomMenu.js' },
  ],
})

const route = useRoute()
const modelPath = computed(() => route.query.path)

//右键菜单是否可用
let availableRightMenuClick = true
/**
 * 修改右键菜单是否可用
 * @param {any} enable
 */
function changeRightMenuClickable(enable) {
  availableRightMenuClick = enable
  rightClickMenu(availableRightMenuClick)
}

const initView = () => {
  initRightMenu()
  initRightClickMenu()
  window.sviewFrameDiv = document.getElementById('sview_1')
}
onMounted(() => {
  let basePath = location.origin + '/'

  window.language =
    M3D.Utility.CookieHelper.getCookie(M3D.Config.language) === '1'
      ? SView.UIManager.languageConstants.EN
      : SView.UIManager.languageConstants.CN

  SView.Windows.Label.setRootPath = basePath

  //初始化多语言
  initView()
  //初始化页面

  sview0 = new Main('sview_1')
  let uiManager = sview0.sviewFrame.getUIManager()
  uiManager.load(RightMenu)
  uiManager.load(RightClickMenu)
  uiManager.load(RightClickSelectMenu)
  uiManager.load(RightClickSelectMeasureMenu)
  uiManager.load(RightClickSelectAnnotateMenu)
  uiManager.load(RightClickSelectRingMenu)
  rightClickMenu(availableRightMenuClick)
  let configManager = sview0.sviewFrame.getConfigManager()

  var cookie = document.cookie

  var cookieArray = cookie.split(';')
  var path
  var isArray
  var isSvlx
  for (var i = 0; i < cookieArray.length; i++) {
    let keyValue = cookieArray[i].split('=')
    keyValue[0] = keyValue[0].replace(/\s*/g, '')
    if (keyValue[0] == 'path') {
      path = keyValue[1]
    }
    if (keyValue[0] == 'isArray') {
      isArray = keyValue[1]
    }
    if (keyValue[0] == 'isSvlx') {
      isSvlx = keyValue[1]
    }
  }

  configManager.setCommandConfigPath(basePath + 'config/commandParameters.json')
  M3D.Config.Parameters.getInstance().setParameter(M3D.Config.licencePath, basePath + 'lic')
  M3D.Config.Parameters.getInstance().setParameter(M3D.Config.aniToolFileUrl, basePath)
  configManager.loadCommandConfigs().then((ret) => {
    let commandsManager = sview0.sviewFrame.getCommandsManager()
    let openCommand = null
    if (ret) {
      openCommand = commandsManager.getCommand(SView.Commands.CommandNames.OPENSAMPLEFILE)
    } else {
      //没有配置文件的情况
      let array = new Map()
      array.set('fullClassName', 'SView.Commands.OpenCommand')
      openCommand = commandsManager.getCommand(SView.Commands.CommandNames.OPENSAMPLEFILE, array)
    }
    if (isArray == 'false') {
      openCommand.path = path
    } else if (path) {
      let paths = path.split(',')
      for (let i = 0; i < paths.length; i++) {
        openCommand.addPath(paths[i])
      }
    }

    if (modelPath.value) {
      openCommand.path = modelPath.value
      commandsManager.execute(SView.Commands.CommandNames.OPENSAMPLEFILE)
    }
  })
  //点击其他位置隐藏
  document.documentElement.onclick = function (e) {
    sview0.sviewFrame.getUIManager().hideLabel('treeRightClickMenu')
    //右键菜单隐藏
    var rm1
    var rm2
    var rm3
    var rm4
    var rm5
    rm1 = document.getElementById(RightClickSelectMenu.RightClickMenu.id)
    rm2 = document.getElementById(RightClickMenu.RightClickMenu.id)
    rm3 = document.getElementById(RightClickSelectAnnotateMenu.RightClickMenu.id)
    rm4 = document.getElementById(RightClickSelectMeasureMenu.RightClickMenu.id)
    rm5 = document.getElementById(RightClickSelectRingMenu.RightClickMenu.id)
    if (rm1 && rm1.style) {
      rm1.style.display = 'none'
    }
    if (rm2 && rm2.style) {
      rm2.style.display = 'none'
    }
    if (rm3 && rm3.style) {
      rm3.style.display = 'none'
    }
    if (rm4 && rm4.style) {
      rm4.style.display = 'none'
    }
    if (rm5 && rm5.style) {
      rm5.style.display = 'none'
    }
  }
})

useEventListener(
  'wheel',
  (event) => {
    // 阻止默认滚动行为
    event.preventDefault()
    // 触发父窗口的滚动限制
    window.parent.scrollTo(window.parent.scrollX, window.parent.scrollY) // 维持父窗口当前位置
  },
  { passive: false },
)

//
// //初始化各个控件库
// function initView() {
//     initRightMenu();
//     initRightClickMenu();
//     window.sviewFrameDiv = document.getElementById("sview_1");
// }
//   console.log('mmm', SView)
// })
</script>
