<template>
  <div class="index-page min-h-screen flex flex-col h-full">
    <NuxtLink to="/" class="logo-container">
      <img src="~/assets/images/logo_w.png" />
      <span inline-block color-white font-size-14 p-l-8 p-b-4>装备产业互联网平台</span>
    </NuxtLink>
    <global-header theme="dark" />
    <div class="search-container">
      <div class="search-container__inner">
        <div class="input-search-top">
          <!-- <a-popover placement="left">
            <template #content>
              <p>AI选型功能尚在研发中，敬请期待。</p>
            </template>
            <img class="daodao" src="~assets/images/daodao_off.png" />
          </a-popover> -->
        </div>
        <search-bar size="large"></search-bar>
      </div>
    </div>
    <div class="statistics-container">
      <div class="statistics-container__inner">
        <div class="statistics" v-for="(item, index) in statisticsList" :key="index">
          <count-up class="figure" :end-val="item.figure">
            <template #suffix>
              <span>{{ item.suffix }}</span>
            </template>
          </count-up>
          <span class="text">{{ item.text }}</span>
        </div>
      </div>
    </div>
    <page-footer></page-footer>
    <side-widgets />
  </div>
</template>

<script lang="ts" setup>
import CountUp from 'vue-countup-v3'
import { getUserDetail } from '@/api/user'

interface StatisticsItem {
  figure: number
  prefix?: string
  suffix?: string
  text: string
}

const statisticsList = ref<StatisticsItem[]>([
  {
    figure: 10,
    suffix: '亿',
    text: '零部件3D模型免费下',
  },
  {
    figure: 2000,
    suffix: '万',
    text: '标准件资料免费查',
  },
  { figure: 500, suffix: '万', text: '与海量工程师/采购/供应商交朋友' },
  { figure: 5000, suffix: '', text: '品牌商入驻' },
  // { figure: 1000, suffix: '', text: '源厂制造商直谈' },
])

const route = useRoute()
onMounted(async () => {
  const token: any = route.query.token
  if (!!token) {
    // 携带token跳转
    localStorage.setItem('access_token', token)
    getUserDetail()
  }
})
</script>

<style lang="less" scoped>
.index-page {
  .global-header-component {
    position: absolute;
    width: 100%;
    background-color: transparent !important;
    z-index: 999;
  }

  .logo-container {
    @apply left-20 xl:left-[calc(50vw-640px)];
    position: absolute;
    // left: calc(50vw - 640px);
    top: 52px;
    z-index: 1;

    img {
      height: 35px;
      vertical-align: bottom;
    }
  }

  .search-container {
    position: relative;
    height: calc(65vh - 100px);
    min-height: 500px;
    text-align: center;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url('~/assets/images/background.jpg');
      background-size: cover;
      backdrop-filter: blur(100px);
      z-index: -1;
    }

    &__inner {
      @apply w-full max-w-900 max-xl:px-20;
      position: relative;
      top: calc(50% - 20px);
      display: inline-block;

      .input-search-top {
        display: flex;
        align-items: flex-end;

        .daodao {
          display: inline-block;
          width: 80px;
          margin: 0 40px 0 16px;
        }

        .search-tag-list {
          text-align: left;
          margin-bottom: 20px;

          .search-tag {
            padding: 6px 8px;
            color: #fff;
            margin-right: 32px;
            border-radius: 2px;
            cursor: pointer;

            &.selected {
              position: relative;
              background-color: #fff;
              color: #1c344f;

              &::after {
                content: '';
                position: absolute;
                bottom: -10px;
                left: 50%;
                transform: translateX(-50%);
                border-left: 10px solid transparent;
                border-right: 10px solid transparent;
                border-top: 10px solid #fff;
              }
            }
          }
        }
      }
    }
  }

  .statistics-container {
    @apply py-20 md:py-60;

    &__inner {
      @apply max-w-1280 flex-wrap gap-y-20 grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 divde-y-3 md:divide-y-0 xl:divide-x-3 divide-primary mx-auto;

      .statistics {
        @apply w-full shrink-0 flex flex-col justify-center text-center;

        // border-right: 3px solid #0093fa;
        // &:first-child {
        //   padding-left: 0;
        // }
        // &:last-child {
        //   padding-right: 0;
        //   border-right: none;
        // }
        .figure {
          display: block;
          margin-bottom: 8px;
          font-size: 48px;
          color: var(--primary-color);
        }

        .text {
          font-size: 20px;
          white-space: nowrap;
        }
      }
    }
  }

  .foot-container {
    // height: calc(35vh - 100px);
    // min-height: 230px;
  }
}
</style>
