<template>
  <div flex :class="reverse ? 'flex-row-reverse' : ''" max-lg="flex-col" px-10>
    <div
      w="1/2"
      h-320
      :class="{
        'mt-30': reverse,
        'z-10': reverse,
      }"
      max-lg="mt-10! w-full"
      rounded-4
      pro
    >
      <slot v-if="$slots.default"></slot>
      <img v-else w-full block h-auto :src="item.pic" alt="" relative loading="lazy" />
    </div>
    <div
      w="1/2"
      relative
      h-320
      card-shadow
      px-60
      :class="{
        'mt-30': !reverse,
        'z-10': !reverse,
      }"
      max-lg="w-full h-auto pb-20 rounded-4 mt-10!"
    >
      <img src="/top.svg" absolute w-48 h-32 top-20 left-50 alt="" />
      <img src="/bottom.svg" absolute w-48 right-60 bottom-58 max-lg="hidden" alt="" />
      <div text-24 font-bold mt-80>{{ item.title }}</div>
      <div text="16 #5f7d95" mt-15 line-height-24>{{ item.content }}</div>
      <nuxt-link v-if="item.link" :to="item.link">
        <a-button type="primary" mt-45>了解更多</a-button>
      </nuxt-link>
    </div>
  </div>
</template>

<script lang="ts" setup>
type Item = {
  pic: string
  title: string
  content: string
  link: string
  name?: string
}
defineProps<{
  item: Item
  reverse?: boolean
}>()
</script>
