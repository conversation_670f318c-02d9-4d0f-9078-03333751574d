<script lang="jsx">
import { companyStore } from '~/store/company'

export default defineComponent({
  props: ['item'],
  setup(props) {
    const ic = (cls) => `i-a-${cls} text-16!`
    const renderItem = (data) => {
      const { name, icon, key, personalSpaceDisplay } = data
      return (
        <a-menu-item
          key={key}
          v-slots={{
            icon: () => <i class={ic(icon)}></i>,
          }}
        >
          <div class="flex items-center gap-8">
            {name}
            {company.isPersonal && !personalSpaceDisplay && <i class={ic('lock-outlined')}></i>}
          </div>
        </a-menu-item>
      )
    }
    const company = companyStore()
    return () => {
      const { name, icon, key, type, children = [] } = props.item
      if (type == '1') {
        return <>{renderItem(props.item)}</>
      } else {
        return (
          <a-sub-menu
            key={key}
            v-slots={{
              icon: () => <i class={ic(icon)}></i>,
              title: () => <span>{name}</span>,
            }}
          >
            {children.map((child) => renderItem(child))}
          </a-sub-menu>
        )
      }
    }
  },
})
</script>
