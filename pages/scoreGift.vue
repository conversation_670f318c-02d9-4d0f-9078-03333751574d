<template>
  <NuxtLayout name="selection">
    <div p-y-8>
      <div flex flex-row justify-start mb-8>
        <a-button type="link" size="small" mr-10 @click="() => $router.go(-1)">&lt; 返回</a-button>
        <a-breadcrumb separator=">">
          <a-breadcrumb-item>
            <NuxtLink to="/">首页</NuxtLink>
          </a-breadcrumb-item>
          <a-breadcrumb-item>积分商城</a-breadcrumb-item>
        </a-breadcrumb>
      </div>

      <div mt-8 flex flex-col class="content-box">
        <a-carousel class="carousel" autoplay dotPosition="bottom">
          <div>
            <NuxtLink :to="{ path: '/agreement/scoreConfig' }" target="_blank">
              <img w-full src="~/assets/images/gift_banner.jpg" />
            </NuxtLink>
          </div>
        </a-carousel>

        <a-divider>积分好礼</a-divider>

        <div flex flex-wrap>
          <div
            v-for="(item, index) in giftList"
            :key="index"
            card--reactive
            hover:translate-y-3
            class="gift-item"
            @click="goToDetail(item)"
          >
            <div w-120 h-120 overflow-hidden>
              <img w-full h-full object-scale-down :src="useImage(convertImage(item.pic))" alt="" />
            </div>
            <div class="gift-name" truncate :title="item.prodName">
              {{ item.prodName }}
            </div>
            <div sub-text mb-8 line-through>市场价：¥{{ item.oriPrice.toFixed(2).toLocaleString() }}</div>
            <div class="gift-score">
              <span font-size-20>{{ item.scorePrice.toLocaleString() }}</span>
              积分
            </div>
          </div>
        </div>
        <div v-if="!giftList.length" pt-100 style="text-align: center">
          <img h-181px src="~/assets/images/daodao_nodata.png" />
          <span text-18px color-primary font-bold>什么也没有...</span>
        </div>
      </div>
    </div>
  </NuxtLayout>
</template>

<script setup>
const router = useRouter()
const route = useRoute()

const giftList = ref([])

const goToDetail = ({ prodId, prodName }) => {
  router.push({
    path: `/giftDetail`,
    query: {
      prodId,
      prodName,
    },
  })
}

const getList = () => {
  http('/mall/search/page', {
    method: 'get',
    params: {
      current: 1,
      size: 200,
      prodType: 3,
      sort: 8,
    },
  }).then((res) => {
    useMall(res, (ret) => {
      if (ret.records && ret.records.length) {
        giftList.value = ret.records[0].products.map((ele) => {
          !ele.oriPrice && (ele.oriPrice = 0)
          !ele.scorePrice && (ele.scorePrice = 0)
          return ele
        })
      }
    })
  })
}

onMounted(() => {
  getList()
})
</script>

<style lang="less" scoped>
.content-box {
  min-height: calc(100vh - 225px);
  position: relative;

  .carousel {
    width: 100%;
    height: 200px;
  }

  .ant-carousel :deep(.slick-slide) {
    text-align: center;
    height: 200px;
    line-height: 200px;
    background: #364d79;
    overflow: hidden;
  }

  .ant-carousel :deep(.slick-slide h3) {
    color: #fff;
  }

  .gift-item {
    width: 18%;
    min-width: 200px;
    margin: 0 1% 20px;
    height: 250px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 16px;

    .gift-name {
      font-size: 16px;
      font-weight: bold;
      line-height: 50px;
      word-break: keep-all;
      max-width: 90%;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .gift-score {
      font-size: 14px;
      font-weight: bold;
      color: var(--primary-color);
    }
  }
}
</style>
