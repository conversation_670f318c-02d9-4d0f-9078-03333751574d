<template>
  <a-layout class="box">
    <a-layout-content>
      <a-row  mb-10>
        <a-col :span="24">
          <a-button type="primary" style="margin-right: 10px" @click="onReadAll">全部标记为已读</a-button>
          <a-button type="primary" @click="onDelAll">全部清除</a-button>
        </a-col>
      </a-row>

      <a-collapse v-model:activeKey="activeKey" :bordered="true">
        <a-collapse-panel 
          v-for="(item, index) in list"
          :key="index"
          :show-arrow="false"
          class="panel-item"
          @click="onRead(item, index)"
        >
          <template #header>
            <div class="title-box">
              <div class="dot">
                <a-badge :count="item.status ? 0 : 1" dot></a-badge>
              </div>
              <div>
                <span>{{ item.message.title }}</span>
              </div>
            </div>
          </template>
          <p pl-20 style="word-break: break-all;" v-html="item.message.message"></p>
          <template #extra>
            <CloseOutlined class="close" @click.stop="onDel(item, index)" />
          </template>
        </a-collapse-panel>
      </a-collapse>

      <a-pagination
        mt-10 text-right
        v-if="list.length"
        v-model:current="page.page"
        v-model:page-size="page.size"
        :total="page.total"
        @change="pageChange"
      />

      <Empty mt-50 v-if="!list.length"></Empty>
    </a-layout-content>
  </a-layout>
</template>

<script setup>
import { userStore } from '~/store/user'
import { messageStore } from '~/store/message';

const page = reactive({
  page: 1,
  size: 10,
  total: 0,
})
const pageChange = (pageNo, pageSize) => {
  activeKey.value = []
  page.page = pageNo
  page.size = pageSize
  getList()
}

const activeKey = ref([])
const user = userStore()
const list = ref([])
const getList = () => {
  http('/mall/p/myNotifyLog/unReadCountList', {
    method: 'get',
    params: {
      size: page.size,
      current: page.page,
      remindId: user.user.userId,
      userMobile: user.user.userMobile
    }
  }).then(res => {
    useMall(res, (ret) => {
      page.total = ret.total
      list.value = (ret.records || []).map(d => {
        try {
          d.message = d.message ? JSON.parse(d.message) : {}
        } catch (err) {
          d.message = d.message
        }
        if (typeof d.message == 'string') {
          d.message = {
            title: d.message,
            message: d.message
          }
        }
        return d
      })
      
      if (!list.value.length && page.page > 1) {
        page.page = 1
        getList()
      }
    })
  })
}

const messageStoreObj = messageStore();
const { refreshMessage } = messageStoreObj;
const onRead = (r, i) => {
  if (r.status == 1) return
  list.value[i].status = 1
  readMsg([r.logId])
}

const onReadAll = () => {
  list.value.forEach(d => d.status = 1)
  const ids = list.value.map(d => d.logId)
  readMsg()
}

const readMsg = (logIds) => {
  http('/mall/p/myNotifyLog/batchRead', {
    method: 'post',
    body: {
      logIds
    }
  }).then(res => {
    useMall(res, refreshMessage)
  })
}

const onDelAll = () => {
  const ids = list.value.map(d => d.logId)
  delMsg()
}

const onDel = (r, i) => {  
  delMsg([r.logId])
}

const delMsg = (logIds) => {
  Modal.confirm({
    title: '提示',
    content: '确定要删除吗？',
    okText: '确定',
    cancelText: '取消',
    onOk() {
      http('/mall/p/myNotifyLog/batchDelete', {
        method: 'post',
        body: {
          logIds
        }
      }).then(res => {
        useMall(res, () => {
          getList()
          refreshMessage()
        })
      })
    },
    onCancel() {
      console.log('Cancel')
    },
  })
}

onMounted(() => {
  getList();
})
</script>

<style lang="less" scoped>
  .title-box{
    display: flex;
    align-items: center;

    .dot{
      width: 20px;
    }
  }
  .box{
    background: #fff;
    width: 100%;
    padding: 20px;

    .title{
      font-size: 20px;
      font-weight: bold;
    }

    .time{
      padding: 20px 0 20px;
      color: #aaa;
    }

    .content{
      border-top: 1px solid #eee;
      padding: 10px 0;
      color: #999;
    }
  }

  .panel-item{
    .close{
      display: none;
    }

    &:hover{
      .close{
        display: inline-block;
      }
    }
  }

  .button{
    padding: 40px;
  }
</style>
