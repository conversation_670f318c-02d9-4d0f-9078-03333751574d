<template>
  <a-layout class="box">
    <a-layout-content>
      <Empty mt-50 v-if="!list.length"></Empty>
    </a-layout-content>
  </a-layout>
</template>

<script setup>
import { getInviteCode } from '~/api/user';

const activeKey = ref([])

const list = ref([])

onMounted(() => {
  
})
</script>

<style lang="less" scoped>
  .title-box{
    display: flex;
    align-items: center;

    .dot{
      width: 20px;
    }
  }
  .box{
    background: #fff;
    width: 100%;
    padding: 20px;

    .title{
      font-size: 20px;
      font-weight: bold;
    }

    .time{
      padding: 20px 0 20px;
      color: #aaa;
    }

    .content{
      border-top: 1px solid #eee;
      padding: 10px 0;
      color: #999;
    }
  }

  .panel-item{
    .close{
      display: none;
    }

    &:hover{
      .close{
        display: inline-block;
      }
    }
  }

  .button{
    padding: 40px;
  }
</style>
