<template>
  <a-layout class="box">
    <a-layout-content>
      <a-spin :spinning="loading">
        <template v-if="notice.id">
          <div class="title">{{ notice.title }}</div>

          <div class="time">发布时间： {{ notice.publishTime }}</div>

          <div class="content" v-html="notice.content" />
        </template>
        <Empty mt-50 v-else></Empty>
      </a-spin>
    </a-layout-content>
  </a-layout>
</template>

<script setup>
import { getNoticeDetails } from '~/api/user';
import { messageStore } from '~/store/message';

const messageStoreObj = messageStore();
const { messages } = messageStoreObj;
const notice = ref({})
const loading = ref(false)
const init = () => {
  const { noticeId } = messages
  getNoticeDetails(noticeId).then((res) => {
    notice.value = res.data || {}
  }).finally(() => {
    loading.value = false
  })
};

onMounted(() => {
  loading.value = true
  setTimeout(() => {
    init()
  }, 1000);
});
</script>

<style lang="less" scoped>
  .box{
    background: #fff;
    width: 100%;
    padding: 20px;

    .title{
      font-size: 20px;
      font-weight: bold;
    }

    .time{
      padding: 20px 0 20px;
      color: #aaa;
    }

    .content{
      border-top: 1px solid #eee;
      padding: 10px 0;
      color: #999;
    }
  }

  .input{
    width: 70%;
  }

  .button{
    padding: 40px;
  }
</style>
