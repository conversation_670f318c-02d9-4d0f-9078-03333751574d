<template>
  <Limit w-full mt-30 v-if="user.user.status == 2" />
  <ClientOnly v-else>
    <div flex-1 p-20 class="address">
      <div grid grid-cols-3 gap-20>
        <div
          class="card h-200 px-20 relative"
          v-for="item in list"
          :key="item.addrId"
        >
          <div h-full w-full flex items-center overflow-hidden>
            <a-form size="small" :labelCol="{ flex: '70px' }">
              <a-form-item label="联系人">{{ item.receiver }}</a-form-item>
              <a-form-item label="所在地区">
                {{ formatAddress(item) }}
              </a-form-item>
              <a-form-item label="详细地址">
                <div break-all line-clamp-2>{{ item.addr }}</div>
              </a-form-item>
              <a-form-item label="手机号">{{ item.mobile }}</a-form-item>
            </a-form>
            <a-tag
              color="green"
              absolute
              top-10
              right-0
              v-if="item.commonAddr == 1"
            >
              默认地址
            </a-tag>
            <div absolute bottom-0 right-0>
              <a-button
                type="link"
                v-if="item.commonAddr == 0"
                @click="setDefault(item.addrId)"
              >
                设为默认地址
              </a-button>
              <a-button type="link" @click="showEdit(item)">编辑</a-button>
              <a-button
                type="link"
                v-if="item.commonAddr == 0"
                @click="remove(item.addrId)"
              >
                删除
              </a-button>
            </div>
          </div>
        </div>
        <div class="card h-200 hover:translate-y-3" v-if="list.length < 3">
          <div
            flex
            items-center
            flex-col
            pt-60
            cursor-pointer
            h-full
            @click="showAdd"
          >
            <PlusCircleOutlined text="40 gray" />
            <div text-gray text-14 mt-10>新增地址</div>
          </div>
        </div>
      </div>

      <a-modal v-model:open="open" :title="title" @ok="handleOk" :afterClose="close">
        <a-spin :spinning="loading">
          <a-form
            :label-col="{ span: 4 }"
            :model="form"
            ref="formRef"
            :rules="rules"
          >
            <a-form-item label="联系人" name="receiver">
              <a-input v-model:value.trim="form.receiver"></a-input>
            </a-form-item>

            <a-form-item label="所在地区" name="address">
              <a-cascader
                v-model:value="form.address"
                :options="addressOptions"
                :load-data="loadData"
                :field-names="{
                  label: 'areaName',
                  value: 'areaId',
                }"
                @change="change"
              ></a-cascader>
            </a-form-item>

            <a-form-item label="详细地址" name="addr">
              <a-input
                v-model:value.trim="form.addr"
                :maxlength="30"
                show-count
              ></a-input>
            </a-form-item>

            <a-form-item label="手机号" name="mobile">
              <a-input v-model:value.trim="form.mobile"></a-input>
            </a-form-item>
          </a-form>
        </a-spin>
      </a-modal>
    </div>
  </ClientOnly>
</template>

<script setup>
import {userStore} from '~/store/user';

const list = ref([])
const getAddress = async () => {
  const res = await http('/mall/p/address/list')
  useMall(res, () => {
    list.value = res.data
  })
}

const user = userStore()

const title = ref('')
const open = ref(false)
const form = ref({})
const formRef = ref()

let count = 0
const showAdd = () => {
  title.value = '新增'
  open.value = true
  count = 0
  form.value = {
    addrId: 0,
  }
  loadData()
}
const showEdit = async (item) => {
  loading.value = true
  title.value = '编辑'
  open.value = true
  count = 0
  const { provinceId, province, cityId, city, areaId, area } = item
  await loadData()
  const p = addressOptions.value.filter((a) => a.areaId == provinceId)
  await loadData(p)
  const c = p[0].children.filter((a) => a.areaId == cityId)
  await loadData(c)
  loading.value = false

  form.value = pick(item, ['addrId', 'receiver', 'mobile', 'addr'])
  form.value.address = [provinceId, cityId, areaId]
  addressSelect.value = [
    {
      areaName: province,
      areaId: provinceId,
    },
    {
      areaName: city,
      areaId: cityId,
    },
    {
      areaName: area,
      areaId,
    },
  ]
}

const loading = ref(false)

const addressOptions = ref([])
const addressSelect = ref()
const loadData = async (selectOptions) => {
  if (count == 0) {
    const res = await http('/mall/p/area/listByPid', {
      params: {
        level: 1,
      },
    })
    useMall(res, () => {
      count++
      addressOptions.value = res.data.map((item) => {
        item.isLeaf = false
        return item
      })
    })
  } else {
    const targetOption = selectOptions.at(-1)
    targetOption.loading = true
    const res = await http('/mall/p/area/listByPid', {
      params: {
        pid: targetOption.areaId,
      },
    })
    targetOption.loading = false
    useMall(res, () => {
      targetOption.children = res.data.map((item) => {
        item.isLeaf = item.level == 3
        return item
      })
      addressOptions.value = [...addressOptions.value]
    })
  }
}
const change = (ids, options) => {
  addressSelect.value = options
}

const rules = {
  receiver: [{ required: true, message: '请输入联系人' }],
  address: [{ required: true, message: '请选择地址' }],
  addr: [{ required: true, message: '请输入详细地址' }],
  mobile: [
    {
      required: true,
      message: '请输入手机号',
    },
    {
      pattern: MOBILE_REG.reg,
      message: MOBILE_REG.msg,
    },
  ],
}

const formData = computed(() => {
  const obj = pick(form.value, ['addr', 'mobile', 'receiver', 'addrId'])
  if (addressSelect.value) {
    const [a, b, c] = addressSelect.value
    obj.province = a.areaName
    obj.provinceId = a.areaId
    obj.city = b.areaName
    obj.cityId = b.areaId
    obj.area = c.areaName
    obj.areaId = c.areaId
  }
  return obj
})

const formatAddress = (item) => {
  const { province, city, area } = item
  return `${province}${city}${area}`
}

const handleOk = () => {
  formRef.value.validate().then(async () => {
    if (title.value == '新增') {
      const res = await http('/mall/p/address/addAddr', {
        method: 'post',
        body: formData.value,
      })
      useMall(res, () => {
        message.success(res.data)
        open.value = false
        getAddress()
      })
    } else {
      const res = await http('/mall/p/address/updateAddr', {
        method: 'put',
        body: formData.value,
      })
      useMall(res, () => {
        message.success(res.data)
        open.value = false
        getAddress()
      })
    }
  })
}

const setDefault = async (id) => {
  const res = await http('/mall/p/address/defaultAddr/' + id, {
    method: 'put',
  })
  useMall(res, () => {
    message.success('修改默认地址成功')
    getAddress()
  })
}

const remove = (id) => {
  Modal.confirm({
    title: '提示',
    content: '确定要删除该收货地址吗?',
    onOk: async () => {
      const res = await http('/mall/p/address/deleteAddr/' + id, {
        method: 'delete',
      })
      useMall(res, () => {
        message.success(res.data)
        getAddress()
      })
    },
  })
}

const close = () => {
  formRef.value?.clearValidate()
}

onMounted(() => {
  getAddress()
})
</script>

<style lang="less" scoped>
.address {
  :deep(.ant-form-item) {
    margin-bottom: 5px !important;
  }
}
</style>
