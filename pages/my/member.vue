<template>
  <Limit w-full mt-30 v-if="user.user.status == 2" />
  <div w-full flex justify-between px-50 py-30 v-else>
    <div
      class="card-shadow flex flex-col text-center w-225 h-300 text-14 overflow-hidden border-rd-8"
      v-for="(item, i) in list"
      :key="i"
    >
      <div h-50 :style="{ backgroundColor: item.headColor }">
        <div text="20 white" line-height-50>{{ item.title }}</div>
      </div>
      <div py-8 flex-1 line-height-20 flex flex-col justify-center>
        <div v-for="text in item.texts" :key="text" sub-text>{{ text }}</div>
      </div>
      <div>
        <div title-text>条件要求</div>
        <div class="mt-12 mb-24 text-12">{{ item.condition }}</div>
      </div>
      <div h-40>
        <div>
          <client-only>
            <a-button
              type="primary"
              :disabled="btnDisable(i)"
              v-if="getStep(step) < i"
              @click="update(item.type)"
              :loading="item.type == MemberLevel.GENERALIZATION && loading"
            >
              {{ text(i) }}
            </a-button>
            <CheckCircleFilled v-else text-24 block color="#00AA40" />
          </client-only>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { upgradeApply } from '~/api/user'
import { ApplyStatus, MemberLevel, User, userStore } from '~/store/user'

const user = userStore()

const step = computed(() => {
  return user.user.level
})
const list = ref([
  {
    title: '普通会员',
    headColor: '#71c4ef',
    texts: ['免费查询产品/供应商', '免费预览产品文档/3D模型'],
    condition: '无',
    type: MemberLevel.NORMAL,
  },
  {
    title: '认证会员',
    headColor: '#67abff',
    texts: ['普通会员全部权益', '云端个人空间'],
    condition: '绑定手机号',
    type: MemberLevel.AUTHENTICATION,
  },
  {
    title: '专业会员',
    headColor: 'var(--primary-color)',
    texts: [
      '认证会员全部权益',
      // '兑换积分商品',
      '创建或加入企业空间',
      '开通全网买或全网推服务',
      '行业价格及趋势预测（即将推出）',
    ],
    condition: '填写所有职业信息并上传职业证照',
    type: MemberLevel.PROFESSIONAL,
  },
  {
    title: '推广会员',
    headColor: '#1c344f',
    texts: ['专业会员全部权益', '推广一刀云产品获得收益分享'],
    condition: '能触达大量机械设计工程师',
    type: MemberLevel.GENERALIZATION,
  },
])

const update = (level: MemberLevel) => {
  if (level == MemberLevel.AUTHENTICATION) {
    authMember()
  } else if (level == MemberLevel.PROFESSIONAL) {
    specialMemeber()
  } else if (level == MemberLevel.GENERALIZATION) {
    proMember()
  }
}

// 认证会员
const authMember = () => {
  Modal.confirm({
    title: '提示',
    content: '请先绑定手机号',
    okText: '去绑定',
    onOk() {
      navigateTo({ path: '/my/info' })
    },
  })
}

const loading = ref(false)
const { refreshUser } = useUser()
const specialMemeber = () => {
  Modal.confirm({
    title: '提示',
    content: '填写所有职业信息并上传职业证照',
    okText: '去完善',
    onOk() {
      navigateTo({ path: '/my/info' })
    },
  })
}
// 推广会员
const proMember = async () => {
  // 检查是否有未补充的职业信息
  const fields: Array<keyof User> = ['realName', 'company', 'identity', 'position', 'workPermit']
  const hasUndef = fields.some((item) => !user.user[item])

  if (hasUndef) {
    Modal.confirm({
      title: '提示',
      content: '请先补充职业信息',
      okText: '去补充',
      onOk() {
        navigateTo({ path: '/my/info' })
      },
    })
  } else {
    loading.value = true
    const res = await upgradeApply('GENERALIZATION')
    loading.value = false
    useRes(res, () => {
      message.success('您已发送升级申请, 请耐心等待')
      refreshUser()
    })
  }
}

const getStep = (level: MemberLevel) => {
  switch (level) {
    case MemberLevel.NORMAL:
      return 0
    case MemberLevel.AUTHENTICATION:
      return 1
    case MemberLevel.PROFESSIONAL:
      return 2
    case MemberLevel.GENERALIZATION:
      return 3
  }
}

const btnDisable = (level: number) => {
  const _current = getStep(step.value)
  if (_current + 1 < level) return true

  if (_current == 2) {
    const status = user.user.upgradeStatus
    if (status == ApplyStatus.PROCESSING) return true
  }
  return false
}

const text = (level: number) => {
  if (level < 3) return '升级'
  if (level == 3) {
    if (user.user.upgradeStatus == ApplyStatus.PROCESSING) return '已发送申请'
  }
  return '升级'
}
</script>
