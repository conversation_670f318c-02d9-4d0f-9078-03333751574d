<template>
  <Limit w-full mt-30 v-if="user.user.status == 2" />
  <div text-16 class="box" v-else>
    <div pb-10>
      1、邀请链接
    </div>
    <div>
      <input v-model="inviteCode" readonly class="input"  />
      <a-button  class="btn" type="primary" @click="onCopy">一键复制</a-button>
    </div>
    <div class="tip">邀请步骤: 复制您的链接,通过微信/QQ/短信等方式发送给您想邀请的好友或群组。</div>

    <div py-10>
      2、邀请二维码
    </div>
    <div v-if="!srcData" style="display: inline-block; position: relative;" id="mySharePost" :oncontextmenu="() => {return false}">
      <div class="img">
        <img mb-24 w-500 src="~/assets/images/invite_bg.jpg"/>
      </div>
      <div class="qrcode-box">
        <figure class="qrcode">
          <vue-qrcode :value="inviteCode" tag="svg" :size="110"></vue-qrcode>
          <img
            class="qrcode__image"
            :src="yidao"
          />
        </figure>
      </div>
    </div>

    <div v-else>
      <img :src="srcData"/>
    </div>
    <div>
      <a id="link"></a>
      <a-button size="mini" type="primary" @click="saveImage()">下载海报</a-button>
    </div>
    <div class="tip" mb-8>邀请步骤: 下载您的分享海报，通过微信/QQ等方式发送给您想邀请的好友或群组。</div>


    <!-- <div id="mySharePost2" :oncontextmenu="() => {return false}">
      <div>
        <figure class="qrcode">
          <vue-qrcode :value="inviteCode" tag="svg" :size="6000"></vue-qrcode>
          <img
            class="qrcode__image"
            :src="yidao"
          />
        </figure>
      </div>
    </div>
    
    <div>
      <a-button size="mini" type="primary" @click="saveImage2()">下载二维码</a-button>
    </div> -->
  </div>
</template>

<script setup>
import html2canvas from "html2canvas"
import VueQrcode from 'vue-qrcode'
import { getInviteCode } from '~/api/user'
import special from "~/assets/images/score-gift/special.jpg"
import yidao from "~/assets/images/yidao.png"
import {userStore} from "~/store/user"

const user = userStore()

const onCopy = async () => {
  const textArea = document.createElement('textArea')
  textArea.value = inviteCode.value
  textArea.style.width = 0
  textArea.style.position = 'fixed'
  textArea.style.left = '-999px'
  textArea.style.top = '10px'
  document.body.appendChild(textArea)
  textArea.select()
  document.execCommand('copy')
  // 移除元素
  document.body.removeChild(textArea)
  message.success('复制成功')

  // await navigator.clipboard.writeText(inviteCode.value)
}

const inviteCode = ref('')
const getFavouriteCaller = () => {
  getInviteCode().then((res) => {
    useMall(res, () => {
      inviteCode.value = window.location.origin + '/?invite=' + res.data.cipherText
      setTimeout(() => {
        mergePic()
      }, 100)
    })
  })
}

const saveImage = () => {
  const link = document.getElementById("link");
  link.setAttribute("download", "邀请海报.png");
  link.setAttribute("href", srcData.value);
  link.click();

  // html2canvas(document.querySelector("#mySharePost")).then((canvas) => {
  //   const image = canvas.toDataURL("image/png").replace("image/png", "image/octet-stream");
  //   const link = document.getElementById("link");
  //   link.setAttribute("download", "邀请海报.png");
  //   link.setAttribute("href", image);
  //   link.click();
  // });
}

const saveImage2 = () => {
  html2canvas(document.querySelector("#mySharePost2")).then((canvas) => {
    var context = canvas.getContext("2d");
    context.mozImageSmoothingEnabled = false;
    context.webkitImageSmoothingEnabled = false;
    context.msImageSmoothingEnabled = false;
    context.imageSmoothingEnabled = false;
    const image = canvas.toDataURL("image/png").replace("image/png", "image/octet-stream");
    const link = document.getElementById("link");
    link.setAttribute("download", "二维码.png");
    link.setAttribute("href", image);
    console.log(image)
    link.click();
  });
}

const srcData = ref('')
const mergePic = () => {
  html2canvas(document.querySelector("#mySharePost")).then((canvas) => {
    const image = canvas.toDataURL("image/png").replace("image/png", "image/octet-stream");
    srcData.value = image
  })
}

onMounted(() => {
  getFavouriteCaller()
})
</script>

<style lang="less" scoped>
.box {
  width: 100%;
  padding: 20px;
}

.input {
  width: calc(100% - 120px);
  border: 1px solid #ccc;
  outline: none;
  padding: 5px;
  margin-right: 10px;
  height: 40px;
  vertical-align: middle;
}

.btn {
  width: 100px;
  height: 40px;
}

.tip{
  padding: 5px 0;
  font-size: 12px;
  color: #999;
}

.special-gift{
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 30px;

  .img{
    overflow: hidden;

    img{
      width: 100%;
    }
  }

  b{
    color: #f42d2d
  }
}

.qrcode-box{
  position: absolute;
  top: 69%;
  left: 67%;
  // transform: translate(-50%, -50%);
}

.qrcode {
  display: inline-block;
  font-size: 0;
  margin-bottom: 0;
  position: relative;
}

.qrcode__image {
  background-color: #fff;
  border: 1px solid #fff;
  width: 20%;
  height: 20%;
  position: absolute;
  top: 50%;
  left: 50%;
  overflow: hidden;
  transform: translate(-50%, -50%);
}
</style>
