<template>
  <a-collapse v-model:activeKey="activeKey" :bordered="false">
    <a-collapse-panel key="" header="This is panel header 1">
      <p>{{ text }}</p>
    </a-collapse-panel>
    <a-collapse-panel key="2" header="This is panel header 2">
      <p>{{ text }}</p>
    </a-collapse-panel>
    <a-collapse-panel key="3" header="This is panel header 3">
      <p>{{ text }}</p>
    </a-collapse-panel>
  </a-collapse>
</template>

<script setup>
import {} from 'vue'
</script>

<style scoped></style>