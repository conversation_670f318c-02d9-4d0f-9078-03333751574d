<template>
  <div flex="~ 1 col items-center" p-y-30>
    <template v-if="user.status == 2">
      <Limit mx-auto />
    </template>
    <template v-else>
      <a-divider orientation="left">基本信息</a-divider>
      <a-form
        ref="basicFormRef"
        :style="{ width: '80%' }"
        :rules="rules"
        :model="formState"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
        autocomplete="off"
      >
        <a-row>
          <a-col :span="12">
            <a-form-item name="nickName" label="用户名">
              <a-input v-model:value="formState.nickName" :maxlength="32" />
            </a-form-item>

            <a-form-item name="birthday" label="生日">
              <a-date-picker value-format="YYYY-MM-DD" class="w-full" v-model:value="formState.birthDate" />
            </a-form-item>

            <!-- <a-form-item name="email" label="邮箱">
<a-input v-model:value="formState.email" />
</a-form-item> -->

            <a-form-item name="personalProfile" label="个人简介">
              <a-textarea v-model:value="formState.userMemo" :maxlength="100" />
            </a-form-item>

            <div flex justify-end gap-8>
              <a-button type="primary" html-type="submit" :loading="loading" @click="onConfirm">更新</a-button>
              <a-button :loading="loading" @click="onReset">重置</a-button>
            </div>
          </a-col>
          <a-col :span="12">
            <a-form-item text-center>
              <a-upload
                v-model:file-list="fileList"
                name="avatar"
                list-type="picture-card"
                :show-upload-list="false"
                action=""
                :before-upload="beforeUpload"
                :customRequest="uploadImage"
              >
                <img v-if="formState.pic" :src="formState.pic" alt="avatar" w-full h-full />
                <div v-else>
                  <plus-outlined></plus-outlined>
                  <div class="ant-upload-text">上传头像</div>
                </div>
              </a-upload>
              <div sub-text>限2MB以内的jpg、jpeg、png文件</div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>

      <job-info></job-info>

      <a-divider orientation="left">账号信息</a-divider>
      <a-form
        ref="accountFormRef"
        :style="{ width: '80%' }"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
        autocomplete="off"
        label-align="left"
      >
        <a-row>
          <a-col :span="12">
            <a-form-item name="phone">
              <template #label>
                <MobileOutlined color="blue" />
                <span ml-4>手机号</span>
              </template>
              <span v-if="formState.userMobile">
                <span>{{ formState.userMobile }}</span>
                <a href="javascript:void(0)" ml-8 @click="handlePhone">修改</a>
              </span>
              <span v-else>
                <span>未绑定</span>
                <a href="javascript:void(0)" ml-8 @click="handlePhone">绑定</a>
              </span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="wxUnionId">
              <template #label>
                <WechatOutlined color="green" />
                <span ml-4>微信</span>
              </template>

              <span v-if="!!formState.unionId">
                <span>{{ formState.tuName || '--' }}</span>
                <a v-if="!!formState.userMobile" href="javascript:void(0)" ml-8 @click="handleUnBindWechat">解绑</a>
              </span>
              <span v-else>
                <a href="javascript:void(0)" ml-8 @click="handleBindWechat">绑定</a>
              </span>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="12">
            <a-form-item name="email">
              <template #label>
                <MailOutlined color="blue" />
                <span ml-4>邮箱</span>
              </template>

              <span v-if="formState.userMail">
                <span>{{ formState.userMail }}</span>
                <a href="javascript:void(0)" ml-8 @click="handleEmail('modify')">修改</a>
              </span>
              <span v-else>
                <span>未绑定</span>
                <a href="javascript:void(0)" ml-8 @click="handleEmail('bind')">绑定</a>
              </span>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- <a-row>
<a-col :span="12" text-right>
<a-button type="primary" danger @click="handleLogoff">注销账号</a-button>
</a-col>
</a-row> -->
      </a-form>

      <a-modal v-model:open="showPhone" :title="formState.userMobile ? '修改手机号' : '绑定手机号'">
        <a-form
          ref="phoneFormRef"
          :style="{ width: '80%' }"
          :rules="phoneRules"
          :model="phoneData"
          :wrapper-col="{ span: 24 }"
          autocomplete="off"
        >
          <a-form-item name="phone">
            <a-input v-model:value="phoneData.phone" placeholder="新手机号" size="large" />
          </a-form-item>

          <a-form-item name="captcha">
            <a-input-group size="large" compact>
              <a-input v-model:value="phoneData.captcha" placeholder="验证码" style="width: calc(100% - 120px)" />
              <a-button
                w-120
                type="primary"
                :loading="loginCaptchaLoading"
                :disabled="loginCaptchaDisabled"
                @click="onLoginCaptchaClick"
              >
                {{ loginCaptchaDisabled ? `重新获取(${loginCaptchaTMinus})` : '获取验证码' }}
              </a-button>
            </a-input-group>
          </a-form-item>
        </a-form>
        <template #footer>
          <a-button key="back" @click="showPhone = false">取消</a-button>
          <a-button
            key="submit"
            type="primary"
            :loading="setPhoneLoading"
            @click="handleSetPhone(!!formState.userMobile ? 2 : 1)"
          >
            确认
          </a-button>
        </template>
      </a-modal>

      <a-modal v-model:open="showBindWechat" title="绑定微信" :footer="null">
        <div text-center id="wx_container"></div>
      </a-modal>

      <a-modal
        v-model:open="showEmail"
        :title="formState.userMail ? '修改邮箱' : '绑定邮箱'"
        @ok="handleEmailOk"
        destroy-on-close
      >
        <a-form :model="mailForm" :rules="mailRule" ref="mailRef">
          <a-form-item name="email">
            <a-input v-model:value="mailForm.email" placeholder="请输入邮箱"></a-input>
          </a-form-item>
          <a-form-item name="emailCode">
            <a-input-group size="large" compact>
              <a-input v-model:value="mailForm.emailCode" placeholder="验证码" style="width: calc(100% - 120px)" />
              <a-button w-120 type="primary" :loading="mailLoading" :disabled="mailDisabled" @click="onMailSend">
                {{ mailDisabled ? `重新获取(${mailTime})` : '获取验证码' }}
              </a-button>
            </a-input-group>
          </a-form-item>
        </a-form>
      </a-modal>
    </template>
  </div>
</template>

<script lang="ts" setup>
import wxinit from '@/assets/js/wxLogin'
import { unBindWx, bindEmail, userUpdate } from '~/api/user'
import type { Rule } from 'ant-design-vue/es/form'
import { uploadFile } from '~/api/common'
import { User, userStore } from '~/store/user'
import JobInfo from './components/JobInfo.vue'

type InfoFormState = Pick<
  User,
  'nickName' | 'userMobile' | 'userMail' | 'pic' | 'birthDate' | 'position' | 'userMemo' | 'unionId' | 'tuName'
>

const loginType: any = ref('')
const userStoreObj = userStore()
const { user } = storeToRefs(userStoreObj)
const { clearUser } = userStoreObj

const basicFormRef: any = ref(null)
const accountFormRef: any = ref(null)

const fileList = ref([])
const formState = ref({} as InfoFormState)
const rules: Record<string, Rule[]> = {
  nickName: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
}
const loading = ref<boolean>(false)
const avatarChange = ref<boolean>(false)

const showBindWechat = ref<boolean>(false)
const showBindWechatTest = ref<boolean>(false)
const iframeSrc = ref('http://10.10.88.63:3000/bindWechatMsg?message=%E7%BB%91%E5%AE%9A%E6%88%90%E5%8A%9F')

const showPhone = ref<boolean>(false)
const setPhoneLoading = ref<boolean>(false)
const loginCaptchaLoading = ref<boolean>(false)
const loginCaptchaDisabled = ref<boolean>(false)
const loginCaptchaTMinus = ref<number>(60)

const mailTime = ref(60)
const mailDisabled = ref(false)
const mailLoading = ref(false)
let loginTimer: any = null
const phoneData = reactive({
  phone: '',
  captcha: '',
})
const phoneRules: Record<string, Rule[]> = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    {
      pattern: MOBILE_REG.reg,
      message: MOBILE_REG.msg,
      whitespace: true,
      trigger: 'blur',
    },
  ],
  captcha: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
}
const phoneFormRef: any = ref(null)
const onLoginCaptchaClick = () => {
  phoneFormRef.value.validateFields(['phone']).then(() => {
    loginCaptchaLoading.value = true
    http('/mall/p/user/sendVerifyCode', {
      method: 'put',
      body: {
        eventType: 'UPDATE_PHONE',
        mobile: phoneData.phone,
      },
    })
      .then((res) => {
        useMall(res, () => {
          message.success('验证码已发送')
          loginCaptchaDisabled.value = true
          loginTimer = setInterval(() => {
            loginCaptchaTMinus.value--
            if (loginCaptchaTMinus.value === 0) {
              loginCaptchaDisabled.value = false
              loginCaptchaTMinus.value = 60
              clearInterval(loginTimer)
              loginTimer = null
            }
          }, 1000)
        })
      })
      .finally(() => {
        loginCaptchaLoading.value = false
      })
  })
}
const isFirstBind = computed(() => !formState.value.userMobile && !formState.value.userMail)
const showSetPwd = () => {
  Modal.confirm({
    title: '提示',
    content: '当前账号尚未设置登录密码，是否现在设置',
    okText: '去设置',
    cancelText: '取消',
    onOk() {
      navigateTo({ path: '/my/security' })
    },
  })
}
const handleSetPhone = (type) => {
  phoneFormRef.value
    .validate()
    .then(() => {
      const { phone, captcha } = phoneData
      const data = {
        mobile: phone,
        verifyCode: captcha,
      }
      setPhoneLoading.value = true
      http('/mall/p/user/updatePhone', {
        method: 'put',
        body: data,
      })
        .then((res) => {
          useMall(res, () => {
            message.success('操作成功')
            showPhone.value = false
            refreshUser()
            // if (loginType.value != 'wechat' && type == 2) {
            //   // 非微信扫码 修改手机号 重新登陆
            //   clearUser()
            //   window.location.href = '/login'
            // } else {
            //   if (isFirstBind.value && type == 1) {
            //     showSetPwd()
            //   }
            //   refreshUser()
            // }
          })
        })
        .finally(() => {
          setPhoneLoading.value = false
        })
    })
    .catch((error: any) => {
      console.log('error', error)
    })
}

watch(showBindWechat, (v) => {
  if (!v) {
    refreshUser()
  }
})

const { refreshUser: updateUser } = useUser()

const refreshUser = async () => {
  await updateUser()
  onReset()
}

const closeModal = () => {
  showBindWechatTest.value = false
  showBindWechat.value = false
}

const beforeUpload = (file: File) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
  if (!isJpgOrPng) {
    message.error('您只能上传jpg、jpeg和png格式的图片')
  }
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    message.error('图片大小不能超过2MB')
  }
  return isJpgOrPng && isLt2M
}

const uploadImage = (data: any) => {
  uploadFile(data.file, 'Avatar')
    .then((res) => {
      if (res.code === 'ok') {
        formState.value.pic = res.data
        avatarChange.value = true
      }
    })
    .catch((err) => {})
}

const onConfirm = () => {
  basicFormRef.value.validate().then(() => {
    loading.value = true
    userUpdate({
      ...formState.value,
      avatarUrl: formState.value.pic,
    })
      .then((res) => {
        useMall(res, () => {
          refreshUser()
          message.success('个人资料已修改')
        })
      })
      .finally(() => {
        loading.value = false
      })
  })
}

const onReset = () => {
  const _info = pick(user.value, [
    'nickName',
    'userMobile',
    'birthDate',
    'userMail',
    'pic',
    'position',
    'personalProfile',
    'unionId',
    'tuName',
    'userMemo',
  ])

  formState.value = _info
}

const handlePhone = async () => {
  showPhone.value = true
  phoneFormRef.value.resetFields()
}

const handleBindWechat = async () => {
  showBindWechat.value = true
  const userId: any = user.value.userId
  const reg = new RegExp('\\+', 'ig')
  const extra = userId ? userId.replace(reg, '%2B') : ''
  await nextTick()
  // @ts-ignore
  new WxLogin({
    self_redirect: true,
    id: 'wx_container',
    appid: 'wx14817ec4ef183e7b',
    scope: 'snsapi_login',
    redirect_uri: encodeURIComponent(`https://www.yidao.cloud/mall/wx/callback?extra=${extra}`),
    state: Math.ceil(Math.random() * 1000),
    style: '',
    href: 'data:text/css;base64,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',
  })
}

const handleUnBindWechat = () => {
  Modal.confirm({
    title: '提示',
    content: '确认解除绑定吗?',
    onOk: () => {
      http('/mall/p/user/unbindWechat', {
        method: 'put',
      }).then((res) => {
        useMall(res, () => {
          message.success('操作成功')
          refreshUser()
        })
      })
    },
  })
}

const initWx = () => {
  // 引入 微信登录
  let script = document.createElement('script')
  script.type = 'text/javascript'
  script.src = 'https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js'
  // 引入成功
  script.onload = function () {
    wxinit(window, document)
  }
  // 引入失败
  script.onerror = function () {
    console.log('wx.js资源加载失败了')
  }
  document.head.appendChild(script)
}

const receiveMessage = (event) => {
  if (event.data === 'closeDialog') {
    closeModal()
  }
  if (event.data === 'reScan') {
    handleBindWechat()
  }
}

onMounted(() => {
  initWx()
  onReset()
  loginType.value = sessionStorage.getItem('loginType') || 'password'
  window.addEventListener('message', receiveMessage, false)
})

onBeforeUnmount(() => {
  window.removeEventListener('message', receiveMessage, false)
})

const showEmail = ref(false)
const mailRule = ref<Obj>({
  email: [
    { required: true, message: '请输入邮箱' },
    {
      pattern: /([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})/,
      message: '邮箱格式不合法',
    },
  ],
  emailCode: [{ required: true, message: '请输入验证码' }],
})

const mailRef = ref()
const mailForm = ref({
  email: '',
  emailCode: '',
})
const emailState = ref('')
const handleEmail = (state) => {
  emailState.value = state
  mailForm.value.email = ''
  mailForm.value.emailCode = ''
  showEmail.value = true
}

const handleEmailOk = () => {
  mailRef.value?.validate().then(async () => {
    const res = await bindEmail({
      userMail: mailForm.value.email,
      verifyCode: mailForm.value.emailCode,
    })
    useMall(res, () => {
      showEmail.value = false
      message.success(emailState.value == 'bind' ? '绑定成功' : '修改成功')
      if (isFirstBind.value && emailState.value == 'bind') {
        showSetPwd()
      }
      nextTick(() => {
        refreshUser()
      })
    })
  })
}

let mailTimer
const onMailSend = () => {
  mailRef.value.validateFields(['email']).then(() => {
    mailLoading.value = true
    http('/mall/p/user/sendVerifyCode', {
      method: 'put',
      body: {
        eventType: 'UPDATE_EMAIL',
        userMail: mailForm.value.email,
      },
    })
      .then((res) => {
        useMall(res, () => {
          message.success('验证码已发送')
          mailDisabled.value = true
          mailTimer = setInterval(() => {
            mailTime.value--
            if (mailTime.value === 0) {
              mailDisabled.value = false
              mailTime.value = 60
              clearInterval(mailTimer)
              mailTimer = null
            }
          }, 1000)
        })
      })
      .finally(() => {
        mailLoading.value = false
      })
  })
}
</script>

<style scoped></style>
