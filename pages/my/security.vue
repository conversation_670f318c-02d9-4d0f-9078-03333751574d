<template>
  <div flex="~ 1 col items-center" p-y-80 relative>
    <div class="absolute left-0 top-0 p-10 w-full" v-if="disableAction">
      <a-alert type="warning" message="您尚未绑定邮箱或手机号，请先去个人中心去绑定手机号或邮箱">
        <template #action>
          <router-link to="/my/info">去绑定</router-link>
        </template>
      </a-alert>
    </div>
    <a-form :disabled="disableAction" ref="formRef" :style="{ width: '40%' }" :rules="rules" :model="formState" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" autocomplete="off">
      <a-form-item name="captcha" :label="type == 'phone'? '手机验证码':'邮箱验证码'">
        <a-input-group size="large" compact>
          <a-input v-model:value="formState.captcha" placeholder="验证码" style="width: calc(100% - 120px)" />
          <a-button w-120 type="primary" :loading="captchaLoading" :disabled="captchaDisabled || disableAction" @click="onCaptchaClick">{{ captchaDisabled ? `重新获取(${captchaTMinus})` : '获取验证码' }}</a-button>
        </a-input-group>
      </a-form-item>
      <a-form-item name="password" label="新密码">
        <a-input-password v-model:value="formState.password" size="large" />
      </a-form-item>
      <a-form-item name="confirmPassword" label="确认密码">
        <a-input-password v-model:value="formState.confirmPassword" size="large" />
      </a-form-item>
      <a-form-item :wrapper-col="{ offset: 6, span: 4 }">
        <a-button w-full type="primary" html-type="submit" :loading="securityLoading" @click="onSecurityConfirm">确定</a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
import type { Rule } from 'ant-design-vue/es/form';
import { userStore } from '~/store/user';
import { PASSWORD_REG } from '@/utils/regex';
import { getCaptcha, getEmailCode, resetPassword } from '~/api/user';

interface formState {
  password: string;
  confirmPassword: string;
  captcha: string;
}

const userStoreObj = userStore();
const { user } = storeToRefs(userStoreObj);
const { clearUser } = userStoreObj;
const router = useRouter();
const formRef: any = ref(null);

const captchaLoading = ref<boolean>(false);
const captchaDisabled = ref<boolean>(false);
const captchaTMinus = ref<number>(60);

let securityTimer: any = null;

const hasPhone = computed(() => !!user.value.userMobile)
const hasEmail = computed(() => !!user.value.userMail)
const disableAction = computed(() => !hasPhone.value && !hasEmail.value)
const type = computed(() => {
  if (hasPhone.value) return 'phone'
  if (hasEmail.value) return 'email'
})

const formState = reactive<formState>({
  password: '',
  confirmPassword: '',
  captcha: '',
});

const rules: Record<string, Rule[]> = {
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    {
      pattern: PASSWORD_REG.reg,
      message: PASSWORD_REG.msg,
      whitespace: true,
      trigger: 'blur',
    },
    {
      validator: async (rule, value) => {
        if (!!formState.confirmPassword && value !== formState.confirmPassword) {
          Promise.reject('两次密码输入不一致');
        } else {
          Promise.resolve();
        }
      },
      trigger: 'blur',
    },
  ],
  confirmPassword: [
    { required: true, message: '请输入确认密码', trigger: 'blur' },
    {
      pattern: PASSWORD_REG.reg,
      message: PASSWORD_REG.msg,
      whitespace: true,
      trigger: 'blur',
    },
    {
      validator: async (rule, value) => {
        if (!!formState.password && value !== formState.password) {
          return Promise.reject('两次密码输入不一致');
        } else {
          return Promise.resolve();
        }
      },
      trigger: 'blur',
    },
  ],
  captcha: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
};

const securityLoading = ref<boolean>(false);

const onCaptchaClick = () => {
  formRef.value.validateFields([]).then(() => {
    
    captchaLoading.value = true;
    http('/mall/p/user/sendVerifyCode', {
      method: 'put',
      body: {
        eventType: 'UPDATE_PASSWORD',
        userMail: user.value.userMail,
        mobile: user.value.userMobile
      }
    })
      .then((res) => {
        if (res.code === 'ok') {
          message.success('验证码已发送');
          captchaDisabled.value = true;
          securityTimer = setInterval(() => {
            captchaTMinus.value--;
            if (captchaTMinus.value === 0) {
              captchaDisabled.value = false;
              captchaTMinus.value = 60;
              clearInterval(securityTimer);
              securityTimer = null;
            }
          }, 1000);
        }
      })
      .finally(() => {
        captchaLoading.value = false;
      });
  });
};

const onSecurityConfirm = () => {
  formRef.value.validate().then(() => {
    const { captcha, password } = formState;
    const data = {
      password: aesEncode(password),
      verifyCode: captcha,
    };
    securityLoading.value = true;
    resetPassword(data)
      .then(res => {
        useMall(res, () => {
          message.success('密码已修改，请重新登录');
          setTimeout(() => {
            clearUser();
            localStorage.removeItem('access_token');
            router.push({
              path: '/login',
              query: {
                type: 'LOGIN',
              },
            });
          }, 1000);

        })
      })
      .finally(() => {
        securityLoading.value = false;
      });
  });
};
</script>

<style scoped></style>
