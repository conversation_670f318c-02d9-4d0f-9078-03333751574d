<template>
  <div class="box">
    <!-- <div class="tip">积分商城即将上线,敬请期待!</div> -->

    <Limit v-if="user.status == 2" />
    <template v-else>
      <div class="score">
        当前积分:
        <span>{{ totalScore }}</span>
      </div>

      <div class="table-list">
        <a-tabs v-model:activeKey="activeKey">
          <a-tab-pane key="1" tab="积分记录">
            <a-table
              :columns="columns"
              row-key="id"
              size="middle"
              :data-source="dataSource"
              :pagination="pagination"
              :locale="locale"
              @change="handleChanger"
            >
              <template #bodyCell="{ column, record, value }">
                <span v-if="column.dataIndex == 'source'">
                  {{ sourceList[value] }}
                </span>

                <span v-if="column.dataIndex == 'score'">
                  <b
                    :style="{
                      color: record.ioType == 1 ? 'red' : 'green',
                    }"
                  >
                    {{ record.ioType == 1 ? '+' : '-' }}{{ value }}
                  </b>
                </span>
              </template>
            </a-table>
          </a-tab-pane>
          <!-- <a-tab-pane key="2" tab="积分任务" disabled>积分任务</a-tab-pane> -->
          <!-- <a-tab-pane key="3" tab="积分兑换">
              <div class="exchange-box">
              <p>{{ new Date().getMonth() + 1 }}月积分礼品</p>
              <scoreExchange />
              </div>
          </a-tab-pane> -->
          <!-- <a-tab-pane key="4" tab="积分任务">
            <div v-if="multiplier > 1" mb-8>
              当前积分活动倍率加成：
              <strong color-red>{{ multiplier }}</strong>
              倍
              <span v-if="endStr">
                ，活动截止时间 :
                <strong color-red>{{ endStr }}</strong>
              </span>
            </div>
            <a-table :columns="ruleColumns" size="middle" row-key="id" :data-source="ruleData" :pagination="false">
              <template #bodyCell="{ column, record, value }">          
                <template v-if="column.dataIndex == 'action'">
                  <a href="javascript:;" v-if="!record.finish" @click="onFinish(record.id)">
                    {{
                      record.id.startsWith('signInCount')
                        ? '待完成'
                        : ['questionnaireInvestigationScore', 'invitedQuestionnaireInvestigationScore'].includes(
                            record.id
                          )
                        ? '即将上线'
                        : '去完成'
                    }}
                  </a>
                  <span color-gray v-else>已完成</span>
                </template>
              </template>
            </a-table>
          </a-tab-pane> -->
          <a-tab-pane key="5" tab="兑换记录">
            <exchange-log />
          </a-tab-pane>
        </a-tabs>
      </div>
    </template>
  </div>
</template>

<script setup>
import hmEmpty from '@/components/hm-empty/index.vue'
import { getScoreLogs, getTotalScore, getScoreConfig } from '~/api/user'
import { userStore } from '~/store/user'
import { sideWidgetsStore } from '~/store/sideWidgets'

import scoreExchange from './components/score-exchange.vue'
import exchangeLog from './components/exchange-log.vue'
import { template } from 'lodash'
const router = useRouter()
const userStoreObj = userStore()
const { user } = storeToRefs(userStoreObj)

const sideWidgetsStoreObj = sideWidgetsStore()
const { setFeedbackPopVisible } = sideWidgetsStoreObj

const locale = ref({
  // emptyText: h(hmEmpty)
})

const scoreConfig = {
  signInScore: '每日登录',
  registerScore: '新用户注册',
  inviteRegisterScore: '邀请注册',
  personalMemberScore: '成为专业会员',
  questionnaireInvestigationScore: '完成每月问卷调查',
  invitedQuestionnaireInvestigationScore: '受邀人完成每月问卷调查',
  downloadScore: '首次下载图纸',
  personalFavoritesScore: '首次使用个人选型库',
  submitEffectiveFeedbackScore: '首次反馈',
  feedbackAcceptedScore: '反馈被采纳',
}

const sourceList = [
  '注册送积分',
  '购物',
  '等级提升',
  '签到',
  '购物抵扣积分',
  '积分过期',
  '余额充值',
  '系统更改积分',
  '--',
  '登录',
  '邀请注册',
  '反馈被采纳',
  '提交有效建议',
  '绑定手机号',
  '绑定真实姓名',
  '绑定公司信息',
  '绑定职位信息',
  '成为专业会员', // 17
  '下载图纸',
  ' 使用个人空间',
]

const columns = [
  {
    title: '操作',
    dataIndex: 'source',
    width: '15%',
    ellipsis: true,
  },
  {
    title: '积分变更',
    dataIndex: 'score',
    width: '10%',
    ellipsis: true,
  },
  {
    title: '详情',
    dataIndex: 'remark',
    width: '55%',
    ellipsis: true,
  },
  {
    title: '变更时间',
    dataIndex: 'createTime',
    width: '20%',
    ellipsis: true,
  },
]

const ruleColumns = [
  {
    title: '任务名称',
    dataIndex: 'source',
    width: '70%',
  },
  {
    title: '积分',
    dataIndex: 'score',
    width: '15%',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '15%',
  },
]

const ruleData = ref([])

const activeKey = ref('1')

const dataSource = ref([])

const multiplier = ref(1)
const pagination = ref({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showTotal: (total) => {
    return `共${total}条数据`
  },
})

const getList = () => {
  getScoreLogs({
    pageNo: pagination.value.current,
    pageSize: pagination.value.pageSize,
  }).then(({ data }) => {
    dataSource.value = data.data
    pagination.value.total = data.totalCount
  })
}

const getScoreInfo = async () => {
  const res = await http('/mall/p/score/scoreInfo')
  useMall(res, () => {
    const { signInCount, scoreList } = res.data
    for (let i = 0; i < scoreList.length; i++) {
      ruleData.value.push({
        id: 'signInCount' + (i + 1),
        source: i == 0 ? '每日登录' : `连续${i + 1}日登录`,
        score: res.data.scoreList[i],
        finish: i < signInCount,
      })
    }
  })
}

const handleChanger = (obj) => {
  pagination.value = obj
  getList()
}

const timeStr = ref('')
const endStr = ref('')
const getMultiplier = (start, end, multiplier = 1) => {
  const time = new Date().getTime()
  const _start = start ? new Date(start).getTime() : ''
  const _end = end ? new Date(end).getTime() : ''
  if (!!_start && !!_end) {
    timeStr.value = start + ` ~ ` + end
    endStr.value = end
    return time >= _start && time <= _end ? multiplier : 1
  }
  if (!!_start && !_end) {
    timeStr.value = start + ` ~ 至今`
    return time >= _start ? multiplier : 1
  }
  return 1
}

const totalScore = ref(0)

/**
 * 判断积分任务是否已完成
 * @param {*} ruleId
 */
const renderFinish = (ruleId) => {
  if (['signInScore', 'registerScore'].includes(ruleId)) {
    return true
  } else if (
    [
      'inviteRegisterScore',
      'questionnaireInvestigationScore',
      'invitedQuestionnaireInvestigationScore',
      'downloadScore',
      'personalFavoritesScore',
      'feedbackAcceptedScore',
      'submitEffectiveFeedbackScore',
    ].includes(ruleId)
  ) {
    return false
  } else if (['personalMemberScore'].includes(ruleId)) {
    const { realName, userMobile, company, position } = user.value
    return !!realName && !!userMobile && !!company && !!position
  }
}

/**
 * 点击【去完成】的逻辑
 * @param {*} ruleId
 */
const onFinish = (ruleId) => {
  switch (ruleId) {
    case 'personalMemberScore':
      router.push('/my/info')
      return
    case 'inviteRegisterScore':
      router.push('/my/invite')
      return
    case 'feedbackAcceptedScore':
    case 'submitEffectiveFeedbackScore':
      setFeedbackPopVisible(true)
    default:
      return
  }
}

onMounted(async () => {
  getList()
  getTotalScore().then((res) => {
    totalScore.value = res.data?.score || 0
  })
  await getScoreInfo()
  getScoreConfig().then(({ data }) => {
    multiplier.value = getMultiplier(data.multiplierStartTime, data.multiplierEndTime, data.multiplier)
    for (const k in scoreConfig) {
      if (k == 'signInScore') {
        // const v = data[k] || [0]
        // v.forEach((d, i) => {
        //   ruleData.value.push({
        //     id: k + (i + 1),
        //     source: i == 0 ? '每日登录' : `连续${i + 1}日登录`,
        //     score: d,
        //     finish: renderFinish(k)
        //   })
        // })
      } else {
        ruleData.value.push({
          id: k,
          source: scoreConfig[k],
          score: data[k] || 0,
          finish: renderFinish(k),
        })
      }
    }
  })
})
</script>

<style scoped>
.box {
  width: 100%;
  padding: 30px;
  font-size: 14px;
}

.tip {
  background: #d7dbe1;
  padding: 10px;
}

.score {
  padding: 10px;
}

.score span {
  font-size: 20px;
  font-weight: bold;
  color: var(--primary-color);
  padding: 0 10px;
}

.table-list {
  padding: 0 10px;
}

.table-list span {
  display: inline-block;
  padding: 0 10px 0 0;
}

.exchange-box {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  border-bottom: 1px solid #efefef;
  padding-bottom: 10px;
}
</style>
