<template>
  <div flex="1 col">
    <div card mb-16>
      <div filter>
        <span filter__title>品牌</span>
        <span filter__content min-h-40>
          <a-checkable-tag
            v-for="option in filters.websites"
            :key="option.value"
            :checked="option.selected"
            @change="
              (checked) => onFilterChange('websites', option.value, checked)
            "
          >
            {{ option.label }}
          </a-checkable-tag>
        </span>
      </div>
      <div filter>
        <span filter__title>类目</span>
        <span filter__content min-h-40>
          <a-checkable-tag
            v-for="option in filters.categories"
            :key="option.value"
            :checked="option.selected"
            @change="
              (checked) => onFilterChange('categories', option.value, checked)
            "
          >
            {{ option.label }}
          </a-checkable-tag>
        </span>
      </div>
      <!-- <div filter>
        <span filter__title>品牌</span>
        <span filter__content>
          <a-checkable-tag v-for="option in filters.brands" :key="option.value" :checked="option.selected" @change="(checked) => onFilterChange('brands', option.value, checked)">
            {{ option.label }}
          </a-checkable-tag>
        </span>
      </div> -->
    </div>
    <div>
      <div v-show="showType === 'list' && !favouriteLoading">
        <template v-if="showFavouriteList.length">
          <list-item
            mb-16
            :favMode="true"
            v-for="item in showFavouriteList"
            :key="item.id"
            :id="item.id"
            :website-img-url="item.website.websiteImgUrl"
            :website-name="item.website.websiteName"
            :image-url="useImage(item.imageUrl)"
            :product-name="item.productName"
            :page-url="item.pageUrl"
            :spec-pdf-url="item.specPdfUrl"
            :highlights="item.highlights"
            :filter-conditions="item.filterConditions"
            @disfavour="onDisfavour(item.id)"
          />
        </template>
        <Empty v-else></Empty>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { getFavourite } from '~/api/user'
interface FilterOption {
  label: string
  value: string
  selected?: boolean
}
interface Filters {
  websites: Array<FilterOption>
  categories: Array<FilterOption>
  brands: Array<FilterOption>
}

const showType = ref('list')
const favouriteLoading = ref(false)
const favouriteList = ref([])
const showFavouriteList = ref([])

const filters: Filters = reactive({
  websites: [],
  categories: [],
  brands: [],
})

const onFilterChange = (
  filterName: string,
  value: string,
  checked: boolean,
) => {
  filters[filterName].forEach((ele) => {
    if (ele.value === value) {
      ele.selected = checked
    }
  })
  refreshFavourite()
}

const updateFilters = () => {
  // 构建筛选条件
  const tempFilters: Filters = {
    websites: new Set(),
    categories: new Set(),
    brands: new Set(),
  }
  favouriteList.value.forEach((ele) => {
    // 构建网站筛选
    tempFilters.websites.add(ele.website.websiteName)
    // 构建类目名筛选
    for (const categoryName of ele.categoryName.split(',')) {
      tempFilters.categories.add(categoryName.trim())
    }
    // 构建品牌筛选
    tempFilters.brands.add(ele.productBrand)
  })
  // 添加全部选项
  for (const filter_key in filters) {
    filters[filter_key] = Array.from(tempFilters[filter_key]).map((ele) => {
      return {
        label: ele,
        value: ele,
        selected: false,
      }
    })
  }
}

const refreshFavourite = () => {
  // 存在有选中的筛选条件 （同一个条件下的选项按或逻辑筛选，不同条件下的选项按与逻辑筛选，如果没有选中的筛选条件，不筛选）
  if (Object.values(filters).some((ele) => ele.some((ele) => ele.selected))) {
    showFavouriteList.value = favouriteList.value.filter((ele) => {
      // 筛选网站
      const websiteFilter = filters.websites.filter((filter) => filter.selected)
      const categoryFilters = filters.categories.filter(
        (filter) => filter.selected,
      )
      const brandFilters = filters.brands.filter((filter) => filter.selected)

      // 筛选网站
      const websiteMatch =
        websiteFilter.length === 0 ||
        websiteFilter.some((filter) => filter.value === ele.website.websiteName)
      // 筛选类目
      const categoryMatch =
        categoryFilters.length === 0 ||
        categoryFilters.some((filter) =>
          ele.categoryName
            .split(',')
            .some((categoryName) => categoryName.trim() === filter.value),
        )
      // 筛选品牌
      const brandMatch =
        brandFilters.length === 0 ||
        brandFilters.some((filter) => filter.value === ele.productBrand)

      return websiteMatch && categoryMatch && brandMatch
    })
  } else {
    showFavouriteList.value = favouriteList.value
  }
}

const onDisfavour = (id) => {
  favouriteList.value = favouriteList.value.filter((ele) => ele.id !== id)
  updateFilters()
  refreshFavourite()
}

const getFavouriteCaller = () => {
  favouriteLoading.value = true
  getFavourite({})
    .then((res) => {
      if (res.code === 'ok') {
        favouriteList.value = res.data
        // 处理 id
        favouriteList.value = favouriteList.value.map((ele) => {
          ele.id = `${ele.website.websiteCode}-${ele.partId}`
          return ele
        })
        updateFilters()
        refreshFavourite()
      }
    })
    .finally(() => {
      favouriteLoading.value = false
    })
}

onMounted(() => {
  getFavouriteCaller()
})
</script>

<style scoped></style>
