<template>
    <div text-center class="img">
      <img mt-36 :src="special" />
      <div color-primary font-size-24 mt-16>8月好礼即将到来，敬请期待！</div>
    </div>
</template>

<script setup>
import fan from "~/assets/images/score-gift/fan.jpg"
import coffee from "~/assets/images/score-gift/coffee.jpg"
import mouse from "~/assets/images/score-gift/mouse.jpg"
import jdCard from "~/assets/images/score-gift/jd-card.jpg"
import special from "~/assets/images/score-gift/special.jpg"
import vx from "~/assets/images/score-gift/vx.jpg"

const gifts = [
  {
    pic: fan,
    name: '冰雾电风扇',
    score: 800
  },
  {
    pic: coffee,
    name: '三顿半速溶咖啡',
    score: 1500
  },
  {
    pic: mouse,
    name: '罗技人体工程学鼠标',
    score: 3000
  },
  {
    pic: jdCard,
    name: '500元京东礼品卡',
    score: 5000
  }
]
</script>

<style lang="less" scoped>
.gifts{
  display: flex;
  flex-wrap: wrap;

  .gift-item{
    width: 25%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    cursor: pointer;

    .img{
      width: 150px;
      height: 150px;
      overflow: hidden;

      img{
        width: 100%;
      }
    }

    b{
      color: #f42d2d
    }
  }
}

.special-gift{
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  .img{
    width: 200px;
    height: 200px;
    overflow: hidden;

    img{
      width: 100%;
    }
  }

  b{
    color: #f42d2d
  }
}

.exchange-tips{
  display: flex;

  .tips{
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 10px 10px 0;
  }

  .vx{
    width: 250px;
    display: flex;
    flex-direction: column;
    align-items: center;

    img{
      width: 120px;
      height: 120px;
    }
  }
}
</style>
