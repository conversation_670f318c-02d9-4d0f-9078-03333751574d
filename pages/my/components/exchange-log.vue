<!-- refundStatus 1 申请退款 2 退款成功 3 部分退款成功 4 退款失败 -1 退款关闭 -->
<!-- status '', '待付款', '待发货', '待收货', '待评价', '已完成', '已取消', '拼团中' -->
<template>
  <div class="bg-#fff flex-1">
    <!-- <a-radio-group 
      name="radioGroup" 
      button-style="solid"
      v-model:value="tabValue"
      @change="onChangeTab"
    >
      <a-radio-button value="1">积分订单</a-radio-button>
      <a-radio-button value="2">审核记录</a-radio-button>
    </a-radio-group> -->

    <a-form :label-col="{ flex: '72px' }">
      <a-row :gutter="10">
        <a-col :span="8">
          <a-form-item label="订单号">
            <a-input placeholder="请输入" v-model:value="form.orderNumber"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-button type="primary" mr-10 @click="search">搜索</a-button>
          <a-button type="primary" @click="reset">重置</a-button>
        </a-col>
      </a-row>
    </a-form>

    <div>
      <a-row class="bg-#efefef p-10 font-bold">
        <a-col :span="12">
          <a-row>
            <a-col :span="18">商品</a-col>
            <a-col :span="6">积分单价/数量</a-col>
          </a-row>
        </a-col>
        <a-col :span="12">
          <a-row>
            <a-col :span="6" text-center>积分总价</a-col>
            <a-col :span="6" text-center>收货人</a-col>
            <a-col v-if="tabValue == 1" :span="6" text-center>订单状态</a-col>
            <a-col v-if="tabValue == 1" :span="6" text-center>操作</a-col>
            <a-col v-if="tabValue == 2" :span="6" text-center>审核结果</a-col>
            <a-col v-if="tabValue == 2" :span="6" text-center>备注</a-col>
          </a-row>
        </a-col>
      </a-row>

      <a-row v-for="item in orderList" :key="item.orderNumber" class="order-item">
        <a-col :span="24" class="title">
          <span pr-20>订单编号: {{ item.orderNumber }}</span>
          <span pr-20>下单时间: {{ item.createTime }}</span>
          <!-- <span
            v-if="item.status >= 5 && item.status !== 7"
            style="float: right; cursor: pointer"
            @click="onDelOrder(item.orderNumber)"
          >
            <DeleteOutlined color="red" />
          </span> -->
        </a-col>
        <a-col :span="12">
          <a-row v-for="_item in item.orderItemDtos" :key="_item.skuId" class="prod-item">
            <a-col :span="18">
              <div flex>
                <img w-60 h-60 m-10 :src="convertImage(_item.pic)" />
                <div flex flex-col flex-1 justify-around overflow-hidden pr-10>
                  <span
                    w-full
                    class="text-ellipsis"
                    style="
                      color: var(--primary-color);
                      cursor: pointer;
                      white-space: normal;
                      display: -webkit-box;
                      -webkit-line-clamp: 2;
                      -webkit-box-orient: vertical;
                    "
                    :title="_item.prodName"
                    @click="
                      () => {
                        router.push({
                          path: `/giftDetail`,
                          query: {
                            prodId: _item.prodId,
                            prodName: _item.prodName,
                          },
                        })
                      }
                    "
                  >
                    {{ _item.prodName }}
                  </span>
                  <span w-full text-12 class="text-ellipsis" :title="_item.partyCode">型号: {{ _item.partyCode }}</span>
                </div>
              </div>
            </a-col>
            <a-col :span="6">
              <div flex flex-col justify-center items-center>
                <span mt-10>{{ _item.price.toFixed(2) }}</span>
                <span mt-10>{{ _item.prodCount }} 件</span>
              </div>
            </a-col>
          </a-row>
        </a-col>
        <a-col :span="12">
          <a-row h-full>
            <a-col :span="6" style="border-left: 1px solid #ccc">
              <div flex justify-center mt-20 color-red>{{ gettotalPrice(item, 'actualTotal') }}</div>
              <div v-if="item.freightAmount" flex justify-center mt-10 color-red>
                (含运费: {{ gettotalPrice(item, 'freightAmount') }})
              </div>
              <div flex justify-center mt-10>共{{ item.productNums }}件</div>
            </a-col>
            <a-col :span="6" style="border-left: 1px solid #ccc">
              <a-popover title="地址信息">
                <template #content>
                  <p>
                    所在地区:
                    {{
                      (item.userAddrOrder.province || '') +
                      (item.userAddrOrder.city || '') +
                      (item.userAddrOrder.area || '')
                    }}
                  </p>
                  <p>详细地址: {{ item.userAddrOrder.addr }}</p>
                </template>
                <div flex justify-center mt-20>{{ item.userAddrOrder.receiver }}</div>
                <div flex justify-center mt-10>{{ item.userAddrOrder.mobile }}</div>
              </a-popover>
            </a-col>
            <a-col v-if="tabValue == 1" :span="6" style="border-left: 1px solid #ccc">
              <div flex justify-center mt-20>{{ statusList[item.status] || '' }}</div>
            </a-col>
            <a-col v-if="tabValue == 1" :span="6" style="border-left: 1px solid #ccc">
              <div flex flex-col items-center mt-12>
                <a-button type="link" @click="onDelOrder(item.orderNumber)">删除</a-button>
                <!-- <a-button
                  type="link"
                  @click="
                    navigateTo({
                      path: '/workSpace/company-space/procure-order/' + item.orderNumber,
                    })
                  "
                >
                  订单详情
                </a-button> -->
              </div>
            </a-col>
            <a-col v-if="tabValue == 2" :span="6" style="border-left: 1px solid #ccc">
              <div flex justify-center mt-20>{{ statusList[item.status] }}</div>
            </a-col>
            <a-col v-if="tabValue == 2" :span="6" style="border-left: 1px solid #ccc">
              <div flex justify-center mt-20>{{ statusList[item.status] }}</div>
            </a-col>
          </a-row>
        </a-col>
      </a-row>

      <hm-empty v-if="!orderList.length" />
    </div>

    <div text-right mt-16 v-if="page.total">
      <a-pagination
        v-model:current="page.current"
        v-model:page-size="page.size"
        :pageSizeOptions="['5', '10', '20', '50', '100']"
        show-size-changer
        show-quick-jumper
        :total="page.total"
        @change="fetchData"
      />
    </div>
  </div>
</template>

<script setup>
import { statusList } from '~/api/order'

const tabValue = ref('1')
const onChangeTab = () => {
  fetchData()
}

import { companyStore } from '~/store/company'

const useCompany = companyStore()

const rangeTime = ref([])
const form = ref({})

const current = ref(0)

// 获取表格数据
const loading = ref(false)
const page = reactive({
  size: 5,
  current: 1,
  total: 0,
})

const orderList = ref([])
const fetchData = () => {
  const params = {
    orderName: '',
    orderType: 3,
    orderMold: 0,
    status: current.value,
    current: page.current,
    size: page.size,
    orderNumber: form.value.orderNumber || '',
    projectNo: form.value.projectNo || '',
    merchantId: useCompany.company.shopCompanyId,
  }
  if (rangeTime.value.length) {
    params.begin = rangeTime.value[0]
    params.end = rangeTime.value[1]
  }
  http('/mall/p/myOrder/myOrderSearch', {
    method: 'get',
    params: {
      ...params,
    },
  }).then((res) => {
    useMall(res, (ret) => {
      orderList.value = ret.records || []
      page.total = ret.total

      if (!orderList.value.length && page.current > 1) {
        page.current = 1
        fetchData()
      }
    })
  })
}

const gettotalPrice = ({ actualTotal, freightAmount, orderItemDtos }, key) => {
  let total = 0
  orderItemDtos.forEach((d) => {
    total += Number(d.price * d.prodCount)
  })
  if (key == 'actualTotal') {
    return actualTotal ? Number(actualTotal).toFixed(2) : total.toFixed(2)
  }
  if (key == 'freightAmount') {
    return freightAmount ? Number(freightAmount).toFixed(2) : (actualTotal - total).toFixed(2)
  }
  return '--'
}

const getRemainTime = (beg) => {
  const allTime = 86400
  const passedTime = (new Date().getTime() - new Date(beg).getTime()) / 1000
  const ret = allTime - passedTime
  if (ret <= 0) {
    return 0
  }
  return ret
}

const onCancelOrder = (orderNumber) => {
  Modal.confirm({
    title: '提示',
    content: '订单取消后将无法恢复,请您谨慎考虑',
    onOk() {
      http(`/mall/p/myOrder/cancel/${orderNumber}`, {
        method: 'put',
      }).then((res) => {
        useMall(res, () => {
          message.success('操作成功')
          fetchData()
        })
      })
    },
    onCancel() {
      console.log('Cancel')
    },
  })
}

const onDelOrder = (orderNumber) => {
  Modal.confirm({
    title: '提示',
    content: '订单删除后将无法恢复,请您谨慎考虑',
    onOk() {
      http(`/mall/p/myOrder/${orderNumber}`, {
        method: 'delete',
      }).then((res) => {
        useMall(res, () => {
          message.success('操作成功')
          fetchData()
        })
      })
    },
    onCancel() {
      console.log('Cancel')
    },
  })
}

/**
 * 订单确认收货
 */
const onConfirmReceive = (orderNumber) => {
  Modal.confirm({
    title: '提示',
    content: '确认收货吗?',
    onOk() {
      http(`/mall/p/myOrder/receipt/${orderNumber}`, {
        method: 'put',
      }).then((res) => {
        useMall(res, () => {
          message.success('操作成功')
          fetchData()
        })
      })
    },
    onCancel() {
      console.log('Cancel')
    },
  })
}

const search = () => {
  page.current = 1
  fetchData()
}

const reset = () => {
  form.value = {}
  page.current = 1
  fetchData()
}

const router = useRouter()

onMounted(() => {
  fetchData()
})
</script>

<style lang="less" scoped>
.order-item {
  margin: 10px 0 20px;
  border: 1px solid #ccc;

  .title {
    background: #efefef;
    padding: 10px;
  }

  .prod-item {
    &:not(:last-child) {
      border-bottom: 1px solid #ccc;
    }

    padding: 10px 0;
  }
}
</style>
