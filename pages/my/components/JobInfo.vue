<template>
  <a-divider orientation="left">职业信息</a-divider>
  <a-form w="4/5" ref="formRef" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" :model="form" :rules="rules">
    <a-row>
      <a-col :span="12">
        <a-form-item label="真实姓名">
          <a-input :maxlength="32" placeholder="请输入真实姓名" v-model:value="form.realName"></a-input>
        </a-form-item>

        <a-form-item label="身份" name="identity">
          <a-select w-full placeholder="请选择身份" v-model:value="form.identity">
            <a-select-option v-for="(item, i) in identites" :key="i" :value="item">
              {{ item }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="公司">
          <!-- <a-input -->
          <!--   :maxlength="50" -->
          <!--   placeholder="请输入公司名称" -->
          <!--   v-model:value="form.company" -->
          <!-- ></a-input> -->
          <Bussiness v-model="form.company" />
        </a-form-item>

        <a-form-item label="职位">
          <a-input :maxlength="50" laceholder="请输入职位" v-model:value="form.position"></a-input>
        </a-form-item>

        <div flex justify-end gap-8>
          <a-button type="primary" @click="update">更新</a-button>
          <a-button @click="reset">重置</a-button>
        </div>
      </a-col>

      <a-col :span="12">
        <a-form-item text-center>
          <a-upload
            v-model:file-list="fileList"
            name="avatar"
            list-type="picture-card"
            :show-upload-list="false"
            action=""
            :before-upload="beforeUpload"
            :customRequest="uploadImage"
          >
            <img v-if="form.workPermit" :src="form.workPermit" alt="avatar" w-full h-full />
            <div v-else>
              <plus-outlined></plus-outlined>
              <div class="ant-upload-text">上传工作证照</div>
            </div>
          </a-upload>
          <div sub-text>
            上传名片、工牌等相关工作证明文件，
            <br />
            限2MB以内的jpg、jpeg、png文件
          </div>
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
</template>

<script setup>
import { cloneDeep } from 'lodash-es'
import { userStore } from '~/store/user'
import { uploadFile } from '~/api/common'
import { userUpdate } from '~/api/user'

const user = userStore()
const fileList = ref([])

const formRef = ref()
const form = ref(cloneDeep(user.user))
const rules = ref({
  // identity: [{ required: true, message: '请选择身份' }],
})

const identites = ['工程师', '采购', '供应商', '其他']
const beforeUpload = (file) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
  if (!isJpgOrPng) {
    message.error('您只能上传jpg、jpeg和png格式的图片')
  }
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    message.error('图片大小不能超过2MB')
  }
  return isJpgOrPng && isLt2M
}

const uploadImage = (data) => {
  uploadFile(data.file, 'WorkPermit')
    .then((res) => {
      useRes(res, () => {
        form.value.workPermit = res.data
      })
    })
    .catch((err) => {})
}

const reset = () => {
  form.value = cloneDeep(user.user)
}

const { refreshUser } = useUser()
const update = () => {
  formRef.value?.validate().then(() => {
    const data = pick(form.value, ['realName', 'identity', 'position', 'workPermit', 'company'])
    userUpdate(data).then((res) => {
      useMall(res, () => {
        message.success('个人资料已修改')
        refreshUser(() => {
          reset()
        })
      })
    })
  })
}
</script>
