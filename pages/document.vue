<template>
  <div>
    <Nav>
      <nuxt-link to="/">首页</nuxt-link>
      <div>找文档</div>
      <div>{{ searchText }}</div>
    </Nav>
    <div flex class="h-[calc(100vh-240px)] overflow-hidden" ref="divRef" max-sm="flex-col h-[calc(100vh-164px)]" pb-16>
      <div :style="{
        width: screenStore.isMobile ? '100%': width + 'px'
      }" h-full rounded-8 bg-white overflow-hidden max-sm="h-300">
          <a-spin :spinning="loading" :wrapper-class-name="'flex-center h-full w-full'" v-if="loading">
            <div w-full h-300></div>
          </a-spin>
          <a-directory-tree
            v-if="treeData.length"
            @select="select"
            :tree-data="(treeData as any[])" 
            v-model:selected-keys="selectedKeys"
            v-model:expanded-keys="expandedKeys" 
            :field-names="{ key: 'id', title: 'name'}" 
            :height="screenStore.isMobile ? 300 : h"
            >
            <template #title="{dataRef}">
              <span :title="dataRef.name" truncate>{{ dataRef.name }}</span>
            </template>
            <template #icon="{ type, expanded, fileUrl }">
              <template v-if="type=='TRUNK'">
                <FolderOpenOutlined v-if="expanded" />
                <FolderOutlined v-else />
              </template>
              <template v-else>
                <FileImageOutlined v-if="ext(fileUrl)=='img'" />
                <VideoCameraOutlined v-else-if="ext(fileUrl) =='video'"/>
                <FilePdfOutlined v-else-if="ext(fileUrl)=='pdf'" />
                <FileOutlined v-else />
              </template>
            </template>
          </a-directory-tree>
          <Empty v-if="init && !treeData.length" mt-30></Empty>
      </div>
      <div w-16 h-full @mousedown="startWatch" cursor-col-resize max-sm:hidden></div>
      <div rounded-8 overflow-hidden bg-white flex-center relative max-sm:mt-10 flex-1 h-full>
        <template v-if="currentNode.id">
          <div :key="currentNode.id" w-full h-full rounded-8 overflow-y-auto>
            <video :src="currentNode.fileUrl" v-if="currentType== 'video'" muted autoplay controls w-full h-full object-fill></video>
            <PDF v-else-if="currentType =='pdf'" :src="currentNode.fileUrl" :page="currentPage" @on-pdf-init="onPdfInit" @on-page-change="pageChange"></PDF>
            <a-image :src="currentNode.fileUrl" v-else-if="currentType=='img'" :previewMask="false" width="100%"></a-image>
          </div>
          <Control 
            v-model:page="currentPage" 
            :node="currentNode" 
            @page-change="pageChange"
            @to-start="currentPage = 1"
            @to-end="currentPage = total"
            @download="download"
            :type="currentType"></Control>
        </template>
        <Empty v-if="init && !currentNode.id"></Empty>
      </div>
    </div>
    <go-register immediate></go-register>
  </div>
</template>

<script lang="ts" setup>
import { getDocument } from '~/api/document'
import { type DocTreeItem } from '~/api/document/type'
import PDF from 'pdf-vue3';
import { userStore } from '~/store/user'
import { useScreen } from '~/store/screen'
const store = userStore()
const screenStore = useScreen()

const h = ref(0)
onMounted(() => {
  fetchDocument()
  resize()
  window.addEventListener('resize', resize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', resize)
})

const resize = () => {
  nextTick(() => {
    h.value = divRef.value?.clientHeight || 0
  })
}

const divRef = ref<HTMLDivElement>()
const route = useRoute()
const loading = ref(true)
const { width, startWatch } = useWidth(300)
const s = ref(route.query.s as string || '')
const expandedKeys = ref<string[]>([])
const selectedKeys = ref<string[]>([])
const currentNode = ref({} as DocTreeItem)
const searchText = computed(() => {
  if (s.value) {
    return `搜索"${s.value}"的结果`
  }
  return '全部'
})
const treeData = ref<DocTreeItem[]>([])
const init = ref(false)
const fetchDocument = async() => {
  loading.value = true
  const res = await getDocument(s.value)
  if (res.code == 'ok') {
    treeData.value = res.data
    expandAll(res.data)
  } else {
    message.error(res.message)
  }
  init.value = true
  loading.value = false
}
const expandAll = (tree: DocTreeItem[]) => {
  const nodes: string[] = []
  let isFind = false
  const expand = (tree: DocTreeItem[]) => {
    tree.forEach(item => {
      if (item.type == 'TRUNK') {
        nodes.push(item.id)
        // @ts-ignore
        item.selectable = false
      } else if (item.type == 'LEAF') {
        if (!isFind) {
          selectedKeys.value = [item.id]
          currentNode.value = item
          isFind = true
        }
      }
      if (item.children) {
        expand(item.children)
      }
    })
  }
  expand(tree)
  expandedKeys.value = nodes
}

const ext = (fileUrl?: string) => {
  if (!fileUrl) return 'file'
  if (/\w.(png|jpg|jpeg|svg|webp|gif|bmp)$/i.test(fileUrl)) {
    return 'img'
  } else if (/\w.(webm|mp3)$/i.test(fileUrl)) {
    return 'video'
  } else if (/\w.(pdf)$/i.test(fileUrl)) {
    return 'pdf'
  }
  return 'file'
}

const select = (_key, e) => {
  currentNode.value = e.node.dataRef
}

const currentType = computed(() => ext(currentNode.value.fileUrl))
const currentPage = ref(1)

const total = ref(0)
const onPdfInit = (pdf) => {
  total.value = pdf.numPages
}
const pageChange = (page: number) => {
  if (page < 1 || page > total.value) return
  currentPage.value = page
}

const download = () => {
  if (!store.user?.id) {
    loginPop.show()
    return
  }
  useDownload(currentNode.value.fileUrl, `${currentNode.value.name}.${currentType.value}`)
}

</script>

<style lang="less" scoped>
:deep(.ant-tree-node-content-wrapper) {
  @apply truncate;
}
</style>