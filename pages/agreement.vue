<template>
  <NuxtLayout name="selection">
    <div p-y-8>
      <div flex flex-row justify-start mb-8>
        <a-button type="link" size="small" mr-10 @click="() => $router.go(-1)">&lt; 返回</a-button>
        <a-breadcrumb separator=">">
          <a-breadcrumb-item>
            <NuxtLink to="/"> 首页 </NuxtLink>
          </a-breadcrumb-item>
          <a-breadcrumb-item>协议与政策</a-breadcrumb-item>
        </a-breadcrumb>
      </div>
      <div mt-8 flex>
        <div mr-16 w-180 flex="~ col" style="background: #fff;">
          <a-menu
            :selectedKeys="selectedKeys"
            mode="inline"
            @click="goTo"
          >
            <a-menu-item key="termsOfService" text-center>
              用户协议
            </a-menu-item>

            <a-menu-item key="disclaimer" text-center>
              免责声明
            </a-menu-item>

            <a-menu-item key="privacyPolicy" text-center>
              隐私政策
            </a-menu-item>

            <a-menu-item key="scoreConfig" text-center>
              积分规则
            </a-menu-item>

            <a-menu-item key="aboutUs" text-center>
              关于我们
            </a-menu-item>
          </a-menu>
          <!-- <div text card flex-1 p-y-8>
            <div p-y-12 font-size-16 text-center cursor-pointer hover:color-primary :class="{ 'color-primary': item.path === route.path }" v-for="item in menuList" :key="item.id" @click="onMenuClick(item)">{{ item.label }}</div>
          </div> -->
        </div>
        <div flex="~ 1">
          <NuxtPage />
        </div>
      </div>
    </div>
  </NuxtLayout>
</template>

<script lang="ts" setup>
import {} from 'vue';

const router = useRouter();
const route = useRoute();
const selectedKeys = computed(() => {
  return [route.path.split('/').pop()]
})

const goTo = ({ key }) => {
  router.push({
    path: `/agreement/${key}`,
  })
}

const menuList = [
  { id: 1, label: '用户协议', path: '/agreement/termsOfService' },
  { id: 2, label: '免责声明', path: '/agreement/disclaimer' },
  { id: 3, label: '隐私政策', path: '/agreement/privacyPolicy' },
  { id: 4, label: '关于我们', path: '/agreement/aboutUs' },

];

const onMenuClick = (menu: any) => {
  router.push(menu.path);
};
</script>

<style scoped></style>
