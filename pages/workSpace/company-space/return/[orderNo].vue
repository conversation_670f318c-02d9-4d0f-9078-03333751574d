<template>
  <div flex-1 bg-white px-20 pb-60 style="position: relative">
    <div v-if="orderInfo.platformInterventionStatus && orderInfo.platformInterventionStatus == -1" class="selections">
      <div class="process-box">
        <div mt-20 class="process">
          <div
            v-for="(item, index) in processList"
            :key="item"
            :class="['item', item.isActive(orderInfo) ? 'active' : '']"
          >
            <img w-60 h-60 :src="item.isActive(orderInfo) ? item.activeIcon : item.icon" />
            <div class="text">
              {{ item.label }}
            </div>
            <div class="time">
              {{ orderInfo[item.field] }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 平台介入 -->
    <!-- <div class="selections"></div> -->

    <div class="selections">
      <a-row class="bg-#efefef p-10 font-bold">
        <a-col :span="12">
          <a-row>
            <a-col :span="24">商品信息</a-col>
          </a-row>
        </a-col>
        <a-col :span="12">
          <a-row>
            <a-col :span="6" text-center>成交时间</a-col>
            <a-col :span="4" text-center>申请数量</a-col>
            <a-col :span="5" text-center>退款金额</a-col>
            <a-col :span="5" text-center>状态</a-col>
            <a-col :span="4" text-center>操作</a-col>
          </a-row>
        </a-col>
      </a-row>

      <a-row class="order-item">
        <a-col :span="24" class="title">
          <span pr-20>订单编号: {{ orderInfo.orderNumber }}</span>
          <span pr-20>退款编号: {{ orderInfo.refundSn || '--' }}</span>
          <span pr-20>
            <shop-outlined color="#f94c30" text-18 />
            {{ orderInfo.shopName || '--' }}
          </span>
        </a-col>
        <a-col :span="12">
          <a-row v-for="_item in goods" :key="_item.skuId" class="prod-item">
            <a-col :span="24">
              <div flex>
                <hm-img style="width: 100px; height: 100px; margin: 5px" :src="_item.pic" />
                <!-- <img w-100 h-100 m-5 :src="convertImage(_item.pic)" /> -->
                <div flex flex-col flex-1 justify-around overflow-hidden pr-10>
                  <span font-bold w-full class="text-ellipsis" :title="_item.prodName">{{ _item.prodName }}</span>
                  <div flex justify-between>
                    <span flex-1 class="text-ellipsis" :title="_item.skuName">{{ _item.skuName }}</span>
                    <span>X{{ _item.prodCount }}</span>
                  </div>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-col>
        <a-col :span="12">
          <a-row h-full>
            <a-col :span="6" style="border-left: 1px solid #ccc">
              <div flex justify-center my-20>
                {{ orderInfo.orderPayTime }}
              </div>
            </a-col>
            <a-col :span="4" style="border-left: 1px solid #ccc">
              <div flex justify-center my-20>
                {{ orderInfo.goodsNum }}
              </div>
            </a-col>
            <a-col :span="5" style="border-left: 1px solid #ccc">
              <div flex justify-center my-20>总价: {{ getPrice(orderInfo.orderAmount) }}</div>
              <div flex justify-center my-20>退款: {{ getPrice(orderInfo.refundAmount) }}</div>
            </a-col>
            <a-col :span="5" style="border-left: 1px solid #ccc">
              <template v-if="orderInfo.platformInterventionStatus && orderInfo.platformInterventionStatus !== -1">
                <div flex justify-center my-20>
                  {{ ['', '仅退款', '退货退款'][orderInfo.applyType] }}
                </div>
                <div flex justify-center my-20>
                  {{
                    ['', '平台介入中', '平台同意退款', '平台拒绝退款', '', '退款完成'][
                      orderInfo.platformInterventionStatus
                    ]
                  }}
                </div>
              </template>
              <template v-else>
                <div flex justify-center my-20>
                  {{ ['', '仅退款', '退货退款'][orderInfo.applyType] }}
                </div>
                <div flex justify-center my-20>
                  {{
                    [
                      '',
                      '买家申请',
                      '商家接受',
                      '买家发货',
                      '商家收货',
                      '退款成功',
                      '买家撤回申请',
                      '商家拒绝',
                      '退款关闭',
                    ][orderInfo.returnMoneySts]
                  }}
                  <tempalte v-if="orderInfo.returnMoneySts === -1">退款关闭</tempalte>
                </div>
              </template>
            </a-col>
            <a-col :span="4" style="border-left: 1px solid #ccc">
              <div flex flex-col items-center my-12>
                <a-button
                  type="link"
                  v-if="
                    (orderInfo.returnMoneySts === 1 ||
                      orderInfo.returnMoneySts === 2 ||
                      orderInfo.returnMoneySts === 7) &&
                    orderInfo.isCancel
                  "
                  @click="cancelApply"
                >
                  撤销申请
                </a-button>

                <a-button
                  type="link"
                  v-if="orderInfo.returnMoneySts === 2 && orderInfo.applyType === 2"
                  @click="writeLogisticsMsg"
                >
                  填写物流
                </a-button>

                <a-button
                  type="link"
                  v-if="orderInfo.returnMoneySts === 3 && orderInfo.applyType === 2"
                  @click="writeLogisticsMsg"
                >
                  修改物流
                </a-button>

                <!-- <a-button
                  type="link"
                  v-if="orderInfo.canApplyRefund && orderInfo.platformInterventionStatus === -1 && (orderInfo.returnMoneySts === 7 || orderInfo.returnMoneySts === -1) && orderInfo.rejectTime"
                  @click="onApplyPlayIntervene(1)"
                >
                  申请平台介入
                </a-button>

                <a-button
                  type="link"
                  v-if="orderInfo.platformInterventionStatus === 1 && isUserHandle"
                  @click="onCancelPlayApply"
                >
                  撤销申请
                </a-button>

                <a-button
                  type="link"
                  v-if="orderInfo.platformInterventionStatus === 1 && isUserHandle"
                  @click="onApplyPlayIntervene(2)"
                >
                  补充凭证
                </a-button>

                <a-button
                  type="link"
                  v-if="orderInfo.platformInterventionStatus === 1 && isUserHandle"
                  @click="onModifyReturnApply"
                >
                  修改申请
                </a-button> -->
              </div>
            </a-col>
          </a-row>
        </a-col>
      </a-row>
    </div>

    <refund-progress :refund-detail="orderInfo" />

    <write-logistics-info ref="logisticsRef" @ok="getOrderDetail" />
  </div>
</template>

<script setup>
import def from '~/assets/images/def.png'
import process1 from '~/assets/images/svg/process1.svg'
import process1_1 from '~/assets/images/svg/process1_1.svg'
import process2 from '~/assets/images/svg/process2.svg'
import process2_1 from '~/assets/images/svg/process2_1.svg'
import process3 from '~/assets/images/svg/process3.svg'
import process3_1 from '~/assets/images/svg/process3_1.svg'
import process4 from '~/assets/images/svg/process4.svg'
import process4_1 from '~/assets/images/svg/process4_1.svg'
import process5 from '~/assets/images/svg/process5.svg'
import process5_1 from '~/assets/images/svg/process5_1.svg'
import refundProgress from './components/refund-progress.vue'
import writeLogisticsInfo from './components/write-logistics-info.vue'
import { companyStore } from '~/store/company'

definePageMeta({
  name: '退款详情',
})

const route = useRoute()
const router = useRouter()

const canReturn = computed(() => {
  const { canRefund } = orderInfo.value
  return canRefund
})

const canAllReturn = computed(() => {
  const { canAllRefund } = orderInfo.value
  return canAllRefund
})

const getRemainTime = (beg) => {
  const allTime = 86400
  const passedTime = (new Date().getTime() - new Date(beg).getTime()) / 1000
  const ret = allTime - passedTime
  if (ret <= 0) {
    return 0
  }
  return ret
}

let processList = [
  {
    label: '买家提交申请',
    field: 'applyTime',
    icon: process1_1,
    activeIcon: process1,
    isActive: ({ returnMoneySts }) => (returnMoneySts <= 4 && returnMoneySts !== -1) || returnMoneySts === 5,
  },
  {
    label: '商家处理申请',
    field: 'handelTime',
    icon: process3_1,
    activeIcon: process3,
    isActive: ({ returnMoneySts }) => returnMoneySts >= 2 && returnMoneySts <= 5,
  },
  {
    label: '买家退货',
    field: 'shipTime',
    icon: process4_1,
    activeIcon: process4,
    isActive: ({ returnMoneySts }) => returnMoneySts >= 3 && returnMoneySts <= 5,
  },
  {
    label: '退款完成',
    field: 'refundTime',
    icon: process5_1,
    activeIcon: process5,
    isActive: ({ returnMoneySts }) => returnMoneySts == 5,
  },
]

const useCompany = companyStore()

/**
 * 撤销申请
 */
const cancelApply = () => {
  // 买家申请 || 商家接受 || 商家拒绝
  const { returnMoneySts, refundSn } = orderInfo.value
  if (returnMoneySts === 1 || returnMoneySts === 7 || returnMoneySts === 2) {
    Modal.confirm({
      title: '提示',
      content: '是否确定撤销本次退款申请？',
      onOk() {
        http(`/mall/p/orderRefund/cancel`, {
          method: 'put',
          body: refundSn,
        }).then((res) => {
          useMall(res, () => {
            message.success('操作成功')
            getOrderDetail()
          })
        })
      },
      onCancel() {
        console.log('Cancel')
      },
    })
  }
}

const logisticsRef = ref(null) // 物流信息弹框显隐
const writeLogisticsMsg = () => {
  logisticsRef.value?.init(orderInfo.value)
}

const goods = ref([])
const orderInfo = ref({})
const getOrderDetail = () => {
  http('/mall/p/orderRefund/info', {
    method: 'get',
    params: {
      refundSn: orderNo,
    },
  }).then((res) => {
    useMall(res, (ret) => {
      orderInfo.value = ret
      goods.value = ret.orderItems || []
      if (ret.applyType == 1 && processList.length == 4) {
        processList.splice(2, 1)
      }
    })
  })
}

const returnOfGoodsRef = ref(null)
const applyForReturn = (data, refundType) => {
  returnOfGoodsRef.value?.init(data, refundType, orderInfo.value)
}

let orderNo = ''
onMounted(() => {
  orderNo = route.params.orderNo
  getOrderDetail()
})
</script>

<style lang="less" scoped>
.selections {
  padding: 20px 0;
  font-size: 14px;
  overflow: auto;

  &:not(:last-child) {
    // border-bottom: 1px solid #ccc;
  }

  .title {
    font-size: 16px;
    font-weight: bold;
  }

  .process-box {
    flex: 1;
    overflow: auto;
    padding: 10px;
    line-height: 30px;

    .tip {
      color: #999;
    }

    .process {
      display: flex;
      text-align: center;
      line-height: 20px;
    }

    .item {
      // margin-right: 20px;
      padding-right: 30px;
      position: relative;

      &:last-child {
        margin: 0;
        padding: 0;

        &:after {
          width: 0;
          height: 0;
        }
      }

      &:after {
        position: absolute;
        top: 25%;
        right: 0;
        display: block;
        content: ' ';
        width: 30px;
        height: 10px;
        background: url(@/assets/images/icons.png) no-repeat -235px -686px;
      }

      .text {
        margin-bottom: 10px;
        color: #999;
      }
      .time {
        color: #999;
        font-family: arial, sans-serif;
        width: 180px;
      }
    }
    .item.active {
      &:after {
        background-position: -235px -721px;
      }

      .text {
        color: #000;
      }
    }
  }
}
.order-item {
  margin: 10px 0 20px;
  border: 1px solid #ccc;

  .title {
    background: #efefef;
    padding: 10px;
    font-size: 14px;
    font-weight: normal;
  }

  .prod-item {
    &:not(:last-child) {
      border-bottom: 1px solid #ccc;
    }

    padding: 10px 0;
  }
}
</style>
