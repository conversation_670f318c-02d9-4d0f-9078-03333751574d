<template>
  <a-modal
    v-model:open="show"
    :title="order.returnMoneySts !== 3 ? '填写物流信息' : '修改物流信息'"
    :width="800"
    @ok="confirm"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
      <a-form-item label="物流公司" name="expressId">
        <a-select
          v-model:value="form.expressId"
          showSearch
          :filterOption="
            (v, { key }) => {
              return key.dvyName.includes(v)
            }
          "
          @change="selectCompany"
        >
          <a-select-option v-for="item in deliveryList" :key="item" :value="item.dvyId">
            {{ item.dvyName }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="物流单号" name="expressNo">
        <a-input v-model:value.trim="form.expressNo" maxlength="30" show-count />
      </a-form-item>

      <a-form-item label="备注说明" name="senderRemarks">
        <a-textarea v-model:value.trim="form.senderRemarks" :maxlength="200" showCount />
      </a-form-item>

      <a-form-item label="物流凭证" name="imgs">
        <imgs-upload :limit="5" :value="form.imgs" :beforeUpload="beforeUpload" @change="(v) => (form.imgs = v)" />
        <div sub-text>最多上传五张凭证,支持jpg,png,每张大小不超过2M</div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
const emit = defineEmits(['ok'])

const checkNo = async (rule, value) => {
  if (!value) {
    return Promise.resolve()
  }
  if (!/^[0-9a-zA-Z]+$/.test(value)) {
    return Promise.reject('请输入正确的物流单号')
  }
  return Promise.resolve()
}

const form = ref({})
const rules = ref({
  expressId: [{ required: true, message: '请选择物流公司', trigger: 'blur' }],
  expressNo: [
    { required: true, message: '请填写物流单号', trigger: 'blur' },
    { validator: checkNo, trigger: 'blur' },
  ],
})

const show = ref(false)
const order = ref({})
const init = (data) => {
  loadDeliveryData()
  order.value = data
  show.value = true
  // 根据物流单号判断回填信息
  if (data.refundDelivery.deyNu) {
    form.value = {
      expressId: data.refundDelivery.deyId,
      expressName: data.refundDelivery.deyName,
      expressNo: data.refundDelivery.deyNu,
      senderRemarks: data.refundDelivery.senderRemarks,
      imgs: data.refundDelivery.imgs,
      mobile: '',
      refundSn: data.refundSn,
    }
    if (form.value.imgs) {
      const temp = form.value.imgs.split(',')
      const _temp = temp.map((d) => convertImage(d))
      form.value.imgs = _temp.join()
    }
  } else {
    form.value = {
      mobile: '',
      refundSn: data.refundSn,
    }
  }
}

const beforeUpload = (file) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
  if (!isJpgOrPng) {
    message.error('您只能上传jpg、jpeg和png格式的图片')
  }
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    message.error('图片大小不能超过2MB')
  }
  return isJpgOrPng && isLt2M
}

onMounted(() => {})

const deliveryList = ref([]) // 物流公司
const loadDeliveryData = () => {
  http('/mall/p/delivery/list', {
    method: 'get',
  }).then(({ data }) => {
    deliveryList.value = data
  })
}

/**
 * 选择物流公司
 */
const selectCompany = (val) => {
  const item = deliveryList.value.find((d) => d.dvyId == val)
  form.value.expressName = item.dvyName
}

const formRef = ref(null)
const confirm = () => {
  formRef.value?.validate().then(() => {
    // 当物流单号长度为0时，请求添加物流信息的接口
    if (order.value.returnMoneySts !== 3) {
      http('/mall/p/orderRefund/submitExpress', {
        method: 'post',
        body: {
          ...form.value,
        },
      }).then((res) => {
        useMall(res, () => {
          message.success('填写成功')
          show.value = false
          emits('ok')
        })
      })
    } else if (order.value.returnMoneySts === 3) {
      // 当物流单号不为空时，请求修改物流信息的接口
      http('/mall/p/orderRefund/reSubmitExpress', {
        method: 'put',
        body: {
          ...form.value,
        },
      }).then((res) => {
        useMall(res, () => {
          message.success('修改成功')
          show.value = false
          emits('ok')
        })
      })
    }
  })
}

defineExpose({
  init,
})
</script>

<style lang="less" scoped></style>
