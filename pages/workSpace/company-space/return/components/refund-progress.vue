<template>
  <div class="return-progress">
    <div class="tit">退款进度</div>
    <div v-if="closingSts" class="item">
      <div class="time">
        {{ refundDetail.interventionFinishTime }}
      </div>
      <div class="text-box">
        <div class="name">
          {{ closingSts }}
        </div>
        <div class="text">平台留言：{{ refundDetail.platformMessage }}</div>
      </div>
    </div>
    <!-- 用户撤销申请 -->
    <div v-if="refundDetail.applyInterventionTime && platInterSts === -1" class="item">
      <div class="time">
        {{ refundDetail.interventionFinishTime }}
      </div>
      <div class="name">用户撤销申请</div>
    </div>
    <!-- 平台介入中 -->
    <div v-for="(intervenItem, intervenInx) in refundInterList" :key="intervenInx" class="item">
      <div class="time">
        {{ intervenItem.createTime }}
      </div>
      <div class="text-box">
        <div class="name">平台介入中</div>
        <div class="text">
          <!-- 0.普通用户 1.商家端 2.平台端 -->
          {{ ['买家', '商家', '平台'][intervenItem.sysType] }}补充凭证
        </div>
        <div v-if="intervenItem.voucherDesc" class="name">凭证说明：</div>
        <div v-if="intervenItem.voucherDesc" class="text">
          {{ intervenItem.voucherDesc }}
        </div>
        <div v-if="intervenItem.imgList.length" class="img-box">
          <img-show
            v-for="(interImg, interInx) in intervenItem.imgList"
            :key="interInx"
            :src="interImg"
            class="longimage"
          />
        </div>
      </div>
    </div>
    <!-- 用户申请平台介入 -->
    <div v-if="refundDetail.applyInterventionTime" class="item">
      <div class="time">
        {{ refundDetail.applyInterventionTime }}
      </div>
      <div class="text-box">
        <div class="name">申请平台介入</div>
        <div class="text">买家申请平台客服介入</div>
        <div class="name">申请理由：</div>
        <div class="text">
          {{ refundDetail.applyInterventionReason }}
        </div>
        <div v-if="applyInterventionImgs.length" class="img-box">
          <img-show
            v-for="(img, applyInterInx) in applyInterventionImgs"
            :key="applyInterInx"
            :src="img"
            class="longimage"
          />
        </div>
      </div>
    </div>
    <!-- 申请关闭 -->
    <div
      v-if="
        (refundDetail.returnMoneySts === -1 || platInterSts !== -1) &&
        refundDetail.handelTime &&
        !refundDetail.rejectTime
      "
      class="item"
    >
      <div class="time">
        {{ refundDetail.updateTime }}
      </div>
      <div class="text-box">
        <div class="name">申请已关闭</div>
      </div>
    </div>
    <!-- 买家撤销 -->
    <div v-if="(refundDetail.returnMoneySts === -1 || platInterSts !== -1) && refundDetail.cancelTime" class="item">
      <div class="time">
        {{ refundDetail.cancelTime }}
      </div>
      <div class="text-box">
        <div class="name">
          {{ nickName }}
        </div>
        <div class="text">买家已撤销退款申请</div>
      </div>
    </div>
    <!-- 商家拒绝 -->
    <div v-if="(refundDetail.returnMoneySts === -1 || platInterSts !== -1) && refundDetail.rejectTime" class="item">
      <div class="time">
        {{ refundDetail.rejectTime }}
      </div>
      <div class="text-box">
        <div class="name">
          {{ refundDetail.shopName }}
        </div>
        <div class="text">
          <p>商家拒绝了你的退款申请</p>
          <p v-if="refundDetail.rejectMessage">拒绝原因："{{ refundDetail.rejectMessage }}"</p>
          <p>你可以和商家积极沟通，重新申请</p>
          <p v-if="rejectImgs.length" class="ref-voucher">
            拒绝凭证：
            <img-show v-for="(img, rejectInx) in rejectImgs" :key="rejectInx" :src="img" class="ref-img" />
          </p>
        </div>
      </div>
    </div>
    <!-- 退款成功 -->
    <div v-if="refundDetail.refundTime && !refundDetail.rejectTime" class="item">
      <div class="time">
        {{ refundDetail.refundTime }}
      </div>
      <div class="text-box">
        <div class="name">
          {{ refundDetail.shopName }}
        </div>
        <div class="text">
          <p>商家已退款，退款成功！</p>
        </div>
      </div>
    </div>
    <!-- 商家收到寄回货物 -->
    <div v-if="refundDetail.applyType === 2 && refundDetail.receiveTime" class="item">
      <div class="time">
        {{ refundDetail.receiveTime }}
      </div>
      <div class="text-box">
        <div class="name">
          {{ refundDetail.shopName }}
        </div>
        <div class="text">
          <p>商家已收到货，等待商家处理退款</p>
        </div>
      </div>
    </div>
    <!-- 买家寄出 -->
    <div v-if="refundDetail.applyType === 2 && refundDetail.shipTime" class="item">
      <div class="time">
        {{ refundDetail.shipTime }}
      </div>
      <div class="text-box">
        <div class="name">
          {{ nickName }}
        </div>
        <div class="text">
          买家已寄出货物。
          <span class="distance">物流公司：{{ refundDetail.refundDelivery.deyName }}</span>
          <span class="distance">物流单号：{{ refundDetail.refundDelivery.deyNu }}</span>
        </div>
      </div>
    </div>
    <!-- 商家同意 退货退款 给出地址 -->
    <div
      v-if="
        refundDetail.applyType === 2 &&
        refundDetail.handelTime &&
        (!refundDetail.rejectTime || refundDetail.refundDelivery?.refundDeliveryId)
      "
      class="item"
    >
      <div class="time">
        {{ refundDetail.handelTime }}
      </div>
      <div class="text-box">
        <div class="name">
          {{ refundDetail.shopName }}
        </div>
        <div class="text">
          <p>商家已同意退款申请</p>
          <p>
            退款地址：{{ refundDetail.refundDelivery.receiverName }}&nbsp;&nbsp;{{
              refundDetail.refundDelivery.receiverMobile
            }}&nbsp;&nbsp;{{ refundDetail.refundDelivery.receiverAddr }}
          </p>
          <p v-if="refundDetail.sellerMsg">商家备注："{{ refundDetail.sellerMsg }}"</p>
        </div>
      </div>
    </div>
    <!-- 商家同意 仅退款 -->
    <div v-if="refundDetail.applyType === 1 && refundDetail.decisionTime && !refundDetail.rejectTime" class="item">
      <div class="time">
        {{ refundDetail.decisionTime }}
      </div>
      <div class="text-box">
        <div class="name">
          {{ refundDetail.shopName }}
        </div>
        <div class="text">
          <p>商家已同意退款申请</p>
          <p v-if="refundDetail.sellerMsg">商家备注："{{ refundDetail.sellerMsg }}"</p>
        </div>
      </div>
    </div>
    <!-- 买家申请 -->
    <div class="item">
      <div class="time">
        {{ refundDetail.applyTime }}
      </div>
      <div class="text-box">
        <div class="name">
          {{ nickName }}
        </div>
        <div class="text">
          <p>
            <span class="distance ft">买家申请退款</span>
            <a-divider type="vertical" />
            <span class="distance">退款类型：{{ ['', '仅退款', '退货退款'][refundDetail.applyType] }}</span>
            <a-divider type="vertical" />
            <span class="distance">
              退款金额：
              <i v-if="refundDetail.refundAmount">{{ getPrice(refundDetail.refundAmount) }}</i>
              <i v-if="refundDetail.refundAmount && refundDetail.refundScore">&nbsp;+&nbsp;</i>
              <i v-if="refundDetail.refundScore">{{ refundDetail.refundScore }}积分</i>
            </span>
            <a-divider type="vertical" />
            <span class="distance">退款原因：{{ refundReasonArray[refundDetail.buyerReason] }}</span>
          </p>
          <p v-if="refundDetail.buyerDesc">退款描述："{{ refundDetail.buyerDesc }}"</p>
          <p v-if="photoFiles.length" class="ref-voucher">
            退款凭证：
            <img-show v-for="(pic, picIndex) in photoFiles" :key="picIndex" :src="pic" class="ref-img" />
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { userStore } from '~/store/user'

const useUser = userStore()

const props = defineProps({
  refundDetail: {
    type: Object,
    default: () => ({}),
  },
})

// 用户昵称
const nickName = ref(useUser.user.nickName)
// 退款原因（总）
const refundReasonArray = computed(() => {
  return ['拍错/多拍/不喜欢', '协商一致退款', '商品破损/少件', '商品与描述不符', '商家发错货', '质量问题', '其他']
})

// 平台介入状态 -1.没有介入 1.用户申请介入 2.平台同意介入 3.平台拒绝介入 5.平台同意退款成功 6.用户撤销
const platInterSts = computed(() => props.refundDetail.platformInterventionStatus)

// 交易结束状态
const closingSts = computed(() => {
  switch (platInterSts.value) {
    case 3:
      return '平台拒绝退款'
    case 5:
      return '平台同意退款成功'
    default:
      return ''
  }
})

// 平台介入中补充凭证列表
const refundInterList = computed(() => {
  const list = props.refundDetail.orderRefundInterventionList || []
  return list.reverse().map((item) => {
    item.imgList = onHandleImgs(item.imgUrls)
    return item
  })
})

// 申请平台介入凭证
const applyInterventionImgs = computed(() => {
  return onHandleImgs(props.refundDetail.applyInterventionImgUrls)
})

// 拒绝凭证
const rejectImgs = computed(() => {
  return onHandleImgs(props.refundDetail.shopImgUrls)
})

// 用户申请凭证
const photoFiles = computed(() => {
  return onHandleImgs(props.refundDetail.photoFiles)
})

// 图片处理
const onHandleImgs = (imgStr) => {
  if (imgStr) {
    return imgStr.split(',').map((url) => {
      return convertImage(url)
    })
  }
  return []
}
</script>

<style lang="less" scoped>
.return-progress {
  margin-top: 20px;
  padding-bottom: 20px;
  border: 1px solid #eee;
  .tit {
    padding: 10px 15px;
    background: #f9f9f9;
    font-size: 14px;
    font-weight: 600;
  }
  .item {
    margin-top: 20px;
    padding: 0 20px;
    display: flex;
    line-height: 20px;
    .time {
      margin-right: 50px;
      color: #999;
      width: 120px;
      font-family: arial;
      font-size: 12px;
    }
    .text-box {
      flex: 1;
      margin-right: 30px;
      .name {
        font-size: 14px;
        font-weight: 600;
      }
      .text {
        margin: 5px 0;
        color: #999;
        word-break: break-word;
      }
      .img-box {
        display: flex;
        flex-wrap: wrap;
        .longimage {
          width: 100px;
          height: 100px;
          margin-right: 10px;
        }
      }
      .ref-voucher {
        display: flex;
        align-items: flex-start;
        margin-top: 10px;
        .ref-img {
          width: 100px;
          height: 100px;
          margin-right: 10px;
        }
      }
    }
    .text-box.pic-container {
      display: flex;
      flex-wrap: wrap;
      .longimage {
        width: 120px;
        height: 120px;
        margin-right: 12px;
      }
    }
  }
}
</style>
