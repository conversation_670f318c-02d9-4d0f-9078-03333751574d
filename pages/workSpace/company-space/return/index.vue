<!-- refundStatus 1 申请退款 2 退款成功 3 部分退款成功 4 退款失败 -1 退款关闭 -->
<!-- status '', '待付款', '待发货', '待收货', '待评价', '已完成', '已取消', '拼团中' -->
<template>
  <div class="">
    <a-form :label-col="{ flex: '72px' }">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-item label="订单号">
            <a-input placeholder="请输入" v-model:value="form.orderNumber"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="退款编号">
            <a-input placeholder="请输入" v-model:value="form.refundSn"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="申请时间">
            <a-range-picker
              value-format="YYYY-MM-DD HH:mm:ss"
              v-model:value="rangeTime"
              format="YYYY-MM-DD HH:mm:ss"
              show-time
            ></a-range-picker>
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-button type="primary" mr-10 @click="search">搜索</a-button>
          <a-button @click="reset">重置</a-button>
        </a-col>
      </a-row>
    </a-form>

    <a-radio-group v-model:value="current" button-style="solid" @change="changeTag">
      <a-radio-button v-for="item in conditions" :key="item.label" :value="item.value">
        {{ item.label }}
      </a-radio-button>
    </a-radio-group>

    <div mt-20>
      <a-row class="bg-#efefef p-10 font-bold">
        <a-col :span="12">
          <a-row>
            <a-col :span="24">商品信息</a-col>
          </a-row>
        </a-col>
        <a-col :span="12">
          <a-row>
            <a-col :span="5" text-center>退货类型</a-col>
            <a-col :span="5" text-center>退款金额</a-col>
            <a-col :span="5" text-center>申请时间</a-col>
            <a-col :span="5" text-center>处理状态</a-col>
            <a-col :span="4" text-center>操作</a-col>
          </a-row>
        </a-col>
      </a-row>

      <a-row v-for="item in orderList" :key="item.orderNumber" class="order-item">
        <a-col :span="24" class="title">
          <span pr-20>订单编号: {{ item.orderNumber }}</span>
          <span pr-20>退款编号: {{ item.refundSn || '--' }}</span>
          <span pr-20>
            <shop-outlined color="#f94c30" text-18 />
            {{ item.shopName || '--' }}
          </span>
        </a-col>
        <a-col :span="12">
          <a-row v-for="_item in item.orderItems" :key="_item.skuId" class="prod-item">
            <a-col :span="24">
              <div flex>
                <hm-img style="width: 100px; height: 100px; margin: 5px" :src="_item.pic" />
                <!-- <img w-100 h-100 m-5 :src="convertImage(_item.pic)" /> -->
                <div flex flex-col flex-1 justify-around overflow-hidden pr-10>
                  <span font-bold w-full class="text-ellipsis" :title="_item.prodName">{{ _item.prodName }}</span>
                  <div flex justify-between>
                    <span flex-1 class="text-ellipsis" :title="_item.skuName">{{ _item.skuName }}</span>
                    <span>X{{ _item.prodCount }}</span>
                  </div>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-col>
        <a-col :span="12">
          <a-row h-full>
            <a-col :span="5" style="border-left: 1px solid #d9d9d9">
              <div flex justify-center my-20>
                {{ ['', '仅退款', '退货退款'][item.applyType] }}
              </div>
            </a-col>
            <a-col :span="5" style="border-left: 1px solid #d9d9d9">
              <div flex justify-center my-20>
                {{ getPrice(item.refundAmount) }}
              </div>
            </a-col>
            <a-col :span="5" style="border-left: 1px solid #d9d9d9">
              <div flex justify-center my-20>{{ item.applyTime }}</div>
            </a-col>
            <a-col :span="5" style="border-left: 1px solid #d9d9d9">
              <div flex justify-center my-20>
                <tempalte v-if="item.platformInterventionStatus && item.platformInterventionStatus !== -1">
                  {{ ['', '待处理', '进行中', '已关闭', '', '已完成'][item.platformInterventionStatus] }}
                  {{ ['', '平台介入中', '平台介入中', '退款关闭', '', '退款成功'][item.platformInterventionStatus] }}
                </tempalte>
                <tempalte v-else>
                  {{
                    [
                      '',
                      '买家申请',
                      '商家接受',
                      '买家发货',
                      '商家收货',
                      '退款成功',
                      '买家撤回申请',
                      '商家拒绝',
                      '退款关闭',
                    ][item.returnMoneySts]
                  }}
                  <tempalte v-if="item.returnMoneySts === -1">退款关闭</tempalte>
                </tempalte>
              </div>
            </a-col>
            <a-col :span="4" style="border-left: 1px solid #d9d9d9">
              <div flex flex-col items-center my-12>
                <a-button
                  type="link"
                  @click="
                    navigateTo({
                      path: '/workSpace/company-space/return/' + item.refundSn,
                    })
                  "
                >
                  查看详情
                </a-button>
              </div>
            </a-col>
          </a-row>
        </a-col>
      </a-row>

      <hm-empty v-if="!orderList.length" />
    </div>

    <div text-right mt-16 v-if="page.total">
      <a-pagination
        v-model:current="page.current"
        v-model:page-size="page.size"
        :pageSizeOptions="['5', '10', '20', '50', '100']"
        show-size-changer
        show-quick-jumper
        :total="page.total"
        @change="fetchData"
      />
    </div>
  </div>
</template>

<script setup>
import { companyStore } from '~/store/company'

const useCompany = companyStore()

const router = useRouter()

const rangeTime = ref([])
const form = ref({})

const conditions = [
  {
    label: '全部',
    value: 0,
    key: 'allCount',
  },
  {
    label: '仅退款',
    value: 1,
    key: 'unPay',
  },
  {
    label: '退货退款',
    value: 2,
    key: 'payed',
  },
]
const current = ref(0)

// 获取表格数据
const loading = ref(false)
const page = reactive({
  size: 5,
  current: 1,
  total: 0,
})

const orderList = ref([])
const fetchData = () => {
  const params = {
    applyType: current.value,
    current: page.current,
    size: page.size,
    orderNumber: form.value.orderNumber || '',
    refundSn: form.value.refundSn || '',
    merchantId: useCompany.company.shopCompanyId,
  }
  if (rangeTime.value.length) {
    params.startTime = rangeTime.value[0]
    params.endTime = rangeTime.value[1]
  }
  http('/mall/p/orderRefund/list', {
    method: 'get',
    params: {
      ...params,
    },
  }).then((res) => {
    useMall(res, (ret) => {
      orderList.value = ret.records || []
      page.total = ret.total

      if (!orderList.value.length && page.current > 1) {
        page.current = 1
        fetchData()
      } else {
        getUserInfo()
      }
    })
  })
}

const gettotalPrice = ({ actualTotal, freightAmount, orderItemDtos }, key) => {
  let total = 0
  orderItemDtos.forEach((d) => {
    total += Number(d.price * d.prodCount)
  })
  if (key == 'actualTotal') {
    return actualTotal ? Number(actualTotal).toFixed(2) : total.toFixed(2)
  }
  if (key == 'freightAmount') {
    return freightAmount ? Number(freightAmount).toFixed(2) : (actualTotal - total).toFixed(2)
  }
  return '--'
}

const getRemainTime = (beg) => {
  const allTime = 86400
  const passedTime = (new Date().getTime() - new Date(beg).getTime()) / 1000
  const ret = allTime - passedTime
  if (ret <= 0) {
    return 0
  }
  return ret
}

const onCancelOrder = (orderNumber) => {
  Modal.confirm({
    title: '提示',
    content: '订单取消后将无法恢复,请您谨慎考虑',
    onOk() {
      http(`/mall/p/myOrder/cancel/${orderNumber}`, {
        method: 'put',
      }).then((res) => {
        useMall(res, () => {
          message.success('操作成功')
          fetchData()
        })
      })
    },
    onCancel() {
      console.log('Cancel')
    },
  })
}

const onDelOrder = (orderNumber) => {
  Modal.confirm({
    title: '提示',
    content: '订单删除后将无法恢复,请您谨慎考虑',
    onOk() {
      http(`/mall/p/myOrder/${orderNumber}`, {
        method: 'delete',
      }).then((res) => {
        useMall(res, () => {
          message.success('操作成功')
          fetchData()
        })
      })
    },
    onCancel() {
      console.log('Cancel')
    },
  })
}

/**
 * 订单确认收货
 */
const onConfirmReceive = (orderNumber) => {
  Modal.confirm({
    title: '提示',
    content: '确认收货吗?',
    onOk() {
      http(`/mall/p/myOrder/receipt/${orderNumber}`, {
        method: 'put',
      }).then((res) => {
        useMall(res, () => {
          message.success('操作成功')
          fetchData()
        })
      })
    },
    onCancel() {
      console.log('Cancel')
    },
  })
}

const orderCountData = ref({})
/**
 * 获取订单数量
 */
const getUserInfo = () => {
  return
  const params = {
    refundSn: form.value.refundSn,
    orderNumber: form.value.orderNumber,
  }
  if (rangeTime.value.length) {
    params.begin = rangeTime.value[0]
    params.end = rangeTime.value[1]
  }
  http('/mall/p/myOrder/myOrderSearchConut', {
    method: 'get',
    params,
  }).then(({ data }) => {
    data[0] = Object.values(data).reduce((res, item) => (res += item))
    orderCountData.value = data
  })
}

const search = () => {
  page.current = 1
  fetchData()
}

const reset = () => {
  form.value = {}
  rangeTime.value = []
  page.current = 1
  fetchData()
}

const changeTag = () => {
  page.current = 1
  fetchData()
}

onMounted(() => {
  fetchData()
})
</script>

<style lang="less" scoped>
.order-item {
  margin: 10px 0 20px;
  border: 1px solid #d9d9d9;

  .title {
    background: #efefef;
    padding: 10px;
  }

  .prod-item {
    &:not(:last-child) {
      border-bottom: 1px solid #d9d9d9;
    }

    padding: 10px 0;
  }
}
</style>
