<template>
  <div class="container">
    <!-- 项目基本信息 -->
    <div class="project-header">
      <div class="header-top">
        <h2>
          高精度数控机床升级
          <span class="project-code">PRJ-2024-001</span>
        </h2>
        <div class="button-group">
          <a-button type="primary"><i class="fas fa-edit"></i> 编辑</a-button>
          <a-button type="primary"><i class="fas fa-chart-line"></i> 项目报表</a-button>
        </div>
      </div>

      <div class="risk-warning"><i class="fas fa-exclamation-triangle"></i> 风险预警：交期延误，独家供应</div>

      <div class="project-info">
        <div class="info-item">
          <div class="info-label">创建时间</div>
          <div class="info-value">{{ projectInfo.createTime }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">负责人</div>
          <div class="info-value">{{ projectInfo.manager }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">截止日期</div>
          <div class="info-value">{{ projectInfo.deadline }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">预算</div>
          <div class="info-value">{{ projectInfo.budget }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">已支出</div>
          <div class="info-value">{{ projectInfo.spent }} ({{ projectInfo.spentPercentage }}%)</div>
        </div>
        <div class="info-item">
          <div class="info-label">当前阶段</div>
          <div class="info-value">
            <a-tag :color="getPhaseColor(projectInfo.currentPhase)">
              {{ projectInfo.currentPhase }}
            </a-tag>
          </div>
        </div>
      </div>

    </div>

    <!-- 选项卡导航 -->
    <div class="tabs-container">
      <a-tabs v-model:activeKey="activeTabKey">
        <a-tab-pane key="bom" tab="物料管理">
          <BomManagement />
        </a-tab-pane>
        <a-tab-pane key="inquiry" tab="询价单管理">
          <RfqManagement />
        </a-tab-pane>
        <a-tab-pane key="purchase" tab="采购订单管理">
          <PoManagement />
        </a-tab-pane>
        <a-tab-pane key="history" tab="操作历史">
          <!-- 操作历史内容将在后续实现 -->
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script>
import BomManagement from './components/bom/index.vue';
import RfqManagement from './components/rfq.vue';
import PoManagement from './components/po.vue';

export default {
  name: 'ProjectDetail',
  components: {
    BomManagement,
    RfqManagement,
    PoManagement
  },
  data() {
    return {
      activeTabKey: 'bom',
      projectInfo: {
        id: 'PRJ-2024-001',
        name: '高精度数控机床升级',
        createTime: '2024-05-10',
        manager: '张工',
        deadline: '2024-09-30',
        budget: '¥1,500,000',
        spent: '¥980,000',
        spentPercentage: 65,
        currentPhase: '进行中',
      },
      currentPhaseIndex: 1, // 对应"进行中"
    };
  },
  computed: {
    isFirstPhase() {
      return this.currentPhaseIndex === 0;
    },
    isLastPhase() {
      return this.currentPhaseIndex === this.projectPhases.length - 1;
    },
    progressWidth() {
      // 当前阶段在整个进度中的百分比
      return (this.currentPhaseIndex / (this.projectPhases.length - 1)) * 100;
    },
  },
  methods: {
    getPhaseColor(phase) {
      const phaseColors = {
        未开始: 'blue',
        进行中: 'green',
        已关闭: 'red',
      };
      return phaseColors[phase] || 'blue';
    },
    advancePhase() {
      if (!this.isLastPhase) {
        this.$confirm({
          title: '确定要推进项目阶段吗?',
          content: `确定要将项目推进到 "${this.projectPhases[this.currentPhaseIndex + 1]}" 吗？`,
          okText: '确认',
          cancelText: '取消',
          onOk: () => {
            // 在实际应用中，应该调用API更新项目阶段
            this.currentPhaseIndex++;
            this.projectInfo.currentPhase = this.projectPhases[this.currentPhaseIndex];
            this.$message.success(`项目已成功推进到 "${this.projectInfo.currentPhase}" 阶段`);
          },
        });
      }
    },
    revertPhase() {
      if (!this.isFirstPhase) {
        this.$confirm({
          title: '确定要回退项目阶段吗?',
          content: `确定要将项目回退到 "${this.projectPhases[this.currentPhaseIndex - 1]}" 吗？这可能会影响已创建的订单状态。`,
          okText: '确认',
          cancelText: '取消',
          onOk: () => {
            // 在实际应用中，应该调用API更新项目阶段
            this.currentPhaseIndex--;
            this.projectInfo.currentPhase = this.projectPhases[this.currentPhaseIndex];
            this.$message.success(`项目已回退到 "${this.projectInfo.currentPhase}" 阶段`);
          },
        });
      }
    },
    fetchProjectDetail() {
      // 获取路由参数中的项目ID
      const projectId = this.$route.params.id || this.$route.query.id;
      if (projectId) {
        console.log('加载项目数据: ' + projectId);
        // 这里应该调用API获取项目详情
        // 示例: this.$api.getProjectDetail(projectId).then(res => { this.projectInfo = res.data })
      }
    },
  },
  mounted() {
    this.fetchProjectDetail();
  },
};
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.breadcrumb-wrapper {
  padding: 16px 24px;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.main-content {
  display: flex;
  flex: 1;
  background-color: #f0f2f5;
}

.side-menu {
  width: 220px;
  background-color: #001529;
  height: 100%;
}

.content-area {
  flex: 1;
  padding: 24px;
  overflow: auto;
}

.project-header {
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 24px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.project-code {
  font-size: 14px;
  color: #888;
  font-weight: normal;
  margin-left: 8px;
}

.button-group {
  display: flex;
  gap: 10px;
}

.risk-warning {
  background-color: #fff7e6;
  border: 1px solid #ffe58f;
  padding: 8px 12px;
  border-radius: 4px;
  color: #d46b08;
  margin-bottom: 15px;
}

.project-info {
  display: flex;
  flex-wrap: wrap;
}

.info-item {
  flex: 0 0 16.666%;
  margin-bottom: 10px;
}

.info-label {
  color: #8c8c8c;
  font-size: 12px;
  margin-bottom: 4px;
}

.info-value {
  font-size: 14px;
  font-weight: 500;
}

.project-phase-control {
  display: flex;
  align-items: center;
  margin-top: 20px;
}

.phase-steps {
  flex: 1;
  margin: 0 15px;
  position: relative;
}

.tabs-container {
  background-color: #fff;
  border-radius: 4px;
}
</style>
