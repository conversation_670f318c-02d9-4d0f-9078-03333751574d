<template>
  <div class="">
    <a-form :model="searchForm">
      <div class="grid grid-cols-[repeat(auto-fill,minmax(250px,1fr))] gap-x-16">
        <a-form-item label="项目名称">
          <a-input v-model:value="searchForm.name" placeholder="请输入项目名称" style="width: 180px" />
        </a-form-item>
        <a-form-item label="项目编号">
          <a-input v-model:value="searchForm.projectNo" placeholder="请输入项目编号" style="width: 180px" />
        </a-form-item>
        <a-form-item label="负责人">
          <a-input v-model:value="searchForm.userId" placeholder="请输入负责人" style="width: 180px" />
        </a-form-item>
        <a-form-item label="状态">
          <a-select v-model:value="searchForm.status" style="width: 150px" placeholder="请选择状态">
            <a-select-option value="0">未开始</a-select-option>
            <a-select-option value="1">进行中</a-select-option>
            <a-select-option value="2">已结束</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">搜索</a-button>
          <a-button @click="handleReset" style="margin-left: 8px">重置</a-button>
        </a-form-item>
      </div>
    </a-form>

    <div class="mb-20">
      <a-button type="primary" @click="showModal">
        <template #icon><plus-outlined /></template>
        新建
      </a-button>
      <a-button type="primary" style="margin-left: 8px">
        <template #icon><download-outlined /></template>
        导出
      </a-button>
    </div>

    <div class="w-full">
      <a-table
        :dataSource="projects"
        :columns="columns"
        :pagination="pagination"
        @change="handleTableChange"
        :scroll="{ x: 'max-content' }"
        :loading="loading"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'name'">
            <div>{{ record.name }}</div>
            <div style="font-size: 12px; color: #888">{{ record.projectNo }}</div>
          </template>

          <template v-if="column.key === 'status'">
            <a-tag :class="['status-tag', getStatusClass(record.status)]">
              {{ statusMap[record.status] }}
            </a-tag>
            <div class="phase-tooltip">
              <a-steps :current="record.status" size="small" progress-dot>
                <a-step v-for="(phase, index) in phases" :key="index" :title="phase" />
              </a-steps>
            </div>
          </template>

          <template v-if="column.key === 'action'">
            <a-space>
              <div link @click="editProject(record)">编辑</div>
              <div link @click="manageProject(record)">
                {{ record.status === 0 ? '删除' : '管理' }}
              </div>
              <!-- <div link @click="viewProject(record)">查看</div> -->
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <a-modal
      v-model:open="modalVisible"
      :title="form.id ? '编辑项目' : '新建项目'"
      @ok="createProject"
      @cancel="
        () => {
          modalVisible = false
          formRef.value?.clearValidate()
        }
      "
      okText="确定"
      cancelText="取消"
    >
      <a-form :model="form" :rules="rules" ref="formRef" layout="vertical">
        <a-form-item label="项目名称" name="name" required>
          <a-input v-model:value="form.name" />
        </a-form-item>
        <a-form-item label="负责人" name="userId" required>
          <a-select
            placeholder="请选择负责人"
            :options="memberList"
            :field-names="{
              label: 'nickName',
              value: 'userId',
              key: 'userId',
            }"
            v-model:value="form.userId"
            show-search
            option-filter-prop="label"
          />
        </a-form-item>
        <a-form-item label="截止日期" name="deadline" required>
          <a-date-picker
            v-model:value="form.deadline"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
            show-time
          />
        </a-form-item>
        <a-form-item label="预算 (¥)" name="budget" required>
          <a-input-number v-model:value="form.budget" :min="0" :max="99999999" :precision="2" style="width: 100%" />
        </a-form-item>
        <a-form-item label="项目描述" name="descriptions">
          <a-textarea
            v-model:value="form.descriptions"
            :maxlength="500"
            show-count
            :auto-size="{ minRows: 3, maxRows: 6 }"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, DownloadOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { useRouter } from 'vue-router'
import { getMemberList } from '~/api/member'
import { companyStore } from '~/store/company'
import dayjs from 'dayjs'
const searchForm = ref({
  name: '',
  projectNo: '',
  userId: '',
  status: undefined,
  warning: '',
})
const modalVisible = ref(false)
const formRef = ref(null)
const router = useRouter()
const memberList = ref([])
const form = ref({
  name: '',
  userId: '',
  deadline: null,
  budget: null,
  descriptions: '',
})

const { company } = storeToRefs(companyStore())

const fetchUserList = async () => {
  try {
    const res = await getMemberList({
      merchantId: company.value.shopCompanyId,
      size: 999,
    })
    if (res.success) {
      const data = JSON.parse(res.data)
      memberList.value = data.records
    }
  } catch (err) {
    message.error('获取组织列表失败')
  }
}

onMounted(() => {
  fetchUserList()
  getProjects()
})

const rules = {
  name: [{ required: true, message: '请输入项目名称' }],
  userId: [{ required: true, message: '请选择负责人' }],
  deadline: [{ required: true, message: '请选择截止日期' }],
  budget: [{ required: true, message: '请输入预算' }],
  descriptions: [{ max: 500, message: '描述不能超过500字符' }],
}

const phases = ['未开始', '进行中', '已结束']
const statusMap = {
  0: '未开始',
  1: '进行中',
  2: '已结束',
}

const columns = [
  {
    title: '项目名称/编号',
    dataIndex: 'name',
    key: 'name',
    width: 200,
  },
  {
    title: '创建时间',
    dataIndex: 'createdTime',
    key: 'createdTime',
    width: 180,
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '-'
    },
  },
  {
    title: '负责人',
    dataIndex: 'userNickname',
    key: 'userNickname',
    width: 120,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '预算(￥)',
    key: 'budget',
    dataIndex: 'budget',
    width: 150,
    customRender({ text }) {
      return getPrice(text) || '-'
    },
  },
  {
    title: '截止日期',
    dataIndex: 'deadline',
    key: 'deadline',
    width: 180,
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '-'
    },
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right',
  },
]

const projects = ref([])

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50'],
  showTotal: (total) => `共 ${total} 条`,
})

const getStatusClass = (statusCode) => {
  const classMap = {
    0: 'status-not-started',
    1: 'status-in-progress',
    2: 'status-closed',
  }
  return classMap[statusCode] || ''
}

const getPhaseIndex = (currentPhase) => {
  return phases.indexOf(currentPhase)
}

const getBudgetClass = (spent, budget) => {
  const ratio = spent / budget
  if (ratio > 1) return 'budget-danger'
  if (ratio > 0.8) return 'budget-warning'
  return ''
}

const formatNumber = (num) => {
  if (!num) return ''
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

const loading = ref(false)
const getProjects = async () => {
  loading.value = true
  const [err, res] = await try_http('/mall/p/sell-project/page', {
    params: {
      name: searchForm.value.name,
      projectNo: searchForm.value.projectNo,
      userId: searchForm.value.userId,
      status: searchForm.value.status,
      warning: searchForm.value.warning,
      current: pagination.current,
      size: pagination.pageSize,
    },
  })
  loading.value = false
  if (!err) {
    projects.value = res.data.records
    pagination.total = res.data.total
  }
}

const handleSearch = () => {
  searchForm.value.page = 1
  getProjects()
}

const handleReset = () => {
  searchForm.value = {
    name: '',
    projectNo: '',
    userId: '',
    status: '',
    warning: '',
  }
  pagination.current = 1
  pagination.pageSize = 10
  getProjects()
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  getProjects()
}

const showModal = () => {
  form.value = {
    name: '',
    userId: '',
    deadline: null,
    budget: null,
    descriptions: '',
  }
  nextTick(() => {
    formRef.value?.clearValidate()
  })
  modalVisible.value = true
}

const createProject = () => {
  formRef.value.validate().then(async () => {
    if (form.value.id) {
      const [err] = await try_http('/mall//p/sell-project', {
        method: 'put',
        body: form.value,
      })
      if (!err) {
        message.success('修改成功')
        getProjects()
        modalVisible.value = false
      }
    } else {
      const [err] = await try_http('/mall/p/sell-project', {
        method: 'post',
        body: form.value,
      })
      if (!err) {
        message.success('项目创建成功')
        getProjects()
        modalVisible.value = false
      }
    }
  })
}

const viewProject = (record) => {
  message.info(`查看项目: ${record.name}`)
}

const editProject = (record) => {
  form.value = {
    id: record.id,
    name: record.name,
    merchantId: record.merchantId,
    userId: record.userId,
    deadline: record.deadline,
    budget: record.budget,
    descriptions: record.descriptions,
  }
  modalVisible.value = true
}

const manageProject = (record) => {
  if (record.status === 0) {
    Modal.confirm({
      title: '提示',
      content: `是否删除项目${record.name}`,
      onOk: async () => {
        const [err] = await try_http(`/mall/p/sell-project/${record.id}`, {
          method: 'delete',
        })
        if (!err) {
          message.success('操作成功')
          getProjects()
        }
      },
    })
  } else {
    router.push(`/project/detail?id=${record.key}`)
  }
}

const route = useRoute
</script>

<style scoped>
.status-tag {
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 12px;
}

.status-not-started {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-in-progress {
  background-color: #fff7e6;
  color: #fa8c16;
}

.status-closed {
  background-color: #f6ffed;
  color: #52c41a;
}

.risk-warning {
  color: #f5222d;
  font-weight: bold;
}

.budget-progress {
  width: 100%;
  height: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  overflow: hidden;
}

.budget-progress-inner {
  height: 100%;
  background-color: #52c41a;
}

.budget-warning .budget-progress-inner {
  background-color: #faad14;
}

.budget-danger .budget-progress-inner {
  background-color: #f5222d;
}

.phase-tooltip {
  display: none;
  position: absolute;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 100;
  width: 600px;
  left: 0;
  top: 100%;
}

.phase-tooltip .ant-steps-item {
  min-width: 80px;
}

td:hover .phase-tooltip {
  display: block;
}
</style>
