<template>
  <div>
    <div class="action-bar">
      <div class="button-group">
        <a-button type="primary">
          <template #icon><i class="fas fa-plus"></i></template>
          添加BOM
        </a-button>
        <a-dropdown>
          <a-button type="primary">
            <template #icon><i class="fas fa-file-invoice"></i></template>
            创建询价单
            <i class="fas fa-caret-down" style="margin-left: 5px;"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="handleIntelligentInquiry">
                <i class="fas fa-robot"></i> 智能创建
              </a-menu-item>
              <a-menu-item key="2" @click="showCreateInquiryModal">
                <i class="fas fa-edit"></i> 手动创建
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
      <div class="filter-row">
        <a-input
          placeholder="搜索BOM..."
          style="width: 200px"
          v-model:value="searchValue"
          allow-clear
        >
          <template #prefix>
            <i class="fas fa-search"></i>
          </template>
        </a-input>
        <a-select
          placeholder="全部品牌"
          style="width: 120px"
          v-model:value="brandFilter"
          allow-clear
        >
          <a-select-option value="">全部品牌</a-select-option>
          <a-select-option value="欧姆龙">欧姆龙</a-select-option>
          <a-select-option value="三菱">三菱</a-select-option>
          <a-select-option value="SEW">SEW</a-select-option>
          <a-select-option value="定制">定制</a-select-option>
        </a-select>
        <a-select
          placeholder="全部分类"
          style="width: 120px"
          v-model:value="categoryFilter"
          allow-clear
        >
          <a-select-option value="">全部分类</a-select-option>
          <a-select-option value="电子元器件">电子元器件</a-select-option>
          <a-select-option value="电气设备">电气设备</a-select-option>
          <a-select-option value="机械部件">机械部件</a-select-option>
          <a-select-option value="结构件">结构件</a-select-option>
        </a-select>
        <a-select
          placeholder="全部询价单"
          style="width: 150px"
          v-model:value="inquiryFilter"
          allow-clear
        >
          <a-select-option value="">全部询价单</a-select-option>
          <a-select-option value="RFQ-2024-001">RFQ-2024-001</a-select-option>
          <a-select-option value="RFQ-2024-002">RFQ-2024-002</a-select-option>
          <a-select-option value="RFQ-2024-003">RFQ-2024-003</a-select-option>
        </a-select>
      </div>
    </div>

    <a-table
      :dataSource="bomList"
      :columns="columns"
      :pagination="false"
      :row-key="record => record.id"
      :expandable="expandableConfig"
      :expanded-row-keys="expandedRowKeys"
      @expand="onExpand"
      :customRow="customRowFn"
    >
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.key === 'name'">
          <div style="font-weight: bold">{{ record.name }}</div>
          <div style="font-size: 12px; color: #888">{{ record.id }}</div>
        </template>
        <template v-else-if="column.key === 'version'">
          <div>
            {{ record.version }}
            <template v-if="!record.isActiveVersion">
              <a-tooltip :title="`当前活跃版本: ${record.activeVersion}`">
                <i class="fas fa-info-circle" style="color: #faad14; margin-left: 5px;"></i>
              </a-tooltip>
            </template>
          </div>
        </template>
        <template v-else-if="column.key === 'updateTime'">
          <div>{{ record.updateTime }}</div>
        </template>
        <template v-else-if="column.key === 'operation'">
          <a href="#" style="color: #f94c30; margin-right: 8px" @click.prevent="removeBom(record)">移除</a>
          <template v-if="!record.isActiveVersion">
            <a-button type="link" size="small" @click="syncMaterials(record)">
              <i class="fas fa-sync-alt"></i> 同步物料
            </a-button>
          </template>
        </template>
      </template>
      <template #expandedRowRender="{ record }">
        <a-table
          :columns="expandedMaterialColumns"
          :dataSource="record.materials"
          :pagination="false"
          size="small"
          :row-key="item => item.id"
        >
          <template #bodyCell="{ column, record: material }">
            <template v-if="column.key === 'materialOperation'">
              <a-button type="link" size="small" @click="viewTransactionHistory(material)">
                <i class="fas fa-history"></i> 交易历史
              </a-button>
            </template>
          </template>
        </a-table>
      </template>
    </a-table>

    <!-- 创建询价单模态框 -->
    <a-modal
      v-model:visible="inquiryModalVisible"
      title="创建询价单"
      width="1000px"
      @ok="handleInquiryModalOk"
      @cancel="handleInquiryModalCancel"
    >
      <div style="margin-bottom: 20px;">
        <a-row :gutter="15">
          <a-col :span="12">
            <a-form-item label="询价单名称">
              <a-input v-model:value="inquiryForm.name" placeholder="请输入询价单名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="询价截止日期">
              <a-date-picker v-model:value="inquiryForm.deadline" style="width: 100%" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="询价说明">
          <a-textarea
            v-model:value="inquiryForm.description"
            placeholder="请输入询价说明..."
            :rows="4"
          />
        </a-form-item>
      </div>
      
      <div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
          <h4 style="margin: 0;">已选物料 ({{ selectedMaterials.length }})</h4>
        </div>
        <a-table
          :dataSource="selectedMaterials"
          :columns="materialColumns"
          size="small"
          :pagination="false"
          :row-key="record => record.id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'deliveryDays'">
              <a-input-number v-model:value="record.deliveryDays" :min="1" :max="365" />
            </template>
            <template v-else-if="column.key === 'remark'">
              <a-input v-model:value="record.remark" />
            </template>
            <template v-else-if="column.key === 'operation'">
              <a href="#" style="color: #f94c30;" @click.prevent="removeMaterial(record)">移除</a>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>

    <!-- 使用交易历史组件 -->
    <transaction-history
      v-model:visible="transactionHistoryModalVisible"
      :material="currentMaterial"
      :inquiry-history-data="inquiryHistoryData"
      :purchase-history-data="purchaseHistoryData"
      @reinquiry="handleReInquiryFromComponent"
      @repurchase="handleRePurchaseFromComponent"
    />
  </div>
</template>

<script setup>
import { ref, reactive, h } from 'vue';
import { message, Modal } from 'ant-design-vue';
import TransactionHistory from './transactionHistory.vue';

const searchValue = ref('');
const brandFilter = ref('');
const categoryFilter = ref('');
const inquiryFilter = ref('');
const inquiryModalVisible = ref(false);
const expandedRowKeys = ref([]);
const transactionHistoryModalVisible = ref(false);
const activeTransactionTab = ref('inquiry');
const currentMaterial = ref({});

const inquiryForm = reactive({
  name: '',
  deadline: null,
  description: '',
});

// 询价历史数据
const inquiryHistoryData = ref([
  {
    id: 'INQ001',
    inquiryNo: 'RFQ-2024-001',
    quantity: 2,
    date: '2024-03-15',
    supplier: '上海电气有限公司',
    unitPrice: 1280.00,
    totalPrice: 2560.00,
    status: '已报价',
    responseTime: '2024-03-18',
    deliveryDays: 30,
    paymentTerms: '款到发货',
    validityPeriod: '30天'
  },
  {
    id: 'INQ002',
    inquiryNo: 'RFQ-2024-001',
    quantity: 2,
    date: '2024-03-15',
    supplier: '欧姆龙自动化（中国）有限公司',
    unitPrice: 1350.00,
    totalPrice: 2700.00,
    status: '已报价',
    responseTime: '2024-03-17',
    deliveryDays: 25,
    paymentTerms: '30%预付，发货前付清',
    validityPeriod: '45天'
  },
  {
    id: 'INQ003',
    inquiryNo: 'RFQ-2024-002',
    quantity: 1,
    date: '2024-04-20',
    supplier: '上海电气有限公司',
    unitPrice: 1260.00,
    totalPrice: 1260.00,
    status: '已报价',
    responseTime: '2024-04-23',
    deliveryDays: 30,
    paymentTerms: '款到发货',
    validityPeriod: '30天'
  },
  {
    id: 'INQ004',
    inquiryNo: 'RFQ-2024-002',
    quantity: 1,
    date: '2024-04-20',
    supplier: '深圳市创新科技有限公司',
    unitPrice: 1180.00,
    totalPrice: 1180.00,
    status: '已报价',
    responseTime: '2024-04-22',
    deliveryDays: 35,
    paymentTerms: '款到发货',
    validityPeriod: '60天'
  },
  {
    id: 'INQ005',
    inquiryNo: 'RFQ-2024-003',
    quantity: 3,
    date: '2024-05-10',
    supplier: '上海电气有限公司',
    unitPrice: 1250.00,
    totalPrice: 3750.00,
    status: '询价中',
    responseTime: '-',
    deliveryDays: null,
    paymentTerms: '-',
    validityPeriod: '-'
  }
]);

// 采购历史数据
const purchaseHistoryData = ref([
  {
    id: 'PO001',
    purchaseNo: 'PO-2024-0125',
    quantity: 2,
    date: '2024-03-25',
    supplier: '上海电气有限公司',
    unitPrice: 1280.00,
    totalPrice: 2560.00,
    status: '已完成',
    receiptDate: '2024-04-22',
    paymentStatus: '已付款',
    paymentDate: '2024-03-25',
    warranty: '12个月',
    qualityCheck: '合格'
  },
  {
    id: 'PO002',
    purchaseNo: 'PO-2024-0252',
    quantity: 1,
    date: '2024-04-30',
    supplier: '深圳市创新科技有限公司',
    unitPrice: 1180.00,
    totalPrice: 1180.00,
    status: '已完成',
    receiptDate: '2024-05-25',
    paymentStatus: '已付款',
    paymentDate: '2024-04-30',
    warranty: '12个月',
    qualityCheck: '合格'
  },
  {
    id: 'PO003',
    purchaseNo: 'PO-2024-0389',
    quantity: 3,
    date: '2024-05-15',
    supplier: '上海电气有限公司',
    unitPrice: 1250.00,
    totalPrice: 3750.00,
    status: '进行中',
    receiptDate: '预计2024-06-15',
    paymentStatus: '预付款已付',
    paymentDate: '2024-05-15',
    warranty: '12个月',
    qualityCheck: '待检'
  },
  {
    id: 'PO004',
    purchaseNo: 'PO-2024-0423',
    quantity: 5,
    date: '2024-05-28',
    supplier: '欧姆龙自动化（中国）有限公司',
    unitPrice: 1300.00,
    totalPrice: 6500.00,
    status: '待发货',
    receiptDate: '预计2024-06-25',
    paymentStatus: '待付款',
    paymentDate: '-',
    warranty: '12个月',
    qualityCheck: '待检'
  }
]);

// 已选物料数据
const selectedMaterials = ref([
  {
    id: '1',
    name: 'PLC控制器',
    model: 'CP1E-N40DR-A',
    brand: '欧姆龙',
    category: '电子元器件',
    quantity: 2,
    deliveryDays: 30,
    remark: '',
  },
  {
    id: '2',
    name: '触摸屏',
    model: 'NB10W-TW01B',
    brand: '欧姆龙',
    category: '电子元器件',
    quantity: 1,
    deliveryDays: 25,
    remark: '',
  },
  {
    id: '3',
    name: '钣金外壳',
    model: 'CNC-SHT-001',
    brand: '定制',
    category: '机械部件',
    quantity: 1,
    deliveryDays: 45,
    remark: '',
  }
]);

// BOM列表数据
const bomList = ref([
  {
    id: 'BOM-001',
    name: '主控制系统',
    standardCount: 30,
    customCount: 10,
    assemblyCount: 5,
    totalCount: 45,
    matchRate: 98,
    matchIssueCount: 0,
    updateTime: '2024-05-12',
    version: 'V1.0',
    isActiveVersion: true,
    activeVersion: 'V1.0',
    materials: [
      {
        id: '1',
        name: 'PLC控制器',
        model: 'CP1E-N40DR-A',
        brand: '欧姆龙',
        category: '电子元器件',
        quantity: 2,
        remark: '标准型号，带RS232通信',
        inquiryNo: 'RFQ-2024-001',
        specs: '标准PLC控制器，40点I/O，继电器输出'
      },
      {
        id: '2',
        name: '交流伺服电机',
        model: 'R88M-K2K030H',
        brand: '欧姆龙',
        category: '电气设备',
        quantity: 4,
        remark: '3000rpm，200V',
        inquiryNo: '无',
        specs: '交流伺服电机，2kW功率，3000rpm'
      },
      {
        id: '3',
        name: '触摸屏',
        model: 'NB10W-TW01B',
        brand: '欧姆龙',
        category: '电子元器件',
        quantity: 1,
        remark: '10英寸宽屏',
        inquiryNo: 'RFQ-2024-001',
        specs: '10英寸宽屏TFT触摸屏，800x480分辨率'
      }
    ]
  },
  {
    id: 'BOM-002',
    name: '传动系统',
    standardCount: 18,
    customCount: 8,
    assemblyCount: 2,
    totalCount: 28,
    matchRate: 85,
    matchIssueCount: 4,
    updateTime: '2024-05-15',
    version: 'V1.2',
    isActiveVersion: false,
    activeVersion: 'V1.5',
    materials: [
      {
        id: '4',
        name: '伺服驱动器',
        model: 'MR-J4-200A',
        brand: '三菱',
        category: '电气设备',
        quantity: 2,
        remark: '200V，2kW',
        inquiryNo: '无',
        specs: '三菱伺服驱动器，2kW功率，SSCNET III/H通信'
      },
      {
        id: '5',
        name: '齿轮箱',
        model: 'GR63SMT16',
        brand: 'SEW',
        category: '机械部件',
        quantity: 2,
        remark: '高精度，减速比1:16',
        inquiryNo: '无',
        specs: '高精度行星齿轮箱，减速比1:16，最大扭矩63Nm'
      }
    ]
  },
  {
    id: 'BOM-003',
    name: '机床外壳',
    standardCount: 5,
    customCount: 7,
    assemblyCount: 0,
    totalCount: 12,
    matchRate: 100,
    matchIssueCount: 0,
    updateTime: '2024-05-18',
    version: 'V2.0',
    isActiveVersion: true,
    activeVersion: 'V2.0',
    materials: [
      {
        id: '6',
        name: '钣金外壳',
        model: 'CNC-SHT-001',
        brand: '定制',
        category: '机械部件',
        quantity: 1,
        remark: '304不锈钢，定制加工',
        inquiryNo: 'RFQ-2024-003',
        specs: '304不锈钢，2mm厚度，CNC加工，喷砂处理'
      },
      {
        id: '7',
        name: '观察窗',
        model: 'GL-500x300',
        brand: '安全玻璃',
        category: '结构件',
        quantity: 2,
        remark: '钢化玻璃，500x300mm',
        inquiryNo: 'RFQ-2024-003',
        specs: '10mm厚度钢化玻璃，耐冲击，500x300mm'
      }
    ]
  }
]);

// 表格列定义
const columns = [
  {
    title: 'BOM名称/编号',
    key: 'name',
    dataIndex: 'name'
  },
  {
    title: '版本',
    key: 'version',
    dataIndex: 'version'
  },
  {
    title: '更新时间',
    key: 'updateTime',
    dataIndex: 'updateTime'
  },
  {
    title: '操作',
    key: 'operation',
  },
];

const materialColumns = [
  {
    title: '产品名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '型号',
    dataIndex: 'model',
    key: 'model',
  },
  {
    title: '品牌',
    dataIndex: 'brand',
    key: 'brand',
  },
  {
    title: '产品分类',
    dataIndex: 'category',
    key: 'category',
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    key: 'quantity',
  },
  {
    title: '期望交期(天)',
    key: 'deliveryDays',
    dataIndex: 'deliveryDays',
  },
  {
    title: '备注',
    key: 'remark',
    dataIndex: 'remark',
  },
  {
    title: '操作',
    key: 'operation',
  }
];

const expandedMaterialColumns = [
  {
    title: '',
    dataIndex: 'checkbox',
    key: 'checkbox',
    width: 50,
    customRender: () => {
      return h('a-checkbox');
    }
  },
  {
    title: '产品名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '型号',
    dataIndex: 'model',
    key: 'model',
  },
  {
    title: '品牌',
    dataIndex: 'brand',
    key: 'brand',
  },
  {
    title: '产品分类',
    dataIndex: 'category',
    key: 'category',
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    key: 'quantity',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
  },
  {
    title: '操作',
    key: 'materialOperation',
  }
];

// 扩展配置
const expandableConfig = {
  expandIcon: ({ expanded, record, onExpand }) => {
    return h('div', {
      class: 'expand-icon-container',
      onClick: (e) => {
        onExpand(record, e);
      }
    }, [
      h('i', {
        class: expanded ? 'fas fa-chevron-down' : 'fas fa-chevron-right'
      })
    ]);
  },
  expandRowByClick: true,
  indentSize: 0
};

// 自定义行属性
const customRowFn = (record) => {
  return {
    class: 'clickable-row',
    onClick: () => {
      toggleExpand(record);
    }
  };
};

// 处理展开事件
const onExpand = (expanded, record) => {
  if (expanded) {
    expandedRowKeys.value = [...expandedRowKeys.value, record.id];
  } else {
    expandedRowKeys.value = expandedRowKeys.value.filter(key => key !== record.id);
  }
};

// 切换展开状态
const toggleExpand = (record) => {
  const key = record.id;
  const expanded = expandedRowKeys.value.includes(key);
  if (expanded) {
    expandedRowKeys.value = expandedRowKeys.value.filter(k => k !== key);
  } else {
    expandedRowKeys.value = [...expandedRowKeys.value, key];
  }
};

// 移除BOM
const removeBom = (record) => {
  Modal.confirm({
    title: '确认移除',
    content: `确定要移除 ${record.name} (${record.id}) 吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      // 移除BOM的逻辑
      const index = bomList.value.findIndex(item => item.id === record.id);
      if (index !== -1) {
        bomList.value.splice(index, 1);
        message.success(`已成功移除 ${record.name}`);
      }
    },
  });
};

// 智能询价
const handleIntelligentInquiry = () => {
  message.info('智能询价单创建功能将在后续版本开发');
};

// 显示创建询价单模态框
const showCreateInquiryModal = () => {
  inquiryModalVisible.value = true;
};

// 处理询价单模态框确认
const handleInquiryModalOk = () => {
  if (!inquiryForm.name) {
    message.error('请输入询价单名称');
    return;
  }
  if (!inquiryForm.deadline) {
    message.error('请选择询价截止日期');
    return;
  }
  
  message.success('询价单创建成功');
  inquiryModalVisible.value = false;
  
  // 重置表单
  Object.assign(inquiryForm, {
    name: '',
    deadline: null,
    description: '',
  });
};

// 处理询价单模态框取消
const handleInquiryModalCancel = () => {
  inquiryModalVisible.value = false;
};

// 移除物料
const removeMaterial = (record) => {
  const index = selectedMaterials.value.findIndex(item => item.id === record.id);
  if (index !== -1) {
    selectedMaterials.value.splice(index, 1);
  }
};

// 同步物料
const syncMaterials = (record) => {
  Modal.confirm({
    title: '确认同步',
    content: `确定要将 ${record.name} (${record.id}) 同步到最新版本吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      // 同步物料的逻辑
      message.success(`已成功将 ${record.name} 同步到最新版本`);
    },
  });
};

// 查看交易历史
const viewTransactionHistory = (material) => {
  currentMaterial.value = {...material, specs: '标准PLC控制器，40点I/O，继电器输出'};
  transactionHistoryModalVisible.value = true;
};

// 处理从组件中触发的再次询价
const handleReInquiryFromComponent = ({ material, record }) => {
  // 关闭交易历史模态框会在组件内部处理
  transactionHistoryModalVisible.value = false;
  
  // 打开创建询价单模态框
  inquiryModalVisible.value = true;
  
  // 预填询价单信息
  inquiryForm.name = `${material.name} 询价单`;
  
  // 将当前物料添加到已选物料中
  const existingIndex = selectedMaterials.value.findIndex(item => 
    item.id === material.id && item.model === material.model);
    
  if (existingIndex === -1) {
    // 添加新物料
    selectedMaterials.value.push({
      id: material.id,
      name: material.name,
      model: material.model,
      brand: material.brand,
      category: material.category,
      quantity: record.quantity,
      deliveryDays: record.deliveryDays || 30,
      remark: `优先询价供应商: ${record.supplier}`,
    });
  } else {
    // 更新已存在的物料
    selectedMaterials.value[existingIndex].quantity = record.quantity;
    selectedMaterials.value[existingIndex].deliveryDays = record.deliveryDays || 30;
    selectedMaterials.value[existingIndex].remark = `优先询价供应商: ${record.supplier}`;
  }
  
  message.success(`已创建询价单，询价物料：${material.name}，供应商：${record.supplier}`);
};

// 处理从组件中触发的再次购买
const handleRePurchaseFromComponent = ({ material, record }) => {
  // 这里应该是创建采购单的实际逻辑
  message.success(`已创建采购单，采购物料：${material.name}，供应商：${record.supplier}，数量：${record.quantity}，单价：¥${record.unitPrice.toFixed(2)}`);
  transactionHistoryModalVisible.value = false;
};
</script>

<style scoped>
.action-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 10px;
}

.filter-row {
  display: flex;
  gap: 10px;
}

.progress-bar {
  background-color: #f5f5f5;
  border-radius: 100px;
  height: 6px;
  margin: 4px 0;
  overflow: hidden;
  width: 100%;
}

.progress-inner {
  background-color: #52c41a;
  border-radius: 100px;
  height: 100%;
}

.match-error {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
}

.material-type {
  background-color: #f5f5f5;
  border-radius: 2px;
  color: #595959;
  display: inline-block;
  font-size: 12px;
  margin-right: 8px;
  padding: 2px 8px;
}

.type-standard {
  background-color: #e6f7ff;
  color: #1890ff;
}

.type-custom {
  background-color: #fff7e6;
  color: #fa8c16;
}

.type-assembly {
  background-color: #f6ffed;
  color: #52c41a;
}

.expand-icon-container {
  cursor: pointer;
  padding: 0 8px;
}

.clickable-row {
  cursor: pointer;
}

.clickable-row:hover {
  background-color: #f5f5f5;
}

.expanded-table-wrapper {
  padding: 16px;
}

.material-info-section {
  margin-bottom: 20px;
}

.transaction-tabs {
  margin-top: 20px;
}
</style>
