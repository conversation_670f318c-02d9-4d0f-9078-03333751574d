<template>
  <div class="purchase-order-management">
    <div class="action-bar">
      <div class="filter-row">
        <a-input placeholder="搜索采购单..." style="width: 200px; margin-right: 10px" />
        <a-select placeholder="全部状态" style="width: 150px">
          <a-select-option value="">全部状态</a-select-option>
          <a-select-option value="pending_payment">待支付</a-select-option>
          <a-select-option value="pending_shipment">待发货</a-select-option>
          <a-select-option value="partial_shipment">部分发货</a-select-option>
          <a-select-option value="pending_receipt">待收货</a-select-option>
          <a-select-option value="completed">已完成</a-select-option>
          <a-select-option value="cancelled">已取消</a-select-option>
        </a-select>
      </div>
      <div class="button-group">
        <a-button type="primary"><i class="fas fa-plus"></i> 创建采购单</a-button>
      </div>
    </div>

    <a-table 
      :dataSource="purchaseOrders" 
      :columns="poColumns" 
      rowKey="id"
      :expandable="expandableConfig"
      class="po-table">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record.status)">{{ record.status }}</a-tag>
        </template>
        <template v-if="column.key === 'action'">
          <a-space>
            <a href="#" style="color: #f94c30">查看</a>
            <a href="#" style="color: #f94c30">跟踪</a>
            <a v-if="record.status === '待收货'" href="#" style="color: #f94c30">收货</a>
            <a v-if="record.status === '待发货'" href="#" style="color: #f94c30">取消</a>
            <a v-if="record.status === '待支付'" href="#" style="color: #f94c30">审批</a>
            <a v-if="record.status === '待支付'" href="#" style="color: #f94c30">编辑</a>
          </a-space>
        </template>
      </template>
    </a-table>

    <div class="risk-warning">
      <i class="fas fa-exclamation-triangle"></i> 风险检查：PO-2024-001预付款比例超过50%，请关注风险控制
    </div>

    <div class="button-group-bottom">
      <a-button type="primary">批量跟踪</a-button>
      <a-button>导出采购单</a-button>
      <a-button>生成报表</a-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, h } from 'vue';

const poColumns = ref([
  { 
    title: '', 
    dataIndex: 'checkbox', 
    width: 40,
    customRender: () => h('a-checkbox')
  },
  { title: '采购单号', dataIndex: 'poNumber', key: 'poNumber' },
  { title: '供应商', dataIndex: 'supplier', key: 'supplier' },
  { title: '金额', dataIndex: 'amount', key: 'amount' },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '操作', key: 'action' },
]);

const itemColumns = ref([
  { title: '产品名称', dataIndex: 'name' },
  { title: '型号', dataIndex: 'model' },
  { title: '品牌', dataIndex: 'brand' },
  { title: '产品分类', dataIndex: 'category' },
  { title: '数量', dataIndex: 'quantity' },
  { title: '单价', dataIndex: 'unitPrice' },
  { title: '总价', dataIndex: 'totalPrice' },
  { title: '备注', dataIndex: 'note' },
  { 
    title: '操作', 
    key: 'action',
    customRender: () => {
      return h('a-space', {}, [
        h('a', { href: '#', style: 'color: #f94c30' }, '编辑'),
        h('a', { href: '#', style: 'color: #f94c30' }, '删除')
      ]);
    }
  }
]);

const purchaseOrders = ref([
  {
    id: 1,
    poNumber: 'PO-2024-001',
    supplier: '上海精密机械有限公司',
    amount: '¥320,000',
    status: '待收货',
    items: [
      {
        id: 1,
        name: '数控系统',
        model: 'SINUMERIK 840D',
        brand: '西门子',
        category: '自动化控制系统',
        quantity: 1,
        unitPrice: '¥180,000',
        totalPrice: '¥180,000',
        note: '进口设备'
      },
      {
        id: 2,
        name: '伺服电机',
        model: '1FK7080',
        brand: '西门子',
        category: '驱动设备',
        quantity: 4,
        unitPrice: '¥35,000',
        totalPrice: '¥140,000',
        note: '标准配置'
      }
    ]
  },
  {
    id: 2,
    poNumber: 'PO-2024-002',
    supplier: '德国西门子自动化（中国）有限公司',
    amount: '¥580,000',
    status: '待发货',
    items: [
      {
        id: 1,
        name: 'PLC控制器',
        model: 'S7-1500',
        brand: '西门子',
        category: '自动化控制系统',
        quantity: 2,
        unitPrice: '¥85,000',
        totalPrice: '¥170,000',
        note: '高性能型'
      },
      {
        id: 2,
        name: '触摸屏',
        model: 'TP1500',
        brand: '西门子',
        category: '人机界面',
        quantity: 3,
        unitPrice: '¥45,000',
        totalPrice: '¥135,000',
        note: '15英寸彩色'
      },
      {
        id: 3,
        name: '通讯模块',
        model: 'CP1543-1',
        brand: '西门子',
        category: '通信设备',
        quantity: 5,
        unitPrice: '¥55,000',
        totalPrice: '¥275,000',
        note: '工业以太网'
      }
    ]
  },
  {
    id: 3,
    poNumber: 'PO-2024-003',
    supplier: '广州工业配件有限公司',
    amount: '¥80,000',
    status: '待支付',
    items: [
      {
        id: 1,
        name: '机床防护罩',
        model: 'MG-2000',
        brand: '国产',
        category: '机床配件',
        quantity: 2,
        unitPrice: '¥15,000',
        totalPrice: '¥30,000',
        note: '定制尺寸'
      },
      {
        id: 2,
        name: '冷却液系统',
        model: 'CL-500',
        brand: '国产',
        category: '辅助系统',
        quantity: 1,
        unitPrice: '¥25,000',
        totalPrice: '¥25,000',
        note: '5L容量'
      },
      {
        id: 3,
        name: '电柜配件',
        model: 'EC-series',
        brand: '国产',
        category: '电气配件',
        quantity: 1,
        unitPrice: '¥25,000',
        totalPrice: '¥25,000',
        note: '含开关、继电器等'
      }
    ]
  }
]);

const expandableConfig = computed(() => ({
  expandedRowRender: (record) => {
    return h('div', { class: 'nested-table-container' }, [
      h('a-table', {
        dataSource: record.items,
        columns: itemColumns.value,
        pagination: false,
        rowKey: 'id',
        class: 'nested-table'
      })
    ]);
  }
}));

const getStatusColor = (status) => {
  const statusColors = {
    '待支付': 'orange',
    '待发货': 'blue',
    '部分发货': 'purple',
    '待收货': 'green',
    '已完成': 'success',
    '已取消': 'default'
  };
  return statusColors[status] || 'blue';
};
</script>

<style scoped>
.purchase-order-management {
  padding: 16px 0;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.filter-row {
  display: flex;
  align-items: center;
}

.button-group, .button-group-bottom {
  display: flex;
  gap: 8px;
}

.button-group-bottom {
  margin-top: 16px;
}

.risk-warning {
  background-color: #fff7e6;
  border: 1px solid #ffe58f;
  padding: 8px 12px;
  border-radius: 4px;
  color: #d46b08;
  margin: 15px 0;
}

.po-table {
  margin-bottom: 10px;
}

.nested-table-container {
  margin: 16px 0;
}

.nested-table {
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}
</style>
