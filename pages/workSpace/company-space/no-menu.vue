<template>
  <div w-full bg-white>
    <div pt-100 style="text-align: center">
      <img h-181px src="~/assets/images/daodao_nodata.png" />
      <span text-18px color-primary font-bold>暂无权限,请联系企业管理员</span>
    </div>
  </div>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { companyStore } from '~/store/company'
import { authMenuStore } from '~/store/authMenu'
const companyStoreObj = companyStore()
const { company } = storeToRefs(companyStoreObj)
const useAuthMenu = authMenuStore()
const { authMenu } = storeToRefs(useAuthMenu)
const router = useRouter()
onMounted(() => {
  const { menuItemKeys } = authMenu.value
  const companyId = company.value.shopCompanyId
  if (menuItemKeys.length) {
    router.replace({
      path: `/workSpace/company-space/${ menuItemKeys[0] }`,
      query: { companyId }
    })
  }
})
</script>
