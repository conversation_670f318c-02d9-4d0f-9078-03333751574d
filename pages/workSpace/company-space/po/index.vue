<template>
  <div>
    <form-wrapper>
      <a-form :model="form" layout="inline">
        <a-form-item label="采购订单号">
          <a-input v-model:value="form.orderNo" placeholder="请输入采购订单号" />
        </a-form-item>

        <a-form-item label="物料名称">
          <a-input v-model:value="form.prodName" placeholder="请输入物料名称" />
        </a-form-item>

        <a-form-item label="物料型号">
          <a-input v-model:value="form.skuCode" placeholder="请输入物料型号" />
        </a-form-item>

        <a-form-item label="下单时间">
          <a-range-picker
            v-model:value="useTimeRange(form, 'createTimeFilter').value"
            show-time
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
            :placeholder="['开始时间', '结束时间']"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item label="订单状态">
          <a-select v-model:value="form.status" placeholder="请选择订单状态" class="w-180px!">
            <a-select-option v-for="key in Object.keys(statusMap).filter((key) => key != 0)" :key="key" :value="key">
              {{ getStatusText(key) }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="采购员">
          <a-input v-model:value="form.username" placeholder="请输入采购员姓名" />
        </a-form-item>

        <a-form-item label="品牌">
          <a-input v-model:value="form.brandName" placeholder="请输入品牌" />
        </a-form-item>

        <a-form-item>
          <div class="flex gap-8px">
            <a-button type="primary" @click="search">查询</a-button>
            <a-button @click="reset">重置</a-button>
          </div>
        </a-form-item>
      </a-form>
    </form-wrapper>

    <!-- <div class="text-right"> -->
    <!--   <view-manager :list="viewList" v-model="viewType"></view-manager> -->
    <!-- </div> -->

    <order-table ref="orderTableRef" :form="form"></order-table>
  </div>
</template>

<script setup>
import orderTable from './components/order-table.vue'
import { useOrder } from './components/useOrder'

const { statusMap, getStatusText } = useOrder()

const form = ref({})
const orderTableRef = ref()

const viewType = ref('order')
const viewList = [
  {
    name: '订单视图',
    type: 'order',
  },
  {
    name: '物料视图',
    type: 'material',
  },
]
const search = async () => {
  if (viewType.value == 'order') {
    orderTableRef.value?.search()
  }
}

const reset = () => {
  form.value = {}
  nextTick(() => {
    search()
  })
}
</script>
