import fileDownload from 'js-file-download'
import { getStatusColor, getStatusText, statusMap } from './utils'

const getPayment = (payment) => {
  const map = {
    1: '现金/电汇',
    2: '银行承兑汇票',
  }
  return map[payment] ?? ''
}

const getPayCondition = (val) => {
  const map = {
    1: '预付款',
    2: '账期结算',
  }
  return map[val] ?? ''
}

const deliveryMap = {
  0: {
    color: 'blue',
    text: '备货中',
  },
  10: {
    color: 'orange',
    text: '部分发货',
  },
  11: {
    color: 'green',
    text: '全部发货',
  },
  20: {
    // Keep purple for '部分收货' as it was for '待发货'
    color: 'purple',
    text: '部分收货',
  },
  21: {
    color: 'green',
    text: '已收货',
  },
  22: {
    color: 'green',
    text: '已收货（部分取消）',
  },
  30: {
    color: 'orange',
    text: '退货中',
  },
  40: {
    color: 'orange',
    text: '客户取消待平台确认',
  },
  41: {
    color: 'orange',
    text: '客户取消待供应商确认',
  },
  50: {
    color: 'green',
    text: '已取消',
  },
}
const financeMap = {
  0: {
    color: 'red',
    text: '未对账',
  },
  10: {
    color: 'yellow',
    text: '部分对账',
  },
  15: {
    color: 'green',
    text: '已全部对账',
  },
  20: {
    color: 'green',
    text: '部分付款',
  },
  30: {
    color: 'green',
    text: '已全部支付',
  },
  40: {
    color: 'green',
    text: '已支付（有退款）',
  },
}

const getFinanceColor = (val) => financeMap[val]?.color ?? ''
const getFinanceText = (val) => financeMap[val]?.text ?? ''

const getDeliveryColor = (val) => {
  return deliveryMap[val]?.color ?? ''
}

const getDeliveryText = (val) => deliveryMap[val]?.text ?? ''

const getCount = (status, text) => (status == 0 ? '-' : text)

export const useOrder = () => {
  const canEdit = (status) => status == 0 && useAuth('po:order:edit')
  const canSubmit = (status) => eq(status, 0, 15) && useAuth('po:order:submit')
  const canDownload = (v) => eq(v, 20, 30, 40) && useAuth('po:order:download')
  const canDelete = (v) => v == 0 && useAuth('po:order:delete')

  const submit = async ({ info, orderId }) => {
    let addrOrderId = info.address?.id || info.addrOrderId
    if (!addrOrderId) {
      message.error('请完善您的收货信息')
      return
    }
    const [err] = await try_http('/mall/p/sell-order/commit', {
      method: 'put',
      body: {
        orderId,
        items: info.items,
        addrOrderId,
        remarks: info.remarks,
        isConfirmed: true,
      },
    })
    if (!err) {
      return true
    }
    return false
  }

  const priceSubmit = async (row) => {
    const [err] = await try_http(`/mall/p/sell-order/confirm/${row.orderId}`, {
      method: 'put',
    })
    if (!err) {
      return true
    } else {
      return false
    }
  }

  const handleDownload = async (url, orderNumber) => {
    const res = await $fetch(url, {
      responseType: 'blob',
    })
    fileDownload(res, `合同-${orderNumber}.docx`)
  }

  return {
    canEdit,
    canSubmit,
    getStatusText,
    getStatusColor,
    getPayment,
    getPayCondition,
    submit,
    getDeliveryColor,
    getDeliveryText,
    getFinanceColor,
    getFinanceText,
    getCount,
    statusMap,
    canDownload,
    handleDownload,
    canDelete,
    priceSubmit,
  }
}
