export const orderStatus = [
  { statu: 0, name: '草稿', color: 'default' },
  { statu: 10, name: '商家确认中', color: 'orange' },
  { statu: 20, name: '已锁定', color: 'orange' },
  { statu: 15, name: '待确认', color: 'orange' },
  { statu: 30, name: '执行中', color: 'blue' },
  { statu: 40, name: '已完成', color: 'green' },
  { statu: 50, name: '已取消', color: 'red' },
]

export const statusMap = orderStatus.reduce((map, item) => {
  map[item.statu] = {
    name: item.name,
    color: item.color,
  }
  return map
}, {})

export const getStatusText = (statu) => {
  return statusMap[statu]?.name ?? ''
}

export const getStatusColor = (statu) => {
  return statusMap[statu]?.color ?? ''
}
