<template>
  <div class="flex gap-8px my-16px">
    <!-- <a-dropdown> -->
    <!--   <a-button type="primary"> -->
    <!--     批量操作 -->
    <!--     <DownOutlined /> -->
    <!--   </a-button> -->
    <!--   <template #overlay> -->
    <!--     <a-menu> -->
    <!--       <a-menu-item @click="handleDelete(selectedRowKeys)">删除</a-menu-item> -->
    <!--       <a-menu-item>开票</a-menu-item> -->
    <!--       <a-menu-item>付款</a-menu-item> -->
    <!--       <a-menu-item>归档</a-menu-item> -->
    <!--     </a-menu> -->
    <!--   </template> -->
    <!-- </a-dropdown> -->

    <!-- <a-button type="primary"> -->
    <!--   <ExportOutlined /> -->
    <!--   导出 -->
    <!-- </a-button> -->
    <!---->
    <!-- <a-button type="primary"> -->
    <!--   <PrinterOutlined /> -->
    <!--   打印 -->
    <!-- </a-button> -->

    <div class="flex-1"></div>
    <column-config @open-config="openConfig" @change-config="changeConfig"></column-config>
  </div>

  <price-alert :ids="selectedRowKeys" class="mb-16px"></price-alert>
  <a-table
    bordered
    row-key="orderId"
    :columns="getColumn('order').value"
    :data-source="data"
    :scroll="{ x: 1500 }"
    :loading="loading"
    :row-selection="{ selectedRowKeys: selectedRowKeys, preserveSelectedRowKeys: true, onChange: onSelectChange }"
    :pagination="pagination"
    size="small"
    @change="pageChange"
    @resize-column="colResize"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'status'">
        <a-tag :color="getStatusColor(record.status)">{{ getStatusText(record.status) }}</a-tag>
      </template>

      <action-button v-if="column.dataIndex == 'orderNumber'" @click="edit(record)">
        {{ record.orderNumber }}
      </action-button>

      <template v-if="column.dataIndex == 'action'">
        <action-button v-if="canEdit(record.status)" v-loading-click="() => edit(record)">编辑</action-button>
        <action-button v-if="canDelete(record.status)" v-loading-click="() => handleDelete([record.orderId])">
          删除
        </action-button>
        <action-button v-if="canSubmit(record.status)" v-loading-click="() => handleSubmit(record)">提交</action-button>
        <action-button
          v-if="canDownload(record.status)"
          v-loading-click="() => handleDownload(record.orderContractFileUrl, record.orderNumber)"
        >
          合同下载
        </action-button>
      </template>
    </template>

    <template #expandedRowRender="{ record: data }">
      <a-table
        bordered
        :pagination="false"
        :columns="getColumn('material').value"
        :data-source="data.items"
        :scroll="{ x: 1500 }"
        row-key="id"
        size="small"
        @resize-column="colResize"
      >
        <template #bodyCell="{ record, column, text }">
          <a-tag v-if="column.dataIndex == 'deliveryStatus'" :color="getDeliveryColor(record.deliveryStatus)">
            {{ getDeliveryText(record.deliveryStatus) }}
          </a-tag>

          <a-tag v-if="column.dataIndex == 'financialStatus'" :color="getFinanceColor(record.financialStatus)">
            {{ getFinanceText(record.financialStatus) }}
          </a-tag>

          <template v-if="['receivedNumber', 'canceledNumber', 'deliveredNumber'].includes(column.dataIndex)">
            {{ getCount(data.status, text) }}
          </template>
        </template>
      </a-table>
    </template>
  </a-table>
</template>

<script setup>
import { useOrder } from './useOrder'
const columns = [
  {
    title: '订单号',
    dataIndex: 'orderNumber',
    minWidth: 180,
    isSystem: true,
    fixed: 'left',
    resizable: true,
  },
  {
    title: '订单状态',
    dataIndex: 'status',
    width: 150,
    resizable: true,
  },
  {
    title: '来源询价单',
    width: 180,
    dataIndex: '',
    defaultHidden: true,
    resizable: true,
  },
  {
    title: '物料数量',
    width: 90,
    customRender({ record }) {
      return calcCount('number', record.items)
    },
    resizable: true,
  },
  {
    title: '总金额（￥）',
    width: 120,
    customRender({ record }) {
      return getPrice(calcCount('totalAmount', record.items))
    },
    resizable: true,
  },
  {
    title: '采购员',
    width: 150,
    dataIndex: 'userName',
    resizable: true,
  },
  {
    title: '下单时间',
    dataIndex: 'createTime',
    width: 180,
    customRender({ text }) {
      return formatTime(text)
    },
    resizable: true,
  },
  {
    title: '结束时间',
    dataIndex: 'finishTime',
    width: 180,
    customRender({ text }) {
      return formatTime(text)
    },
    resizable: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    isSystem: true,
    width: 150,
    resizable: true,
  },
]

const materialColumn = [
  { title: '物料名称', dataIndex: 'prodName', width: 200 },
  {
    title: '品牌',
    width: 150,
    customRender({ record }) {
      return has(record.replaceBrandId) ? '研选' : record.brandName
    },
  },
  {
    title: '型号',
    dataIndex: 'skuCode',
    width: 180,
    customRender({ record }) {
      return has(record.replaceBrandId) ? record.replaceSkuCode : record.skuCode
    },
  },
  {
    title: '参考品牌',
    dataIndex: 'replaceBrandName',
    width: 150,
    customRender({ record }) {
      return has(record.replaceBrandId) ? record.brandName : '-'
    },
  },
  {
    title: '参考型号',
    dataIndex: 'replaceSkuCode',
    width: 180,
    customRender({ record }) {
      return has(record.replaceBrandId) ? record.skuCode : '-'
    },
  },
  { title: '分类', dataIndex: 'categoryName', width: 150 },
  { title: '数量', dataIndex: 'number', width: 100 },
  {
    title: '已发货数量',
    dataIndex: 'deliveredNumber',
    width: 120,
  },
  { title: '已收货数量', dataIndex: 'receivedNumber', width: 120 },
  { title: '已取消数量', dataIndex: 'canceledNumber', width: 120 },
  { title: '物流状态', dataIndex: 'deliveryStatus', width: 120 },
  { title: '财务状态', dataIndex: 'financialStatus', width: 120 },
  {
    title: '单价（¥）',
    dataIndex: 'price',
    width: 120,
    customRender({ text }) {
      return getPrice(text)
    },
  },
  {
    title: '总价（¥）',
    dataIndex: 'totalAmount',
    width: 150,
    customRender({ text }) {
      return getPrice(text)
    },
  },
  { title: '操作', dataIndex: 'action', width: 180, fixed: 'right', isSystem: true },
]

const props = defineProps({
  form: {
    type: Object,
    default: () => ({}),
  },
})

const { form } = toRefs(props)
const data = ref([])
const loading = ref(false)

const search = async () => {
  page.current = 1
  fetchData()
}

const fetchData = async () => {
  const body = {
    ...pick(page, ['current', 'size']),
    ...form.value,
  }
  loading.value = true
  const [err, res] = await try_http('/mall/p/sell-order/page', {
    method: 'post',
    body,
  })
  loading.value = false

  if (!err) {
    page.total = res.data.total
    data.value = res.data.records
  }
}
const { page, pagination, pageChange } = usePage(fetchData)

const {
  canEdit,
  canSubmit,
  getStatusColor,
  getStatusText,
  getDeliveryColor,
  getDeliveryText,
  getFinanceText,
  getFinanceColor,
  getCount,
  submit,
  canDownload,
  handleDownload,
  canDelete,
  priceSubmit,
} = useOrder()

const edit = (row) => {
  navigateTo(`/workSpace/company-space/po-detail?orderId=${row.orderNumber}`)
}
const handleSubmit = useDebounceFn(async (row) => {
  let res = false
  if (row.status == 0) {
    res = await submit({
      info: row,
      orderId: row.orderId,
    })
  } else {
    res = await priceSubmit(row)
  }
  if (res) {
    message.success('操作成功')
    search()
  }
})
// 表格列配置
const { fetchConfig, columnData, getColumn, changeConfig } = useColumn({
  key: 'PO_LIST_PC',
  config: [
    {
      key: 'order',
      name: '订单列配置',
      columns,
    },
    {
      key: 'material',
      name: '物料列配置',
      columns: materialColumn,
    },
  ],
  descriptions: '采购管理订单视图列配置',
})

const openConfig = (callback) => {
  callback(columnData.value)
}

defineExpose({
  search,
})

onMounted(async () => {
  await fetchConfig()
  search()
})

const selectedRowKeys = ref([])
const onSelectChange = (keys) => {
  selectedRowKeys.value = keys
}

const handleDelete = async (ids) => {
  if (!ids.length) {
    message.warn('请选择至少一条记录')
    return
  }
  const [err] = await try_http('/mall/p/sell-order', {
    method: 'delete',
    body: ids,
  })
  if (!err) {
    message.success('操作成功')
    selectedRowKeys.value = selectedRowKeys.value.filter((id) => !ids.includes(id))
    fetchData()
  }
}
</script>
