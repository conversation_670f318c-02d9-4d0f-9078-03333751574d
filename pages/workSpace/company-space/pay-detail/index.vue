<template>
  <div>
    <a-spin :spinning="loading">
      <a-page-header :title="title" @back="$router.go(-1)" :sub-title="subTitle">
        <template #extra>
          <!-- <Actions @refresh="fetchInfo" :info="info" /> -->
        </template>
      </a-page-header>

      <a-card class="pay-card mt-24px">
        <div>
          <a-alert type="warning" show-icon v-if="isOvertime">
            <template #message>
              <div>
                <span>付款提醒：</span>
                <span class="font-bold text-primary">付款已逾期，请立即处理</span>
              </div>
            </template>
            <template #description>
              请尽快完成剩余
              <span class="text-20px text-primary font-bold ml-4px">{{ getPrice(info.payableTotalAmount) }}</span>
              的支付。逾期可能会影响您的信用记录及后续合作。
            </template>
          </a-alert>
          <div class="payment-info">
            <div class="payment-info-flex">
              <div class="payment-info-section">
                <p><strong>线下打款说明：</strong></p>
                <div class="payment-detail-item">
                  <span class="label">账户名称：</span>
                  <span class="value">研选工场（苏州）网络有限公司</span>
                  <a-button type="link" size="small" @click="copyToClipboard('研选工场（苏州）网络有限公司')">
                    <copy-outlined />
                  </a-button>
                </div>
                <div class="payment-detail-item">
                  <span class="label">开户银行：</span>
                  <span class="value">招商银行股份有限公司苏州独墅湖支行</span>
                  <a-button type="link" size="small" @click="copyToClipboard('招商银行股份有限公司苏州独墅湖支行')">
                    <copy-outlined />
                  </a-button>
                </div>
                <div class="payment-detail-item">
                  <span class="label">银行账号：</span>
                  <span class="value">512916361810001</span>
                  <a-button type="link" size="small" @click="copyToClipboard('512916361810001')">
                    <copy-outlined />
                  </a-button>
                </div>
                <div class="payment-detail-item">
                  <span class="label">税号：</span>
                  <span class="value">91320594MAE5L8PD96</span>
                  <a-button type="link" size="small" @click="copyToClipboard('91320594MAE5L8PD96')">
                    <copy-outlined />
                  </a-button>
                </div>
                <div class="payment-detail-item">
                  <span class="label">银联号：</span>
                  <span class="value">308305008197</span>
                  <a-button type="link" size="small" @click="copyToClipboard('308305008197')">
                    <copy-outlined />
                  </a-button>
                </div>
                <div class="payment-detail-item">
                  <span class="label">开户行地址：</span>
                  <span class="value">苏州工业园区启月街288号</span>
                  <a-button type="link" size="small" @click="copyToClipboard('苏州工业园区启月街288号')">
                    <copy-outlined />
                  </a-button>
                </div>
                <p class="note">*请使用公司对公账户进行转账</p>
              </div>
              <div class="payment-info-section payment-tip">
                <div class="tip-header">
                  <info-circle-outlined />
                  <strong>注意事项</strong>
                </div>
                <p>请确保转账时：</p>
                <ul>
                  <li>使用公司对公账户转账</li>
                  <li>转账备注中注明付款单号</li>
                  <li>保留银行转账凭证</li>
                  <li>转账完成后及时将转账凭证作为附件上传</li>
                </ul>
                <p>以确保款项能及时核销入账</p>
              </div>
            </div>
          </div>
        </div>
      </a-card>

      <a-card title="基本信息" class="mt-24px">
        <a-descriptions bordered :column="3">
          <a-descriptions-item label="付款单号">{{ info.orderNumber }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ formatTime(info.createTime) }}</a-descriptions-item>
          <a-descriptions-item label="所属对账单">
            <action-button @click="go('/workSpace/company-space/inv-detail', info.rsOrderNumber)">
              {{ info.rsOrderNumber }}
            </action-button>
          </a-descriptions-item>
          <a-descriptions-item label="应付总额">
            <span class="font-bold">{{ getPrice(info.payableTotalAmount) }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="实付总额">
            <span class="text-green font-bold">{{ getPrice(info.paidAmount) }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="待付总额">
            <span class="text-red font-bold">{{ getPrice(info.unpaidAmount) }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="付款完成日期">{{ formatTime(info.finishTime) }}</a-descriptions-item>
        </a-descriptions>
      </a-card>

      <a-card title="物料信息" class="mt-14px">
        <a-table borderd :columns="columns" :data-source="info.items || []">
          <template #bodyCell="{ column, text }">
            <action-button
              v-if="column.dataIndex == 'sellOrderNumber'"
              @click="
                navigateTo({
                  path: '/workSpace/company-space/po-detail',
                  query: {
                    orderId: text,
                  },
                })
              "
            >
              {{ text }}
            </action-button>
          </template>
        </a-table>
      </a-card>
    </a-spin>
  </div>
</template>

<script setup>
import { usePay } from '../pay/usePay'
import dayjs from 'dayjs'

definePageMeta({
  pageName: '付款单详情',
})

onMounted(() => {
  fetchInfo()
})

const copy = useCopy()
const copyToClipboard = (val) => {
  copy(val)
  message.success('复制成功')
}

const fetchInfo = async () => {
  if (!orderNumber) {
    message.error('订单信息不存在')
    return
  }
  loading.value = true
  const [err, res] = await try_http(`/mall/p/payment-order/details/${orderNumber}`)
  loading.value = false
  if (!err) {
    info.value = res.data
  }
}

const { getStatusText, go } = usePay()

const info = ref({})
const route = useRoute()

const orderNumber = route.query.orderNumber
const loading = ref(false)

const title = computed(() => {
  return info.value.orderNumber ? `付款单详情-${info.value.orderNumber}` : '付款单详情'
})

const subTitle = computed(() => {
  return '状态：' + getStatusText(info.value.status)
})

const columns = ref([
  { title: '物料名称', dataIndex: 'prodName', key: 'materialName', width: 200 },
  {
    title: '品牌',
    width: 150,
    customRender({ record }) {
      return has(record.replaceBrandId) ? '研选' : record.brandName
    },
  },
  {
    title: '型号',
    dataIndex: 'skuCode',
    width: 180,
    customRender({ record }) {
      return has(record.replaceBrandId) ? record.replaceSkuCode : record.skuCode
    },
  },
  {
    title: '参考品牌',
    dataIndex: 'replaceBrandName',
    width: 150,
    customRender({ record }) {
      return has(record.replaceBrandId) ? record.brandName : '-'
    },
  },
  {
    title: '参考型号',
    dataIndex: 'replaceSkuCode',
    width: 180,
    customRender({ record }) {
      return has(record.replaceBrandId) ? record.skuCode : '-'
    },
  },
  {
    title: '数量',
    dataIndex: 'number',
    key: 'quantity',
    width: 100,
    customRender({ text }) {
      return getPrice(text)
    },
  },
  {
    title: '单价',
    dataIndex: 'price',
    key: 'price',
    width: 120,
    customRender({ text }) {
      return getPrice(text)
    },
  },
  {
    title: '总价',
    dataIndex: 'totalAmount',
    key: 'totalAmount',
    width: 150,
    customRender({ text }) {
      return getPrice(text)
    },
  },
  { title: '所属采购单号', dataIndex: 'sellOrderNumber', key: 'poNo', width: 150 },
])

const isOvertime = computed(() => {
  if (!info.value.overdueTime) return false
  return dayjs().isAfter(dayjs(info.value.overdueTime), 'millisecond')
})
</script>

<style lang="less" scoped>
:deep(.pay-card) {
  border-left: 4px solid #faad14;
}
.payment-info {
  margin-top: 16px;
  padding: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;

  .payment-info-flex {
    display: flex;
    gap: 16px;

    .payment-info-section {
      flex: 1;

      &.payment-tip {
        // background-color: #fffbe6;
        // border: 1px solid #ffe58f;
        border-radius: 4px;
        padding: 12px;

        .tip-header {
          color: #faad14;
          margin-bottom: 8px;
          font-size: 14px;

          .anticon {
            margin-right: 6px;
          }
        }

        ul {
          padding-left: 18px;
          margin: 6px 0;

          li {
            margin-bottom: 4px;
          }
        }
      }
    }
  }

  p {
    margin: 6px 0;
    line-height: 1.5;
  }

  .payment-detail-item {
    display: flex;
    align-items: center;
    margin: 8px 0;

    .label {
      font-weight: 500;
      min-width: 80px;
    }

    .value {
      margin-right: 4px;
    }
  }

  .note {
    color: #ff4d4f;
    margin-top: 12px;
    font-weight: bold;
  }

  .buttons {
    margin-top: 16px;
    display: flex;
    gap: 12px;
  }
}
</style>
