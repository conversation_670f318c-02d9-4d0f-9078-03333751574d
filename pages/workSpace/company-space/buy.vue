<template>
  <div flex-1 flex="~ col" divide="x #e1e1e1" bg-white px-16 py-20>
    <div flex-1 flex flex-col items-center v-if="user">
      <div mt-64 text-28 font-bold>全网买专属客服</div>
      <a-avatar :size="120" :src="user.pic" bg-primary mt-40>{{ user.nickName }}</a-avatar>
      <div text-24 font-bold mt-20>{{ user.nickName }}</div>
      <div text-16 mt-16 relative text="#999">
        <div v-if="!!user.company" flex items-center>
          <div i-mdi-company mr-10></div>
          <div>{{ user.company }}</div>
        </div>
        <div v-if="!!user.identity" flex items-center mt-12>
          <div i-mdi-tie mr-10></div>
          <div>{{ user.identity }}</div>
        </div>
        <div v-if="!!user.userMail" flex items-center mt-12>
          <div i-mdi-email mr-10></div>
          <div>{{ user.userMail }}</div>
        </div>
        <div v-if="!!user.userMobile" flex items-center mt-12>
          <div i-mdi-cellphone mr-10></div>
          <div>{{ user.userMobile }}</div>
        </div>
      </div>
    </div>
    <a-divider v-if="user"></a-divider>
    <div sub-text font-size-14 line-height-24 border-none px-12 pb-28 v-if="user">
      全网买专属客服服务内容：
      <br />
      1.与企业客户深入沟通，了解并分析其具体的采购需求，并提供专业的建议和方案；
      <br />
      2.根据客户需求，推荐适合的产品，提供详细的产品信息和报价方案；
      <br />
      3.协助客户完成采购订单的提交和确认，实时跟踪订单进度,及时反馈信息；
      <br />
      4.处理客户的投诉、咨询等售后问题，提供贴心周到的售后支持；
      <br />
      5.主动与客户保持沟通，提供个性化的服务，增强客户黏性。
    </div>
    <a-result v-if="!user" title="尚未配置全网买客服"></a-result>
  </div>
</template>

<script setup>
import { companyStore } from '~/store/company'
const store = companyStore()
const user = ref()

const fetchData = async () => {
  const res = await http('/mall/p/merchant-user/customer-service/' + store.company.shopCompanyId)
  useMall(res, () => {
    user.value = res.data
  })
}

onMounted(() => {
  fetchData()
})
</script>
