<template>
  <div>
    <form-wrapper>
      <a-form :model="form" layout="inline">
        <a-form-item label="采购订单号">
          <a-input v-model:value="form.orderNo" placeholder="请输入采购订单号" />
        </a-form-item>

        <a-form-item label="物料名称">
          <a-input v-model:value="form.prodName" placeholder="请输入物料名称" />
        </a-form-item>

        <a-form-item label="物料型号">
          <a-input v-model:value="form.skuCode" placeholder="请输入物料型号" />
        </a-form-item>

        <a-form-item label="创建时间">
          <range-picker v-model="form.createTimeFilter"></range-picker>
        </a-form-item>

        <a-form-item label="采购员">
          <a-input v-model:value="form.username" placeholder="请输入采购员姓名" />
        </a-form-item>

        <a-form-item label="品牌">
          <a-input v-model:value="form.brandName" placeholder="请输入品牌" />
        </a-form-item>

        <a-form-item>
          <div class="flex gap-8px">
            <a-button type="primary" @click="search">查询</a-button>
            <a-button @click="reset">重置</a-button>
          </div>
        </a-form-item>
      </a-form>
    </form-wrapper>

    <!-- <div class="text-right"> -->
    <!--   <view-manager :list="viewList" v-model="viewType"></view-manager> -->
    <!-- </div> -->

    <order-table ref="orderTableRef" :form="form"></order-table>
  </div>
</template>

<script setup>
import orderTable from './components/order-table.vue'

const form = ref({})
const orderTableRef = ref()

const viewType = ref('order')
const viewList = [
  {
    name: '订单视图',
    type: 'order',
  },
  {
    name: '物料视图',
    type: 'material',
  },
]
const search = async () => {
  if (viewType.value == 'order') {
    orderTableRef.value?.search()
  }
}

const reset = () => {
  form.value = {}
  search()
}
</script>
