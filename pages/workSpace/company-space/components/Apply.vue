<template>
  <a-form mt-20 :label-col="{ flex: '80px' }">
    <div grid grid-cols-3 gap-x-16>
      <a-form-item label="用户名">
        <a-input v-model:value="formState.nickName" placeholder="请输入" allowClear></a-input>
      </a-form-item>

      <a-form-item label="姓名">
        <a-input placeholder="请输入" v-model:value="formState.realName" allowClear></a-input>
      </a-form-item>

      <a-form-item label="手机号">
        <a-input placeholder="请输入" v-model:value="formState.userMobile" allowClear></a-input>
      </a-form-item>

      <a-form-item label="审核状态">
        <a-select placeholder="请选择" v-model:value="formState.auditStatus" w-180 allowClear>
          <a-select-option :value="ComStatus.noApply">未审核</a-select-option>
          <a-select-option :value="ComStatus.apply">已审核</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="审核结果">
        <a-select placeholder="请选择" v-model:value="formState.auditResult" w-180 allowClear>
          <a-select-option :value="ComResult.fail">未通过</a-select-option>
          <a-select-option :value="ComResult.pass">已通过</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="审核时间">
        <a-range-picker
          v-model:value="range"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD HH:mm:ss"
          allowClear
          show-time
        ></a-range-picker>
      </a-form-item>

      <a-form-item>
        <a-space>
          <a-button type="primary" @click="search">搜索</a-button>
          <a-button @click="reset">重置</a-button>
        </a-space>
      </a-form-item>
    </div>
  </a-form>

  <div mt-20>
    <a-table :columns="columns" :data-source="list" :scroll="{ x: 1500 }" :pagination="false" :loading="loading">
      <template #bodyCell="{ column, value, record }">
        <div v-if="column.dataIndex === 'auditStatus'">
          {{ formatStatus(value) }}
        </div>

        <div v-if="column.dataIndex === 'auditResult'">
          {{ formatResult(value) }}
        </div>

        <div v-if="column.dataIndex === 'action'">
          <a-button type="link" @click="showDetail(record)">详情</a-button>
          <a-button
            type="link"
            v-if="useAuth('auth:member:audit') && record.auditStatus == ComStatus.noApply"
            @click="showApply(record)"
          >
            审核
          </a-button>
          <!-- <a-button type="link" v-else>删除</a-button> -->
        </div>
      </template>
    </a-table>

    <a-pagination
      my-16
      text-right
      v-model:current="page.current"
      v-model:page-size="page.size"
      show-quick-jumper
      :total="page.total"
      @change="after"
    />

    <Detail ref="detailRef" />
    <apply-detail ref="applyRef" @ok="after"></apply-detail>
  </div>
</template>

<script setup lang="ts">
import { ComResult, ComStatus } from '~/api/mall-platform/types'
import { getMemberList } from '~/api/member'
import { Member } from '~/api/member/type'
import { Pagination } from '~/api/shop/type'
import Detail from './Detail.vue'
import ApplyDetail from './ApplyDetail.vue'
import { companyStore } from '~/store/company'
type State = {
  nickName: string
  realName: string
  userMobile: string
  auditStatus: ComStatus
  auditResult: ComResult
}

const range = ref()
const page = ref({
  current: 1,
  size: 10,
  total: 0,
})

const formatStatus = (status: ComStatus) => {
  switch (status) {
    case ComStatus.noApply:
      return '未审核'
    case ComStatus.apply:
      return '已审核'
  }
}

const formatResult = (result: ComResult) => {
  switch (result) {
    case ComResult.fail:
      return '未通过'
    case ComResult.pass:
      return '已通过'
  }
}

const merchantId = companyStore().company.shopCompanyId

const loading = ref(false)

const formState = ref({} as State)
const list = ref<Member[]>([])
const fetchMember = async () => {
  loading.value = true
  const res = await getMemberList({
    merchantId,
    ...time.value,
    ...pick(page.value, ['current', 'size']),
    ...formState.value,
  })
  loading.value = false
  useMall(res, () => {
    const ret = JSON.parse(res.data) as Pagination<Member>
    page.value.total = ret.total
    list.value = ret.records
  })
}

const time = computed(() => {
  if (!range.value) {
    return {
      queryBeginTime: undefined,
      queryEndTime: undefined,
    }
  }
  const [queryBeginTime, queryEndTime] = range.value
  return {
    queryBeginTime,
    queryEndTime,
  }
})

const columns = ref([
  {
    title: '用户名',
    dataIndex: 'nickName',
  },
  {
    title: '姓名',
    dataIndex: 'realName',
  },
  {
    title: '手机号',
    dataIndex: 'userMobile',
  },
  {
    title: '申请时间',
    dataIndex: 'applyTime',
  },
  {
    title: '审核状态',
    dataIndex: 'auditStatus',
  },
  {
    title: '审核结果',
    dataIndex: 'auditResult',
  },
  {
    title: '部门',
    dataIndex: 'position',
  },
  {
    title: '审核时间',
    dataIndex: 'joinTime',
  },
  {
    title: '备注',
    dataIndex: 'remark',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '160px',
    fixed: 'right',
  },
] as any)

const search = () => {
  fetchMember()
}

const reset = () => {
  range.value = undefined
  formState.value = {} as State
  page.value.current = 1
  fetchMember()
}
onMounted(() => {
  fetchMember()
})

const detailRef = ref<InstanceType<typeof Detail>>()

const showDetail = (user) => {
  detailRef.value?.show(user as Member)
}

const applyRef = ref<InstanceType<typeof ApplyDetail>>()
const showApply = (user) => {
  applyRef.value?.show(user as Member)
}

const after = () => {
  fetchMember()
}
</script>
