<template>
  <a-modal
    :width="800"
    title="添加成员"
    v-model:open="open"
    @ok="confirm"
    :ok-button-props="{
      disabled: !select.keys.length,
    }"
  >
    <a-input-group compact>
      <a-input v-model:value="mobile" placeholder="输入手机号搜索" class="w-400!" @press-enter="addUser"></a-input>
      <a-button type="primary" @click="addUser" :loading="loading">
        <PlusOutlined />
      </a-button>
    </a-input-group>

    <div mt-20>
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :pagination="false"
        row-key="userId"
        :row-selection="{
          selectedRowKeys: select.keys,
          onChange: changeSelect,
          getCheckboxProps,
        }"
        :scroll="{ x: 1000 }"
      >
        <template #bodyCell="{ column, record }">
          <div
            v-if="column.dataIndex == 'status'"
            :class="{
              'text-red': formatStatus(record as Member).code == -1,
              'text-green': formatStatus(record as Member).code == 1,
            }"
          >
            {{ formatStatus(record as Member).text }}
          </div>

          <div v-if="column.dataIndex == 'action'">
            <a-button type="link" @click="remove(record)">移除</a-button>
          </div>
        </template>
      </a-table>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { Member } from '~/api/member/type'
import { companyStore } from '~/store/company'
import { MemberLevel } from '~/store/user'
const props = defineProps<{
  treeMap: Record<string, any>
}>()

const open = ref(false)

const mobile = ref('')

const show = () => {
  dataSource.value = []
  mobile.value = ''
  open.value = true
  select.keys = []
  select.rows = []
}

const select = reactive({
  keys: [] as string[],
  rows: [] as Member[],
})

const columns = [
  {
    title: '用户名',
    dataIndex: 'nickName',
    width: 150,
  },
  {
    title: '姓名',
    dataIndex: 'realName',
    width: 150,
  },
  {
    title: '公司',
    dataIndex: 'company',
    width: 200,
  },
  {
    title: '可邀请状态',
    dataIndex: 'status',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 80,
    fixed: 'right',
  },
]

const route = useRoute()
const dataSource = ref<any[]>([])

const merchantId = companyStore().company.shopCompanyId
const loading = ref(false)

const userSet = computed(() => {
  const set: Set<string> = new Set()
  dataSource.value.forEach((item) => {
    set.add(item.userMobile)
  })
  return set
})

const addUser = async () => {
  if (!mobile.value) {
    return message.error('请输入手机号')
  }
  if (userSet.value.has(mobile.value)) {
    return message.error('该用户已添加')
  }
  loading.value = true
  const [err, res] = await try_http<any>('/mall/p/user/getUserInfoByMobile', {
    query: {
      userMobile: mobile.value,
      merchantId,
    },
  })

  loading.value = false
  if (!err) {
    const _user = res.data.user
    _user.flag = res.data.flag
    dataSource.value.unshift(_user)
    mobile.value = ''
  }
}

const formatStatus = (item) => {
  const flagMap = {
    1: '已是当前企业成员',
    2: '审核中的成员',
    3: '非认证会员及以上',
  }
  if (item.flag == 0) {
    return { code: 1, text: '可邀请' }
  } else {
    return { code: -1, text: flagMap[item.flag] }
  }
}

const remove = (item) => {
  dataSource.value = dataSource.value.filter((d) => d.userMobile != item.userMobile)
}

const emits = defineEmits(['ok'])

const confirm = async () => {
  const data = select.rows.map((item) => {
    return {
      userMobile: item.userMobile,
      auditResult: 1,
      auditStatus: 1,
      merchantId,
    }
  })
  const res = await plat_http.post('/shop/merchantUser/saveBatch', {
    merchantUsers: JSON.stringify(data),
  })
  useMall(res, () => {
    message.success('邀请成功')
    open.value = false
    emits('ok')
  })
}

function changeSelect(keys, rows) {
  select.keys = keys
  select.rows = rows
}

function getCheckboxProps(record) {
  return {
    disabled: formatStatus(record).code == -1,
  }
}

defineExpose({
  show,
})
</script>
