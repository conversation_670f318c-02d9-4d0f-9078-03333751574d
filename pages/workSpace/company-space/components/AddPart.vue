<template>
  <a-modal
    :title="current == Type.add ? '新建部门' : '编辑部门'"
    v-model:open="open"
    destroy-on-close
    :confirm-loading="loading"
    @ok="handleOk"
  >
    <a-form>
      <a-form-item label="上级部门" v-bind="validateInfos.parentId">
        <a-tree-select
          v-if="treeData.length"
          :tree-default-expand-all="true"
          v-model:value="form.parentId"
          :tree-data="computedTreeData"
          :field-names="{ value: 'id', label: 'name' }"
        ></a-tree-select>
      </a-form-item>

      <a-form-item label="部门名称" v-bind="validateInfos.name">
        <a-input placeholder="请输入部门名称" :maxlength="20" v-model:value="form.name"></a-input>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { Form } from 'ant-design-vue'
import { omit } from 'lodash-es'

type OrgNode = {
  name: string
  parentId: string
  id?: string
}
enum Type {
  add,
  edit,
}
const current = ref(Type.add)
const open = ref(false)
const treeData = ref([])
const computedTreeData = computed(() => {
  if (current.value == Type.add) return treeData.value
  const dfs = (arr) => {
    const list: Obj[] = []
    arr.forEach((item) => {
      const map = omit(item, 'children')
      if (item.id == form.value.id) {
        map.disabled = true
      }
      if (item.children?.length) {
        map.children = dfs(item.children)
      }
      list.push(map)
    })
    return list
  }
  return dfs(treeData.value)
})
let merchantId = ''

const show = (node: OrgNode, _treeData, _merchantId) => {
  form.value.name = node.name
  form.value.parentId = node.parentId
  merchantId = _merchantId
  treeData.value = _treeData
  open.value = true
  nextTick(() => {
    clearValidate()
  })
}

const add = (node: OrgNode, _treeData, _merchantId) => {
  current.value = Type.add
  form.value.id = undefined
  show(node, _treeData, _merchantId)
}

const edit = (node: OrgNode, _treeData, _merchantId) => {
  current.value = Type.edit
  form.value.id = node.id
  show(node, _treeData, _merchantId)
}

const form = ref<OrgNode>({
  id: '',
  name: '',
  parentId: '',
})

const rules = reactive({
  name: [
    {
      required: true,
      message: '请输入部门名称',
      whitespace: true,
    },
  ],
  parentId: [
    {
      required: true,
      message: '请选择上级部门',
    },
  ],
})

const { validate, validateInfos, clearValidate } = Form.useForm(form.value, rules)
defineExpose({
  add,
  edit,
})

const emits = defineEmits(['ok'])

const loading = ref(false)
const handleOk = () => {
  validate().then(async () => {
    if (current.value == Type.add) {
      loading.value = true
      try {
        await plat_http.post('/shop/org', {
          merchantId,
          ...form.value,
        })
        message.success('添加成功')
        open.value = false
        emits('ok')
      } catch (err) {
        console.log('%c Line:127 🥛 err', 'color:#51AC58', err)
      }
      loading.value = false
    } else {
      loading.value = true
      try {
        await plat_http.put('/shop/org', {
          merchantId,
          ...form.value,
        })
        message.success('修改成功')
        open.value = false
        emits('ok')
      } catch (err) {
        console.log('%c Line:136 🥛 err', 'color:#1FDD53', err)
      }
      loading.value = false
    }
  })
}
</script>
