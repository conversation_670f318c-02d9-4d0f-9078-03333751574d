<template>
  <div flex flex-1 overflow-y-auto>
    <div w-210 h-full border-r border="#f1f1f1" pt-10>
      <a-tree
        :tree-data="treeData as any"
        :field-names="{ key: 'id' }"
        v-model:selected-keys="orgs"
        @select="fetchMember"
        block-node
      >
        <template #title="{ data }">
          <div flex>
            <div truncate flex-1 :title="data.name">{{ data.name }}</div>
            <a-dropdown v-if="hasAction">
              <MoreOutlined />
              <template #overlay>
                <a-menu>
                  <a-menu-item v-if="useAuth('auth:member:addOrg')" @click="addPart(data)">
                    <PlusOutlined />
                    创建子部门
                  </a-menu-item>
                  <a-menu-item
                    v-if="useAuth('auth:member:editOrg')"
                    :disabled="data.parentId == 0"
                    @click="editPart(data)"
                  >
                    <EditOutlined />
                    编辑部门
                  </a-menu-item>
                  <a-menu-item
                    v-if="useAuth('auth:member:delOrg')"
                    :disabled="true || data.parentId == 0"
                    @click="remove(data)"
                  >
                    <DeleteOutlined />
                    删除部门
                  </a-menu-item>
                  <a-menu-item v-if="useAuth('auth:member:upOrg')" :disabled="true || data.parentId == 0">
                    <ArrowUpOutlined />
                    上移
                  </a-menu-item>
                  <a-menu-item v-if="useAuth('auth:member:downOrg')" :disabled="true || data.parentId == 0">
                    <ArrowDownOutlined />
                    下移
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </template>
      </a-tree>
    </div>
    <div flex-1 pl-20 overflow-x-hidden mt-20>
      <a-form>
        <div grid grid-cols-4 gap-16>
          <a-form-item label="用户名">
            <a-input placeholder="请输入" v-model:value="form.nickName"></a-input>
          </a-form-item>

          <a-form-item label="姓名">
            <a-input placeholder="请输入" v-model:value="form.realName"></a-input>
          </a-form-item>

          <a-form-item label="手机号">
            <a-input placeholder="请输入" v-model:value="form.userMobile"></a-input>
          </a-form-item>

          <a-form-item>
            <a-space>
              <a-button type="primary" @click="search">搜索</a-button>
              <a-button @click="reset">重置</a-button>
            </a-space>
          </a-form-item>
        </div>
      </a-form>

      <div mb-20>
        <a-space>
          <a-button v-if="useAuth('auth:member:invite')" type="primary" @click="showAdd">添加</a-button>
          <a-button v-if="useAuth('auth:member:invite')" type="primary" @click="showInvite">邀请</a-button>
          <a-popconfirm
            v-if="useAuth('auth:member:del')"
            title="是否确认删除选中的用户?"
            @confirm="batchDelete"
            :disabled="!select.keys.length"
          >
            <a-button :disabled="!select.keys.length">批量删除</a-button>
          </a-popconfirm>
        </a-space>
      </div>

      <client-only>
        <a-table
          :loading="loading > 0"
          row-key="id"
          :columns="columns"
          :data-source="dataSource"
          :scroll="{ x: 1500 }"
          :pagination="false"
          :row-selection="{
            selectedRowKeys: select.keys,
            onChange: changeSelect,
          }"
        >
          <template #bodyCell="{ column, record, value }">
            <div v-if="column.dataIndex == 'nickName'">
              <span>{{ value }}</span>
              <span class="text-primary" v-if="record.isAdmin">（管理员）</span>
            </div>
            <a-button type="link" v-if="column.dataIndex == 'workPermit'" @click="showPermit(value)">查看</a-button>
            <template v-if="column.dataIndex == 'action'">
              <a-button :disabled="!isAdmin || record.isAdmin" type="link" @click="transformAdmin(record)">
                转移管理员
              </a-button>
              <a-button v-if="useAuth('auth:member:edit')" type="link" @click="edit(record)" :disabled="!isAdmin">
                编辑
              </a-button>
              <a-popconfirm
                v-if="useAuth('auth:member:del')"
                :title="`是否删除用户${record.nickName}`"
                @confirm="removeUser(record)"
                :disabled="record.isAdmin || !isAdmin"
              >
                <a-button type="link" :disabled="record.isAdmin || !isAdmin">删除</a-button>
              </a-popconfirm>
            </template>
          </template>
        </a-table>
      </client-only>

      <a-pagination
        my-16
        text-right
        v-model:current="page.current"
        v-model:page-size="page.size"
        show-quick-jumper
        :total="page.total"
        @change="afterAddOrg"
      />
    </div>

    <Invite :tree-map="treeData.map" ref="inviteRef" @ok="afterAddOrg" />

    <AddPart ref="partRef" @ok="afterAddOrg" />

    <Edit ref="editRef" @ok="afterAddOrg" />
    <a-image
      class="hidden!"
      :src="currentSrc"
      :preview="{
        visible,
        onVisibleChange: changeVisible,
      }"
    ></a-image>

    <a-modal v-model:open="inviteVisible" title="邀请成员" :footer="null">
      <a-textarea readonly :value="inviteText" :auto-size="true" disabled></a-textarea>
      <div class="text-right mt-4">
        <a-button type="primary" @click="handleCopy">复制</a-button>
      </div>
    </a-modal>

    <a-modal v-model:open="transformModalVisible" title="转移管理员" @ok="handleTransform">
      <div>
        <p>您将要把管理员权限转移给{{ currentTransformUser?.nickName }}，该操作不可撤销。请输入验证码完成操作。</p>
        <msg-input storageKey="transform-admin" v-model:value="sms" :sendApi="sendMsg"></msg-input>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="tsx">
import { ComResult, ComStatus } from '~/api/mall-platform/types'
import { getMemberList, getOrgList } from '~/api/member'
import { Pagination } from '~/api/shop/type'
import Invite from './invite.vue'
import AddPart from './AddPart.vue'
import Edit from './Edit.vue'
import { Member } from '~/api/member/type'
import { userStore } from '~/store/user'
import { companyStore } from '~/store/company'
type State = {
  nickName: string
  realName: string
  userMobile: string
}

const route = useRoute()
const loading = ref(0)
const merchantId = computed(() => companyStore().company.shopCompanyId)
const form = ref({} as State)
const user = userStore()

const hasAction = computed(() => {
  const permissions = [
    'auth:member:addOrg',
    'auth:member:editOrg',
    'auth:member:delOrg',
    'auth:member:upOrg',
    'auth:member:downOrg',
  ]
  return permissions.some((item) => useAuth(item))
})

const page = reactive({
  current: 1,
  size: 10,
  total: 0,
})

const dataSource = ref<Obj[]>([])

const fetchMember = async () => {
  loading.value++
  const res = await getMemberList({
    ...form.value,
    ...pick(page, ['current', 'size']),
    orgId: orgs.value[0],
    merchantId: merchantId.value,
    auditStatus: ComStatus.apply,
    auditResult: ComResult.pass,
  })
  loading.value--
  try {
    if (res.success) {
      const ret = JSON.parse(res.data) as Pagination
      page.total = ret.total
      dataSource.value = ret.records
    }
  } catch (err) {
    console.error(err)
  }
}

const reset = () => {
  form.value = {} as State
}

const treeData = ref<Obj[]>([])
const treeLoading = ref(false)
const fetchOrg = async () => {
  treeLoading.value = true
  const res = await getOrgList({
    merchantId: merchantId.value,
    size: 9999,
  })
  treeLoading.value = false
  try {
    if (res.success) {
      const ret = JSON.parse(res.data) as Pagination<any>
      treeData.value = arr_to_tree(ret.records, 'id', 'parentId')
    }
  } catch (err) {
    console.error(err)
  }
}
const isAdmin = ref(false)

const fetchPermission = async () => {
  const res = await plat_http.get('/shop/merchantUser/getOneByCond', {
    userMobile: user.user.userMobile,
    merchantId: merchantId.value,
    auditResult: 1,
    auditStatus: 1,
  })

  try {
    if (res.success) {
      const ret = JSON.parse(res.data)
      isAdmin.value = ret.isAdmin == 1
    }
  } catch (err) {
    console.error(err)
  }
}

onMounted(async () => {
  await fetchOrg()
  Promise.all([fetchMember(), fetchPermission()])
})

const columns = ref([
  {
    title: '用户名',
    dataIndex: 'nickName',
  },
  {
    title: '姓名',
    dataIndex: 'realName',
  },
  {
    title: '手机号',
    dataIndex: 'userMobile',
  },
  {
    title: '身份',
    dataIndex: 'identity',
  },
  {
    title: '公司',
    dataIndex: 'company',
  },
  {
    title: '职位',
    dataIndex: 'position',
  },
  {
    title: '工作证照',
    dataIndex: 'workPermit',
  },
  {
    title: '加入时间',
    dataIndex: 'joinTime',
    width: 200,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 280,
  },
] as any)

const orgs = ref([])
const inviteRef = ref<InstanceType<typeof Invite>>()
const showAdd = () => {
  inviteRef.value?.show()
}

const partRef = ref<InstanceType<typeof AddPart>>()
const addPart = (data) => {
  partRef.value?.add(
    {
      parentId: data.id,
      name: '',
    },
    treeData.value,
    merchantId.value,
  )
}

const editPart = (data) => {
  partRef.value?.edit(
    {
      parentId: data.parentId,
      name: data.name,
      id: data.id,
    },
    treeData.value,
    merchantId.value,
  )
}

// 删除部门
const remove = async (data) => {
  const res = await plat_http.delete('/shop/org/' + data.id)
  try {
    if (res.success) {
      message.success('删除成功')
      fetchMember()
      fetchOrg()
    }
  } catch (err) {
    console.error(err)
  }
}

const afterAddOrg = () => {
  fetchOrg()
  fetchMember()
}

const search = () => {
  fetchMember()
}

const editRef = ref<InstanceType<typeof Edit>>()
const edit = (user) => {
  editRef.value?.show(user)
}

const batchDelete = async () => {
  const res = await plat_http.delete('/shop/merchantUser/deleteBatch', {
    params: JSON.stringify(select.rows.map((item) => ({ id: item.id, orgId: item.orgId }))),
  })

  try {
    if (res.success) {
      message.success('删除成功')
      afterAddOrg()
    }
  } catch (err) {
    console.error(err)
  }
}

// 删除用户
const removeUser = async (user) => {
  let _user = user as Member
  const res = await plat_http.delete(`/shop/merchantUser?id=${_user.id}&orgId=${_user.orgId}`)
  try {
    if (res.success) {
      message.success('删除成功')
      afterAddOrg()
    }
  } catch (err) {
    console.error(err)
  }
}

const select = reactive({
  keys: [] as string[],
  rows: [] as Obj[],
})
const changeSelect = (keys, rows) => {
  select.keys = keys
  select.rows = rows
}

const currentSrc = ref('')
const visible = ref(false)
const changeVisible = (value) => {
  visible.value = value
}
const showPermit = (val) => {
  currentSrc.value = val
  visible.value = true
}

const inviteVisible = ref(false)
const inviteText = ref('')
const yanxuan = useRuntimeConfig().public.baseUrl.VITE_YANXUAN
const showInvite = async () => {
  const [err, res] = await try_http('/mall/p/inviter-cipher/my', {
    query: {
      merchantId: merchantId.value,
    },
  })
  if (!err) {
    console.log('res', res.data)
    inviteText.value = `${user.user.realName || user.user.nickName}邀请您加入研选工场，并成为${companyStore().company.merchantName}的成员
邀请链接地址: ${yanxuan}/register?inviteCode=${res.data.cipherText}
`
    inviteVisible.value = true
  }
}

const copy = useCopy()
const handleCopy = () => {
  copy(inviteText.value)
  message.success('复制成功')
}

const sendMsg = async () => {
  const [err] = await try_http('/mall/p/shop/sendCode', {
    method: 'post',
    body: {
      mobile: user.user.userMobile,
      eventType: 'VALID',
    },
  })
  return !err
}

const logout = useLogout()
const sms = ref('')
const transformModalVisible = ref(false)
const currentTransformUser = ref<any>(null)

const transformAdmin = (row) => {
  sms.value = ''
  currentTransformUser.value = row
  transformModalVisible.value = true
}

const handleTransform = async () => {
  if (!sms.value) {
    message.error('请输入验证码')
    return
  }
  const [err] = await try_http(`/mall/p/merchant-user/manager-shift`, {
    method: 'put',
    body: {
      userId: currentTransformUser.value.id,
      verifyCode: sms.value,
    },
  })
  if (!err) {
    message.success('转移成功,即将退出登录')
    transformModalVisible.value = false
    setTimeout(() => {
      logout()
    }, 1000)
  }
}
</script>

<style lang="less" scoped>
:deep(.ant-tree-treenode) {
  padding: 0 !important;
  .ant-tree-node-content-wrapper {
    padding-right: 12px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow-x: hidden;
    background: none;
  }
}
:deep(.ant-tree-treenode-selected) {
  @apply bg-primary! text-white!;
}
</style>
