<template>
  <a-form
    ref="formRef"
    :rules="rules"
    :model="formState"
    :disabled="!formEditable"
    :label-col="{ span: 24 }"
    autocomplete="off"
  >
    <a-form-item name="merchantName" label="企业名称">
      <Bussiness
        v-model:value="formState.merchantName"
        @change="handleBussinessChange"
        :disabled="isSupplier || formType == 2"
      />
    </a-form-item>
    <a-form-item name="creditCode" label="统一社会信用代码">
      <a-input
        :disabled="formType === 2 || isSupplier"
        v-model:value="formState.creditCode"
        size="large"
        :maxlength="20"
      />
    </a-form-item>
    <a-form-item name="residence" label="住所">
      <a-input v-model:value="formState.residence" size="large" :maxlength="50" />
    </a-form-item>
    <a-form-item name="representative" label="法定代表人">
      <a-input v-model:value="formState.representative" size="large" :maxlength="20" />
    </a-form-item>
    <a-form-item name="capital" label="注册资本(万元)">
      <a-input-number
        v-model:value="formState.capital"
        size="large"
        type="number"
        prefix="¥"
        :min="0"
        :max="99999999"
        style="width: 100%"
      />
    </a-form-item>
    <a-form-item name="foundTime" label="成立日期">
      <a-date-picker
        :disabled="formType === 2"
        v-model:value="formState.foundTime"
        valueFormat="YYYY-MM-DD HH:mm:ss"
        size="large"
        style="width: 100%"
      />
    </a-form-item>
    <a-form-item name="startTime" label="营业期限">
      <a-date-picker
        v-model:value="formState.startTime"
        valueFormat="YYYY-MM-DD HH:mm:ss"
        size="large"
        style="width: 45%"
      />
      <span class="text-center" style="display: inline-block; width: 10%">~</span>
      <a-date-picker
        v-model:value="formState.endTime"
        valueFormat="YYYY-MM-DD HH:mm:ss"
        placeholder="无固定期限"
        size="large"
        style="width: 45%"
      />
    </a-form-item>
    <a-form-item name="businessScope" label="经营范围">
      <a-textarea v-model:value="formState.businessScope" :maxlength="500" show-count />
    </a-form-item>
    <a-form-item class="text-center" name="businessLicense" label="营业执照电子版">
      <img-upload
        name="businessLicense"
        :value="formState.businessLicense"
        :before-upload="beforeUpload"
        @change="(v) => (formState.businessLicense = v)"
      />
      <div color-subText>限2MB以内的jpg、jpeg、png文件</div>
    </a-form-item>
    <a-form-item class="text-center" name="identityCardFront" label="法人身份证(人像面)">
      <img-upload
        name="identityCardFront"
        :value="formState.identityCardFront"
        :before-upload="beforeUpload"
        @change="(v) => (formState.identityCardFront = v)"
      />
      <div color-subText>限2MB以内的jpg、jpeg、png文件</div>
    </a-form-item>
    <a-form-item class="text-center" name="identityCardLater" label="法人身份证(国徽面)">
      <img-upload
        name="identityCardLater"
        :value="formState.identityCardLater"
        :before-upload="beforeUpload"
        @change="(v) => (formState.identityCardLater = v)"
      />
      <div color-subText>限2MB以内的jpg、jpeg、png文件</div>
    </a-form-item>
  </a-form>
</template>

<script setup>
import dayjs from 'dayjs'
import { getShopCompanyByCreditCode, companyUpgradation } from '~/api/mall-platform/index'

const formRef = ref(null)
const formType = ref(1) // 1-创建，2-更新
const isSupplier = ref(false) // 是否是开通供应商
const formState = ref({
  merchantName: '',
  creditCode: '',
  residence: '',
  representative: '',
  capital: '',
  foundTime: '',
  startTime: '',
  endTime: '',
  businessScope: '',
  businessLicense: '',
  identityCardFront: '',
  identityCardLater: '',
})
const formEditable = ref(true)

const vaildCreditCode = async (rule, value) => {
  if (!value) return Promise.resolve()
  const reg = /^(([0-9A-Za-z]{15})|([0-9A-Za-z]{18})|([0-9A-Za-z]{20}))$/
  if (!reg.test(value)) {
    return Promise.reject('请输入正确的统一社会信用代码')
  }
  if (!isSupplier.value && formType.value != 2) {
    const res = await getShopCompanyByCreditCode({
      creditCode: value,
      merchantId: formState.value.shopCompanyId || 0,
    })
    // await http_mall(res, () => {})
    if (res.data == 'true') {
      return Promise.reject('该企业已入驻,请联系企业管理员开通服务')
      // 91320594MA26AEATX5
    }
    return Promise.resolve()
  }
  return Promise.resolve()
}

const beforeUpload = (file) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
  if (!isJpgOrPng) {
    message.error('您只能上传jpg、jpeg和png格式的图片')
  }
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    message.error('图片大小不能超过2MB')
  }
  return isJpgOrPng && isLt2M
}

const rules = ref({
  merchantName: [
    { required: true, message: '请输入企业名称', trigger: 'blur' },
    { min: 2, max: 50, message: '企业名称长度在2-50个字符之间', trigger: 'blur' },
  ],
  creditCode: [
    { required: true, message: '请输入统一社会信用代码', trigger: 'blur' },
    { validator: vaildCreditCode, trigger: 'blur' },
  ],
  representative: [
    { required: true, message: '请输入法定代表人姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '法定代表人姓名长度在2-20个字符之间', trigger: 'blur' },
  ],
  // businessLicense: [{ required: true, message: '请上传营业执照', trigger: 'blur' }],
  // identityCardFront: [{ required: true, message: '请上传法人身份证正面照片', trigger: 'blur' }],
  // identityCardLater: [{ required: true, message: '请上传法人身份证反面照片', trigger: 'blur' }],
})

const init = (type, data, editable, isSupplierMode = false) => {
  formType.value = type
  isSupplier.value = isSupplierMode
  if (!!data) {
    formState.value = data
  }
  formEditable.value = editable
}

const handleBussinessChange = (bussiness) => {
  formState.value.creditCode = bussiness.creditCode
  formState.value.representative = bussiness.legalPersonName
  if (!bussiness.regCapital.includes('美元')) {
    formState.value.capital = bussiness.regCapital
  }
  formState.value.foundTime = dayjs(bussiness.estiblishTime).format('YYYY-MM-DD HH:mm:ss')
}
defineExpose({ init, formRef, formState })
</script>

<style scoped></style>
