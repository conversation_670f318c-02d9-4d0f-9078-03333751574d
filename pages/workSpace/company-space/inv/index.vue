<template>
  <div>
    <form-wrapper>
      <a-form layout="inline">
        <a-form-item label="对账单号">
          <a-input v-model:value="form.orderNo" placeholder="请输入对账单号"></a-input>
        </a-form-item>

        <a-form-item label="对账状态">
          <a-select v-model:value="form.status" placeholder="请输入对账状态" class="w-180px!">
            <a-select-option v-for="statu in Object.keys(statusMap)" :key="statu" :value="statu">
              {{ getStatusText(statu) }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="对账周期">
          <range-picker v-model="form.periodFilter"></range-picker>
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button type="primary" @click="search">查询</a-button>
            <a-button @click="reset">重置</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </form-wrapper>

    <a-table
      @resize-column="colResize"
      :data-source="data"
      :columns="columns"
      :pagination="pagination"
      @change="pageChange"
      :scroll="{ x: '100%' }"
      :loading="loading"
      size="small"
      bordered
    >
      <template #bodyCell="{ record, column }">
        <action-button
          v-if="column.dataIndex == 'orderNumber'"
          @click="
            navigateTo({
              path: '/workSpace/company-space/inv-detail',
              query: {
                orderNumber: record.orderNumber,
              },
            })
          "
        >
          {{ record.orderNumber }}
        </action-button>

        <a-tag v-if="column.dataIndex == 'status'" :color="getStatusColor(record.status)">
          {{ getStatusText(record.status) }}
        </a-tag>

        <a-tag v-if="column.dataIndex == 'invoiceStatus'" :color="getInvoiceColor(record.invoiceStatus)">
          {{ getInvoiceText(record.invoiceStatus) }}
        </a-tag>

        <div v-if="column.dataIndex == 'action'">
          <action-button v-if="canInv(record.status)" v-loading-click="() => handleInv(record)">对账</action-button>
          <!-- <action-button v-if="canPay(record.status)">付款</action-button> -->
          <action-button
            v-if="canApply(record.status, record.invoiceStatus)"
            v-loading-click="() => handleApply([record.orderId])"
          >
            申请开票
          </action-button>
          <action-button v-if="canExport()" v-loading-click="() => handleExport(record.orderNumber)">
            导出
          </action-button>
          <!-- <action-button v-if="canDownload(record.status, record.invoiceStatus)">下载发票</action-button> -->
        </div>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { useInv } from './useInv.js'
onMounted(() => {
  fetchData()
})
const form = ref({})
const data = ref([])
const loading = ref(false)

const {
  statusMap,
  getStatusText,
  getStatusColor,
  getInvoiceText,
  getInvoiceColor,
  canApply,
  canDownload,
  canInv,
  canPay,
  apply,
  inv,
  handleExport,
  canExport,
} = useInv()

const fetchData = async () => {
  loading.value = true
  const [err, res] = await try_http('/mall/p/reconciliation-statement/page', {
    method: 'post',
    body: {
      size: page.size,
      current: page.current,
      ...form.value,
    },
  })
  loading.value = false
  if (!err) {
    data.value = res.data.records
    page.total = res.data.total
  }
}
const { pagination, pageChange, page } = usePage(fetchData)

const search = () => {
  page.current = 1
  fetchData()
}
const reset = () => {
  form.value = {}
}

const columns = ref([
  {
    title: '对账单号',
    width: 200,
    minWidth: 200,
    dataIndex: 'orderNumber',
    resizable: true,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
    customRender({ text }) {
      return formatTime(text)
    },
    resizable: true,
  },
  {
    title: '对账周期',
    width: 200,
    resizable: true,
    customRender({ record }) {
      const { beginDate, endDate } = record
      if (beginDate && endDate) {
        return `${formatTime(beginDate, 'YYYY-MM-DD')}至${formatTime(endDate, 'YYYY-MM-DD')}`
      }
      return '-'
    },
  },
  {
    title: '总金额',
    dataIndex: 'totalAmount',
    width: 120,
    customRender({ text }) {
      return getPrice(text)
    },
    resizable: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 120,
    resizable: true,
  },
  {
    title: '开票状态',
    dataIndex: 'invoiceStatus',
    width: 120,
    resizable: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 200,
  },
])

const handleInv = async (record) => {
  const [err] = await inv(record.orderNumber)
  if (!err) {
    message.success('操作成功')
    fetchData()
  }
}

const handleApply = async (ids) => {
  const [err] = await apply(ids)
  if (!err) {
    message.success('操作成功')
    fetchData()
  }
}
</script>
