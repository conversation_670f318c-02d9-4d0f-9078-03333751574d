import fileDownload from 'js-file-download'
import { companyStore } from '~/store/company'

export const useInv = () => {
  const statusMap = {
    0: {
      text: '生成中',
      color: 'orange',
    },
    10: {
      text: '待对账',
      color: 'blue',
    },
    20: {
      text: '已锁定',
      color: 'green',
    },
    30: {
      text: '已结算',
      color: 'purple',
    },
    35: {
      text: '部分结算',
      color: 'purple',
    },
  }

  const invoiceStatusMap = {
    0: {
      text: '未开票',
      color: 'default',
    },
    1: {
      text: '已开票',
      color: 'green',
    },
    2: {
      text: '开票中',
      color: 'orange',
    },
  }

  const paymentMap = {
    1: {
      text: '预付款',
      color: 'volcano',
    },
    2: {
      text: '账期结算',
      color: 'geekblue',
    },
  }

  const getStatusText = (v) => statusMap[v]?.text || ''
  const getStatusColor = (v) => statusMap[v]?.color

  const getInvoiceText = (v) => invoiceStatusMap[v]?.text || ''
  const getInvoiceColor = (v) => invoiceStatusMap[v]?.color
  const getPaymentText = (v) => paymentMap[v]?.text || ''
  const getPaymentColor = (v) => paymentMap[v]?.color

  const getCycle = (row) => {
    const { beginDate, endDate } = row
    if (beginDate && endDate) {
      return `${formatTime(beginDate)}至${formatTime(endDate)}`
    }
    return ''
  }

  const canInv = (v) => eq(v, 10) && useAuth('inv:order:inv')
  const canExport = () => useAuth('inv:order:export')
  const canPay = (v) => eq(v, 20)
  const canApply = (v, i) => eq(v, 20, 30) && eq(i, 0) && useAuth('inv:order:apply')
  const canDownload = (v, i) => eq(v, 20, 30) && eq(i, 1)

  const inv = (orderNo) => {
    return new Promise((resolve, reject) => {
      Modal.confirm({
        title: '提示',
        content: '对账操作将锁定对账单并生成付款单。此操作不可撤销，是否继续？',
        onOk: async () => {
          const res = await try_http(`/mall/p/reconciliation-statement/confirm/${orderNo}`, {
            method: 'put',
          })
          resolve(res)
        },
        onCancel() {
          reject()
        },
      })
    })
  }

  const company = companyStore()
  const apply = useDebounceFn(async (ids) => {
    const [err, res] = await try_http('/mall/shop/invoice/page', {
      type: 0,
      merchantId: company.company.shopCompanyId,
    })
    if (!err) {
      const list = res.data.records
      if (!list.length) {
        Modal.confirm({
          title: '提示',
          content: '您的企业未设置开票信息，请前往填写',
          onOk() {
            navigateTo('/workSpace/company-space/invoice-info')
          },
        })
      } else {
        return await try_http('/mall/p/reconciliation-statement/invoice', {
          method: 'put',
          body: ids,
        })
      }
    }
  }, 200)

  const handleExport = useDebounceFn(async (orderNo) => {
    message.success('导出中请稍等')
    const [err, res] = await try_http(`/mall/p/reconciliation-statement/export/${orderNo}`, {
      method: 'post',
      responseType: 'blob',
    })
    if (err) return
    fileDownload(res, `对账单-${orderNo}.xlsx`)
  }, 200)

  return {
    statusMap,
    getStatusText,
    getStatusColor,
    invoiceStatusMap,
    getInvoiceText,
    getInvoiceColor,
    paymentMap,
    getPaymentText,
    getPaymentColor,
    canInv,
    canPay,
    canApply,
    canDownload,
    getCycle,

    inv,
    apply,
    handleExport,
    canExport,
  }
}
