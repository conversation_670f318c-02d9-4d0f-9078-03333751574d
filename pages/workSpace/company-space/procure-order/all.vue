<template>
  <div bg-white flex-1 px-16 py-8 text-14>
    <div bg="#f2f6fc" h-40 flex items-center px-10>
      <div font-bold>采购编号：</div>
      <div min-w-150 mr-30>{{ order.platformOrderNumber }}</div>
      <div font-bold>下单时间：</div>
      <div min-w-200 mr-30>{{ order.createTime }}</div>
      <div font-bold>子订单数量：</div>
      <div>{{ order.orderCount }}</div>
    </div>
    <a-tabs @change="changeShop" v-model:active-key="current">
      <a-tab-pane v-for="item in orderList" :key="item.orderNumber" :tab="item.shopName"></a-tab-pane>
    </a-tabs>
    <Order v-if="current" :order-no="current">
      <template #delivery>
        <Delivery :platform-order-number="platformOrderNumber" />
      </template>
    </Order>
  </div>
</template>

<script setup>
import { companyStore } from '~/store/company'
import Order from './[orderNo].vue'

definePageMeta({
  name: '全部订单',
})

const route = useRoute()
const platformOrderNumber = computed(() => route.query.platformOrderNumber)

const company = companyStore()
const orderList = ref([])

const changeShop = (val) => {
  console.log('%c Line:32 🥛 val', 'color:#24ECBA', val)
}

const current = ref()

const order = computed(() => {
  if (orderList.value.length) {
    const [item] = orderList.value
    return item
  } else {
    return {}
  }
})

const fetchOrder = async () => {
  const res = await http('/mall/p/myOrder/myOrderSearch', {
    params: {
      size: 10,
      merchantId: company.company.shopCompanyId,
      status: 0,
      orderType: 0,
      ordermold: 0,
      platformOrderNumber: platformOrderNumber.value,
    },
  })
  useMall(res, () => {
    console.log('%c Line:23 🥛 res', 'color:#207B8D', res)
    orderList.value = res.data.records || []
    if (orderList.value) {
      current.value = orderList.value[0].orderNumber
    }
  })
}

onMounted(() => {
  fetchOrder()
})
</script>
