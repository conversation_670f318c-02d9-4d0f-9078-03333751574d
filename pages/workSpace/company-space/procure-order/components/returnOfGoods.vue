<template>
  <a-modal
    :width="800"
    title="申请退款"
    :maskClosable="false"
    v-model:open="open"
    @ok="confirm"
  >
    <div min-h-500>
      <a-form mt-10 :label-col="{ flex: '100px' }" :rules="rules" :model="form" ref="formRef">
        <a-form-item label="退款商品">
          <div 
            v-for="(item, index) in returnGoods" 
            :key="item.skuId"
            flex
          >
            <hm-img
              style="width: 80px; height: 80px; margin: 0 3px"
              :src="item.pic"
            />
            <!-- <img w-80 h-80 :src="convertImage(item.pic)" /> -->
            <div flex flex-col flex-1 justify-around overflow-hidden>
              <span class="text-clamp">{{ item.prodName }}</span>
              <span class="text-ellipsis">{{ item.skuName }} X{{ item.prodCount || 0 }}</span>
            </div>
          </div>
        </a-form-item>

        <a-form-item label="退款方式" name="applyType">
          <a-radio-group v-model:value="form.applyType" @change="() => { form.buyerReason = null }">
            <a-radio :value="1">仅退款</a-radio>
            <a-radio :value="2" :disabled="![3, 5].includes(order.status)">退货退款</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="退款原因" name="buyerReason">
          <a-select
            style="width: 200px"
            v-model:value="form.buyerReason"
            placeholder="退款原因"
            allowClear
          >
            <a-select-option 
              v-for="(item, index) in refundReasonArray" 
              :key="item" 
              :value="index"
              :disabled="form.applyType == 1 && [2, 3, 4, 5].includes(index)"
            >
              {{ item }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="退款数量" name="goodsNum" v-if="refundType == 2 && form.applyType == 2">
          <a-input-number
            style="width: 200px"
            v-model:value="form.goodsNum"
            :max="maxReturnConut"
            :min="1"
            @change="() => {
              form.actualTotal = (form.goodsNum * goodPrice).toFixed(2)
            }"
          />
        </a-form-item>

        <a-form-item label="退款金额" name="actualTotal">
          <a-input-number
            style="width: 200px"
            v-model:value="form.actualTotal"
            :max="maxReturnMoney"
            :min="0"
            disabled
          />
          <span sub-text pl-10 style="vertical-align: -webkit-baseline-middle">最多可退{{ getPrice(maxReturnMoney) }}</span>
        </a-form-item>

        <a-form-item label="手机号码" name="buyerMobile">
          <a-input
            style="width: 200px"
            v-model:value="form.buyerMobile"
          />
        </a-form-item>

        <a-form-item label="退款说明" name="buyerDesc">
          <a-textarea
            v-model:value="form.buyerDesc"
            :rows="5"
            :maxlength="200" 
            showCount
          />
        </a-form-item>

        <a-form-item label="退款凭证" name="photoFiles">
          <imgs-upload
            :limit="5"
            :value="form.photoFiles"
            :beforeUpload="beforeUpload"
            @change="(v) => form.photoFiles = v"
          />
          <div sub-text>最多上传五张凭证,支持jpg,png,每张大小不超过2M</div>
        </a-form-item>

      </a-form>
    </div>
  </a-modal>
</template>

<script setup>
import { userStore } from '~/store/user'

const useUser = userStore()

const emits = defineEmits(['ok'])

const refundReasonArray = computed(() => {
  return [
    '拍错/多拍/不喜欢',
    '协商一致退款',
    '商品破损/少件',
    '商品与描述不符',
    '商家发错货',
    '质量问题',
    '其他'
  ]
})

const beforeUpload = (file, list) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
  if (!isJpgOrPng) {
    message.error('您只能上传jpg、jpeg和png格式的图片')
  }
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    message.error('图片大小不能超过2MB')
  }
  return isJpgOrPng && isLt2M
}

const isTel = (v) => {
  // 400电话的正则
  const tel1 = /^400[0-9]{7}$/
  // 800电话正则
  const tel2 = /^800[0-9]{7}$/
  // 手机号码正则
  const tel3 = /^1[3-9]([0-9]{9})$/
  // 座机号码正则
  const tel4 = /^0\d{2,3}-\d{7,8}$/
  return (tel1.test(v) || tel2.test(v) || tel3.test(v) || tel4.test(v))
}

const validateTel = async (rule, value) => {
  if (value) {
    if (isTel(value)) {
      return Promise.resolve()
    } else {
      return Promise.reject('请输入正确的电话')
    }
  } else {
    return Promise.resolve()
  }
}

const rules = ref({
  applyType: [
    { required: true, message: '退款方式不能为空', trigger: 'blur' }
  ],
  buyerReason: [
    { required: true, message: '退款原因不能为空', trigger: 'blur' }
  ],
  actualTotal: [
    { required: true, message: '退款金额不能为空', trigger: 'blur' }
  ],
  buyerMobile: [
    { required: true, message: '手机号不能为空', trigger: 'blur' },
    { validator: validateTel, trigger: 'blur' }
  ],
  buyerDesc: [
    { required: true, message: '退款说明不能为空', trigger: 'blur' }
  ]
})
const form = ref({})
const open = ref(false)
const returnGoods = ref([])
const order = ref({})
const refundType = ref(null)
const maxReturnMoney = ref(0)
const maxReturnConut = ref(0)
const goodPrice = ref(0) // 商品单价
const init = (data, type, orderInfo) => {
  open.value = true
  returnGoods.value = data
  order.value = orderInfo
  refundType.value = type // 1 整单 2 单个

  nextTick(() => {
    formRef.value?.resetFields()
    form.value = {
      applyType: 1,
      buyerReason: null,
      refundType: type,
      actualTotal: type == 1 ? orderInfo.canRefundAmount : data[0]?.actualTotal,
      buyerMobile: useUser.user.userMobile,
      buyerDesc: '',
      photoFiles: '',
      giveawayItemIds: [],
      orderNumber: orderInfo.orderNumber,
      orderItemId: type == 1 ? undefined : data[0]?.orderItemId,
      goodsNum: type == 1 ? undefined : data[0]?.prodCount
    }
    maxReturnMoney.value = Number(form.value.actualTotal || 0).toFixed(2)
    maxReturnConut.value = form.value.goodsNum || 0
    goodPrice.value = type == 1 ? 0 : data[0]?.price
  })
}

const formRef = ref(null)
const confirm = () => {
  formRef.value.validate().then(async () => {
    http(`/mall/p/orderRefund/apply`, {
      method: 'post',
      body: {
        ...form.value,
        refundAmount: form.value.actualTotal || 0,      
        isReceiver: form.value.applyType == 1 ? 0 : 1,
      }
    }).then(res => {
      useMall(res, () => {
        message.success('操作成功')
        open.value = false
        emits('ok')
      })
    })
  })
}

defineExpose({
  init
})
</script>

<style lang="less" scoped>
.text-clamp{
  width: 100%;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>
