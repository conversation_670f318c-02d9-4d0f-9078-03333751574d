<template>
  <div flex-1 bg-white px-20 pb-60 style="position: relative">
    <div class="selections">
      <div flex flex-wrap>
        <div w-300 p-10 line-height-30 text-center style="border-right: 1px solid #ccc">
          <div>订单号: {{ orderInfo.orderNumber }}</div>
          <div text-20 font-bold style="color: #f94c30">
            {{ statusList[orderInfo.status] }}
          </div>
          <div v-if="orderInfo.status == 1">
            <span pr-10>剩余支付时间</span>
            <count-down
              style="display: inline-block; color: red"
              :allTime="getRemainTime(orderInfo.createTime)"
              separator="时,分,秒"
            />
          </div>
          <div v-if="orderInfo.status == 1" p-10>
            <a-popover>
              <template #title>
                <span>提示</span>
              </template>
              <template #content>
                <span>完成线下付款后,研选客服将为您更新订单进度</span>
              </template>
              <a-button type="primary" mr-10 @click="onPay" disabled>立即支付</a-button>
            </a-popover>
            <a-button @click="onCancelOrder(orderInfo.orderNumber)">取消订单</a-button>
          </div>
          <div v-if="orderInfo.status == 3" p-10>
            <a-button type="primary" @click="onConfirmReceive(orderInfo.orderNumber)">确认收货</a-button>
          </div>
        </div>

        <div class="process-box">
          <div class="tip">付款时请您仔细查看订单细节以及配送信息,以确保您能及时收到货物。</div>
          <div mt-20 class="process">
            <div
              v-for="(item, index) in processList"
              :key="index"
              :class="['item', item.isActive(orderInfo) ? 'active' : '']"
            >
              <img w-60 h-60 :src="item.isActive(orderInfo) ? item.activeIcon : item.icon" />
              <div class="text">
                {{ item.label }}
              </div>
              <div class="time">
                {{ orderInfo[item.field] }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="selections" flex justify-around>
      <div flex-1 overflow-hidden>
        <div class="title">收货信息</div>
        <div flex flex-col p-10 line-height-30>
          <div flex>
            <span w-80>联系人:</span>
            <span>{{ userAddrDto.receiver }}</span>
          </div>
          <div flex>
            <span w-80>所在地区:</span>
            <span>{{ (userAddrDto.province || '') + (userAddrDto.city || '') + (userAddrDto.area || '') }}</span>
          </div>
          <div flex>
            <span w-80>详细地址:</span>
            <span flex-1 class="text-ellipsis" :title="userAddrDto.addr">{{ userAddrDto.addr }}</span>
          </div>
          <div flex>
            <span w-80>手机号:</span>
            <span>{{ userAddrDto.mobile }}</span>
          </div>
        </div>
      </div>

      <div flex-1 overflow-hidden>
        <div class="title">配送信息</div>
        <div flex flex-col p-10 line-height-30>
          <div flex>
            <span w-80>发货信息:</span>
            <span>{{ ['线上发货', '快递发货', '自提', '无需快递', '同城配送'][orderInfo.dvyType] }}</span>
          </div>
        </div>
      </div>

      <div flex-1 overflow-hidden>
        <div class="title">其他信息</div>
        <div flex flex-col p-10 line-height-30>
          <div flex>
            <span w-80>询价单号:</span>
            <span flex-1>{{ orderInfo.inquiryOrderNo || '--' }}</span>
          </div>
          <div flex>
            <span w-80>项目编号:</span>
            <span flex-1>{{ orderInfo.projectNo || '--' }}</span>
          </div>
          <div flex>
            <span w-80>订单备注:</span>
            <span flex-1>{{ orderInfo.remarks || '--' }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="selections">
      <div class="title">商品明细</div>
      <div p-10>
        <div flex items-center text-16 mt-20>
          <shop-outlined color="#f94c30" text-20 pr-10 />
          <span font-bold>{{ orderInfo.shopName }}</span>
          <!-- <a-tag v-if="true" color="#f94c30" ml-10>联营</a-tag> -->
        </div>
        <a-table :columns="columns" row-key="skuId" size="middle" :data-source="goods" :pagination="false">
          <template #bodyCell="{ column, record, value }">
            <span v-if="column.dataIndex == 'price'">{{ getPrice(value) }}/个</span>
            <span v-if="column.dataIndex == 'totalPrice'">
              {{ getPrice(value) }}
            </span>
            <span v-if="column.dataIndex == 'action'">
              <a-button
                v-if="record.refundSn"
                type="link"
                @click="
                  navigateTo({
                    path: '/workSpace/company-space/return/' + record.refundSn,
                  })
                "
              >
                查看退款
              </a-button>
              <a-button v-if="canReturn && !record.refundSn" type="link" @click="applyForReturn([record], 2)">
                申请退款
              </a-button>
            </span>
          </template>
        </a-table>
      </div>
    </div>

    <div class="selections">
      <div class="title">物流信息</div>
      <div p-10 id="_orderNo">
        <slot name="delivery" />
        <Delivery :orderNo="route.params.orderNo" v-if="route.params.orderNo && !$slots.delivery" />
      </div>
    </div>

    <div flex flex-col items-end text-14>
      <!-- 按钮 -->
      <div flex p-10>
        <a-button v-if="canAllReturn" type="primary" @click="applyForReturn(goods, 1)">整单退款</a-button>
      </div>
      <div flex p-10 text-right>
        <span w-65>商品总额:</span>
        <span min-w-100>{{ getPrice(totalPrice) }}</span>
      </div>
      <div flex p-10 text-right>
        <span w-65>商品运费:</span>
        <span min-w-100>{{ getPrice(transfee) }}</span>
      </div>
      <div flex p-10 text-right>
        <span w-65>应付总额:</span>
        <span min-w-100 style="color: red">{{ getPrice(totalPrice + transfee) }}</span>
      </div>
    </div>

    <return-of-goods ref="returnOfGoodsRef" @ok="getOrderDetail" />
  </div>
</template>

<script setup>
import process1 from '~/assets/images/svg/process1.svg'
import process1_1 from '~/assets/images/svg/process1_1.svg'
import process2 from '~/assets/images/svg/process2.svg'
import process2_1 from '~/assets/images/svg/process2_1.svg'
import process3 from '~/assets/images/svg/process3.svg'
import process3_1 from '~/assets/images/svg/process3_1.svg'
import process4 from '~/assets/images/svg/process4.svg'
import process4_1 from '~/assets/images/svg/process4_1.svg'
import process5 from '~/assets/images/svg/process5.svg'
import process5_1 from '~/assets/images/svg/process5_1.svg'
import returnOfGoods from './components/returnOfGoods.vue'
import { companyStore } from '~/store/company'
import { statusList } from '~/api/order'

definePageMeta({
  name: '订单详情',
})
const route = useRoute()
const router = useRouter()

const canReturn = computed(() => {
  const { canRefund } = orderInfo.value
  return canRefund
})

const canAllReturn = computed(() => {
  const { canAllRefund } = orderInfo.value
  return canAllRefund
})

const getRemainTime = (beg) => {
  const allTime = 86400
  const passedTime = (new Date().getTime() - new Date(beg).getTime()) / 1000
  const ret = allTime - passedTime
  if (ret <= 0) {
    return 0
  }
  return ret
}

const columns = [
  {
    title: '商品名称',
    dataIndex: 'prodName',
    ellipsis: true,
  },
  {
    title: '型号',
    dataIndex: 'partyCode',
    ellipsis: true,
  },
  {
    title: '数量',
    dataIndex: 'prodCount',
    width: 150,
    ellipsis: true,
  },
  {
    title: '成交单价',
    dataIndex: 'price',
    width: 150,
    ellipsis: true,
  },
  {
    title: '小计',
    dataIndex: 'totalPrice',
    width: 150,
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 150,
    ellipsis: true,
  },
]

const processList = [
  {
    label: '提交订单',
    field: 'createTime',
    icon: process1_1,
    activeIcon: process1,
    isActive: ({ status }) => status >= 1 && status !== 6,
  },
  {
    label: '买家付款',
    field: 'payTime',
    icon: process2_1,
    activeIcon: process2,
    isActive: ({ status, payTime }) => status >= 2 && status !== 6 && payTime !== null,
  },
  {
    label: '商家发货',
    field: 'dvyTime',
    icon: process3_1,
    activeIcon: process3,
    isActive: ({ status, dvyTime }) => status >= 3 && status !== 6 && dvyTime !== null,
  },
  {
    label: '等待收货',
    field: 'dvyTime',
    icon: process4_1,
    activeIcon: process4,
    isActive: ({ status, dvyTime }) => status >= 3 && status !== 6 && dvyTime !== null,
  },
  {
    label: '订单完成',
    field: 'finallyTime',
    icon: process5_1,
    activeIcon: process5,
    isActive: ({ status, fianllyTime }) => status >= 5 && status !== 6 && fianllyTime !== null,
  },
]

const useCompany = companyStore()

const onCancelOrder = (orderNumber) => {
  Modal.confirm({
    title: '提示',
    content: '订单取消后将无法恢复,请您谨慎考虑',
    onOk() {
      http(`/mall/p/myOrder/cancel`, {
        method: 'put',
        params: {
          orderNumbers: orderNumber,
        },
      }).then((res) => {
        useMall(res, () => {
          message.success('操作成功')
          router.push({
            path: `/workSpace/company-space/procure-order`,
          })
        })
      })
    },
    onCancel() {
      console.log('Cancel')
    },
  })
}

const onConfirmReceive = (orderNumber) => {
  Modal.confirm({
    title: '提示',
    content: '确认收货吗?',
    onOk() {
      http(`/mall/p/myOrder/receipt/${orderNumber}`, {
        method: 'put',
      }).then((res) => {
        useMall(res, () => {
          message.success('操作成功')
          getOrderDetail()
        })
      })
    },
    onCancel() {
      console.log('Cancel')
    },
  })
}

const onPay = () => {
  message.success('操作成功,请等待客服确认')
}

const goods = ref([])
const totalPrice = ref(0)
const transfee = ref(0)
const getGoodsList = (data) => {
  totalPrice.value = 0
  goods.value = []
  data.forEach((d) => {
    d.totalPrice = Number(d.price * d.prodCount)
    totalPrice.value += d.totalPrice
    goods.value.push({
      ...d,
    })
  })
}

const orderInfo = ref({})
const userAddrDto = ref({})
const getOrderDetail = () => {
  http('/mall/p/myOrder/orderDetail', {
    method: 'get',
    params: {
      orderNumber: orderNo.value,
    },
  }).then((res) => {
    useMall(res, (ret) => {
      orderInfo.value = ret
      transfee.value = ret.transfee || 0
      userAddrDto.value = ret.userAddrDto
      getGoodsList(ret.orderItemDtos || [])
    })
  })
}

const returnOfGoodsRef = ref(null)
const applyForReturn = (data, refundType) => {
  returnOfGoodsRef.value?.init(data, refundType, orderInfo.value)
}

const props = defineProps({
  orderNo: {
    type: String,
    required: false,
  },
})
const orderNo = computed(() => route.params.orderNo || props.orderNo)

onMounted(() => {
  // orderNo = route.params.orderNo || props.orderNo
  getOrderDetail()
})

watch(orderNo, () => {
  if (orderNo.value) {
    getOrderDetail()
  }
})
</script>

<style lang="less" scoped>
.selections {
  padding: 20px 10px 20px;
  font-size: 14px;

  &:not(:last-child) {
    border-bottom: 1px solid #ccc;
  }

  .title {
    font-size: 16px;
    font-weight: bold;
  }

  .process-box {
    flex: 1;
    overflow: auto;
    padding: 10px;
    line-height: 30px;
    margin-left: 20px;

    .tip {
      color: #999;
    }

    .process {
      display: flex;
      text-align: center;
      line-height: 20px;
    }

    .item {
      // margin-right: 10px;
      padding-right: 30px;
      position: relative;

      &:last-child {
        margin: 0;
        padding: 0;

        &:after {
          width: 0;
          height: 0;
        }
      }

      &:after {
        position: absolute;
        top: 25%;
        right: 0;
        display: block;
        content: ' ';
        width: 30px;
        height: 10px;
        background: url(@/assets/images/icons.png) no-repeat -235px -686px;
      }

      .text {
        margin-bottom: 10px;
        color: #999;
      }
      .time {
        color: #999;
        font-family: arial, sans-serif;
        width: 100px;
      }
    }
    .item.active {
      &:after {
        background-position: -235px -721px;
      }

      .text {
        color: #000;
      }
    }
  }
}
</style>
