<!-- refundStatus 1 申请退款 2 退款成功 3 部分退款成功 4 退款失败 -1 退款关闭 -->
<!-- status '', '待付款', '待发货', '待收货', '待评价', '已完成', '已取消', '拼团中' -->
<template>
  <div class="">
    <a-form :label-col="{ flex: '72px' }">
      <a-row :gutter="16">
        <a-col :span="5">
          <a-form-item label="采购单号">
            <a-input placeholder="请输入" v-model:value="form.platformOrderNumber"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="5">
          <a-form-item label="订单号">
            <a-input placeholder="请输入" v-model:value="form.orderNumber"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="5">
          <a-form-item label="项目编号">
            <a-input placeholder="请输入" v-model:value="form.projectNo"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="5">
          <a-form-item label="开票状态">
            <a-select v-model:value="form.invoiceState" placeholder="请选择" allowClear>
              <a-select-option :value="2">已开票</a-select-option>
              <a-select-option :value="1">未开票</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="10">
          <a-form-item label="下单时间">
            <a-range-picker
              value-format="YYYY-MM-DD HH:mm:ss"
              v-model:value="rangeTime"
              format="YYYY-MM-DD HH:mm:ss"
              show-time
              style="width: 100%"
            ></a-range-picker>
          </a-form-item>
        </a-col>
        <a-col :span="5">
          <a-button type="primary" mr-10 @click="search">搜索</a-button>
          <a-button @click="reset">重置</a-button>
        </a-col>
      </a-row>
    </a-form>

    <a-radio-group v-model:value="current" button-style="solid" @change="changeTag">
      <a-radio-button v-for="item in conditions" :key="item.label" :value="item.value">
        {{ item.label }}
        {{ orderCountData[item.value] || 0 }}
      </a-radio-button>
    </a-radio-group>

    <div mt-20>
      <a-row class="bg-#efefef p-10 font-bold">
        <a-col :span="14">
          <a-row>
            <a-col :span="10">商品</a-col>
            <a-col :span="6">规格</a-col>
            <a-col :span="4">单价</a-col>
            <a-col :span="4">数量</a-col>
          </a-row>
        </a-col>
        <a-col :span="10">
          <a-row>
            <a-col :span="8" text-center>品牌</a-col>
            <a-col :span="6" text-center>实付款</a-col>
            <a-col :span="4" text-center>状态</a-col>
            <a-col :span="6" text-center>操作</a-col>
          </a-row>
        </a-col>
      </a-row>

      <a-row v-for="item in tableData" :key="item.platformOrderNumber" class="order-item">
        <a-col :span="24" class="title">
          <div flex items-center>
            <a-tooltip title="您订单中的商品分属不同品牌,故拆分为以下订单分开处理">
              <div
                class="i-mdi-alert-circle inline-block text-red text-16 mr-4 cursor-pointer"
                v-if="item.orderCount > 1"
              />
            </a-tooltip>
            <span font-bold pr-20>{{ item.createTime }}</span>
            <span pr-20>采购单: {{ item.platformOrderNumber || '--' }}</span>
            <span pr-20>项目号: {{ item.projectNo || '--' }}</span>
            <span pr-20 v-if="item.orderCount > 1">
              订单数:
              <b color-red-5>{{ item.orderCount }}</b>
            </span>
            <span v-if="item.orderCount > 1">
              订单总金额:
              <b color-red-5>{{ getPrice(item._totalPrice) }}</b>
            </span>
            <span pr-20 v-if="item.orderCount > 1">
              <b color-red-5>(含运费: {{ getFreight(item) }})</b>
            </span>
            <span pr-20 v-if="item.orderCount == 1">订单号: {{ item.orderNumber }}</span>
            <!-- <span pr-20 v-if="item.orderCount == 1 && item.status > 1">支付单号: {{ item.payNo || '--' }}</span> -->
          </div>
          <div flex items-center>
            <div class="link" @click="downloadContract(item)">下载合同</div>
            <div
              class="link mx-10"
              @click="
                navigateTo({
                  path: '/workSpace/company-space/procure-order/all',
                  query: {
                    platformOrderNumber: item.platformOrderNumber,
                  },
                })
              "
            >
              查看详情
            </div>
            <a-dropdown v-if="item.orderCount > 1">
              <a style="cursor: pointer">
                批量操作
                <DownOutlined />
              </a>
              <template #overlay>
                <a-menu @click="({ key }) => onBatchClick(key, item)">
                  <!-- <a-menu-item key="pay">
                  立即支付
                </a-menu-item> -->

                  <a-menu-item key="cancel" :disabled="canCancel(item)">取消订单</a-menu-item>

                  <!-- <a-menu-item key="return">
                  退款
                </a-menu-item> -->
                </a-menu>
              </template>
            </a-dropdown>
            <span
              v-if="item.orderCount == 1 && item.status >= 5 && item.status !== 7"
              style="cursor: pointer"
              @click="onDelOrder(item.orderNumber)"
            >
              <DeleteOutlined color="red" />
            </span>
          </div>
        </a-col>

        <a-col :span="24" v-for="_order in item._children" :key="_order.orderNumber">
          <div v-if="item.orderCount > 1" class="sub-title">
            <span font-bold pr-20>{{ _order.createTime }}</span>
            <span pr-20>订单号: {{ _order.orderNumber }}</span>
            <span pr-20>项目号: {{ _order.projectNo || '--' }}</span>
            <span pr-20 v-if="_order.status > 1">支付单号: {{ _order.payNo || '--' }}</span>
            <span
              v-if="_order.status >= 5 && _order.status !== 7"
              style="float: right; cursor: pointer"
              @click="onDelOrder(_order.orderNumber)"
            >
              <DeleteOutlined color="red" />
            </span>
          </div>
          <a-row>
            <a-col :span="14">
              <a-row v-for="_item in _order.orderItemDtos" :key="_item.orderNumber" class="prod-item">
                <a-col :span="10">
                  <div flex>
                    <hm-img style="width: 100px; height: 100px; margin: 10px" :src="_item.pic" />
                    <!-- <img w-100 h-100 m-10 :src="convertImage(_item.pic)" /> -->
                    <div flex flex-col flex-1 justify-around overflow-hidden pr-10>
                      <span font-bold w-full class="text-ellipsis" :title="_item.prodName">{{ _item.prodName }}</span>
                      <span w-full class="text-ellipsis" :title="_item.partyCode">型号: {{ _item.partyCode }}</span>
                      <span w-full class="text-ellipsis" :title="_item.partyCode">物料编码: {{ _item.partyCode }}</span>
                      <span w-full v-if="!_item.returnMoneySts || _item.returnMoneySts < 0 || _item.returnMoneySts > 5">
                        <a-tag color="red">
                          <template v-if="[2, 4].includes(_order.status)">
                            {{ _item.itemStatus === 0 ? '发货完成' : _item.itemStatus == -1 ? '待发货' : '部分发货' }}
                          </template>
                          <template v-else>
                            {{ statusList[_order.status] }}
                          </template>
                        </a-tag>
                      </span>
                      <span w-full v-else>
                        <a-tag color="red">
                          {{ returnMoneyStsList[_item.returnMoneySts] }}
                        </a-tag>
                      </span>
                    </div>
                  </div>
                </a-col>
                <a-col :span="6">
                  <div flex h-full flex-col justify-around>
                    <!-- 显示前四个规格 -->
                    <span v-for="__item in (_item.properties || '').split(';').slice(0, 4)" :key="__item" mb-5>
                      {{ __item.split(':')[0] }}: {{ __item.split(':')[1] }}
                    </span>
                  </div>
                </a-col>
                <a-col :span="4">
                  <div flex flex-col justify-center>
                    <span v-if="_item.oriPrice" m-10 style="text-decoration: line-through; color: #ccc">
                      {{ getPrice(_item.oriPrice) }}/个
                    </span>
                    <span m-10>{{ getPrice(_item.price) }}/个</span>
                  </div>
                </a-col>
                <a-col :span="4">
                  <div flex flex-col justify-center>
                    <span m-10>{{ _item.prodCount }}</span>
                  </div>
                </a-col>
              </a-row>
            </a-col>
            <a-col :span="10">
              <a-row h-full>
                <a-col :span="8" style="border-left: 1px solid #d9d9d9">
                  <div flex justify-center items-center mt-12 font-bold>
                    <a-tag color="#f94c30" v-if="_order.isSelf">联营</a-tag>
                    {{ _order.shopName }}
                  </div>
                  <div flex justify-center mt-8 px-5>
                    <hm-img style="width: 100%; max-height: 80px" :src="_order.shopLogo" :preview="false" />
                    <!-- <img h-80 m-4 :src="convertImage(_order.shopLogo)" /> -->
                  </div>
                </a-col>
                <a-col :span="6" style="border-left: 1px solid #d9d9d9">
                  <div flex justify-center my-20 color-red-5 title-text font-size-16>
                    {{ getPrice(gettotalPrice(_order, 'actualTotal', item.orderCount)) }}
                  </div>
                  <div flex justify-center my-20 color-red-5>
                    {{ gettotalPrice(_order, 'freightAmount', item.orderCount) }}
                  </div>
                </a-col>
                <a-col :span="4" style="border-left: 1px solid #d9d9d9">
                  <div flex justify-center my-20>{{ statusList[_order.status] }}</div>
                  <div flex justify-center my-20 color-red-5 v-if="_order.refundStatus == 1">(退款中)</div>
                </a-col>
                <a-col :span="6" style="border-left: 1px solid #d9d9d9">
                  <div flex flex-col items-center my-12>
                    <div v-if="_order.status == 1" text-center mt-8>
                      剩余支付时间
                      <count-down color-red-5 :allTime="getRemainTime(_order.createTime)" />
                    </div>

                    <a-button
                      type="link"
                      @click="
                        navigateTo({
                          path: '/workSpace/company-space/procure-order/' + _order.orderNumber,
                        })
                      "
                    >
                      订单详情
                    </a-button>
                    <!-- <a-button type="link">合同下载</a-button> -->
                    <a-button v-if="_order.status == 1" type="link" @click="onCancelOrder(_order.orderNumber)">
                      取消订单
                    </a-button>
                    <a-button v-if="_order.status == 3" type="link" @click="onConfirmReceive(_order.orderNumber)">
                      确认收货
                    </a-button>
                    <a-button v-if="_order.fileId" type="link" @click="onViewInv(_order)">查看发票</a-button>
                  </div>
                </a-col>
              </a-row>
            </a-col>
          </a-row>
        </a-col>
      </a-row>

      <hm-empty v-if="!tableData.length" />
    </div>

    <div text-right mt-16 v-if="page.total">
      <a-pagination
        v-model:current="page.current"
        v-model:page-size="page.size"
        :pageSizeOptions="['10', '20', '50', '100']"
        show-size-changer
        show-quick-jumper
        :total="page.total"
        @change="fetchData"
      />
    </div>

    <doc-preview ref="docPreviewRef" />
  </div>
</template>

<script setup>
import { statusList, returnMoneyStsList } from '~/api/order'
import { companyStore } from '~/store/company'
import def from '~/assets/images/def.png'
import fileDownload from 'js-file-download'
const router = useRouter()

const useCompany = companyStore()

const rangeTime = ref([])
const form = ref({})

const conditions = [
  {
    label: '全部',
    value: 0,
    key: 'allCount',
  },
  {
    label: '待支付',
    value: 1,
    key: 'unPay',
  },
  {
    label: '待发货',
    value: 2,
    key: 'payed',
  },
  {
    label: '部分发货',
    value: 4,
    key: 'someSend',
  },
  {
    label: '待收货',
    value: 3,
    key: 'consignment',
  },
  {
    label: '已完成',
    value: 5,
    key: 'success',
  },
  {
    label: '已取消',
    value: 6,
    key: 'close',
  },
]
const current = ref(0)

// 获取表格数据
const loading = ref(false)
const page = reactive({
  size: 10,
  current: 1,
  total: 0,
})

const orderList = ref([])
const fetchData = () => {
  const params = {
    orderName: '',
    orderType: 0,
    orderMold: 0,
    status: current.value,
    current: page.current,
    size: page.size,
    orderNumber: form.value.orderNumber || '',
    projectNo: form.value.projectNo || '',
    invoiceState: form.value.invoiceState || '',
    merchantId: useCompany.company.shopCompanyId,
    platformOrderNumber: form.value.platformOrderNumber || '',
  }
  if (rangeTime.value.length) {
    params.begin = rangeTime.value[0]
    params.end = rangeTime.value[1]
  }
  http('/mall/p/myOrder/myOrderSearch', {
    method: 'get',
    params: {
      ...params,
    },
  }).then((res) => {
    useMall(res, (ret) => {
      orderList.value = ret.records || []
      page.total = ret.total

      setTableData()

      if (!orderList.value.length && page.current > 1) {
        page.current = 1
        fetchData()
      } else {
        getUserInfo()
      }
    })
  })
}

const tableData = ref([])
const setTableData = () => {
  const dataList = []
  orderList.value.forEach((d) => {
    const { platformOrderNumber, orderNumber, createTime, payNo, projectNo, orderCount, status } = d
    if (!platformOrderNumber) {
      dataList.push({
        platformOrderNumber: '',
        orderNumber,
        createTime,
        projectNo,
        payNo,
        orderCount: 1,
        status,
        _children: [d],
      })
      return
    }
    const item = dataList.find((d) => d.platformOrderNumber == platformOrderNumber)
    if (item) {
      ;(item._totalPrice += d.actualTotal), item._children.push(d)
    } else {
      dataList.push({
        platformOrderNumber,
        orderNumber,
        createTime,
        projectNo,
        payNo,
        orderCount,
        status,
        _totalPrice: d.actualTotal,
        _children: [d],
      })
    }
  })
  tableData.value = dataList
}

const gettotalPrice = ({ actualTotal, freightAmount, orderItemDtos, isSelf }, key, count) => {
  if (key == 'actualTotal') {
    let total = 0
    orderItemDtos.forEach((d) => {
      total += Number(d.price * d.prodCount)
    })
    return actualTotal || total
  }
  if (key == 'freightAmount') {
    if (isSelf && count > 1) {
      return '(运费已合并计算)'
    } else {
      return `(含运费：${getPrice(freightAmount)})`
    }
  }
  return '--'
}

const getFreight = (item) => {
  const freight = (item._children || []).reduce((res, item) => {
    res += item.freightAmount || 0
    return res
  }, 0)
  return getPrice(freight)
}

const getRemainTime = (beg) => {
  const allTime = 86400
  const passedTime = (new Date().getTime() - new Date(beg).getTime()) / 1000
  const ret = allTime - passedTime
  if (ret <= 0) {
    return 0
  }
  return ret
}

const onCancelOrder = (orderNumber) => {
  Modal.confirm({
    title: '提示',
    content: '订单取消后将无法恢复,请您谨慎考虑',
    onOk() {
      http(`/mall/p/myOrder/cancel`, {
        method: 'put',
        params: {
          orderNumbers: orderNumber,
        },
      }).then((res) => {
        useMall(res, () => {
          message.success('操作成功')
          fetchData()
        })
      })
    },
    onCancel() {
      console.log('Cancel')
    },
  })
}

const onDelOrder = (orderNumber) => {
  Modal.confirm({
    title: '提示',
    content: '订单删除后将无法恢复,请您谨慎考虑',
    onOk() {
      http(`/mall/p/myOrder/${orderNumber}`, {
        method: 'delete',
      }).then((res) => {
        useMall(res, () => {
          message.success('操作成功')
          fetchData()
        })
      })
    },
    onCancel() {
      console.log('Cancel')
    },
  })
}

/**
 * 订单确认收货
 */
const onConfirmReceive = (orderNumber) => {
  Modal.confirm({
    title: '提示',
    content: '确认收货吗?',
    onOk() {
      http(`/mall/p/myOrder/receipt/${orderNumber}`, {
        method: 'put',
      }).then((res) => {
        useMall(res, () => {
          message.success('操作成功')
          fetchData()
        })
      })
    },
    onCancel() {
      console.log('Cancel')
    },
  })
}

const orderCountData = ref({})
/**
 * 获取订单数量
 */
const getUserInfo = () => {
  const params = {
    // status: current.value,
    projectNo: form.value.projectNo,
    orderNumber: form.value.orderNumber,
    merchantId: useCompany.company.shopCompanyId,
  }
  if (rangeTime.value.length) {
    params.begin = rangeTime.value[0]
    params.end = rangeTime.value[1]
  }
  http('/mall/p/myOrder/myOrderSearchConut', {
    method: 'get',
    params,
  }).then(({ data }) => {
    data[0] = Object.values(data).reduce((res, item) => (res += item))
    orderCountData.value = data
  })
}

const search = () => {
  page.current = 1
  fetchData()
}

const reset = () => {
  form.value = {}
  rangeTime.value = []
  page.current = 1
  fetchData()
}

const changeTag = () => {
  page.current = 1
  router.replace({
    query: {
      ...router.currentRoute.value.query,
      status: current.value,
    },
  })
  fetchData()
}

// 监听路由参数变化,同步tag状态
watch(
  () => router.currentRoute.value.query.status,
  (val) => {
    if (val) {
      current.value = Number(val)
    }
  },
  { immediate: true },
)

const canCancel = (order) => {
  return !order._children.filter((d) => d.status == 1).length
}

const onBatchClick = (key, order) => {
  if (key == 'cancel') {
    Modal.confirm({
      title: '提示',
      content: '待付款状态的订单将被全部取消,是否继续?',
      onOk() {
        const orders = order._children.filter((d) => d.status == 1)
        const orderNumbers = orders.map((d) => d.orderNumber).join()
        http(`/mall/p/myOrder/cancel`, {
          method: 'put',
          params: {
            orderNumbers: orderNumbers,
          },
        }).then((res) => {
          useMall(res, () => {
            message.success('操作成功')
            fetchData()
          })
        })
      },
      onCancel() {
        console.log('Cancel')
      },
    })
  }
}

const docPreviewRef = ref(null)
const onViewInv = (order) => {
  const path = order.fileId
  docPreviewRef.value?.init(path)
}

onMounted(() => {
  fetchData()
  document.addEventListener('visibilitychange', visibilitychange)
})

onUnmounted(() => {
  document.removeEventListener('visibilitychange', visibilitychange)
})

const visibilitychange = () => {
  console.log('visibilitychange')
  fetchData()
}

const downloadContract = async (item) => {
  const [child] = item._children
  if (child) {
    const orderContractFileUrl = child.orderContractFileUrl
    const res = await $fetch(orderContractFileUrl)
    const suffix = orderContractFileUrl.replace(/.*\.(.*)/, '$1')
    fileDownload(res, `${item.platformOrderNumber}_合同.${suffix}`)
  } else {
    message.error('该合同无法下载')
  }
  // fileDownload
}
</script>

<style lang="less" scoped>
.order-item {
  margin: 10px 0 20px;
  border: 1px solid #d9d9d9;
  .title {
    background: #efefef;
    padding: 4px 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .sub-title {
    border-top: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
    padding: 10px;
  }

  .prod-item {
    &:not(:last-child) {
      border-bottom: 1px solid #ccc;
    }

    padding: 10px 0;
  }
}
</style>
