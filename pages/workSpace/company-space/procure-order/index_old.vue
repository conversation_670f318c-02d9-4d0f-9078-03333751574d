<!-- refundStatus 1 申请退款 2 退款成功 3 部分退款成功 4 退款失败 -1 退款关闭 -->
<!-- status '', '待付款', '待发货', '待收货', '待评价', '已完成', '已取消', '拼团中' -->
<template>
  <div class="bg-#fff flex-1 p-20 overflow-x-hidden">
    <a-form mt-10 :label-col="{ flex: '72px' }">
      <a-row :gutter="10">
        <a-col :span="6">
          <a-form-item label="采购单号">
            <a-input placeholder="请输入" v-model:value="form.platformOrderNumber"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="订单号">
            <a-input placeholder="请输入" v-model:value="form.orderNumber"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="项目编号">
            <a-input placeholder="请输入" v-model:value="form.projectNo"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="下单时间">
            <a-range-picker
              value-format="YYYY-MM-DD HH:mm:ss"
              v-model:value="rangeTime"
              format="YYYY-MM-DD HH:mm:ss"
              show-time
              style="width: 100%"
            ></a-range-picker>
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-button type="primary" mr-10 @click="search">搜索</a-button>
          <a-button type="primary" @click="reset">重置</a-button>
        </a-col>
      </a-row>
    </a-form>

    <a-radio-group v-model:value="current" button-style="solid" @change="changeTag">
      <a-radio-button v-for="item in conditions" :key="item.label" :value="item.value">
        {{ item.label }}
        {{ orderCountData[item.value] || 0 }}
      </a-radio-button>
    </a-radio-group>

    <div mt-20>
      <a-row class="bg-#efefef p-10 font-bold">
        <a-col :span="14">
          <a-row>
            <a-col :span="10">商品</a-col>
            <a-col :span="6">规格</a-col>
            <a-col :span="4">单价</a-col>
            <a-col :span="4">数量</a-col>
          </a-row>
        </a-col>
        <a-col :span="10">
          <a-row>
            <a-col :span="6" text-center>品牌</a-col>
            <a-col :span="6" text-center>实付款</a-col>
            <a-col :span="6" text-center>状态</a-col>
            <a-col :span="6" text-center>操作</a-col>
          </a-row>
        </a-col>
      </a-row>

      <a-row v-for="item in orderList" :key="item.orderNumber" class="order-item">
        <a-col :span="24" class="title">
          <span font-bold pr-20>{{ item.createTime }}</span>
          <span pr-20>订单号: {{ item.orderNumber }}</span>
          <span pr-20 v-if="item.status > 1">支付单号: {{ item.payNo || '--' }}</span>
          <span pr-20>项目号: {{ item.projectNo || '--' }}</span>
          <span
            v-if="item.status >= 5 && item.status !== 7"
            style="float: right; cursor: pointer"
            @click="onDelOrder(item.orderNumber)"
          >
            <DeleteOutlined color="red" />
          </span>
        </a-col>
        <a-col :span="14">
          <a-row v-for="_item in item.orderItemDtos" :key="_item.skuId" class="prod-item">
            <a-col :span="10">
              <div flex>
                <img w-100 h-100 m-10 :src="convertImage(_item.pic)" />
                <div flex flex-col flex-1 justify-around overflow-hidden pr-10>
                  <span font-bold w-full class="text-ellipsis" :title="_item.prodName">{{ _item.prodName }}</span>
                  <span w-full class="text-ellipsis" :title="_item.partyCode">型号: {{ _item.partyCode }}</span>
                  <span w-full class="text-ellipsis" :title="_item.partyCode">物料编码: {{ _item.partyCode }}</span>
                  <span w-full v-if="!_item.returnMoneySts || _item.returnMoneySts < 0 || _item.returnMoneySts > 5">
                    <a-tag color="red">
                      <template v-if="[2, 4].includes(item.status)">
                        {{ _item.itemStatus === 0 ? '发货完成' : _item.itemStatus == -1 ? '待发货' : '部分发货' }}
                      </template>
                      <template v-else>
                        {{ statusList[item.status] }}
                      </template>
                    </a-tag>
                  </span>
                  <span w-full v-else>
                    <a-tag color="red">
                      {{ returnMoneyStsList[_item.returnMoneySts] }}
                    </a-tag>
                  </span>
                </div>
              </div>
            </a-col>
            <a-col :span="6">
              <div flex h-full flex-col justify-around>
                <!-- 显示前四个规格 -->
                <span v-for="__item in (_item.properties || '').split(';').slice(0, 4)" :key="__item" mb-5>
                  {{ __item.split(':')[0] }}: {{ __item.split(':')[1] }}
                </span>
              </div>
            </a-col>
            <a-col :span="4">
              <div flex flex-col justify-center>
                <span v-if="_item.oriPrice" m-10 style="text-decoration: line-through; color: #ccc">
                  {{ getPrice(_item.oriPrice) }}/个
                </span>
                <span m-10>{{ getPrice(_item.price) }}/个</span>
              </div>
            </a-col>
            <a-col :span="4">
              <div flex flex-col justify-center>
                <span m-10>{{ _item.prodCount }}</span>
              </div>
            </a-col>
          </a-row>
        </a-col>
        <a-col :span="10">
          <a-row h-full>
            <a-col :span="6" style="border-left: 1px solid #ccc">
              <div flex justify-center my-20 font-bold>{{ item.shopName }}</div>
              <div flex justify-center>
                <img w-100 h-100 m-10 :src="convertImage(item.shopLogo)" />
              </div>
            </a-col>
            <a-col :span="6" style="border-left: 1px solid #ccc">
              <div flex justify-center my-20 color-red>{{ getPrice(gettotalPrice(item, 'actualTotal')) }}</div>
              <div flex justify-center my-20 color-red>
                (含运费: {{ getPrice(gettotalPrice(item, 'freightAmount')) }})
              </div>
            </a-col>
            <a-col :span="6" style="border-left: 1px solid #ccc">
              <div flex justify-center my-20>{{ statusList[item.status] }}</div>
              <div flex justify-center my-20 color-red v-if="item.refundStatus == 1">(退款中)</div>
              <!-- <div flex justify-center my-20>
                {{ statusList[item.status] }}
                <span v-if="item.refundStatus == 1">(退款中)</span>
              </div> -->
            </a-col>
            <a-col :span="6" style="border-left: 1px solid #ccc">
              <div flex flex-col items-center my-12>
                <div v-if="item.status == 1" text-center mt-8>
                  剩余支付时间
                  <count-down style="color: red" :allTime="getRemainTime(item.createTime)" />
                </div>

                <a-button
                  type="link"
                  @click="
                    navigateTo({
                      path: '/workSpace/company-space/procure-order/' + item.orderNumber,
                    })
                  "
                >
                  订单详情
                </a-button>
                <!-- <a-button type="link">合同下载</a-button> -->
                <a-button v-if="item.status == 1" type="link" @click="onCancelOrder(item.orderNumber)">
                  取消订单
                </a-button>
                <a-button v-if="item.status == 3" type="link" @click="onConfirmReceive(item.orderNumber)">
                  确认收货
                </a-button>
              </div>
            </a-col>
          </a-row>
        </a-col>
      </a-row>

      <hm-empty v-if="!orderList.length" />
    </div>

    <div text-right mt-16 v-if="page.total">
      <a-pagination
        v-model:current="page.current"
        v-model:page-size="page.size"
        :pageSizeOptions="['10', '20', '50', '100']"
        show-size-changer
        show-quick-jumper
        :total="page.total"
        @change="fetchData"
      />
    </div>
  </div>
</template>

<script setup>
import { statusList, returnMoneyStsList } from '~/api/order'
import { companyStore } from '~/store/company'

const useCompany = companyStore()

const rangeTime = ref([])
const form = ref({})

const conditions = [
  {
    label: '全部',
    value: 0,
    key: 'allCount',
  },
  {
    label: '待支付',
    value: 1,
    key: 'unPay',
  },
  {
    label: '待发货',
    value: 2,
    key: 'payed',
  },
  {
    label: '部分发货',
    value: 4,
    key: 'someSend',
  },
  {
    label: '待收货',
    value: 3,
    key: 'consignment',
  },
  {
    label: '已完成',
    value: 5,
    key: 'success',
  },
  {
    label: '已取消',
    value: 6,
    key: 'close',
  },
]
const current = ref(0)

// 获取表格数据
const loading = ref(false)
const page = reactive({
  size: 10,
  current: 1,
  total: 0,
})

const orderList = ref([])
const fetchData = () => {
  const params = {
    orderName: '',
    orderType: 0,
    orderMold: 0,
    status: current.value,
    current: page.current,
    size: page.size,
    orderNumber: form.value.orderNumber || '',
    projectNo: form.value.projectNo || '',
    merchantId: useCompany.company.shopCompanyId,
    platformOrderNumber: form.value.platformOrderNumber || '',
  }
  if (rangeTime.value.length) {
    params.begin = rangeTime.value[0]
    params.end = rangeTime.value[1]
  }
  http('/mall/p/myOrder/myOrderSearch', {
    method: 'get',
    params: {
      ...params,
    },
  }).then((res) => {
    useMall(res, (ret) => {
      orderList.value = ret.records || []
      page.total = ret.total

      setTableData()

      if (!orderList.value.length && page.current > 1) {
        page.current = 1
        fetchData()
      } else {
        getUserInfo()
      }
    })
  })
}

const tableData = ref([])
const setTableData = () => {
  const dataList = []
  orderList.value.forEach((d) => {
    const { platformOrderNumber, orderNumber, createTime, payNo, projectNo, orderCount } = d
    if (!platformOrderNumber) {
      dataList.push({
        platformOrderNumber: '',
        orderNumber,
        createTime,
        projectNo,
        payNo,
        orderCount: 1,
        _children: [d],
      })
      return
    }
    const item = dataList.find((d) => d.platformOrderNumber == platformOrderNumber)
    if (item) {
      ;(item._totalPrice += d.actualTotal), item._children.push(d)
    } else {
      dataList.push({
        platformOrderNumber,
        orderNumber,
        createTime,
        projectNo,
        payNo,
        orderCount,
        _totalPrice: d.actualTotal,
        _children: [d],
      })
    }
  })
  tableData.value = dataList
}

const gettotalPrice = ({ actualTotal, freightAmount, orderItemDtos }, key) => {
  let total = 0
  orderItemDtos.forEach((d) => {
    total += Number(d.price * d.prodCount)
  })
  if (key == 'actualTotal') {
    return actualTotal ? Number(actualTotal).toFixed(2) : total.toFixed(2)
  }
  if (key == 'freightAmount') {
    return freightAmount ? Number(freightAmount).toFixed(2) : (actualTotal - total).toFixed(2)
  }
  return '--'
}

const getRemainTime = (beg) => {
  const allTime = 86400
  const passedTime = (new Date().getTime() - new Date(beg).getTime()) / 1000
  const ret = allTime - passedTime
  if (ret <= 0) {
    return 0
  }
  return ret
}

const onCancelOrder = (orderNumber) => {
  Modal.confirm({
    title: '提示',
    content: '订单取消后将无法恢复,请您谨慎考虑',
    onOk() {
      http(`/mall/p/myOrder/cancel/${orderNumber}`, {
        method: 'put',
      }).then((res) => {
        useMall(res, () => {
          message.success('操作成功')
          fetchData()
        })
      })
    },
    onCancel() {
      console.log('Cancel')
    },
  })
}

const onDelOrder = (orderNumber) => {
  Modal.confirm({
    title: '提示',
    content: '订单删除后将无法恢复,请您谨慎考虑',
    onOk() {
      http(`/mall/p/myOrder/${orderNumber}`, {
        method: 'delete',
      }).then((res) => {
        useMall(res, () => {
          message.success('操作成功')
          fetchData()
        })
      })
    },
    onCancel() {
      console.log('Cancel')
    },
  })
}

/**
 * 订单确认收货
 */
const onConfirmReceive = (orderNumber) => {
  Modal.confirm({
    title: '提示',
    content: '确认收货吗?',
    onOk() {
      http(`/mall/p/myOrder/receipt/${orderNumber}`, {
        method: 'put',
      }).then((res) => {
        useMall(res, () => {
          message.success('操作成功')
          fetchData()
        })
      })
    },
    onCancel() {
      console.log('Cancel')
    },
  })
}

const orderCountData = ref({})
/**
 * 获取订单数量
 */
const getUserInfo = () => {
  const params = {
    // status: current.value,
    projectNo: form.value.projectNo,
    orderNumber: form.value.orderNumber,
    merchantId: useCompany.company.shopCompanyId,
  }
  if (rangeTime.value.length) {
    params.begin = rangeTime.value[0]
    params.end = rangeTime.value[1]
  }
  http('/mall/p/myOrder/myOrderSearchConut', {
    method: 'get',
    params,
  }).then(({ data }) => {
    data[0] = Object.values(data).reduce((res, item) => (res += item))
    orderCountData.value = data
  })
}

const search = () => {
  page.current = 1
  fetchData()
}

const reset = () => {
  form.value = {}
  rangeTime.value = []
  page.current = 1
  fetchData()
}

const changeTag = () => {
  page.current = 1
  fetchData()
}

const router = useRouter()

onMounted(() => {
  fetchData()
  document.addEventListener('visibilitychange', visibilitychange)
})

onUnmounted(() => {
  document.removeEventListener('visibilitychange', visibilitychange)
})

const visibilitychange = () => {
  console.log('visibilitychange')
  fetchData()
}
</script>

<style lang="less" scoped>
.order-item {
  margin: 10px 0 20px;
  border: 1px solid #ccc;

  .title {
    background: #efefef;
    padding: 10px;
  }

  .prod-item {
    &:not(:last-child) {
      border-bottom: 1px solid #ccc;
    }

    padding: 10px 0;
  }
}
</style>
