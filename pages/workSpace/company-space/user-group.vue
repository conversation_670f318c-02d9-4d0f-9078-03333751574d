<template>
  <div flex flex-1 overflow-y-auto bg-white>
    <div w-210 h-full border-r border="#f1f1f1">
      <a-tree
        v-if="groupTree && groupTree.length"
        :field-names="{ key: 'id' }"
        :tree-data="groupTree"
        default-expand-all
        v-model:selected-keys="selGroup"
        @select="getMemberAndRole"
        block-node
      >
        <template #title="{ data }">
          <div flex>
            <div truncate flex-1 :title="data.name">{{ data.name }}</div>
            <a-dropdown>
              <MoreOutlined />
              <template #overlay>
                <a-menu>
                  <a-menu-item v-if="useAuth('auth:group:addGroup')" @click="onAdd(data)">
                    <PlusOutlined />
                    创建子用户组
                  </a-menu-item>

                  <a-menu-item
                    v-if="useAuth('auth:group:editGroup') && !data.isRoot"
                    @click="onEdit(data)"
                    :disabled="data.builtin"
                  >
                    <EditOutlined />
                    编辑
                  </a-menu-item>

                  <a-menu-item
                    v-if="useAuth('auth:group:delGroup') && !data.isRoot"
                    @click="onDel(data)"
                    :disabled="data.builtin"
                  >
                    <DeleteOutlined />
                    删除
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </template>
      </a-tree>
    </div>

    <div v-if="selGroup.length" flex-1 pl-20 overflow-x-hidden mt-10>
      <a-divider orientation="left">成员</a-divider>
      <div v-if="!isSelRoot" flex flex-wrap px-50>
        <a-tag
          mb-10
          v-for="(item, index) in memberList"
          :key="item.id"
          :closable="useAuth('auth:group:delMem')"
          @close="onDelMember(item.id)"
        >
          <template #icon v-if="item.isOrg == 1">
            <FolderOpenOutlined />
          </template>
          {{ item.nickName }} {{ item.realName ? `(${item.realName})` : '' }}
        </a-tag>
        <a-button v-if="useAuth('auth:group:addMem')" type="primary" size="small" @click="onAddMember">
          <template #icon>
            <PlusOutlined />
          </template>
        </a-button>
      </div>

      <a-divider orientation="left">角色</a-divider>
      <div v-if="!isSelRoot" flex flex-wrap px-50>
        <a-tag
          mb-10
          v-for="(item, index) in roleList"
          :key="item.id"
          :closable="useAuth('auth:group:delRole')"
          @close="onDelRole(item.id)"
        >
          {{ item.name }}
        </a-tag>
        <a-button v-if="useAuth('auth:group:addRole')" type="primary" size="small" @click="onAddRole">
          <template #icon>
            <PlusOutlined />
          </template>
        </a-button>
      </div>
    </div>

    <a-modal :width="500" :title="modalTitle" :maskClosable="false" v-model:open="open" @ok="confirm">
      <a-form mt-20 :label-col="{ flex: '100px' }" ref="formRef" :model="dataForm" :rules="rules">
        <a-form-item label="用户组名称" name="name">
          <a-input v-model:value="dataForm.name" placeholder="请输入" allowClear :maxlength="30" show-count />
        </a-form-item>
      </a-form>
    </a-modal>

    <a-modal :width="500" title="添加角色" :maskClosable="false" v-model:open="openRole" @ok="confirmRole">
      <a-form mt-20 :label-col="{ flex: '80px' }" ref="roleFormRef" :model="roleDataForm" :rules="rules">
        <a-form-item label="角色" name="roleIds">
          <a-select v-model:value="roleDataForm.roleIds" placeholder="请选择" mode="multiple" allowClear>
            <a-select-option
              v-for="item in roleOption.filter((d) => roleList.findIndex((t) => t.roleId == d.roleId) == -1)"
              :key="item.roleId"
              :value="item.roleId"
            >
              {{ item.roleName }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>

    <userOrg ref="userOrgRef" @ok="onUserOk" />
  </div>
</template>

<script setup>
import { companyStore } from '~/store/company'
import userOrg from './components/selUserOrg.vue'

const useCompany = companyStore()
const rules = {
  name: [{ required: true, message: '请输入用户组名称', trigger: 'blur', whitespace: true }],
  roleIds: [{ required: true, message: '请选择角色', trigger: 'change' }],
}

const modalTitle = ref('新增用户组')
const open = ref(false)
const onAdd = (data) => {
  open.value = true
  modalTitle.value = '新增用户组'
  nextTick(() => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    dataForm.value = {
      parentId: data.id,
    }
  })
}

const onEdit = (data) => {
  open.value = true
  modalTitle.value = '编辑用户组'
  dataForm.value = {
    ...data,
  }
}

const onDel = ({ id }) => {
  http(`/mall/p/sys/enterpriseUserGroup/${id}`, {
    method: 'delete',
  }).then((res) => {
    useMall(res, (ret) => {
      if (selGroup.value[0] == id) {
        selGroup.value = []
      }
      getGroupList()
    })
  })
}

const dataForm = ref({})
const formRef = ref(null)
const confirm = () => {
  formRef.value.validate().then(() => {
    http('/mall/p/sys/enterpriseUserGroup', {
      method: dataForm.value.id ? 'put' : 'post',
      body: {
        ...dataForm.value,
        enterpriseId: useCompany.company.shopCompanyId,
      },
    }).then((res) => {
      useMall(res, (ret) => {
        open.value = false
        getGroupList()
      })
    })
  })
}

const groupTree = ref([])
const expandedKeys = ref([])
const selGroup = ref([])
const getGroupList = () => {
  const merchantId = useCompany.company.shopCompanyId
  if (!merchantId) return
  http(`/mall/p/sys/enterpriseUserGroup/list/${merchantId}`, {
    method: 'get',
    params: {},
  }).then((res) => {
    useMall(res, (ret) => {
      groupTree.value = ret
      if (ret.length && !selGroup.value.length) {
        selGroup.value = [ret[0].id]
      }
      getMemberAndRole()
    })
  })
}

const isSelRoot = computed(() => {
  return selGroup.value[0] == groupTree.value[0]?.id
})

const memberList = ref([])
const roleList = ref([])
const getMemberAndRole = () => {
  if (!selGroup.value.length) return
  onGetGroupRole()
  onGetGroupMem()
}

// 成员 部门
const userOrgRef = ref(null)
const onAddMember = () => {
  console.log(memberList.value)
  userOrgRef.value?.init({
    orgs: memberList.value.filter((d) => d.isOrg == 1).map((d) => d.unionId),
    mems: memberList.value.filter((d) => d.isOrg == 0).map((d) => d.unionId),
  })
}

const onUserOk = (data) => {
  const postData = data.map((d) => {
    d.userGroupId = selGroup.value[0]
    return d
  })
  http('/mall/p/sys/enterpriseUserGroupMember', {
    method: 'post',
    body: postData,
  }).then((res) => {
    useMall(res, (ret) => {
      onGetGroupMem()
    })
  })
}

const onDelMember = (id) => {
  http(`/mall/p/sys/enterpriseUserGroupMember`, {
    method: 'delete',
    params: {
      id,
    },
  }).then((res) => {
    useMall(res, (ret) => {
      onGetGroupMem()
    })
  })
}

const onGetGroupMem = () => {
  http(`/mall/p/sys/enterpriseUserGroupMember/list`, {
    method: 'get',
    params: {
      groupId: selGroup.value[0],
    },
  }).then((res) => {
    useMall(res, (ret) => {
      memberList.value = ret
    })
  })
}
// 成员 部门

// 角色
const roleOption = ref([])
const getRoleList = () => {
  const merchantId = useCompany.company.shopCompanyId
  if (!merchantId) return
  http(`/mall/p/sys/enterpriseRole/list`, {
    method: 'get',
    params: {
      enterpriseId: merchantId,
    },
  }).then((res) => {
    useMall(res, (ret) => {
      roleOption.value = ret
    })
  })
}

const openRole = ref(false)
const onAddRole = () => {
  openRole.value = true
  nextTick(() => {
    roleFormRef.value.resetFields()
    roleFormRef.value.clearValidate()
  })
}

const roleDataForm = ref({})
const roleFormRef = ref(null)
const confirmRole = () => {
  roleFormRef.value.validate().then(() => {
    http('/mall/p/sys/enterpriseUserGroupRole', {
      method: 'post',
      body: {
        groupId: selGroup.value[0],
        roleIds: roleDataForm.value.roleIds,
      },
    }).then((res) => {
      useMall(res, (ret) => {
        openRole.value = false
        onGetGroupRole()
      })
    })
  })
}

const onDelRole = (id) => {
  http(`/mall/p/sys/enterpriseUserGroupRole`, {
    method: 'delete',
    body: [id],
  }).then((res) => {
    useMall(res, (ret) => {
      onGetGroupRole()
    })
  })
}

const onGetGroupRole = () => {
  http(`/mall/p/sys/enterpriseUserGroupRole/list`, {
    method: 'get',
    params: {
      groupId: selGroup.value[0],
    },
  }).then((res) => {
    useMall(res, (ret) => {
      roleList.value = ret
    })
  })
}
// 角色

onMounted(async () => {
  getGroupList()
  getRoleList()
})
</script>

<style lang="less" scoped>
:deep(.ant-tree-treenode) {
  padding: 0 !important;
  .ant-tree-node-content-wrapper {
    padding-right: 12px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow-x: hidden;
    background: none;
  }
}
:deep(.ant-tree-treenode-selected) {
  @apply bg-primary! text-white!;
}
</style>
