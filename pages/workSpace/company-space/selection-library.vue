<template>
  <div text-20 w-full bg-white>
    <!-- <div flex p-10>
      <RollbackOutlined v-if="crumbs.length >= 2" mr-10 cursor-pointer @click="onBackItem" />
      <a-breadcrumb separator=">">
        <a-breadcrumb-item 
          v-for="(item, index) in crumbs"
          :key="item.key"
          cursor-pointer
          @click="onClickCrumb(index)"
        >
          {{ item.label }}
        </a-breadcrumb-item>
      </a-breadcrumb>
    </div> -->

    <div flex justify-between p-10>
      <div>
        <!-- <a-button type="primary" mr-10 @click="onAddFloder">新建文件夹</a-button> -->
        <!-- <a-button type="primary">批量添加</a-button> -->
      </div>
      <div>
        <a-input-search
          v-model:value="searchKey"
          placeholder="型号搜索"
          style="width: 300px"
          allow-clear
          @search="onSearch"
        />
      </div>
    </div>

    <!-- 列表 -->
    <div p-10 style="">
      <a-table
        style="width: 100%"
        :columns="columns"
        row-key="id"
        size="middle"
        :scroll="{ x: tableWidth + 'px' }"
        :data-source="dataSource"
        :pagination="pagination"
        @change="handleChanger"
      >
        <template #bodyCell="{ column, record, value }">
          <span v-if="column.dataIndex == 'name'" @click="onOpenItem(record)" :title="record.prodName">
            {{ record.prodName }}
          </span>

          <span v-if="column.dataIndex == 'model'" class="fav-model" @click="onOpenFav(record.partId)" :title="value">
            {{ value }}
          </span>

          <span v-if="column.dataIndex == 'price'">
            {{ getPrice(value) }}
          </span>

          <template v-if="column.dataIndex == 'action'">
            <!-- <a-button type="link">编辑</a-button> -->
            <a-popconfirm v-if="useAuth('edu:selection:del')" @confirm="onDel(record.id)" title="确认删除吗?">
              <a-button type="link">删除</a-button>
            </a-popconfirm>
            <!-- <a-button type="link">查看供应商</a-button> -->
            <!-- <a-button type="link">打听价格</a-button> -->
          </template>
        </template>
      </a-table>
    </div>

    <a-modal v-model:open="newFloder" title="新建文件夹">
      <a-form
        ref="floderFormRef"
        :rules="floderRules"
        :model="floderFrom"
        :wrapper-col="{ span: 24 }"
        autocomplete="off"
      >
        <a-form-item name="name" label="文件夹名称">
          <a-input v-model:value="floderFrom.name" placeholder="文件夹名称" />
        </a-form-item>

        <a-form-item name="parentId" label="上级文件夹">
          <a-tree-select
            v-model:value="floderFrom.parentId"
            style="width: 100%"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            :fieldNames="{ label: 'name', value: 'id' }"
            :tree-data="floderList"
            placeholder="上级文件夹"
            tree-default-expand-all
            allowClear
          />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button key="back" @click="newFloder = false">取消</a-button>
        <a-button key="submit" type="primary" @click="onAddFloderSubmit">确认</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup>
import { userStore } from '~/store/user'
import { companyStore } from '~/store/company'

const userStoreObj = userStore()
const { user } = storeToRefs(userStoreObj)
const companyStoreObj = companyStore()
const { company } = storeToRefs(companyStoreObj)

const pagination = ref({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showTotal: (total) => {
    return `共${total}条数据`
  },
})

const crumbs = ref([
  {
    key: 1,
    label: '全部',
  },
])

const searchKey = ref('')
const onSearch = () => {
  getList()
}

const columns = [
  {
    title: '名称',
    dataIndex: 'name',
    width: 200,
    fixed: 'left',
    ellipsis: true,
  },
  {
    title: '类目',
    dataIndex: 'categoryName',
    width: 120,
    ellipsis: true,
  },
  {
    title: '品牌',
    dataIndex: 'brandName',
    width: 100,
    ellipsis: true,
  },
  {
    title: '型号',
    dataIndex: 'model',
    width: 120,
    ellipsis: true,
  },
  {
    title: '价格',
    dataIndex: 'price',
    width: 100,
    ellipsis: true,
  },
  {
    title: '交期',
    dataIndex: 'deliveryTime',
    width: 100,
    ellipsis: true,
  },
  {
    title: '供应商报价',
    dataIndex: 'supplierPrice',
    width: 100,
    ellipsis: true,
  },
  // {
  //   title: '供应商交期承诺',
  //   dataIndex: 'supplierDeliveryTime',
  //   width: 130,
  //   ellipsis: true
  // },
  // {
  //   title: '行业平均成交价',
  //   dataIndex: 'transactionPrice',
  //   width: 130,
  //   ellipsis: true
  // },
  // {
  //   title: '行业平均交期',
  //   dataIndex: 'transactionDeliveryTime',
  //   width: 120,
  //   ellipsis: true
  // },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 100,
    ellipsis: true,
  },
]

const tableWidth = computed(() => {
  const widths = columns.map((d) => Number(d.width))
  return widths.reduce((a, c) => a + c, 0)
})

const route = useRoute()
const dataSource = ref([])
const getList = () => {
  const merchantId = route.query.companyId || company.value.shopCompanyId
  if (!merchantId) return
  http('/mall/shop/personalFavorites/getPersonalFavoritesByFolderIdAndUserId', {
    method: 'get',
    params: {
      merchantId,
      userId: user.value.userId,
      folderId: 0,
      current: pagination.value.current,
      pageSize: pagination.value.pageSize,
      model: searchKey.value,
    },
  }).then((res) => {
    useMall(res, (ret) => {
      dataSource.value = ret.records
      pagination.value.total = ret.total

      if (!dataSource.value.length && pagination.value.current > 1) {
        pagination.value.current = 1
        getList()
      }
    })
  })
}

const handleChanger = (obj) => {
  pagination.value = obj
  getList()
}

const onDel = (id) => {
  http(`/mall/shop/personalFavorites`, {
    method: 'delete',
    params: {
      userMobile: user.value.userMobile,
      id,
    },
  }).then((res) => {
    useMall(res, (ret) => {
      getList()
    })
  })
}

const onOpenFav = (partId) => {
  const url = `${location.origin}/parts/${partId}`
  window.open(url)
  // window.open('https://www.yidao.cloud/parts/yiheda-1045')
}

const floderFrom = ref({
  name: '',
  parentId: '',
})

const floderRules = ref({
  name: [
    {
      required: true,
      message: '文件夹名称不能为空',
      trigger: 'blur',
    },
  ],
})

const newFloder = ref(false)
const onAddFloder = () => {
  floderFrom.value.name = ''
  floderFrom.value.parentId = ''
  newFloder.value = true
  // 查询文件夹列表
  http('/mall/shop/favoritesFolder/getByUserId', {
    method: 'get',
    params: {
      userId: user.value.userId,
    },
  }).then((res) => {
    floderList.value = res.data
  })
}

const floderList = ref([])
const floderFormRef = ref(null)
const onAddFloderSubmit = () => {
  floderFormRef.value.validate().then(() => {
    http('/mall/shop/favoritesFolder', {
      method: 'post',
      body: {
        name: floderFrom.value.name,
        parentId: floderFrom.value.parentId || 0,
        userId: user.value.userId,
      },
    }).then((res) => {
      useMall(res, () => {
        newFloder.value = false
        getList()
      })
    })
  })
}

const onOpenItem = (item) => {
  crumbs.value.push({
    key: item.id,
    label: item.prodName,
  })
  // TODO 查询
}

const onBackItem = (item) => {
  crumbs.value.pop()
  // TODO 查询
}

const onClickCrumb = (index) => {
  crumbs.value = crumbs.value.slice(0, index + 1)
  // TODO 查询
}

onMounted(() => {
  getList()
})
</script>

<style lang="less" scoped>
.fav-model {
  cursor: pointer;
  color: var(--primary-color);
}
</style>
