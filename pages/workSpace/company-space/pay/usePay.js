export const usePay = () => {
  const statusMap = {
    0: {
      text: '待支付',
      color: 'orange',
    },
    10: {
      text: '已支付',
      color: 'green',
    },
    20: {
      text: '已逾期',
      color: 'red',
    },
  }

  const getStatusText = (v) => statusMap[v]?.text || ''
  const getStatusColor = (v) => statusMap[v]?.color

  const go = (path, orderNumber) => {
    navigateTo({
      path,
      query: {
        orderNumber,
      },
    })
  }

  return {
    statusMap,
    getStatusText,
    getStatusColor,
    go,
  }
}
