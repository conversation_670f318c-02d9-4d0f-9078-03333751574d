<template>
  <div>
    <form-wrapper>
      <a-form layout="inline">
        <a-form-item label="付款单号">
          <a-input v-model:value="form.orderNo" placeholder="请输入付款单号"></a-input>
        </a-form-item>

        <a-form-item label="状态">
          <a-select v-model:value="form.status" class="w-180px!" placeholder="请选择状态">
            <a-select-option v-for="key in Object.keys(statusMap)" :key="key" :value="key">
              {{ getStatusText(key) }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="所属对账单">
          <a-input v-model:value="form.reconciliationOrderNo" placeholder="请输入所属对账单"></a-input>
        </a-form-item>

        <a-form-item label="创建时间">
          <range-picker v-model:value="form.createTimeFilter"></range-picker>
        </a-form-item>

        <a-form-item label="付款逾期时间">
          <range-picker v-model:value="form.overdueTimeFilter"></range-picker>
        </a-form-item>

        <a-form-item>
          <a-button type="primary" mr-8px @click="search">查询</a-button>
          <a-button @click="reset">重置</a-button>
        </a-form-item>
      </a-form>
    </form-wrapper>

    <!-- <div class="flex"> -->
    <!--   <a-button type="primary">导出</a-button> -->
    <!-- </div> -->

    <a-table
      :loading="loading"
      :columns="columns"
      :data-source="data"
      :pagination="pagination"
      @change="pageChange"
      @resize-column="colResize"
      bordered
      :scroll="{ x: 1500 }"
      size="small"
    >
      <template #bodyCell="{ record, column, text }">
        <a-tag v-if="column.dataIndex == 'status'" :color="getStatusColor(text)">{{ getStatusText(text) }}</a-tag>
        <action-button
          v-if="column.dataIndex == 'orderNumber'"
          @click="go('/workSpace/company-space/pay-detail', text)"
        >
          {{ text }}
        </action-button>
        <action-button
          v-if="column.dataIndex == 'rsOrderNumber'"
          @click="go('/workSpace/company-space/inv-detail', text)"
        >
          {{ text }}
        </action-button>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { usePay } from './usePay'

onMounted(() => {
  fetchData()
})
const form = ref({})
const data = ref([])
const loading = ref(false)

const { getStatusText, getStatusColor, statusMap, go } = usePay()

const fetchData = async () => {
  loading.value = true
  const [err, res] = await try_http('/mall/p/payment-order/page', {
    method: 'post',
    body: {
      current: page.current,
      size: page.size,
      ...form.value,
    },
  })
  loading.value = false
  if (!err) {
    data.value = res.data.records
    page.total = res.data.total
  }
}

const search = () => {
  page.current = 1
  fetchData()
}

const reset = () => {
  form.value = {}
  search()
}

const { page, pageChange, pagination } = usePage(fetchData)
const columns = ref([
  { title: '付款单号', dataIndex: 'orderNumber', width: 210, resizable: true },
  { title: '状态', dataIndex: 'status', width: 120 },
  {
    title: '应付总额',
    dataIndex: 'payableTotalAmount',
    width: 120,
    customRender({ text }) {
      return getPrice(text)
    },
  },
  {
    title: '实付总额',
    dataIndex: 'paidAmount',
    width: 120,
    customRender({ text }) {
      return getPrice(text)
    },
  },
  {
    title: '待付总额',
    dataIndex: 'unpaidAmount',
    width: 120,
    customRender({ text }) {
      return getPrice(text)
    },
  },
  { title: '所属对账单', dataIndex: 'rsOrderNumber', width: 200, resizable: true },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
    customRender({ text }) {
      return formatTime(text)
    },
  },
  {
    title: '付款完成时间',
    dataIndex: 'finishTime',
    width: 180,
    customRender({ text }) {
      return formatTime(text)
    },
  },
  {
    title: '付款逾期时间',
    dataIndex: 'overdueTime',
    width: 180,
    customRender({ text }) {
      return formatTime(text)
    },
  },
  // { title: '操作', width: 100, fixed: 'right' },
])
</script>
