<template>
  <div class="p-20px bg-white">
    <!-- Filter Section -->
    <a-form :model="filterForm" layout="inline" class="mb-20px">
      <a-form-item label="消息类型">
        <a-select v-model:value="filterForm.type" placeholder="请选择消息类型" allow-clear class="w-220px">
          <a-select-option v-for="type in messageTypes" :key="type.value" :value="type.value">
            {{ type.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="阅读状态">
        <a-select v-model:value="filterForm.readStatus" placeholder="请选择阅读状态" allow-clear class="w-220px">
          <a-select-option v-for="status in readStatuses" :key="status.value" :value="status.value">
            {{ status.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="时间范围">
        <range-picker v-model="filterForm.timeRange" class="w-350px" start-placeholder="开始日期" end-placeholder="结束日期" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" danger @click="onSearch" :loading="loading">查询</a-button>
        <a-button class="ml-10px" @click="onReset" :loading="loading">重置</a-button>
      </a-form-item>
    </a-form>

    <!-- Summary and Actions -->
    <div class="flex justify-between items-center mb-20px text-14px">
      <div class="flex items-center gap-20px">
        <div class="flex items-center">
          <a-badge status="error" class="mr-5px" />
          <span>未读: {{ unreadCount }}</span>
        </div>
        <div class="flex items-center">
          <a-badge status="success" class="mr-5px" />
          <span>已读: {{ readCount }}</span>
        </div>
        <div class="flex items-center">
          <a-badge status="default" class="mr-5px" />
          <span>总计: {{ totalCount }}</span>
        </div>
      </div>
      <div>
        <a-button danger @click="markAllAsReadHandler" :loading="loading">全部已读</a-button>
        <a-button danger class="ml-10px" @click="clearRead" :loading="loading">清空已读</a-button>
      </div>
    </div>

    <!-- Notifications List -->
    <div class="notification-list">
      <a-spin :spinning="loading">
        <div v-for="item in notifications" :key="item.id" class="notification-item" :class="{ 'is-read': item.read }">
          <div class="flex items-start p-15px">
            <div class="flex-shrink-0 w-120px text-center pt-5px">
              <a-tag :color="getMessageType(item.type)?.color">{{ getMessageType(item.type)?.label }}</a-tag>
            </div>
            <div class="flex-1 ml-20px">
              <div class="title-text font-bold mb-5px text-16px">
                <a-badge :dot="!item.read" class="mr-10px">
                  <span>{{ item.title }}</span>
                </a-badge>
              </div>
              <div class="content-text text-gray-500 text-14px" v-text="item.content"></div>
            </div>
            <div class="flex-shrink-0 ml-auto pl-20px text-right">
              <div class="timestamp-text text-gray-500 mb-5px text-14px">{{ item.timestamp }}</div>
              <a-space>
                <a-button type="link" @click="viewDetails(item)">查看</a-button>
                <a-button v-if="!item.read" type="link" @click="markAsRead(item)">已读</a-button>
                <a-button type="link" danger @click="deleteNotification(item)">删除</a-button>
              </a-space>
            </div>
          </div>
          <a-divider class="my-0!" />
        </div>
        <hm-empty v-if="notifications.length === 0" />
      </a-spin>
    </div>

    <!-- Pagination -->
    <div class="flex justify-end mt-20px">
      <a-pagination v-model:current="pagination.current" v-model:pageSize="pagination.pageSize"
        :total="pagination.total" show-quick-jumper show-size-changer />
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, computed, onMounted, watch } from 'vue';
import RangePicker from '~/components/range-picker/index.vue';
import { getMessageList, markMessageAsRead, deleteMessage, batchMarkAsRead, markAllAsRead, clearReadMessages } from '~/api/message';
import { userStore } from '~/store/user';
import { usemessageStore } from '~/store/message';

const user = userStore();
const messageStore = usemessageStore();

const filterForm = reactive({
  type: undefined,
  readStatus: undefined,
  timeRange: undefined,
});

const messageTypes = ref([
  { value: '询价管理', label: '询价管理', color: 'orange' },
  { value: '采购管理', label: '采购管理', color: 'purple' },
  { value: '收货管理', label: '收货管理', color: 'green' },
  { value: '付款管理', label: '付款管理', color: 'blue' },
  { value: '对账管理', label: '对账管理', color: 'red' }
]);

const readStatuses = ref([
  { value: false, label: '未读' },
  { value: true, label: '已读' },
]);

// 真实消息数据
const notifications = ref([]);
const loading = ref(false);

// 解析消息内容JSON
const parseMessageContent = (messageStr) => {
  try {
    return JSON.parse(messageStr);
  } catch (error) {
    console.error('解析消息内容失败:', error);
    return {
      title: '消息标题',
      message: messageStr,
      menuModule: '系统消息',
      detailPageUrl: ''
    };
  }
};

// 转换API数据为显示格式
const transformMessageData = (apiData) => {
  return apiData.map(item => {
    const parsedContent = parseMessageContent(item.message);
    return {
      id: item.logId,
      type: parsedContent.menuModule || '系统消息',
      title: parsedContent.title,
      content: parsedContent.message,
      read: item.status === 1, // API中 0-未读, 1-已读
      timestamp: item.createTime.split(' ')[0], // 只取日期部分
      detailUrl: parsedContent.detailPageUrl,
      orderNumber: item.orderNumber,
      rawData: item // 保存原始数据供操作使用
    };
  });
};

// 统计数据从接口获取
const totalUnreadCount = ref(0);
const totalMessageCount = ref(0);

const unreadCount = computed(() => totalUnreadCount.value);
const readCount = computed(() => totalMessageCount.value - totalUnreadCount.value);
const totalCount = computed(() => totalMessageCount.value);

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
});

// 加载消息数据
const loadMessages = async () => {
  if (!user.user.userId) {
    console.warn('用户未登录');
    return;
  }

  loading.value = true;
  try {
    const params = {
      userId: user.user.userId,
      current: pagination.current,
      size: pagination.pageSize,
    };

    // 添加筛选条件
    if (filterForm.type) {
      params.type = filterForm.type;
    }
    if (filterForm.readStatus !== undefined) {
      params.status = filterForm.readStatus ? 1 : 0;
    }
    if (filterForm.timeRange) {
      params.startTime = filterForm.timeRange.beginTime;
      params.endTime = filterForm.timeRange.endTime;
    }

    const res = await getMessageList(params);
    if (res.success && res.data) {
      notifications.value = transformMessageData(res.data.page.records);
      pagination.total = res.data.page.total;

      // 更新统计数据（基于接口返回的总体数据）
      totalUnreadCount.value = res.data.unReadCount || 0;
      totalMessageCount.value = res.data.page.total || 0;

      // 同步更新消息store中的未读数量
      messageStore.updatemsgCount();
    }
  } catch (error) {
    console.error('加载消息失败:', error);
  } finally {
    loading.value = false;
  }
};

// 监听分页变化
watch(() => pagination.current, () => {
  loadMessages();
});

watch(() => pagination.pageSize, () => {
  pagination.current = 1;
  loadMessages();
});

// 页面挂载时加载数据
onMounted(() => {
  loadMessages();
});

const onSearch = () => {
  pagination.current = 1; // 重置到第一页
  loadMessages();
};

const onReset = () => {
  filterForm.type = undefined;
  filterForm.readStatus = undefined;
  filterForm.timeRange = undefined;
  pagination.current = 1;
  loadMessages();
};

const getMessageType = (type) => {
  return messageTypes.value.find(t => t.value === type);
}

const markAllAsReadHandler = async () => {
  if (!user.user.userId) {
    console.warn('用户未登录');
    return;
  }

  try {
    const res = await markAllAsRead(user.user.userId);
    if (res.success) {
      // 重新加载数据
      loadMessages();
    }
  } catch (error) {
    console.error('全部标记已读失败:', error);
  }
};

const clearRead = async () => {
  try {
    const res = await clearReadMessages();
    if (res.success) {
      // 重新加载数据
      loadMessages();
    }
  } catch (error) {
    console.error('清空已读消息失败:', error);
  }
};

const viewDetails = (item) => {
  // 如果有详情页面URL，跳转到详情页面
  if (item.detailUrl) {
    navigateTo(item.detailUrl);
  } else {
    console.log('查看详情:', item);
  }
  // 标记为已读
  if (!item.read) {
    markAsRead(item);
  }
};

const markAsRead = async (item) => {
  if (item.read) return;

  if (!user.user.userId) {
    console.warn('用户未登录');
    return;
  }

  try {
    const res = await markMessageAsRead(item.rawData.logId, user.user.userId);
    if (res.success) {
      // 重新加载数据以获取最新的统计信息
      loadMessages();
    }
  } catch (error) {
    console.error('标记已读失败:', error);
  }
};

const deleteNotification = async (item) => {
  try {
    const res = await deleteMessage(item.rawData.logId);
    if (res.success) {
      // 重新加载数据
      loadMessages();
    }
  } catch (error) {
    console.error('删除消息失败:', error);
  }
};

</script>

<style lang="less" scoped>
.notification-item {
  transition: all 0.3s;
  border-left: 4px solid transparent;
  position: relative;

  :deep(.ant-btn-link) {
    padding: 0 5px;
  }

  &:hover {
    background-color: #f7f7f7;
  }

  // 未读消息样式 - 参考原型图
  &:not(.is-read) {
    background-color: #fef7f7; // 浅红色背景
    border-left-color: #ef4444; // 红色左边框

    .title-text {
      color: #1f2937;
      font-weight: 600;
    }

    .content-text {
      color: #374151;
    }

    &:hover {
      background-color: #fef2f2;
    }
  }

  // 已读消息样式
  &.is-read {
    background-color: #fafafa;
    border-left-color: #e5e7eb;

    .title-text,
    .content-text,
    .timestamp-text {
      color: #9ca3af;
    }

    .title-text {
      font-weight: normal;
    }

    :deep(.ant-badge-dot) {
      display: none;
    }

    &:hover {
      background-color: #f5f5f5;
    }
  }
}
</style>
