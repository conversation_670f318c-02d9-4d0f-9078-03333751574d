<template>
  <div>
    <a-form :label-col="{ flex: '80px' }">
      <div grid grid-cols-3 gap-x-16>
        <a-form-item label="角色名称">
          <a-input v-model:value="searchForm.roleName" placeholder="请输入" allowClear />
        </a-form-item>

        <!-- <a-form-item label="状态">
          <a-select
            placeholder="请选择"
            v-model:value="searchForm.status"
            w-180
            allowClear
          >
            <a-select-option :value="1">启用</a-select-option>
            <a-select-option :value="0">禁用</a-select-option>
          </a-select>
        </a-form-item> -->

        <a-form-item>
          <a-space>
            <a-button type="primary" @click="search">搜索</a-button>
            <a-button @click="reset">重置</a-button>
          </a-space>
        </a-form-item>
      </div>
    </a-form>

    <div>
      <a-button v-if="useAuth('auth:role:add')" type="primary" @click="onAdd">新建</a-button>
    </div>

    <!-- 列表 -->
    <div py-10>
      <a-table
        style="width: 100%"
        :columns="columns"
        row-key="id"
        size="middle"
        :data-source="dataSource"
        :pagination="pagination"
        @change="handleChanger"
      >
        <template #bodyCell="{ column, record, value }">
          <template v-if="column.dataIndex == 'action'">
            <a-button v-if="useAuth('auth:role:edit')" type="link" @click="onEdit(record.roleId)">编辑</a-button>

            <a-popconfirm v-if="useAuth('auth:role:del')" title="确认删除?" @confirm="onDel(record.roleId)">
              <a-button type="link" :disabled="record.builtin">删除</a-button>
            </a-popconfirm>
          </template>
        </template>
      </a-table>
    </div>

    <a-modal
      :width="800"
      :title="modalTitle"
      v-model:open="visible"
      @ok="confirm"
      :ok-button-props="{
        disabled: false,
      }"
    >
      <a-form mt-20 :label-col="{ flex: '80px' }" ref="formRef" :model="dataForm" :rules="rules">
        <div grid grid-cols-2 gap-x-16>
          <a-form-item label="角色名称" name="roleName">
            <a-input
              v-model:value="dataForm.roleName"
              :disabled="dataForm.builtin"
              placeholder="请输入"
              allowClear
              :maxlength="10"
              show-count
            />
          </a-form-item>

          <!-- <a-form-item label="状态">
            <a-select
              placeholder="请选择"
              v-model:value="dataForm.status"
              w-180
              allowClear
            >
              <a-select-option :value="1">启用</a-select-option>
              <a-select-option :value="0">禁用</a-select-option>
            </a-select>
          </a-form-item> -->
        </div>

        <a-form-item label="备注" name="remark">
          <a-input v-model:value="dataForm.remark" placeholder="请输入" allowClear :maxlength="100" show-count />
        </a-form-item>

        <a-divider orientation="left">权限信息</a-divider>

        <div class="grid grid-cols-2">
          <a-tree
            checkable
            :tree-data="menuTree"
            :fieldNames="{
              children: 'list',
              title: 'name',
              key: 'menuId',
            }"
            v-model:checkedKeys="checkedKeys"
            @check="onCheck"
            @select="onSelect"
            ref="treeRef"
          />

          <div>
            <a-checkbox
              v-for="item in currentButtons"
              :checked="buttonSet.has(item.menuId)"
              :key="item.menuId"
              :value="item.menuId"
              @change="(val) => changeButtonPermisson(val, item.menuId)"
            >
              {{ item.name }}
            </a-checkbox>
          </div>
        </div>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { companyStore } from '~/store/company'
import { omit } from 'lodash-es'

const useCompany = companyStore()
const pagination = ref({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showTotal: (total) => {
    return `共${total}条数据`
  },
})
const rules = {
  roleName: [{ required: true, message: '请输入角色名称', trigger: 'blur', whitespace: true }],
}
const searchForm = ref({})
const search = () => {
  pagination.value.current = 1
  getList()
}

const reset = () => {
  searchForm.value = {}
}

const handleChanger = (obj) => {
  pagination.value = obj
  getList()
}

const columns = [
  {
    title: '角色名称',
    dataIndex: 'roleName',
    width: 200,
    fixed: 'left',
    ellipsis: true,
  },
  // {
  //   title: '状态',
  //   dataIndex: 'status',
  //   width: 100,
  //   ellipsis: true
  // },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 300,
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 100,
    ellipsis: true,
  },
]

const dataSource = ref([])
const getList = () => {
  const merchantId = useCompany.company.shopCompanyId
  if (!merchantId) return
  http('/mall/p/sys/enterpriseRole/page', {
    method: 'get',
    params: {
      enterpriseId: merchantId,
      current: pagination.value.current,
      size: pagination.value.pageSize,
      ...searchForm.value,
    },
  }).then((res) => {
    useMall(res, (ret) => {
      dataSource.value = ret.records
      pagination.value.total = ret.total

      if (!dataSource.value.length && pagination.value.current > 1) {
        pagination.value.current = 1
        getList()
      }
    })
  })
}

const menuTree = ref([])
const buttonMap = ref({})
const menuMap = ref({})
const checkedKeys = ref([])
const buttonSet = ref(new Set())

const filter = (list = []) => {
  const map = {}
  const menuMap = {}
  const dfs = (list) => {
    const arr = []
    for (const item of list) {
      const _item = omit(item, 'list')
      menuMap[item.menuId] = item.type
      if (eq(item.type, 0, 1)) {
        arr.push(_item)
      }
      if (item.type == 2) {
        if (!map[item.parentId]) {
          map[item.parentId] = []
        }
        map[item.parentId].push(item)
      }
      if (item.list?.length) {
        _item.list = dfs(item.list)
      }
    }
    return arr
  }
  const tree = dfs(list)
  return [tree, map, menuMap]
}

const getMenuList = () => {
  const merchantId = useCompany.company.shopCompanyId
  if (!merchantId) return
  http(`/mall/p/sys/enterpriseMenu/getEnterpriseMenuTree`, {
    method: 'get',
    params: {},
  }).then((res) => {
    const [tree, _buttonMap, _menuMap] = filter(res.data)
    menuTree.value = tree
    buttonMap.value = _buttonMap
    menuMap.value = _menuMap
    setMenuList(res.data)
  })
}

const menuList = ref([])
const setMenuList = (menu) => {
  menu.forEach((d) => {
    menuList.value.push(d)
    if (d.list && d.list.length) {
      setMenuList(d.list)
    }
  })
}

const modalTitle = ref('新建角色')
const visible = ref(false)
const onAdd = () => {
  visible.value = true
  modalTitle.value = '新建角色'
  nextTick(() => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    checkedKeys.value = []
    dataForm.value = {}
    buttonSet.value = new Set()
  })
}

const onEdit = (roleId) => {
  visible.value = true
  modalTitle.value = '编辑角色'
  http(`/mall/p/sys/enterpriseRole/info/${roleId}`, {
    method: 'get',
    params: {},
  }).then((res) => {
    useMall(res, (ret) => {
      dataForm.value = {
        ...ret,
      }
      const list = []
      const selects = []
      ret.menuIdList.forEach((key) => {
        const type = menuMap.value[key]
        if (eq(type, 1)) {
          list.push(key)
        }
        if (eq(type, 2)) {
          selects.push(key)
        }
      })
      checkedKeys.value = list
      buttonSet.value = new Set(selects)
    })
  })
}

const onDel = (id) => {
  http('/mall/p/sys/enterpriseRole', {
    method: 'delete',
    body: [id],
  }).then((res) => {
    useMall(res, (ret) => {
      getList()
    })
  })
}

const active = ref(null)
const onSelect = (keys) => {
  const [key] = keys
  active.value = key
}
const currentButtons = computed(() => {
  return buttonMap.value[active.value] || []
})
const changeButtonPermisson = (val, key) => {
  if (val.target.checked) {
    buttonSet.value.add(key)
  } else {
    buttonSet.value.delete(key)
  }
}

const dataForm = ref({})
const formRef = ref(null)
const confirm = () => {
  const keys = [...checkedKeys.value, ...treeRef.value.halfCheckedKeys, ...buttonSet.value]
  formRef.value.validate().then(() => {
    http('/mall/p/sys/enterpriseRole', {
      method: dataForm.value.roleId ? 'put' : 'post',
      body: {
        ...dataForm.value,
        menuIdList: keys,
        enterpriseId: useCompany.company.shopCompanyId,
      },
    }).then((res) => {
      useMall(res, (ret) => {
        visible.value = false
        getList()
      })
    })
  })
}

const treeRef = ref()
onMounted(() => {
  getList()
  getMenuList()
})
</script>
