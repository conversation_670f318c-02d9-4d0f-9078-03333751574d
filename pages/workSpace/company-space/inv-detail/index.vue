<template>
  <div>
    <a-spin :spinning="loading">
      <a-page-header :title="title" @back="$router.go(-1)" :sub-title="subTitle">
        <template #extra>
          <Actions @refresh="fetchInfo" :info="info" />
        </template>
      </a-page-header>

      <a-card title="对账流程">
        <a-steps :current="steps.current" v-if="steps.current > -1">
          <a-step v-for="item in steps.list" :key="item.name" :title="item.name" :description="item.time"></a-step>
        </a-steps>
      </a-card>

      <a-card title="基本信息" class="mt-24px detail-card">
        <a-descriptions bordered :column="3">
          <a-descriptions-item label="订单号">{{ info.orderNumber }}</a-descriptions-item>
          <!-- <a-descriptions-item label="付款条件">
            <a-tag :color="getPayconditionColor(info.paymentTerm)">{{ getPayconditionText(info.paymentTerm) }}</a-tag>
          </a-descriptions-item> -->
          <a-descriptions-item label="对账周期">{{ getCycle(info) }}</a-descriptions-item>
          <a-descriptions-item label="总金额">{{ getPrice(info.totalAmount) }}</a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(info.status)">{{ getStatusText(info.status) }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="开票状态">
            <a-tag :color="getInvoiceColor(info.invoiceStatus)">{{ getInvoiceText(info.invoiceStatus) }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ formatTime(info.createTime) }}</a-descriptions-item>
          <a-descriptions-item label="对账时间">{{ formatTime(info.reconciliationTime) }}</a-descriptions-item>
        </a-descriptions>
      </a-card>

      <a-card title="物料明细" class="mt-24px">
        <form-wrapper>
          <a-form layout="inline">
            <a-form-item>
              <a-input v-model:value="form.prodName" placeholder="物料名称"></a-input>
            </a-form-item>
            <a-form-item>
              <a-input v-model:value="form.skuCode" placeholder="型号"></a-input>
            </a-form-item>
            <a-form-item>
              <a-input v-model:value="form.brandName" placeholder="品牌"></a-input>
            </a-form-item>
            <a-form-item>
              <a-input v-model:value="form.deliveryNoteNo" placeholder="收货单号"></a-input>
            </a-form-item>
            <a-form-item>
              <a-input v-model:value="form.sellOrderNo" placeholder="采购单号"></a-input>
            </a-form-item>
            <a-form-item>
              <range-picker
                v-model="form.receivingTimeFilter"
                start-placeholder="收货日期起"
                end-placeholder="收货日期止"
              ></range-picker>
            </a-form-item>
            <a-form-item>
              <a-button type="primary" @click="search">查询</a-button>
              <a-button @click="reset" ml-8>重置</a-button>
            </a-form-item>
          </a-form>
        </form-wrapper>
        <a-table
          :columns="columns"
          :data-source="data"
          :pagination="pagination"
          size="middle"
          :scroll="{ x: 1500 }"
          @resize-column="colResize"
          @change="pageChange"
          :loading="loading1"
        >
          <template #bodyCell="{ record, column }">
            <action-button
              v-if="column.dataIndex == 'deliveryNoteNo'"
              @click="
                navigateTo({
                  path: '/workSpace/company-space/return-detail',
                  query: {
                    orderNumber: record.deliveryNoteNo,
                  },
                })
              "
            >
              {{ record.deliveryNoteNo }}
            </action-button>
            <action-button
              v-if="column.dataIndex == 'sellOrderNumber'"
              @click="
                navigateTo({
                  path: '/workSpace/company-space/po-detail',
                  query: {
                    orderId: record.sellOrderNumber,
                  },
                })
              "
            >
              {{ record.sellOrderNumber }}
            </action-button>

            <template v-if="column.dataIndex == 'action'">
              <action-button v-if="canReturn(info.status)">退货</action-button>
            </template>
          </template>
          <template #summary>
            <a-table-summary-row>
              <a-table-summary-cell>
                <strong>合计</strong>
              </a-table-summary-cell>

              <a-table-summary-cell :col-span="6" class="text-center">
                <strong class="text-primary">{{ calcCount('price', data) }}</strong>
              </a-table-summary-cell>

              <a-table-summary-cell>
                <strong class="text-primary">{{ getPrice(calcCount('totalAmount', data)) }}</strong>
              </a-table-summary-cell>
            </a-table-summary-row>
          </template>
        </a-table>
      </a-card>

      <a-card title="相关单据" class="mt-24px">
        <a-tabs v-model:activeKey="activeTab">
          <a-tab-pane key="purchase" tab="采购单">
            <a-table
              :columns="purchaseColumns"
              :data-source="relatedSellOrders"
              :pagination="false"
              size="middle"
              :scroll="{ x: 800 }"
            >
              <template #bodyCell="{ record, column }">
                <action-button
                  v-if="column.dataIndex == 'orderNumber'"
                  @click="
                    navigateTo({
                      path: '/workSpace/company-space/po-detail',
                      query: {
                        orderId: record.orderNumber,
                      },
                    })
                  "
                >
                  {{ record.orderNumber }}
                </action-button>
                <a-tag v-if="column.dataIndex == 'status'" :color="getOrderStatusColor(record.status)">
                  {{ getOrderStatusText(record.status) }}
                </a-tag>
                <template v-if="column.dataIndex == 'createTime'">
                  {{ formatTime(record.createTime) }}
                </template>
                <template v-if="column.dataIndex == 'finishTime'">
                  {{ formatTime(record.finishTime) }}
                </template>
                <template v-if="column.dataIndex == 'totalAmount'">
                  {{ getPrice(record.totalAmount) }}
                </template>
                <template v-if="column.dataIndex == 'toBeReconciledAmount'">
                  {{ getPrice(record.toBeReconciledAmount) }}
                </template>
              </template>
            </a-table>
          </a-tab-pane>
          <a-tab-pane key="payment" tab="付款单">
            <a-table
              :columns="paymentColumns"
              :data-source="relatedPaymentOrders"
              :pagination="false"
              size="middle"
              :scroll="{ x: 800 }"
            >
              <template #bodyCell="{ record, column }">
                <action-button
                  v-if="column.dataIndex == 'orderNumber'"
                  @click="
                    navigateTo({
                      path: '/workSpace/company-space/pay-detail',
                      query: {
                        orderNumber: record.orderNumber,
                      },
                    })
                  "
                >
                  {{ record.orderNumber }}
                </action-button>
                <a-tag v-if="column.dataIndex == 'status'" :color="getPaymentOrderStatusColor(record.status)">
                  {{ getPaymentOrderStatusText(record.status) }}
                </a-tag>
                <template v-if="column.dataIndex == 'finishTime'">
                  {{ formatTime(record.finishTime) }}
                </template>
                <template v-if="column.dataIndex == 'payableTotalAmount'">
                  {{ getPrice(record.payableTotalAmount) }}
                </template>
                <template v-else-if="column.dataIndex == 'paidAmount'">
                  {{ getPrice(record.paidAmount) }}
                </template>
              </template>
            </a-table>
          </a-tab-pane>
        </a-tabs>
      </a-card>

      <div class="flex mt-24px">
        <div class="flex-1"></div>
        <Actions @refresh="fetchInfo" :info="info" />
      </div>
    </a-spin>
  </div>
</template>

<script setup>
import Actions from './components/actions.vue'
import { useInv } from '../inv/useInv'
import { useOrder } from '../po/components/useOrder'
import { usePay } from '../pay/usePay'

definePageMeta({
  pageName: '对账单详情',
})

onMounted(() => {
  fetchInfo()
  fetchData()
})

const steps = computed(() => {
  let current = -1
  const list = [
    {
      name: '待对账',
      time: formatTime(info.value.createTime),
    },
    {
      name: '已锁定',
      time: formatTime(info.value.reconciliationTime),
    },
    {
      name: '已结算',
    },
  ]
  list.forEach((item) => {
    if (item.time) {
      current++
    }
  })
  return {
    current,
    list,
  }
})

const fetchInfo = async () => {
  if (!orderNumber) {
    message.error('订单信息不存在')
    return
  }
  loading.value = true
  const [err, res] = await try_http(`/mall/p/reconciliation-statement/details/${orderNumber}`)
  loading.value = false
  if (!err) {
    info.value = res.data
  }
}

const { getStatusText, getStatusColor, getPaymentText, getPaymentColor, getCycle, getInvoiceText, getInvoiceColor } =
  useInv()

const { getStatusText: getOrderStatusText, getStatusColor: getOrderStatusColor } = useOrder()

const { getStatusText: getPaymentOrderStatusText, getStatusColor: getPaymentOrderStatusColor } = usePay()

const info = ref({})
const route = useRoute()

const orderNumber = route.query.orderNumber
const loading = ref(false)

const title = computed(() => {
  return info.value.orderNumber ? `对账单详情-${info.value.orderNumber}` : '对账单详情'
})

const subTitle = computed(() => {
  return '状态：' + getStatusText(info.value.status)
})

const columns = ref([
  { title: '物料名称', dataIndex: 'prodName', minWidth: 180, width: 180, resizable: true },
  {
    title: '品牌',
    width: 150,
    customRender({ record }) {
      return has(record.replaceBrandId) ? '研选' : record.brandName
    },
  },
  {
    title: '型号',
    dataIndex: 'skuCode',
    width: 180,
    customRender({ record }) {
      return has(record.replaceBrandId) ? record.replaceSkuCode : record.skuCode
    },
  },
  {
    title: '参考品牌',
    dataIndex: 'replaceBrandName',
    width: 150,
    customRender({ record }) {
      return has(record.replaceBrandId) ? record.brandName : '-'
    },
  },
  {
    title: '参考型号',
    dataIndex: 'replaceSkuCode',
    width: 180,
    customRender({ record }) {
      return has(record.replaceBrandId) ? record.skuCode : '-'
    },
  },
  { title: '数量', dataIndex: 'number', width: 80, resizable: true },
  {
    title: '单价',
    dataIndex: 'price',
    width: 120,
    customRender({ text }) {
      return getPrice(text)
    },
  },
  {
    title: '总价',
    dataIndex: 'totalAmount',
    width: 120,
    customRender({ text }) {
      return getPrice(text)
    },
  },
  {
    title: '收货日期',
    dataIndex: 'receivingTime',
    width: 150,
    customRender({ text }) {
      return formatTime(text)
    },
  },
  { title: '收货单号', dataIndex: 'deliveryNoteNo', width: 200 },
  { title: '采购单号', dataIndex: 'sellOrderNumber', width: 200 },
])

const purchaseColumns = ref([
  { title: '采购单号', dataIndex: 'orderNumber', width: 200 },
  { title: '状态', dataIndex: 'status', width: 100 },
  { title: '下单时间', dataIndex: 'createTime', width: 150 },
  { title: '结算时间', dataIndex: 'finishTime', width: 150 },
  { title: '总金额', dataIndex: 'totalAmount', width: 120 },
  { title: '本次对账金额', dataIndex: 'toBeReconciledAmount', width: 120 },
])

const paymentColumns = ref([
  { title: '付款单号', dataIndex: 'orderNumber', width: 200 },
  { title: '付款金额', dataIndex: 'payableTotalAmount', width: 120 },
  { title: '付款日期', dataIndex: 'finishTime', width: 150 },
  { title: '状态', dataIndex: 'status', width: 100 },
])

const form = ref({})
const data = ref([])
const loading1 = ref(false)

const activeTab = ref('purchase')
const relatedSellOrders = computed(() => info.value.relatedSellOrders || [])
const relatedPaymentOrders = computed(() => (info.value.relatedPaymentOrder ? [info.value.relatedPaymentOrder] : []))
const fetchData = async () => {
  loading1.value = true
  const [err, res] = await try_http('/mall/p/reconciliation-statement/item/page', {
    method: 'post',
    body: {
      current: page.current,
      size: page.size,
      orderNo: orderNumber,
      ...form.value,
    },
  })
  loading1.value = false
  if (!err) {
    data.value = res.data.records
    page.total = res.data.total
  }
}
const search = () => {
  page.current = 1
  fetchData()
}
const reset = () => {
  form.value = {}
  search()
}
const { page, pagination, pageChange } = usePage(fetchData)
</script>
