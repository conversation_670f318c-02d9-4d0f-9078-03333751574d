<template>
  <div class="flex gap-8px">
    <a-button type="primary" v-if="canInv(info.status)" v-loading-click="() => handleInv()">确认对账</a-button>
    <a-button type="primary" v-if="canApply(info.status, info.invoiceStatus)" @click="handleApply([info.orderId])">
      申请开票
    </a-button>
    <a-button type="primary" @click="handleExport(info.orderNumber)" v-if="canExport()">导出对账单</a-button>
    <!-- <a-button :loading="loading2" v-if="canReturn(info.status)" type="primary" @click="handleReturn">退货</a-button> -->
  </div>
</template>

<script setup>
import { useInv } from '../../inv/useInv'
const props = defineProps({
  info: {
    default: () => ({}),
  },
})

const emits = defineEmits(['refresh'])

const { inv, canInv, canApply, apply, handleExport, canExport } = useInv()

const handleInv = async () => {
  const [err] = await inv(props.info.orderNumber)
  if (!err) {
    message.success('操作成功')
    navigateTo('/workSpace/company-space/inv')
  }
}

const handleApply = async (ids) => {
  const [err] = await apply(ids)
  if (!err) {
    message.success('操作成功')
    emits('refresh')
  }
}
</script>
