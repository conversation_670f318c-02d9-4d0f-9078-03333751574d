<template>
  <a-modal
    v-model:open="visible"
    title="选择产品分类"
    @ok="handleOk"
    @cancel="handleCancel"
    :okButtonProps="{
      disabled: !selectedCategory,
    }"
  >
    <a-tree-select
      v-model:value="selectedCategory"
      :tree-data="categoryOptions"
      placeholder="请选择产品分类"
      :fieldNames="{
        children: 'children',
        label: 'categoryName',
        key: 'categoryId',
        value: 'categoryId',
      }"
      tree-node-filter-prop="categoryName"
      style="width: 100%"
      :tree-default-expand-all="true"
      :show-search="true"
      :getPopupContainer="(el) => el.parentElement"
    />
  </a-modal>
</template>

<script setup>
const visible = ref(false)
const selectedCategory = ref(null)
const categoryOptions = ref([])

const loadCategories = async () => {
  const [err, res] = await try_http('/mall/category/search')
  const dfs = (list) => {
    list.forEach((item) => {
      item.selectable = item.type == 'LEAF'
      if (item.children?.length) {
        dfs(item.children)
      }
    })
  }
  if (!err) {
    dfs(res.data)
    categoryOptions.value = res.data
  }
}
let _cb

const open = (row, cb) => {
  _cb = cb
  selectedCategory.value = row.categoryId || null
  visible.value = true
  loadCategories()
}

const handleOk = () => {
  _cb?.(selectedCategory.value)
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}

defineExpose({
  open,
})
</script>
