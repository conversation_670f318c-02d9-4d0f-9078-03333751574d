<template>
  <a-modal v-model:open="open" title="添加物料" @ok="handleOk" :confirm-loading="loading" width="700px">
    <!-- 智能解析区域 -->
    <div class="mb-4">
      <a-textarea
        v-model:value="parseText"
        placeholder="粘贴物料信息，自动识别并填充表单。例如：TI德州仪器 LM2596S-5.0/NOPB 稳压器芯片 数量：100个 交期：15天 备注：用于电源模块"
        :rows="3"
      />
      <div class="mt-8 flex justify-end">
        <a-button @click="clearParseText" class="mr-8">清空</a-button>
        <a-button type="primary" @click="parseContent" :disabled="!parseText" :loading="parseLoading">解析</a-button>
      </div>
    </div>

    <a-divider />

    <!-- 表单 -->
    <a-form :model="form" :rules="rules" ref="formRef" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <a-form-item label="物料名称" name="prodName">
        <a-input placeholder="请输入物料名称" v-model:value="form.prodName" />
      </a-form-item>

      <a-form-item label="型号/规格" name="skuCode">
        <a-input placeholder="请输入型号或规格" v-model:value="form.skuCode" />
      </a-form-item>

      <a-form-item label="品牌" name="brandName">
        <a-auto-complete
          v-model:value="form.brandName"
          :options="brandOptions"
          @search="searchBrand"
          placeholder="请输入品牌名称"
        />
      </a-form-item>

      <a-form-item label="采购数量" name="number">
        <a-input-number placeholder="请输入数量" :min="1" class="w-full" v-model:value="form.number" :precision="0" />
      </a-form-item>

      <a-form-item label="期望交期(天)">
        <a-input-number
          placeholder="请输入期望交期"
          :min="0"
          class="w-full"
          v-model:value="form.expectTerm"
          :precision="0"
        />
      </a-form-item>

      <a-form-item label="备注">
        <a-textarea v-model:value="form.notes" placeholder="请输入备注信息（选填）" :rows="2" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
let _cb
const open = ref(false)
const form = ref({})
const formRef = ref()
const parseText = ref('')
const parseLoading = ref(false)

const rules = {
  prodName: { required: true, message: '请输入物料名称' },
  skuCode: { required: true, message: '请输入物料型号' },
  brandName: { required: true, message: '请输入品牌' },
  number: [{ required: true, message: '请输入数量' }],
}

const showAdd = (cb) => {
  _cb = cb
  form.value = {}
  parseText.value = ''
  open.value = true
}

const loading = ref(false)

const brandOptions = ref([])
const searchBrand = async (name) => {
  const [err, res] = await try_http('/mall/brand/list', {
    params: {
      status: 1,
      name,
    },
  })
  if (!err) {
    console.log('res', res)
    brandOptions.value = res.data.map((item) => {
      return {
        value: item.name,
      }
    })
  }
}

// 智能解析功能
const parseContent = async () => {
  parseLoading.value = true
  const [err, res] = await try_http('/mall/p/llm/agent/parseMaterial', {
    method: 'post',
    body: {
      user_input: parseText.value,
    },
  })
  parseLoading.value = false
  if (err) return
  const { brand, modelCode, productName, count, exceptedDeliveryDays, remark } = res.data
  form.value = {
    prodName: productName,
    brandName: brand,
    skuCode: modelCode,
    number: count,
    expectTerm: exceptedDeliveryDays,
    notes: remark,
  }
  console.log('res', res)
}

// 清空解析文本
const clearParseText = () => {
  parseText.value = ''
}

const handleOk = async () => {
  await formRef.value.validateFields()
  loading.value = true
  const [err, res] = await try_http('/mall/p/personal-inquiry/import-boms', {
    method: 'post',
    body: [form.value],
  })
  loading.value = false
  if (err) return
  if (res.data.status) {
    message.success('操作成功')
    open.value = false
    _cb()
  } else {
    message.error('当前sku和询价器已有数据冲突')
  }
}

defineExpose({
  showAdd,
})
</script>
