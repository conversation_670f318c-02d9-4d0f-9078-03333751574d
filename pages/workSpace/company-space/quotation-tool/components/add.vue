<template>
  <a-modal v-model:open="open" :title="title" :width="900">
    <div mb-12 max-w-300>
      <a-input-search
        v-model:value="skuCode"
        enter-button
        placeholder="搜索物料型号"
        @search="searchSku"
      ></a-input-search>
    </div>
    <a-table
      :loading="loading"
      :scroll="{ x: 'max-content' }"
      :columns="columns"
      :data-source="list"
      row-key="rowKey"
      :row-selection="{
        type: 'radio',
        selectedRowKeys: select.keys,
        onChange: (keys, rows) => {
          select.keys = keys
          select.rows = rows
        },
      }"
      :pagination="false"
    >
      <template #bodyCell="{ column, record, text }">
        <div w-200 truncate :title="text" v-if="column.dataIndex == 'productName'">{{ text }}</div>
        <div v-if="column.dataIndex == 'action'">
          <a-button type="link" :disabled="record.souce == 0" @click="goDetail(record)">详情</a-button>
        </div>
        <a-tooltip v-if="column.dataIndex == 'categoryName'" :title="record.categoryPath">
          {{ text }}
        </a-tooltip>
      </template>
    </a-table>

    <Temp ref="tempRef"></Temp>

    <template #footer>
      <a-button @click="open = false">取消</a-button>
      <a-button v-if="!isFromSearch" type="primary" @click="showAdd">添加临时物料</a-button>
      <a-button :loading="confirmLoading" type="primary" @click="handleOk" :disabled="!select.keys.length">
        确定
      </a-button>
    </template>
  </a-modal>
</template>

<script setup>
import Temp from './temp.vue'
const open = ref(false)
const title = ref('')
const isFromSearch = ref(false)

const { formatTrade } = useFormatter()

const skuCode = ref('')
const count = ref(0)

let callback
let row
const add = (cb) => {
  callback = cb
  title.value = '添加物料'
  isFromSearch.value = true
  select.keys = []
  select.rows = []
  list.value = []
  skuCode.value = ''
  count.value = 0
  open.value = true
}

const edit = (record, cb) => {
  callback = cb
  row = record
  title.value = '匹配物料'
  isFromSearch.value = false
  select.keys = []
  select.rows = []
  list.value = []
  skuCode.value = record.skuCode
  count.value = 0
  open.value = true
  searchSku(skuCode.value)
}

const showManualAdd = (cb) => {
  tempRef.value?.showAdd(cb)
}

const columns = ref([
  {
    title: '物料名称',
    dataIndex: 'productName',
  },
  {
    title: '型号',
    dataIndex: 'skuCode',
  },
  {
    title: '品牌',
    dataIndex: 'brandName',
  },
  {
    title: '产品分类',
    dataIndex: 'categoryName',
  },
  // {
  //   title: '交期',
  //   dataIndex: 'tradeTerm',
  //   customRender({ text }) {
  //     return formatTrade(text)
  //   },
  // },
  // {
  //   title: '单价',
  //   dataIndex: 'price',
  // },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
])

const list = ref([])
const select = reactive({
  keys: [],
  rows: [],
})
const loading = ref(false)
const searchSku = async (keyword) => {
  if (!has(keyword)) return
  loading.value = true
  const res = await request.post('/selection/search-by-sku', {
    onlyMall: true,
    keyword,
    pageSize: 5,
  })
  loading.value = false
  count.value++
  useRes(res, () => {
    list.value = res.data.map((item) => {
      item.rowKey = genUUID()
      return item
    })
    res.data.forEach(async (item) => {
      const res = await http('/mall/p/inquiry-list/total-discount-price', {
        method: 'post',
        body: {
          prodId: item.prodId,
          skuId: item.skuId,
          skuCode: item.skuCode,
          number: 1,
        },
      })
      useMall(
        res,
        () => {
          item.price = res.data.skuPrice
          item.tradeTerm = res.data.tradeTerm
        },
        () => {},
      )
    })
  })
}

const router = useRouter()
const goDetail = (row) => {
  let _path
  if (has(row.brandCode) && row.id.startsWith('mall-')) {
    _path = `/brand/${row.brandCode}/${row.id}`
  } else {
    _path = `/parts/${row.id}`
  }
  const path = router.resolve({
    path: _path,
    query: {
      typeCode: row.skuCode,
    },
  })

  const href = path.href

  window.open(href)
}

const confirmLoading = ref(false)
const handleOk = async () => {
  const [sku] = select.rows
  const { id, skuId, skuCode, productName, prodId, source } = sku
  if (title.value == '添加物料') {
    confirmLoading.value = true
    const [err, res] = await try_http('/mall/p/personal-inquiry', {
      method: 'post',
      body: {
        partId: id,
        skuId,
        skuCode,
        prodName: productName,
      },
    })
    confirmLoading.value = false
    if (err) return
    message.success('操作成功')
    callback()
    open.value = false
  } else {
    confirmLoading.value = true
    const [err, res] = await try_http('/mall/p/personal-inquiry/replace', {
      method: 'put',
      body: {
        skuId: +skuId,
        skuCode,
        prodId,
        source,
        id: row.id,
      },
    })
    confirmLoading.value = false
    if (err) return
    message.success('操作成功')
    callback()
    open.value = false
  }
}

const tempRef = ref()
const showAdd = () => {
  tempRef.value?.showAdd(() => {
    callback()
    open.value = false
  })
}

defineExpose({
  add,
  edit,
  showManualAdd,
})
</script>
