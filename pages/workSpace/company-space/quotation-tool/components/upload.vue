<template>
  <a-upload
    v-if="useAuth('procure:ansPrice:import')"
    :show-upload-list="false"
    action=""
    accept=".xlsx"
    :customRequest="customRequest"
    :before-upload="beforeUpload"
  >
    <a-space-compact>
      <a-button type="primary">导入BOM</a-button>
      <a-tooltip title="下载模板">
        <a-button type="primary" :icon="h(DownloadOutlined)" @click.stop="downTemplate"></a-button>
      </a-tooltip>
    </a-space-compact>
  </a-upload>

  <a-modal
    v-model:open="open"
    title="导入BOM"
    width="90%"
    @ok="handleOk"
    @cancel="handleCancel"
    :confirm-loading="loading"
  >
    <a-spin :spinning="parsing">
      <vxe-table
        :style="{
          '--vxe-ui-table-column-padding-default': 0,
          '--vxe-ui-table-cell-padding-left': '1px',
          '--vxe-ui-table-cell-padding-right': '1px',
        }"
        border
        height="500"
        :data="data"
        :scoll-y="{ enabled: true, gt: 0 }"
        :column-config="{ resizable: true }"
        :row-config="{ height: 40 }"
      >
        <vxe-column type="seq" width="60">
          <template #header>
            <div></div>
          </template>
          <template #default="{ seq }">
            <div px-8 text-center>{{ seq }}</div>
          </template>
        </vxe-column>
        <vxe-column v-for="column in columns" :title="column.title">
          <template #header>
            <a-dropdown trigger="click">
              <div flex justify-between items-center px-8 cursor-pointer h-40>
                {{ has(column.title) ? column.title : '请选择' }}
                <down-square-outlined />
              </div>
              <template #overlay>
                <a-menu>
                  <a-menu-item v-for="item in selects" :key="item.key" @click="changeHeader(column, item)">
                    {{ item.label }}
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </template>
          <template #default="{ row }">
            <parse-item :record="row" :column="column" @remove="remove"></parse-item>
          </template>
        </vxe-column>
        <vxe-column title="操作">
          <template #default="{ row }">
            <a-popconfirm title="确定删除?" @confirm="remove(row)">
              <span text-primary cursor-pointer ml-8>删除</span>
            </a-popconfirm>
          </template>
        </vxe-column>
      </vxe-table>
    </a-spin>

    <div mt-10>
      <a-tooltip>
        <div class="i-mdi-question-mark-circle inline-block relative top-5 text-20 cursor-pointer"></div>
        <template #title>
          <p>1.以上表格内容由系统自动识别生成，请根据实际情况调整每一列的表头，其中“型号”,“品牌”不可缺失</p>
          <p>2.“数量”一栏中，每个单元格内必须为数字</p>
        </template>
      </a-tooltip>
      使用说明
    </div>
  </a-modal>
</template>

<script setup lang="jsx">
// import { VxeTable, VxeColumn, VxeUI } from 'vxe-table'
// import 'vxe-table/lib/style.css'
import dayjs from 'dayjs'
import { bomMap, parse, revertKeys } from './parse'
import ParseItem from './parseItem.vue'
import { DownloadOutlined } from '@ant-design/icons-vue'
import fileDownload from 'js-file-download'

// VxeUI.setLanguage('zh-CN')

const open = ref(false)
const columns = ref([])
const data = ref([])
const parsing = ref(false)

const emits = defineEmits(['ok'])

const customRequest = async (e) => {
  open.value = true
  parsing.value = true
  const res = await parse(e.file)
  parsing.value = false

  if (res) {
    const _columns = res.columns.map((item) => {
      const { value, label } = item
      return {
        title: label,
        dataIndex: value,
      }
    })
    columns.value = _columns

    data.value = res.arr
    // console.log('%c Line:125 🥛 data', 'color:#437D68', data.value)
  }

  // const formData = new FormData()
  // formData.append('file', data.file)
  // const res = await http('/mall/p/personal-inquiry/import-skus', {
  //   method: 'post',
  //   body: formData,
  // })
  // useMall(res, () => {
  //   message.success('导入成功')
  //   fetchData()
  //   updateAnsCount()
  // })
}

const downTemplate = async () => {
  const res = await $fetch('/bom.xlsx', {
    responseType: 'blob',
  })
  fileDownload(res, 'bomTemplate.xlsx')
}

const beforeUpload = (file) => {
  const isXlsx = file.name.includes('xlsx')
  if (!isXlsx) {
    message.error('您只能上传xlsx格式的模板')
  }
  return isXlsx
}

const selects = computed(() => {
  return Object.entries(bomMap).map((item) => {
    const [label, value] = item
    return {
      label,
      value,
    }
  })
})

const changeHeader = (column, item) => {
  const { label, value } = item
  const find = columns.value.find((col) => col.dataIndex == value)
  if (find) {
    data.value.forEach((d) => {
      let temp = d[value]
      d[value] = d[column.dataIndex]
      d[column.dataIndex] = temp
    })
    find.dataIndex = column.dataIndex
    find.title = column.title

    column.title = label
    column.dataIndex = value
  } else {
    data.value.forEach((d) => {
      d[value] = d[column.dataIndex]
      delete d[column.dataIndex]
    })
    column.title = label
    column.dataIndex = value
  }
}

const mergeSame = (list) => {
  const arr = []
  let hasSame = false
  const map = new Map()
  for (const item of list) {
    const { brandName, skuCode } = item
    let key = skuCode
    if (brandName) key += brandName
    if (!map.has(key)) {
      item.number = Number(item.number ?? 0)
      map.set(key, item)
      arr.push(item)
    } else {
      hasSame = true
      const _item = map.get(key)
      _item.number += Number(item.number ?? 0)
    }
  }
  return [arr, hasSame]
}

const loading = ref(false)
const handleOk = async () => {
  const list = []
  const find = columns.value.find((item) => item.dataIndex == 'skuCode')
  if (!find) return message.warning('表格列缺失：型号')
  for (const [index, row] of data.value.entries()) {
    const obj = {}
    const keys = Object.keys(revertKeys)
    for (const key of keys) {
      const value = row[key]
      if (!has(value)) {
        if (key == 'skuCode') return message.warn(`第${index + 1}行缺少型号`)
        if (key == 'brandName') return message.warn(`第${index + 1}行缺少品牌`)
      } else {
        if (key == 'expectTerm') {
          if (Number(value) != value) {
            const isValid = dayjs(value).isValid()
            if (isValid) {
              const d = Math.ceil(dayjs(value).diff(dayjs(), 'd', true))
              obj[key] = d < 0 ? 0 : d
            }
          } else {
            obj[key] = value
          }
        } else if (key == 'number') {
          if (Number(value) != value) {
            return message.warn('数量必须为数字')
          } else {
            obj[key] = value
          }
        } else if (key == 'replaceStatus') {
          obj[key] = value == '是' ? 1 : 0
        } else {
          obj[key] = value
        }
      }
    }
    list.push(obj)
  }

  // const [mergeList, hasSame] = mergeSame(list)
  loading.value = true
  const [err, res] = await try_http('/mall/p/personal-inquiry/import-boms', {
    method: 'post',
    body: list,
  })
  loading.value = false
  if (err) return
  if (res.data.status) {
    // if (hasSame) {
    //   message.success('导入成功，重复的sku已合并')
    // } else {
    // }
    message.success('导入成功')
    open.value = false
    emits('ok')
  } else {
    Modal.confirm({
      title: '提示',
      showCancel: false,
      content: (
        <div>
          {Object.keys(res.data.errors).map((key, index) => {
            const [line, ...others] = res.data.errors[key]
            if (others.length) {
              return (
                <div>
                  {index + 1}、型号
                  <span class="text-red mx-2">{key}</span>第<span class="text-red mx-2">{line}</span>
                  行和第
                  <span class="text-red mx-2">{others.join('，')}</span>
                  行冲突
                </div>
              )
            } else {
              return (
                <div>
                  {index + 1}、型号
                  <span class="text-red mx-2">{key}</span>第<span class="text-red mx-2">{line}</span>
                  行和询价器已有数据冲突
                </div>
              )
            }
          })}
        </div>
      ),
    })
  }
}

const handleCancel = () => {
  data.value = []
  columns.value = []
}

const remove = (row) => {
  data.value = data.value.filter((item) => item != row)
}
</script>

<style lang="less" scoped>
.upload-table {
  :deep(td.ant-table-cell) {
    padding: 0 !important;
    height: 32px;
  }
}
</style>
