import E, { ValueType } from 'exceljs'
import dayjs from 'dayjs'

export const bomMap = {
  物料名称: 'prodName',
  型号: 'skuCode',
  品牌: 'brandName',
  数量: 'number',
  期望交期: 'expectTerm',
  备注: 'notes',
  产品分类: 'categoryName',
  接受平替: 'replaceStatus',
}
const bomKeys = Object.keys(bomMap)
export const revertKeys = Object.entries(bomMap).reduce((res, item) => {
  const [key, val] = item
  res[val] = key
  return res
}, {})

export const parse = async (data) => {
  const workbook = new E.Workbook()
  await workbook.xlsx.load(data)
  const sheet = workbook.worksheets[0]
  const rows = sheet.getRows(1, sheet.rowCount)
  let matrix = []
  if (rows) {
    rows.forEach((row, index) => {
      let arr = []
      for (let column = 1; column <= sheet.columnCount; column++) {
        const cell = row.getCell(column)
        let { value, type, isMerged } = cell
        if (index) {
          const top = rows[index - 1].getCell(column)
          if (cell.isMergedTo(top)) {
            let belongTop = true
            for (let c = 1; c <= sheet.columnCount; c++) {
              let _top = rows[index - 1].getCell(c).value
              let _current = rows[index].getCell(c).value
              if (has(_current) && _current != _top) {
                belongTop = false
                break
              }
            }
            if (belongTop) {
              break
            }
          }
        }
        if (isMerged) {
          if (column > 1) {
            const left = row.getCell(column - 1)
            if (cell.isMergedTo(left)) {
              break
            }
          }
        }
        if (type == 4) {
          // 日期
          value = dayjs(value).format('YYYY-MM-DD')
        } else if (type == 5) {
          // 超链接
          value = value.text
        } else if (type == 8) {
          // 富文本
          value = cell.text
        } else if (type == 1) {
          // 合并单元格
          value = value.text
        } else if (type == 6) {
          value = value.result
        }
        arr.push(value)
      }
      arr = filterArr(arr)
      if (arr.length) {
        matrix.push(arr)
      }
    })
  }
  const parsed = parseMatrix(matrix)
  const header = getHeader(parsed)

  if (header) {
    return getTable(header, matrix)
  }
}

function filterArr(arr) {
  let flag = false
  return arr.reduceRight((res, item) => {
    if (flag) {
      res.unshift(item)
    } else if (item != null) {
      flag = true
      res.unshift(item)
    }
    return res
  }, [])
}

function levenshteinDistance(s, t) {
  const dp = Array.from({ length: s.length + 1 }, () => Array(t.length + 1).fill(0))

  for (let i = 0; i <= s.length; i++) {
    dp[i][0] = i
  }
  for (let j = 0; j <= t.length; j++) {
    dp[0][j] = j
  }

  for (let i = 1; i <= s.length; i++) {
    for (let j = 1; j <= t.length; j++) {
      if (s[i - 1] === t[j - 1]) {
        dp[i][j] = dp[i - 1][j - 1]
      } else {
        dp[i][j] = Math.min(dp[i - 1][j] + 1, dp[i][j - 1] + 1, dp[i - 1][j - 1] + 1)
      }
    }
  }

  return dp[s.length][t.length]
}

function similarity(s, t) {
  return s.length - levenshteinDistance(s, t)
}

function lcs(s1, s2) {
  const dp = Array(s1.length + 1)
    .fill(null)
    .map(() => Array(s2.length + 1).fill(0))
  for (let i = 1; i <= s1.length; i++) {
    for (let j = 1; j <= s2.length; j++) {
      if (s1[i - 1] === s2[j - 1]) {
        dp[i][j] = dp[i - 1][j - 1] + 1
      } else {
        dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1])
      }
    }
  }

  // Reconstruct LCS
  let i = s1.length,
    j = s2.length
  let lcsStr = ''
  while (i > 0 && j > 0) {
    if (s1[i - 1] === s2[j - 1]) {
      lcsStr = s1[i - 1] + lcsStr
      i--
      j--
    } else if (dp[i - 1][j] > dp[i][j - 1]) {
      i--
    } else {
      j--
    }
  }
  return lcsStr
}

function isUniqArr(arr) {
  const set = new Set()
  for (const item of arr) {
    if (!set.has(item)) {
      set.add(item)
    } else {
      return false
    }
  }
  return true
}

const parseMatrix = (matrix) => {
  const matched = []
  const matchedKey = new Set()
  for (const row of matrix) {
    // if (!isUniqArr(row)) {
    //   continue
    // }
    let count = 0
    const matchMap = {}
    row.forEach((item) => {
      if (typeof item != 'string') return
      for (let i = 0; i < bomKeys.length; i++) {
        const name = bomKeys[i]
        const s = lcs(name, item)
        if (s.length >= 2) {
          if (!matchedKey.has(name)) {
            matchedKey.add(name)
          }
          if (item == s) {
            count += 100
          } else {
            count += 1
          }
          if (!matchMap[item]) {
            matchMap[item] = [name, s]
          } else {
            const match = matchMap[item]
            if (s.length > match[1].length) {
              matchMap[item] = [name, s]
            }
          }
        }
      }
    })
    matched.push({ count, matchMap, row })
  }
  return matched
}

const getHeader = (parsed) => {
  let maxL = -1,
    maxS = 0
  let header, fb
  for (const item of parsed) {
    const { count, row } = item
    if (count > maxS) {
      maxS = count
      header = item
    }
    if (row.length > maxL) {
      maxL = row.length
      fb = item
    }
  }
  if (maxS) {
    return header
  }

  return parsed[0]
  // if (maxL) {
  //   return
  // }
}

const getTable = (header, matrix) => {
  const { row, matchMap } = header
  const idx = matrix.findIndex((item) => item == row)
  const arr = []
  let columns
  if (idx > -1) {
    columns = row.map((item) => {
      if (matchMap[item]) {
        const label = matchMap[item][0]
        return {
          label,
          value: bomMap[label],
        }
      } else {
        return {
          label: item,
          value: has(item) ? item : genUUID(),
        }
      }
    })

    // 计算最大列
    let columnCount = columns.length
    for (let i = idx + 1; i < matrix.length; i++) {
      const count = matrix[i].length
      if (count > columnCount) {
        columnCount = count
      }
    }

    if (columnCount > columns.length) {
      const dis = columnCount - columns.length
      for (let i = 0; i < dis; i++) {
        columns.push({
          label: null,
          value: genUUID(),
        })
      }
    }

    for (let i = idx + 1; i < matrix.length; i++) {
      const record = {}
      columns.forEach((item, index) => {
        record[item.value] = matrix[i][index]
      })
      arr.push(record)
    }
    return { columns, arr }
  }
}
