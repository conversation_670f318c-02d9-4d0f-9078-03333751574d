<template>
  <a-input
    ref="inputRef"
    v-if="edit.cell == record && edit.dataIndex == column.dataIndex"
    v-model:value="record[column.dataIndex]"
    :status="status"
    @blur="blur"
  ></a-input>
  <div
    hover-border="1px solid primary"
    truncate
    h-32
    flex
    items-center
    px-8
    hover-px-7
    cursor-pointer
    v-else
    @click="showEdit(record, column)"
  >
    <span v-if="has(record[column.dataIndex])">
      {{ record[column.dataIndex] }}
    </span>
    <a-tooltip v-else-if="['skuCode'].includes(column.dataIndex)" :title="`${column.title}不能为空`">
      <div class="i-mdi-alert-circle inline-block relative top-5 text-red text-20 mr-4 cursor-pointer"></div>
    </a-tooltip>
  </div>
</template>

<script setup>
defineProps({
  record: {
    type: Object,
    default: () => ({}),
  },
  column: {
    type: Object,
    default: () => ({}),
  },
})

const edit = reactive({
  cell: {},
  dataIndex: null,
})

const inputRef = ref()
const status = ref('')
const showEdit = (row, column) => {
  edit.cell = row
  edit.dataIndex = column.dataIndex
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const blur = () => {
  edit.cell = {}
  edit.dataIndex = null
}

const emits = defineEmits(['remove'])

const remove = (row) => {
  emits('remove', row)
}
</script>
