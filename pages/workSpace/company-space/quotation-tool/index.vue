<template>
  <div>
    <div class="h-[calc(100vh-164px)] flex flex-col">
      <!-- <div class="px-8"> -->
      <!--   <Nav> -->
      <!--     <nuxt-link to="/">首页</nuxt-link> -->
      <!--     <div>询价器</div> -->
      <!--   </Nav> -->
      <!-- </div> -->

      <div class="mb-12 px-8">
        <client-only>
          <a-space>
            <a-dropdown v-if="useAuth('procure:ansPrice:add')">
              <a-button type="primary">
                添加物料
                <down-outlined />
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="showSearch">从平台搜索</a-menu-item>
                  <a-menu-item @click="showManualAdd">手动添加</a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>

            <upload v-if="useAuth('procure:ansPrice:import')" @ok="handleImport"></upload>
            <!-- <a-button type="primary"> -->
            <!--   导入BOM -->
            <!--   <DownloadOutlined /> -->
            <!-- </a-button> -->

            <a-button v-if="useAuth('procure:ansPrice:del')" danger type="primary" @click="showBatchRemove">
              批量删除
            </a-button>
          </a-space>
        </client-only>
      </div>

      <div ref="tableWrapperRef" class="flex-1 px-8 overflow-hidden">
        <client-only>
          <a-spin :spinning="loading">
            <vxe-table
              v-if="height"
              :data="tableData"
              :height="height"
              auto-resize
              ref="tableRef"
              :key="height"
              :scroll-y="{ enabled: true, gt: 0 }"
              :row-config="{
                keyField: 'id',
              }"
              :column-config="{
                resizable: true,
              }"
              :checkbox-config="{
                reserve: true,
              }"
            >
              <vxe-column type="checkbox" width="60">
                <template #header="{ checked, indeterminate }">
                  <a-checkbox :checked="checked" :indeterminate="indeterminate" @click="toggleAll"></a-checkbox>
                </template>
                <template #checkbox="{ checked, indeterminate, row }">
                  <a-checkbox :checked="checked" :indeterminate="indeterminate" @click="toggleRow(row)"></a-checkbox>
                </template>
              </vxe-column>
              <vxe-column title="物料名称" width="250" show-overflow="ellipsis">
                <template #default="{ row }">
                  <Data :data="errStatus(row)">
                    <template #default="{ data }">
                      <!-- <a-tooltip v-if="data.state != 0" :title="data.msg"> -->
                      <!--   <div -->
                      <!--     class="i-mdi-alert-circle inline-block relative top-5 text-blue text-20 mr-4 cursor-pointer" -->
                      <!--   ></div> -->
                      <!-- </a-tooltip> -->

                      <span :title="row.prodName">
                        {{ row.prodName }}
                      </span>
                    </template>
                  </Data>
                </template>
              </vxe-column>

              <!-- <vxe-column title="产品分类" width="200"> -->
              <!--   <template #default="{ row }"> -->
              <!--     <a-tooltip -->
              <!--       v-if="row.categoryName && !row.categoryId" -->
              <!--       title="该产品分类在平台未找到，您可以点击分类名称更新产品分类" -->
              <!--     > -->
              <!--       <div -->
              <!--         class="i-mdi-alert-circle inline-block relative top-5 text-blue text-20 mr-4 cursor-pointer" -->
              <!--       ></div> -->
              <!--     </a-tooltip> -->
              <!--     <span px-8> -->
              <!--       {{ row.categoryName }} -->
              <!--     </span> -->
              <!--     <a-button type="link" @click="openCategorySelect(row)"> -->
              <!--       <i class="i-a-edit-outlined"></i> -->
              <!--     </a-button> -->
              <!--   </template> -->
              <!-- </vxe-column> -->

              <vxe-column title="型号" width="200">
                <template #default="{ row }">
                  <Data :data="errStatus(row)">
                    <template #default="{ data }">
                      <div truncate link v-if="data.state == 0" @click="goSku(row.skuCode)">
                        {{ row.skuCode }}
                      </div>
                      <div v-else truncate>{{ row.skuCode }}</div>
                    </template>
                  </Data>
                </template>
              </vxe-column>
              <vxe-column title="品牌" field="brandName" width="200"></vxe-column>
              <!-- <vxe-column title="交期" width="100"> -->
              <!--   <template #default="{ row }"> -->
              <!--     {{ getText(formatTrade(row.tradeTerm), row) }} -->
              <!--   </template> -->
              <!-- </vxe-column> -->
              <vxe-column title="期望交期（天）" width="150">
                <template #default="{ row }">
                  <a-input-number
                    v-model:value="row.expectTerm"
                    @change="changeRow(row)"
                    @blur="confirmNumber(row)"
                    :precision="0"
                    :min="0"
                    :step="1"
                  />
                </template>
              </vxe-column>

              <vxe-column title="数量" width="100">
                <template #default="{ row }">
                  <a-input-number
                    v-model:value="row.number"
                    @change="changeRow(row)"
                    @blur="confirmNumber(row)"
                    :precision="0"
                    :min="1"
                    :step="1"
                  ></a-input-number>
                </template>
              </vxe-column>

              <vxe-column title="接受平替" width="140" align="center">
                <template #default="{ row }">
                  <a-checkbox :checked="row.replaceStatus == 1" @change="changeIfAcceptReplace(row)"></a-checkbox>
                </template>
              </vxe-column>

              <!-- <vxe-column title="折后单价" width="100"> -->
              <!--   <template #default="{ row }"> -->
              <!--     {{ getText(getPrice(row.price), row) }} -->
              <!--   </template> -->
              <!-- </vxe-column> -->
              <!---->
              <!-- <vxe-column title="总价" width="100"> -->
              <!--   <template #default="{ row }"> -->
              <!--     {{ getText(getPrice(row.totalPrice), row) }} -->
              <!--   </template> -->
              <!-- </vxe-column> -->

              <vxe-column title="备注" field="notes" min-width="300">
                <template #default="{ row }">
                  <div class="flex items-center w-full overflow-hidden">
                    <a-popover :title="row.notes">
                      <div class="truncate">
                        {{ row.notes }}
                      </div>
                    </a-popover>
                    <a-button class="shrink-0" type="link" @click="editNotes(row)">
                      <i class="i-a-edit-outlined"></i>
                    </a-button>
                  </div>
                </template>
              </vxe-column>

              <vxe-column title="操作" fixed="right" width="200">
                <template #default="{ row }">
                  <a-space>
                    <!-- <div -->
                    <!--   class="link" -->
                    <!--   v-if="useAuth('procure:ansPrice:match') && row.source == 2 && row.matchedStatus == 0" -->
                    <!--   @click="showMatch(row)" -->
                    <!-- > -->
                    <!--   匹配商品 -->
                    <!-- </div> -->
                    <!-- <template v-else> -->
                    <!--   <a-button type="link" @click="goDetail(record)" :disabled="record.source != 0">查看详情</a-button> -->
                    <!--   <div v-if="useAuth('procure:ansPrice:set')" class="link" @click="openSetting(row)">设置备选</div> -->
                    <!-- </template> -->
                    <a-popconfirm
                      v-if="useAuth('procure:ansPrice:del')"
                      @confirm="remove(row)"
                      title="是否删除我的询价"
                    >
                      <div class="link">删除</div>
                    </a-popconfirm>
                  </a-space>
                </template>
              </vxe-column>
            </vxe-table>
          </a-spin>
        </client-only>
      </div>
      <div class="h-12"></div>
    </div>

    <div flex text-14 items-center gap-10 h-60 bg-white pl-16 z-99>
      <div w-full h-full mx-auto flex items-center gap-10>
        <!-- <div>项目</div> -->
        <!-- <div w-300> -->
        <!--   <a-select -->
        <!--     class="w-full!" -->
        <!--     :field-names="{ -->
        <!--       label: 'name', -->
        <!--       value: 'projectNo', -->
        <!--     }" -->
        <!--     @change="handleChangeProject" -->
        <!--     placeholder="请选择项目" -->
        <!--     :options="projects" -->
        <!--   ></a-select> -->
        <!-- </div> -->
        <div>截止时间</div>
        <a-date-picker
          v-model:value="project.deadline"
          placeholder="请选择截止时间"
          :disabled-date="disabledDate"
          :disabled-time="disabledTime"
          show-time
          format="YYYY-MM-DD HH:mm"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
        <div flex-1></div>
        <div>
          已选择
          <span text-red font-bold>{{ selects.length }}</span>
          个商品，共
          <span text-red font-bold>{{ tableData.length }}</span>
          个商品
        </div>

        <a-button w-100 h-full text-16 font-bold rounded-0 border-none @click="clear">清空</a-button>

        <a-button
          w-100
          h-full
          text-16
          font-bold
          rounded-0
          v-if="useAuth('procure:ansPrice:submit')"
          type="primary"
          :loading="submitLoading"
          @click="submit"
        >
          提交
        </a-button>
      </div>
    </div>

    <a-modal title="编辑备注" v-model:open="showNotes" @ok="confirmEdit" :confirm-loading="editLoading">
      <a-form>
        <a-form-item label="备注">
          <a-textarea v-model:value="row.notes" placeholder="请输入" :maxlength="200" show-count></a-textarea>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 产品分类选择 -->
    <CategorySelectModal ref="categorySelectModal"></CategorySelectModal>
    <!-- 设置备选 -->
    <set-back ref="setRef"></set-back>
    <!-- 添加匹配商品 -->
    <add ref="addRef"></add>
    <!-- 临时商品 -->
    <temp ref="tempRef"></temp>
  </div>
</template>

<script lang="ts" setup>
import dayjs, { Dayjs } from 'dayjs'
import { omit } from 'lodash-es'
import SetBack from '~/components/SetBack/index.vue'
import CategorySelectModal from './components/CategorySelectModal.vue'
import { sideWidgetsStore } from '~/store/sideWidgets'
import add from './components/add.vue'
import upload from './components/upload.vue'
import { VxeTableInstance } from 'vxe-table'
import { DownOutlined } from '@ant-design/icons-vue'
import Temp from './components/temp.vue'
import { companyStore } from '~/store/company'
definePageMeta({
  noSidebar: 1,
  pageName: '询价器',
})

const { formatTrade } = useFormatter()
const { updateAnsCount } = sideWidgetsStore()

const route = useRoute()
const ChatKey = route.query.ck

const getText = (text, record) => {
  const status = errStatus(record)
  if (status.state == 1) {
    return text
  } else if (status.state == 2) {
    return has(text) ? text : '--'
  } else {
    return text
  }
}

const projectNo = ref('')

const tableData = ref<Obj[]>([])
const loading = ref(false)
const fetchData = async () => {
  loading.value = true
  const [err, res] = await try_http<Obj[]>('/mall/p/personal-inquiry/list', {
    headers: {
      ChatKey,
    },
  })
  loading.value = false
  if (!err) {
    tableData.value = res.data
  }
}

const selects = ref<obj[]>([])

const clear = () => {
  Modal.confirm({
    title: '该操作将清空询价器，是否继续？',
    async onOk() {
      const res = await http('/mall/p/personal-inquiry/clear', {
        method: 'delete',
      })
      useMall(res, () => {
        message.success('操作成功')
        fetchData()
        updateAnsCount()
      })
    },
  })
}

const { isPersonal } = storeToRefs(companyStore())
const submitLoading = ref(false)
const submit = async () => {
  const rows = selects.value
  if (!rows.length) {
    message.warn('请先选择至少一条数据！')
    return
  }
  // if (!project.value.projectNo) {
  //   message.warn('请选择项目号')
  //   return
  // }
  if (!project.value.deadline) {
    message.warn('请选择截止时间')
    return
  }
  // @ts-ignore
  // const keys = select.rows
  //   .filter((item) => {
  //     const { source, matchedStatus, validStatus } = item
  //     if (source == 2 && matchedStatus == 0) return false
  //     if (source == 1 || validStatus != 0) return false
  //     return true
  //   })
  //   .map((item: any) => item.id)
  // @ts-ignore
  const keys = rows.map((item) => item.id)
  if (!keys.length) {
    message.warn('请选择可以询价的商品')
    return
  }
  submitLoading.value = true
  const [err, res] = await try_http('/mall/p/inquiry-list-item/commit', {
    headers: {
      ChatKey,
    },
    method: 'post',
    body: {
      projectNo: project.value.projectNo,
      deadline: project.value.deadline,
      personalInquiryIds: keys,
    },
  })
  submitLoading.value = false
  if (err) return
  if (isPersonal.value) {
    const { status, requestQuota, totalQuota, usedQuota } = res.data
    if (status) {
      Guide.show(`今日已询价物料：${usedQuota}/${totalQuota}种`)
    } else {
      Guide.show(`今日已询价物料：${usedQuota}/${totalQuota}种，剩余额度不足`)
      return
    }
  }
  message.success('操作成功')
  updateAnsCount()
  fetchData()
  navigateTo({
    // path: `/workSpace/company-space/ans-price?projectNo=${project.value.projectNo}`,
    path: `/workSpace/company-space/ans-price`,
  })
}

// 编辑
const row = ref<Obj>({})
const showNotes = ref(false)
const editLoading = ref(false)
const editNotes = (_row) => {
  row.value = pick(_row, ['id', 'number', 'notes', 'expectTerm', 'categoryId', 'replaceStatus'])
  showNotes.value = true
}
const confirmEdit = async () => {
  editLoading.value = true
  const res = await http('/mall/p/personal-inquiry', {
    method: 'put',
    body: row.value,
  })
  editLoading.value = false
  useMall(res, () => {
    // message.success('操作成功')
    showNotes.value = false
    updateAnsCount()
    fetchData()
  })
}
const changeRow = (_row) => {
  row.value = pick(_row, ['id', 'number', 'notes', 'expectTerm', 'categoryId', 'replaceStatus'])
}
const confirmNumber = (_row) => {
  row.value = pick(_row, ['id', 'number', 'notes', 'expectTerm', 'categoryId', 'replaceStatus'])
  loading.value = true
  confirmEdit()
}

const changeIfAcceptReplace = ({ replaceStatus, ...others }) => {
  row.value = {
    replaceStatus: replaceStatus == 0 ? 1 : 0,
    ...pick(others, ['id', 'number', 'notes', 'expectTerm', 'categoryId']),
  }
  confirmEdit()
}

// 操作
// 查看详情
const goDetail = (row) => {
  const url = `${useRuntimeConfig().public.baseUrl.VITE_MALL_URL}/detail?prodId=${row.prodId}`
  window.open(url)
}

// 删除
const remove = async (row) => {
  const res = await http('/mall/p/personal-inquiry/' + row.id, {
    method: 'delete',
  })
  useMall(res, () => {
    message.success('删除成功')
    updateAnsCount()
    fetchData()
  })
}

const showBatchRemove = () => {
  const keys = selects.value.map((item) => item.id)
  if (!keys.length) {
    message.error('请选择至少一条数据')
    return
  }
  Modal.confirm({
    title: '提示',
    content: '是否删除选中的内容？',
    onOk: async () => {
      await batchRemove(keys)
    },
  })
}

const batchRemove = async (keys) => {
  const res = await http('/mall/p/personal-inquiry/removeBatchByIds', {
    method: 'delete',
    body: keys,
  })
  useMall(res, () => {
    message.success('操作成功')
    fetchData()
    updateAnsCount()
  })
}

// 设置备选
const setRef = ref<InstanceType<typeof SetBack>>()
const openSetting = (record) => {
  let list: Obj[] = []
  if (!record.alternatives) {
    list = [omit(record, 'alternatives')]
  } else {
    list = record.alternatives
  }
  const master = record.skuId
  setRef.value?.open(list, master, record.number, record.prodId, async (show, data) => {
    loading.value = true
    const res = await http('/mall/p/personal-inquiry', {
      method: 'put',
      body: {
        id: record.id,
        alternatives: data,
      },
    })
    loading.value = false
    useMall(res, () => {
      show.value = false
      fetchData()
    })
  })
}

const addRef = ref<InstanceType<typeof add>>()
const tempRef = ref<InstanceType<typeof Temp>>()
const showSearch = () => {
  addRef.value?.add(() => {
    fetchData()
    updateAnsCount()
  })
}

const showManualAdd = () => {
  tempRef.value?.showAdd(() => {
    fetchData()
    updateAnsCount()
  })
}

const showMatch = (record) => {
  addRef.value?.edit(record, () => {
    fetchData()
    updateAnsCount()
  })
}

const errStatus = (record) => {
  const { source, matchedStatus, validStatus } = record
  if (source == 2 && matchedStatus == 0)
    return { state: 1, msg: '未匹配或不支持交易的商品，请设置备选商品，或作为临时商品直接提交询价' }
  if (source == 1 || validStatus != 0)
    return { state: 2, msg: '未匹配或不支持交易的商品，请手动更换商品，或作为临时商品直接提交询价' }
  return { state: 0 }
}

const goSku = async (code: string) => {
  if (!has(code)) return
  const res = await request.post('/selection/search-suggestion', {
    pageNo: 1,
    pageSize: 1,
    searchType: 'BY_TYPE',
    keyword: code,
  })
  useRes(res, () => {
    const [part] = res.data
    if (part.partId) {
      navigateTo({
        path: `/parts/${part.partId}`,
        query: {
          typeCode: code,
        },
      })
    }
  })
}

const handleImport = () => {
  fetchData()
  updateAnsCount()
}

const tableRef = ref<VxeTableInstance>()
const categorySelectModal = ref()

const openCategorySelect = (row) => {
  categorySelectModal.value.open(row, async (categoryId) => {
    const [err, res] = await try_http('/mall/p/personal-inquiry', {
      method: 'put',
      body: {
        id: row.id,
        categoryId,
      },
    })
    if (!err) {
      message.success('更新产品分类成功')
      fetchData()
    }
  })
}
const toggleRow = (row) => {
  tableRef.value?.toggleCheckboxRow(row)
  selects.value = tableRef.value?.getCheckboxRecords() || []
}
const toggleAll = () => {
  tableRef.value?.toggleAllCheckboxRow()
  selects.value = tableRef.value?.getCheckboxRecords() || []
}

const projects = ref<Obj[]>([])
const project = ref<Obj>({
  deadline: dayjs().add(3, 'd').format('YYYY-MM-DD') + ' 23:59:59',
})
const getDefault = () => {
  const [item] = projects.value
  if (item) {
    project.value.projectNo = item.projectNo
  }
}
const fetchProjects = async () => {
  const [err, res] = await try_http('/mall/p/sell-project/search')
  if (!err) {
    projects.value = res.data
    getDefault()
  }
}
const handleChangeProject = (_, option) => {
  project.value.projectNo = option.projectNo
}

const tableWrapperRef = ref()
const { height } = useElementSize(tableWrapperRef)

const range = (start: number, end: number) => {
  const result: number[] = []
  for (let i = start; i < end; i++) {
    result.push(i)
  }
  return result
}

const disabledDate = (current: Dayjs) => {
  return current && current < dayjs().subtract(1, 'day')
}

const disabledTime = (current: Dayjs) => {
  const now = dayjs()
  const selectedDate = dayjs(current ?? undefined)
  if (selectedDate.isBefore(now)) {
    project.value.deadline = now.format('YYYY-MM-DD HH:mm:ss')
  }

  if (selectedDate.isSame(now, 'day')) {
    const currentHour = now.hour()
    const currentMinute = now.minute()
    const currentSecond = now.second()

    if (selectedDate.hour() > currentHour) {
      return {
        disabledHours: () => range(0, currentHour),
        disabledMinutes: () => [],
        disabledSeconds: () => [],
      }
    } else if (selectedDate.hour() === currentHour) {
      return {
        disabledHours: () => range(0, currentHour),
        disabledMinutes: () => range(0, currentMinute),
        disabledSeconds: () => range(0, currentSecond),
      }
    } else {
      return {
        disabledHours: () => range(0, 24),
        disabledMinutes: () => range(0, 60),
        disabledSeconds: () => range(0, 60),
      }
    }
  }

  if (selectedDate.isBefore(now, 'day')) {
    return {
      disabledHours: () => range(0, 24),
      disabledMinutes: () => range(0, 60),
      disabledSeconds: () => range(0, 60),
    }
  }

  return {}
}

onMounted(() => {
  fetchData()
  fetchProjects()
})
</script>
