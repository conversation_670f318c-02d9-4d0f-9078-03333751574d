<template>
  <div>
    <a-spin :spinning="loading">
      <a-page-header :title="title" @back="$router.go(-1)" :sub-title="subTitle">
        <template #extra>
          <Actions @refresh="fetchInfo" :info="info" />
        </template>
      </a-page-header>

      <a-card title="订单流程">
        <a-steps size="small" :current="process.current">
          <a-step v-for="item in process.list" :key="item.title" :title="item.title">
            <template #description>
              <div>{{ formatTime(item.time) || '未开始' }}</div>
            </template>
          </a-step>
        </a-steps>
      </a-card>

      <a-card title="基本信息" class="mt-24px detail-card">
        <a-row :gutter="24">
          <a-col :span="8">
            <div class="info-item">
              <span class="label">订单号：</span>
              <span class="value">{{ info.orderNumber }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <span class="label">订单状态：</span>
              <span class="value">
                <a-tag :color="getStatusColor(info.status)">{{ getStatusText(info.status) }}</a-tag>
              </span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <span class="label">下单时间：</span>
              <span class="value">{{ formatTime(info.createTime) }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <span class="label">采购员：</span>
              <span class="value">{{ info.userName }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <span class="label">联系电话：</span>
              <span class="value">{{ info.userMobile }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <span class="label">付款方式：</span>
              <span class="value">{{ getPayment(info.paymentModel) }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <span class="label">付款条件：</span>
              <span class="value">{{ getPayconditionText(info.paymentTerm) }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <span class="label">总金额：</span>
              <span class="value important">{{ getPrice(calcCount('totalAmount', info.items)) }}</span>
            </div>
          </a-col>
          <!-- <a-col :span="8">
            <div class="info-item">
              <span class="label">当前剩余账期额度：</span>
              <span class="value important">{{ getPrice(info.residue) }}</span>
            </div>
          </a-col> -->
        </a-row>
      </a-card>

      <a-card title="物料明细" class="mt-24px">
        <a-table
          :columns="columns"
          :data-source="info.items || []"
          :pagination="false"
          size="middle"
          :scroll="{ x: 1500 }"
        >
          <template #bodyCell="{ record, column }">
            <template v-if="column.dataIndex == 'number' && info.status == 0">
              <a-input-number
                :min="record.miniOrderQuantity ?? 1"
                :precision="0"
                v-model:value="record.number"
                class="w-full"
                @change="(val) => changeNumber(val, record)"
              ></a-input-number>
            </template>

            <a-tag v-if="column.dataIndex == 'deliveryStatus'" :color="getDeliveryColor(record.deliveryStatus)">
              {{ getDeliveryText(record.deliveryStatus) }}
            </a-tag>

            <a-tag v-if="column.dataIndex == 'financialStatus'" :color="getFinanceColor(record.financialStatus)">
              {{ getFinanceText(record.financialStatus) }}
            </a-tag>

            <template v-if="column.dataIndex == 'action'">
              <action-button
                v-if="info.status == 0"
                :disabled="info.items.length <= 1"
                @click="removeMaterialItem(record)"
              >
                删除
              </action-button>
            </template>
          </template>
        </a-table>

        <div class="summary-section">
          <a-row justify="end">
            <a-col :span="8">
              <div class="summary-item">
                <span class="label">物料总数：</span>
                <span class="value">{{ calcCount('number', info.items) }} 件</span>
              </div>
              <div class="summary-item">
                <span class="label">物料总价：</span>
                <span class="value important">{{ getPrice(calcCount('totalAmount', info.items)) }}</span>
              </div>
              <!-- <div class="summary-item"> -->
              <!--   <span class="label"> -->
              <!--     运费 -->
              <!--     <a-tooltip> -->
              <!--       <template #title> -->
              <!--         <div>1. 本明细的价格若未做特殊说明，均为含税价格。</div> -->
              <!--         <div>2. 在询价单转为订单后，将根据订单总价加收运费，具体规则如下：</div> -->
              <!--         <div v-for="(item, index) in ruleMsg" :key="index">{{ item }}</div> -->
              <!--       </template> -->
              <!--       <InfoCircleFilled /> -->
              <!--     </a-tooltip> -->
              <!--     ： -->
              <!--   </span> -->
              <!--   <span class="value" v-if="info.status != 0">{{ getPrice(info.freightFee) }}</span> -->
              <!-- </div> -->
              <div class="summary-item total">
                <span class="label">应付总额：</span>
                <span class="value important">
                  {{ getPrice((info.status == 0 ? 0 : info.freightFee ?? 0) + calcCount('totalAmount', info.items)) }}
                </span>
              </div>
            </a-col>
          </a-row>
        </div>
      </a-card>

      <receive-and-invoice :info="info" class="mt-24px"></receive-and-invoice>

      <div class="flex mt-24px">
        <div class="flex-1"></div>
        <Actions @refresh="fetchInfo" :info="info" />
      </div>
    </a-spin>
  </div>
</template>

<script setup>
import { useOrder } from '../po/components/useOrder'
import receiveAndInvoice from './components/receive-and-invoice.vue'
import Actions from './components/actions.vue'

definePageMeta({
  pageName: '订单详情',
})

onMounted(() => {
  fetchInfo()
  getRule()
})

const fetchInfo = async () => {
  if (!orderId) {
    message.error('订单信息不存在')
    return
  }
  loading.value = true
  const [err, res] = await try_http(`/mall/p/sell-order/details/${orderId}`)
  loading.value = false
  if (!err) {
    info.value = res.data
  }
}

const { getRule, ruleMsg } = useFreightFeeRule()

const {
  getStatusText,
  getStatusColor,
  getPayment,
  getPayCondition,
  getDeliveryColor,
  getDeliveryText,
  getFinanceText,
  getFinanceColor,
  getCount,
} = useOrder()

const info = ref({})
const route = useRoute()

const orderId = route.query.orderId
const loading = ref(false)

const title = computed(() => {
  return info.value.orderNumber ? `订单详情-${info.value.orderNumber}` : '订单详情'
})

const subTitle = computed(() => {
  return '状态：' + getStatusText(info.value.status)
})

const process = computed(() => {
  const list = [
    { title: '创建草稿', time: info.value.createTime },
    { title: '提交订单', time: info.value.commitTime },
    { title: '订单确认', time: info.value.confirmTime },
    { title: '完成订单', time: info.value.finishTime },
  ]
  let current = -1
  list.forEach((item) => {
    if (item.time) {
      current++
    }
  })
  return {
    list,
    current,
  }
})

const columns = ref([
  { title: '物料名称', dataIndex: 'prodName', width: 180 },
  {
    title: '品牌',
    width: 150,
    customRender({ record }) {
      return has(record.replaceBrandId) ? '研选' : record.brandName
    },
  },
  {
    title: '型号',
    dataIndex: 'skuCode',
    width: 180,
    customRender({ record }) {
      return has(record.replaceBrandId) ? record.replaceSkuCode : record.skuCode
    },
  },
  {
    title: '参考品牌',
    dataIndex: 'replaceBrandName',
    width: 150,
    customRender({ record }) {
      return has(record.replaceBrandId) ? record.brandName : '-'
    },
  },
  {
    title: '参考型号',
    dataIndex: 'replaceSkuCode',
    width: 180,
    customRender({ record }) {
      return has(record.replaceBrandId) ? record.skuCode : '-'
    },
  },
  { title: '分类', dataIndex: 'categoryName', width: 120 },
  // { title: '来源询价单', dataIndex: 'rfqNo', width: 150 },
  { title: '数量', dataIndex: 'number', width: 80 },
  // { title: '最小起订量', dataIndex: 'miniOrderQuantity', width: 120 },
  {
    title: '已发货数量',
    dataIndex: 'deliveredNumber',
    customRender({ text }) {
      return getCount(info.value.status, text)
    },
    width: 110,
  },
  {
    title: '已收货数量',
    dataIndex: 'receivedNumber',
    customRender({ text }) {
      return getCount(info.value.status, text)
    },
    width: 110,
  },
  {
    title: '已取消数量',
    dataIndex: 'canceledNumber',
    customRender({ text }) {
      return getCount(info.value.status, text)
    },
    width: 110,
  },
  { title: '物流状态', dataIndex: 'deliveryStatus', width: 100 },
  { title: '财务状态', dataIndex: 'financialStatus', width: 100 },
  {
    title: '单价（¥）',
    dataIndex: 'price',
    width: 100,
    customRender: ({ text }) => getPrice(text),
  },
  {
    title: '总价（¥）',
    dataIndex: 'totalAmount',
    width: 120,
    customRender: ({ text }) => getPrice(text),
  },
  { title: '预计到货日期', dataIndex: 'estimatedDeliveryDate', width: 150 },
  { title: '操作', dataIndex: 'action', width: 180, fixed: 'right' },
])

const removeMaterialItem = (row) => {
  info.value.items = info.value.items.filter((item) => item.id != row.id)
}

const changeNumber = (val, row) => {
  row.totalAmount = val * row.price
}
</script>

<style lang="less" scoped>
.detail-card {
  .info-item {
    display: flex;
    margin-bottom: 16px;

    .label {
      color: rgba(0, 0, 0, 0.65);
      min-width: 90px;
      flex-shrink: 0;
    }

    .value {
      flex: 1;
      font-weight: 500;

      &.important {
        color: #f94c30;
        font-weight: 600;
      }
    }
  }
}

.summary-section {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px dashed #e8e8e8;

  .summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 0 24px;

    .label {
      color: rgba(0, 0, 0, 0.65);
    }

    .value {
      font-weight: 500;

      &.important {
        color: #f5222d;
        font-weight: 600;
        font-size: 16px;
      }
    }

    &.total {
      font-size: 16px;
      margin-top: 12px;
      padding-top: 12px;
      border-top: 1px solid #e8e8e8;
    }
  }
}
</style>
