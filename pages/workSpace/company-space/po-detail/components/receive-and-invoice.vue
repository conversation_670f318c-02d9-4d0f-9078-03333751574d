<template>
  <a-card title="收货与开票信息">
    <a-tabs default-active-key="1">
      <a-tab-pane key="1" tab="收货信息">
        <div v-if="info.status == 0" class="mb-16px">
          <a-button type="primary" @click="showReceive">选择收货信息</a-button>
        </div>
        <a-descriptions :column="2" bordered v-if="address">
          <a-descriptions-item label="联系人">{{ address.receiver }}</a-descriptions-item>
          <a-descriptions-item label="手机号">{{ address.mobile }}</a-descriptions-item>
          <a-descriptions-item label="地区" :span="2">{{ getAddress(address) }}</a-descriptions-item>
          <a-descriptions-item label="详细地址" :span="2">{{ address.addr }}</a-descriptions-item>
          <a-descriptions-item label="备注" :span="2">
            <a-input v-model:value="info.remarks" placeholder="请输入"></a-input>
          </a-descriptions-item>
        </a-descriptions>
      </a-tab-pane>
      <a-tab-pane key="2" tab="开票信息">
        <!-- <div v-if="info.status == 0" class="mb-16px"> -->
        <!--   <a-button type="primary" @click="showInvoice">选择开票信息</a-button> -->
        <!-- </div> -->
        <a-descriptions :column="2" bordered v-if="invoice">
          <a-descriptions-item label="发票抬头">{{ invoice.title }}</a-descriptions-item>
          <a-descriptions-item label="税号">{{ invoice.invoiceNum }}</a-descriptions-item>
          <a-descriptions-item label="注册地址" :span="2">{{ invoice.invoiceAddr }}</a-descriptions-item>
          <a-descriptions-item label="电话">{{ invoice.invoiceTel }}</a-descriptions-item>
          <a-descriptions-item label="开户行">{{ invoice.invoiceBank }}</a-descriptions-item>
          <a-descriptions-item label="银行账户" :span="2">{{ invoice.invoiceBankNum }}</a-descriptions-item>
        </a-descriptions>
      </a-tab-pane>
    </a-tabs>

    <a-modal v-model:open="receiveOpen" title="选择收货信息" :width="800" @ok="chooseAddr">
      <a-table
        :loading="loading1"
        :columns="receiverColumns"
        :data-source="receiverList"
        :pagination="false"
        :row-selection="{ type: 'radio', selectedRowKeys: selects.keys1, onChange: onReceiverSelectChange }"
        row-key="id"
        size="small"
      >
        <template #emptyText>
          <div>
            您当前所在的企业暂未添加收货信息，
            <action-button @click="navigateTo('/workSpace/company-space/addr-info')">前往添加</action-button>
          </div>
        </template>
      </a-table>
    </a-modal>

    <a-modal v-model:open="invoiceOpen" title="选择开票信息" :width="800" @ok="chooseInvoice">
      <a-table
        :loading="loading2"
        :columns="invoiceColumns"
        :data-source="invoiceList"
        :pagination="false"
        :row-selection="{ type: 'radio', selectedRowKeys: selects.keys2, onChange: onInvoiceSelectChange }"
        :scroll="{ x: 1500 }"
        row-key="id"
        size="small"
      >
        <template #emptyText>
          <div>
            您当前所在的企业暂未添加开票信息，
            <action-button @click="navigateTo('/workSpace/company-space/invoice-info')">前往添加</action-button>
          </div>
        </template>
      </a-table>
    </a-modal>
  </a-card>
</template>

<script setup>
import { companyStore } from '~/store/company'

const props = defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
})
const { info } = toRefs(props)

const { company } = storeToRefs(companyStore())
const address = computed(() => props.info.address)
const invoice = computed(() => props.info.invoice)

const getAddress = ({ province, city, area }) => {
  return [province, city, area].join('/')
}

const selects = reactive({
  keys1: [],
  rows1: [],
  keys2: [],
  rows2: [],
})

const receiveOpen = ref(false)
const receiverColumns = [
  { title: '联系人', dataIndex: 'receiver', key: 'receiver' },
  { title: '联系电话', dataIndex: 'mobile', key: 'mobile' },
  {
    title: '地区',
    customRender({ record }) {
      return getAddress(record)
    },
  },
  { title: '详细地址', dataIndex: 'addr', key: 'addr' },
  {
    title: '默认',
    dataIndex: 'commonAddr',
    key: 'commonAddr',
    customRender: ({ text }) => (text == 1 ? '✓' : ''),
  },
]
const receiverList = ref([])
const loading1 = ref(false)
const showReceive = async () => {
  loading1.value = true
  receiveOpen.value = true
  selects.keys1 = []
  selects.rows1 = []
  const [err, res] = await try_http('/mall/shop/merchantAddr/page', {
    params: {
      type: 0,
      merchantId: company.value.shopCompanyId,
    },
  })
  loading1.value = false
  if (!err) {
    receiverList.value = res.data.records || []
    if (address.value?.id) {
      selects.keys1 = [address.value.id]
      const row = receiverList.value.find((item) => item.id == address.value.id)
      if (row) {
        selects.rows1 = [row]
      }
    }
  }
}
const onReceiverSelectChange = (keys, rows) => {
  selects.keys1 = keys
  selects.rows1 = rows
}
const chooseAddr = () => {
  if (!selects.rows1.length) {
    message.warn('请选择收货信息')
    return
  }
  const [row] = selects.rows1
  info.value.address = row
  receiveOpen.value = false
}

// 添加状态变量
const invoiceOpen = ref(false)
const invoiceList = ref([])
const loading2 = ref(false)

// 添加开票信息列定义
const invoiceColumns = [
  { title: '发票抬头', dataIndex: 'title', width: 200 },
  { title: '税号', dataIndex: 'invoiceNum', width: 180 },
  { title: '注册地址', dataIndex: 'invoiceAddr', width: 250 },
  { title: '电话', dataIndex: 'invoiceTel', width: 150 },
  { title: '开户行', dataIndex: 'invoiceBank', width: 200 },
  { title: '银行账户', dataIndex: 'invoiceBankNum', width: 200 },
]

// 添加开票信息选择方法
const showInvoice = async () => {
  loading2.value = true
  invoiceOpen.value = true
  selects.keys2 = []
  selects.rows2 = []
  const [err, res] = await try_http('/mall/shop/invoice/page', {
    params: {
      type: 0,
      merchantId: company.value.shopCompanyId,
      size: 999,
    },
  })
  loading2.value = false
  if (!err) {
    invoiceList.value = res.data.records || []
    if (info.value.invoice?.id) {
      selects.keys2 = [info.value.invoice.id]
      const row = invoiceList.value.find((item) => item.id == info.value.invoice.id)
      if (row) {
        selects.rows2 = [row]
      }
    }
  }
}

// 添加选择回调
const onInvoiceSelectChange = (keys, rows) => {
  selects.keys2 = keys
  selects.rows2 = rows
}

// 添加确认选择方法
const chooseInvoice = () => {
  if (!selects.rows2.length) {
    message.warn('请选择开票信息')
    return
  }
  const [row] = selects.rows2
  info.value.invoice = row
  invoiceOpen.value = false
}
</script>
