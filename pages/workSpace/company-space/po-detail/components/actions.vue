<template>
  <div class="flex gap-8px">
    <a-button :loading="loading1" v-if="canEdit(info.status)" @click="stash">暂存</a-button>
    <a-button :loading="loading2" v-if="canSubmit(info.status)" type="primary" @click="handleSubmit">提交</a-button>
    <a-button
      type="primary"
      v-if="canDownload(info.status)"
      @click="handleDownload(info.orderContractFileUrl, info.orderNumber)"
    >
      下载合同
    </a-button>
  </div>
</template>

<script setup>
import { useOrder } from '../../po/components/useOrder'

const props = defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
})

const { info } = toRefs(props)

const { canEdit, canSubmit, submit, canDownload, handleDownload, priceSubmit } = useOrder()

const emits = defineEmits(['refresh'])

const loading1 = ref(false)
const loading2 = ref(false)
const stash = async () => {
  loading1.value = true
  const [err] = await try_http('/mall/p/sell-order/commit', {
    method: 'put',
    body: {
      orderId: info.value.orderId,
      items: info.value.items,
      addrOrderId: info.value.address?.id,
      remarks: info.value.remarks,
      isConfirmed: false,
    },
  })
  loading1.value = false
  if (!err) {
    message.success('操作成功')
    emits('refresh')
  }
}

const handleSubmit = async () => {
  loading2.value = true
  let res = false
  if (info.value.status == 0) {
    res = await submit({
      info: info.value,
      orderId: info.value.orderId,
    })
  } else {
    res = await priceSubmit(info.value)
  }
  loading2.value = false
  if (res) {
    message.success('操作成功')
    navigateTo('/workSpace/company-space/po')
    // emits('refresh')
  }
}
</script>
