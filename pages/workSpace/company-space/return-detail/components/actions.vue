<template>
  <div class="flex gap-8px">
    <a-button type="primary" v-if="canReceivue(info.status)" @click="handleReceive">收货</a-button>
    <!-- <a-button :loading="loading2" v-if="canReturn(info.status)" type="primary" @click="handleReturn">退货</a-button> -->
  </div>
</template>

<script setup>
import { useReturn } from '../../return-manage/useReturn'
const props = defineProps({
  info: {
    default: () => ({}),
  },
})

const emits = defineEmits(['refresh'])

const { canReturn, canReceivue, receive } = useReturn()

const handleReturn = () => {}
const handleReceive = async () => {
  Modal.confirm({
    title: '提示',
    content: '是否确认收货',
    onOk: async () => {
      const [err] = await receive(props.info.orderNumber)
      if (!err) {
        message.success('操作成功')
        navigateTo('/workSpace/company-space/return-manage')
      }
    },
  })
}
</script>
