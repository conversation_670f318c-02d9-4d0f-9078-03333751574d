<template>
  <div>
    <a-spin :spinning="loading">
      <a-page-header :title="title" @back="$router.go(-1)" :sub-title="subTitle">
        <template #extra>
          <Actions @refresh="fetchInfo" :info="info" />
        </template>
      </a-page-header>

      <a-card title="基本信息" class="mt-24px detail-card">
        <a-descriptions bordered :column="2">
          <a-descriptions-item label="订单号">{{ info.orderNumber }}</a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(info.status)">{{ getStatusText(info.status) }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="物料总数量">{{ info.totalNumber }}</a-descriptions-item>
          <a-descriptions-item label="物流单数量">{{ info.logisticsNumber }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ formatTime(info.createTime) }}</a-descriptions-item>
          <a-descriptions-item label="收货时间">{{ formatTime(info.receivingTime) }}</a-descriptions-item>
          <a-descriptions-item label="备注">{{ info.remarks }}</a-descriptions-item>
        </a-descriptions>
      </a-card>

      <a-card title="物料明细" class="mt-24px">
        <a-table
          :columns="columns"
          :data-source="info.items || []"
          :pagination="false"
          size="middle"
          :scroll="{ x: 1200 }"
        >
          <template #bodyCell="{ record, column }">
            <action-button v-if="column.dataIndex == 'sellOrderNumber'">{{ record.sellOrderNumber }}</action-button>

            <template v-if="column.dataIndex == 'action'">
              <action-button v-if="canReturn(info.status)">退货</action-button>
            </template>
          </template>
        </a-table>
      </a-card>

      <a-card title="收货与物流信息" class="mt-24px">
        <a-tabs>
          <a-tab-pane key="0" tab="收货信息">
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="收货人">{{ info.consigneeName }}</a-descriptions-item>
              <a-descriptions-item label="联系电话">{{ info.consigneeMobile }}</a-descriptions-item>
              <a-descriptions-item label="收货地址">{{ info.consigneeAddress }}</a-descriptions-item>
            </a-descriptions>
          </a-tab-pane>
          <a-tab-pane key="1" tab="物流信息">
            <a-tabs type="card" v-if="info.logistics">
              <a-tab-pane v-for="(item, index) in info.logistics" :key="item.dvyId" :tab="`物流${index + 1}`">
                <a-descriptions :column="2" bordered>
                  <a-descriptions-item label="物流公司">{{ item.deliveryDto.companyName }}</a-descriptions-item>
                  <a-descriptions-item label="物流单号">{{ item.deliveryDto.dvyFlowId }}</a-descriptions-item>
                </a-descriptions>

                <div v-if="item.deliveryDto.traces?.length">
                  <a-form-item label="物流状态" class="mt-20">
                    <a-tag :color="getDeliveryColor(item.deliveryDto.state)">
                      {{ getDeliveryText(item.deliveryDto.state) }}
                    </a-tag>
                  </a-form-item>
                  <a-steps
                    size="small"
                    :current="item.deliveryDto.traces.length"
                    progress-dot
                    :items="getItems(item.deliveryDto.traces)"
                    direction="vertical"
                  ></a-steps>
                </div>

                <div v-else py-10 text-gray>暂无物流信息，请您稍后再试</div>
              </a-tab-pane>
            </a-tabs>
          </a-tab-pane>
        </a-tabs>
      </a-card>

      <div class="flex mt-24px">
        <div class="flex-1"></div>
        <Actions @refresh="fetchInfo" :info="info" />
      </div>
    </a-spin>
  </div>
</template>

<script setup>
import Actions from './components/actions.vue'
import { useReturn } from '../return-manage/useReturn'

definePageMeta({
  pageName: '送货单详情',
})

onMounted(() => {
  fetchInfo()
})

const fetchInfo = async () => {
  if (!orderNumber) {
    message.error('订单信息不存在')
    return
  }
  loading.value = true
  const [err, res] = await try_http(`/mall/p/delivery-note/info/${orderNumber}`)
  loading.value = false
  if (!err) {
    info.value = res.data
  }
}

const { getStatusText, getStatusColor, canReturn, getDeliveryText, getDeliveryColor } = useReturn()

const info = ref({})
const route = useRoute()

const orderNumber = route.query.orderNumber
const loading = ref(false)

const title = computed(() => {
  return info.value.orderNumber ? `送货单详情-${info.value.orderNumber}` : '送货单详情'
})

const subTitle = computed(() => {
  return '状态：' + getStatusText(info.value.status)
})

const columns = ref([
  { title: '物料名称', dataIndex: 'prodName', width: 180 },
  {
    title: '品牌',
    width: 150,
    customRender({ record }) {
      return has(record.replaceBrandId) ? '研选' : record.brandName
    },
  },
  {
    title: '型号',
    dataIndex: 'skuCode',
    width: 180,
    customRender({ record }) {
      return has(record.replaceBrandId) ? record.replaceSkuCode : record.skuCode
    },
  },
  {
    title: '参考品牌',
    dataIndex: 'replaceBrandName',
    width: 150,
    customRender({ record }) {
      return has(record.replaceBrandId) ? record.brandName : '-'
    },
  },
  {
    title: '参考型号',
    dataIndex: 'replaceSkuCode',
    width: 180,
    customRender({ record }) {
      return has(record.replaceBrandId) ? record.skuCode : '-'
    },
  },
  { title: '数量', dataIndex: 'number', width: 80 },
  { title: '采购单号', dataIndex: 'sellOrderNumber', width: 150 },
  { title: '操作', dataIndex: 'action', width: 100, fixed: 'right' },
])

const getItems = (list = []) => {
  return list
    .slice(0)
    .reverse()
    .map((item) => {
      return {
        title: item.acceptTime,
        description: item.acceptStation,
      }
    })
}
</script>
