<template>
  <ClientOnly>
    <div flex-1 bg-white class="address-container">
      <!-- 地址列表 -->
      <div class="address-grid">
        <!-- 地址卡片 -->
        <div
          class="card address-card"
          v-for="item in list"
          :key="item.id"
          :class="{ 'default-address': item.commonAddr == 1 }"
        >
          <!-- 默认地址标识 -->
          <div class="default-badge" v-if="item.commonAddr == 1">默认地址</div>

          <!-- 主要内容区域 -->
          <div class="card-main">
            <!-- 联系人姓名 -->
            <div class="title-text contact-name">{{ item.receiver }}</div>

            <!-- 手机号 -->
            <div class="text contact-phone">
              <PhoneOutlined class="phone-icon" />
              {{ item.mobile }}
            </div>

            <!-- 地址信息 -->
            <div class="address-info">
              <EnvironmentOutlined class="location-icon" />
              <div class="address-text">
                <div class="sub-text region">{{ formatAddress(item) }}</div>
                <div class="text detail">{{ item.addr }}</div>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex address-actions">
            <a-button
              v-if="useAuth('procure:addr:set') && item.commonAddr == 0"
              type="text"
              size="small"
              @click="setDefault(item)"
              class="button-link action-btn default-btn"
            >
              设为默认
            </a-button>
            <a-button
              v-if="useAuth('procure:addr:edit')"
              type="text"
              size="small"
              @click="showEdit(item)"
              class="button-link action-btn edit-btn"
            >
              编辑
            </a-button>
            <a-button
              v-if="useAuth('procure:addr:del') && item.commonAddr == 0"
              type="text"
              size="small"
              @click="remove(item.id)"
              class="action-btn delete-btn"
            >
              删除
            </a-button>
          </div>
        </div>

        <!-- 新增地址卡片 -->
        <div
          class="card--reactive add-address-card flex-center"
          v-if="useAuth('procure:addr:add') && list.length < 5"
          @click="showAdd"
        >
          <div class="add-content" style="text-align: center">
            <div class="add-icon">
              <PlusCircleOutlined />
            </div>
            <div class="text add-text">新增地址</div>
            <div class="sub-text add-hint">最多可添加5个地址</div>
          </div>
        </div>
      </div>

      <!-- 美化的表单弹窗 -->
      <a-modal
        v-model:open="open"
        :title="title"
        @ok="handleOk"
        :afterClose="close"
        width="520px"
        :ok-text="title === '新增' ? '新增地址' : '保存修改'"
        cancel-text="取消"
        class="address-modal"
      >
        <a-spin :spinning="loading">
          <div class="form-container">
            <a-form
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 18 }"
              :model="form"
              ref="formRef"
              :rules="rules"
              layout="horizontal"
            >
              <a-form-item label="联系人" name="receiver">
                <a-input v-model:value.trim="form.receiver" placeholder="请输入联系人姓名" size="large">
                  <template #prefix>
                    <UserOutlined class="text-#999" />
                  </template>
                </a-input>
              </a-form-item>

              <a-form-item label="所在地区" name="address">
                <a-cascader
                  v-model:value="form.address"
                  :options="addressOptions"
                  :load-data="loadData"
                  :field-names="{
                    label: 'areaName',
                    value: 'areaId',
                  }"
                  @change="change"
                  placeholder="请选择省市区"
                  size="large"
                >
                  <template #suffixIcon>
                    <EnvironmentOutlined class="text-#999" />
                  </template>
                </a-cascader>
              </a-form-item>

              <a-form-item label="详细地址" name="addr">
                <a-textarea
                  v-model:value.trim="form.addr"
                  :maxlength="50"
                  show-count
                  placeholder="请输入详细地址，如街道、楼牌号等"
                  :rows="3"
                  size="large"
                />
              </a-form-item>

              <a-form-item label="手机号" name="mobile">
                <a-input v-model:value.trim="form.mobile" placeholder="请输入11位手机号码" size="large">
                  <template #prefix>
                    <PhoneOutlined class="text-#999" />
                  </template>
                </a-input>
              </a-form-item>
            </a-form>
          </div>
        </a-spin>
      </a-modal>
    </div>
  </ClientOnly>
</template>

<script setup>
import { companyStore } from '~/store/company'

const useCompany = companyStore()
const activeKey = ref('0')
const onChangeTab = (e) => {
  getAddress()
}

const list = ref([])
const getAddress = async () => {
  const res = await http('/mall/shop/merchantAddr/page', {
    params: {
      type: activeKey.value,
      merchantId: useCompany.company.shopCompanyId,
    },
  })
  useMall(res, (ret) => {
    const _list = ret.records || []
    const _i = _list.findIndex((d) => d.commonAddr == 1)
    if (_i > -1) {
      const item = _list.splice(_i, 1)
      list.value = [...item, ..._list]
    } else {
      list.value = _list
    }
  })
}

const title = ref('')
const open = ref(false)
const form = ref({})
const formRef = ref()

let count = 0
const showAdd = () => {
  title.value = '新增'
  open.value = true
  count = 0
  form.value = {
    id: 0,
  }
  loadData()
}
const showEdit = async (item) => {
  loading.value = true
  title.value = '编辑'
  open.value = true
  count = 0
  const { provinceId, province, cityId, city, areaId, area } = item
  await loadData()
  const p = addressOptions.value.filter((a) => a.areaId == provinceId)
  await loadData(p)
  const c = p[0].children.filter((a) => a.areaId == cityId)
  await loadData(c)
  loading.value = false

  form.value = pick(item, ['id', 'receiver', 'mobile', 'addr'])
  form.value.address = [provinceId, cityId, areaId]
  addressSelect.value = [
    {
      areaName: province,
      areaId: provinceId,
    },
    {
      areaName: city,
      areaId: cityId,
    },
    {
      areaName: area,
      areaId,
    },
  ]
}

const loading = ref(false)

const addressOptions = ref([])
const addressSelect = ref()
const loadData = async (selectOptions) => {
  if (count == 0) {
    const res = await http('/mall/p/area/listByPid', {
      params: {
        level: 1,
      },
    })
    useMall(res, () => {
      count++
      addressOptions.value = res.data.map((item) => {
        item.isLeaf = false
        return item
      })
    })
  } else {
    const targetOption = selectOptions.at(-1)
    targetOption.loading = true
    const res = await http('/mall/p/area/listByPid', {
      params: {
        pid: targetOption.areaId,
      },
    })
    targetOption.loading = false
    useMall(res, () => {
      targetOption.children = res.data.map((item) => {
        item.isLeaf = item.level == 3
        return item
      })
      addressOptions.value = [...addressOptions.value]
    })
  }
}
const change = (ids, options) => {
  addressSelect.value = options
}

const rules = {
  receiver: [{ required: true, message: '请输入联系人' }],
  address: [{ required: true, message: '请选择地址' }],
  addr: [{ required: true, message: '请输入详细地址' }],
  mobile: [
    {
      required: true,
      message: '请输入手机号',
    },
    {
      pattern: MOBILE_REG.reg,
      message: MOBILE_REG.msg,
    },
  ],
}

const formData = computed(() => {
  const obj = pick(form.value, ['addr', 'mobile', 'receiver', 'id'])
  if (addressSelect.value) {
    const [a, b, c] = addressSelect.value
    obj.province = a.areaName
    obj.provinceId = a.areaId
    obj.city = b.areaName
    obj.cityId = b.areaId
    obj.area = c.areaName
    obj.areaId = c.areaId
  }
  obj.merchantId = useCompany.company.shopCompanyId
  obj.type = activeKey.value
  obj.status = 1
  return obj
})

const formatAddress = (item) => {
  const { province, city, area } = item
  return `${province}${city}${area}`
}

const handleOk = () => {
  formRef.value.validate().then(async () => {
    if (title.value == '新增') {
      const res = await http('/mall/shop/merchantAddr', {
        method: 'post',
        body: formData.value,
      })
      useMall(res, () => {
        message.success('操作成功')
        open.value = false
        getAddress()
      })
    } else {
      const res = await http('/mall/shop/merchantAddr', {
        method: 'put',
        body: formData.value,
      })
      useMall(res, () => {
        message.success('操作成功')
        open.value = false
        getAddress()
      })
    }
  })
}

const setDefault = async (item) => {
  form.value = item
  const res = await http('/mall/shop/merchantAddr', {
    method: 'put',
    body: {
      ...formData.value,
      id: item.id,
      commonAddr: 1,
    },
  })
  useMall(res, () => {
    message.success('修改默认地址成功')
    getAddress()
  })
}

const close = () => {
  formRef.value?.clearValidate()
}

const remove = (id) => {
  Modal.confirm({
    title: '提示',
    content: `确定要删除该${activeKey.value == 0 ? '收货' : '收票'}地址吗?`,
    onOk: async () => {
      const res = await http('/mall/shop/merchantAddr/' + id, {
        method: 'delete',
      })
      useMall(res, () => {
        message.success(res.data)
        getAddress()
      })
    },
  })
}

const route = useRoute()
onMounted(() => {
  if (route.query?.type) {
    activeKey.value = route.query.type
  }
  getAddress()
})
</script>

<style lang="less" scoped>
.address-container {
  .address-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
    gap: 20px;
    padding: 16px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 16px;
      padding: 16px;
    }
  }

  .address-card {
    padding: 0;
    border: 2px solid #e8e8e8;
    overflow: hidden;

    &:hover {
      border-color: #d9d9d9;
    }

    &.default-address {
      border-color: #f94c30;
    }
  }

  .default-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background: #f94c30;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: normal;
    z-index: 1;
  }

  .card-main {
    padding: 20px 20px 16px 20px;
  }

  .contact-name {
    font-size: 18px;
    margin-bottom: 12px;
    line-height: 1.4;
  }

  .contact-phone {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .phone-icon {
      color: #f94c30;
      margin-right: 6px;
      font-size: 14px;
    }
  }

  .address-info {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;

    .location-icon {
      color: #f94c30;
      margin-right: 8px;
      margin-top: 2px;
      font-size: 14px;
      flex-shrink: 0;
    }

    .address-text {
      flex: 1;
      line-height: 1.5;

      .region {
        margin-bottom: 4px;
      }

      .detail {
        line-height: 1.6;
        word-break: break-all;
      }
    }
  }

  .address-actions {
    justify-content: flex-end;
    gap: 8px;
    padding: 12px 20px;
    border-top: 1px solid #f5f5f5;
    background: #fafafa;

    .action-btn {
      height: 28px;
      padding: 0 12px;
      border-radius: 4px;
      font-size: 14px;
      line-height: 1;
      border: none;
      background: transparent;

      &.delete-btn {
        color: #ff4d4f;

        &:hover {
          background: #fff1f0;
          color: #ff4d4f;
        }
      }
    }
  }

  .add-address-card {
    border: 2px dashed #d9d9d9;
    min-height: 200px;

    &:hover {
      border-color: #f94c30;
      background: #fff9f8;

      .add-icon {
        color: #f94c30;
        transform: scale(1.05);
      }

      .add-text {
        color: #f94c30;
      }
    }

    .add-content {
      .add-icon {
        font-size: 40px;
        color: #d9d9d9;
        margin-bottom: 12px;
        transition: all 0.3s ease;
      }

      .add-text {
        font-weight: 500;
        margin-bottom: 6px;
        transition: color 0.3s ease;
      }
    }
  }
}

// 表单样式优化
:deep(.address-modal) {
  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    padding: 20px 24px;

    .ant-modal-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }
  }

  .ant-modal-body {
    padding: 24px;
  }

  .ant-modal-footer {
    border-top: 1px solid #f0f0f0;
    padding: 16px 24px;

    .ant-btn {
      height: 40px;
      border-radius: 6px;
      font-weight: 500;

      &.ant-btn-primary {
        background: var(--primary-color);
        border-color: var(--primary-color);

        &:hover {
          background: #e6431a;
          border-color: #e6431a;
        }
      }
    }
  }
}

.form-container {
  .ant-form-item {
    margin-bottom: 24px;

    .ant-form-item-label {
      > label {
        font-weight: 500;
        color: #333;
      }
    }

    .ant-input,
    .ant-cascader,
    .ant-input-number {
      border-radius: 6px;
      border-color: #d9d9d9;

      &:hover {
        border-color: var(--primary-color);
      }

      &:focus,
      &.ant-input-focused,
      &.ant-cascader-focused {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(249, 76, 48, 0.1);
      }
    }

    .ant-input-affix-wrapper {
      border-radius: 6px;

      &:hover {
        border-color: var(--primary-color);
      }

      &.ant-input-affix-wrapper-focused {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(249, 76, 48, 0.1);
      }
    }
  }
}

// 响应式优化
@media (max-width: 768px) {
  .address-container {
    .address-card {
      .card-main {
        padding: 16px;
      }

      .contact-name {
        font-size: 16px;
      }

      .address-actions {
        padding: 10px 16px;
        gap: 6px;

        .action-btn {
          font-size: 11px;
          padding: 0 8px;
        }
      }
    }

    .add-address-card {
      min-height: 160px;

      .add-content {
        .add-icon {
          font-size: 32px;
        }

        .add-text {
          font-size: 13px;
        }
      }
    }
  }
}
</style>
