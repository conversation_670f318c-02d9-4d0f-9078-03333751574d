<template>
  <div flex flex-col>
    <a-tabs v-model:active-key="current">
      <a-tab-pane v-for="item in tags" :key="item.tag" :tab="item.label"></a-tab-pane>
    </a-tabs>

    <Member v-if="current == Tag.member" />
    <Apply v-if="current == Tag.apply" />
  </div>
</template>

<script setup lang="ts">
import Member from './components/Member.vue'
import Apply from './components/Apply.vue'

enum Tag {
  member,
  apply,
}
const tags = [
  {
    label: '成员列表',
    tag: Tag.member,
  },
  {
    label: '成员申请',
    tag: Tag.apply,
  },
]

const current = ref(Tag.member)
</script>

<style lang="less" scoped>
:deep(.ant-tabs-nav) {
  margin: 0 !important;
}
</style>
