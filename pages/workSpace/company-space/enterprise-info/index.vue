<template>
  <div>
    <div class="w-full max-w-600 mx-auto">
      <a-alert v-if="mallStatus == 3" text-center message="您的企业正在审核中，请耐心等待审核结果" type="warning" />
      <a-alert
        v-else-if="mallStatus === 50"
        text-center
        message="您的企业未能通过审核，请在消息中心中查看详情"
        type="error"
        closable
      />
      <enterprise-form ref="enterpriseFormRef" />
      <div v-if="mallStatus !== 3" flex items-center justify-between mt-5>
        <a-button flex-1 mr-4 size="large" @click="onReset">重置</a-button>
        <a-button flex-1 type="primary" size="large" @click="onSubmit">提交审核</a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { companyStore } from '~/store/company'
import { userStore } from '~/store/user'
import { try_http } from '~/utils/http'
import { cloneDeep } from 'lodash-es'
import enterpriseForm from '../components/enterprise-form.vue'
import { getShopCompanyByShopCompanyId } from '~/api/mall-platform'

const enterpriseFormRef = ref(null)
const userStoreObj = userStore()
const { user } = storeToRefs(userStoreObj)
const companyStoreObj = companyStore()
const company = ref({})
const oldCompany = ref({})

const mallStatus = computed(() => {
  // 0: 审核中 1: 正常 2: 审核不通过
  return company.value.status
})

const getInfo = async (companyId) => {
  try {
    const res = await getShopCompanyByShopCompanyId(companyId)

    if (res.data) {
      company.value = JSON.parse(res.data)
      oldCompany.value = JSON.parse(res.data)
      enterpriseFormRef.value?.init(2, company.value, mallStatus.value !== 3)
    }
  } catch (error) {
    message.error('获取企业信息失败')
  }
}

const onSubmit = async () => {
  await enterpriseFormRef.value.formRef.validate()
  const formState = enterpriseFormRef.value.formState
  const submitData = {
    merchantShortName: formState.merchantShortName,
    merchantSlogan: formState.merchantSlogan,
    merchantPhone: formState.merchantPhone,
    merchantMail: formState.merchantMail,
    mainProduct: formState.mainProduct,
    merchantWebsite: formState.merchantWebsite,
    merchantLogo: formState.merchantLogo,
    promotionalImg: formState.promotionalImg,
    userMobile: user.value.userMobile,
    shopCompanyId: company.value.shopCompanyId,
    applyType: 5,
  }

  const [err, res] = await try_http('/mall/p/shopCompany', {
    method: 'put',
    body: submitData,
  })

  if (err) {
    message.error('提交失败')
    return
  }

  if (res.code == 0) {
    getInfo(company.value.shopCompanyId)
    message.success('提交成功，请耐心等待审核')
  } else {
    message.error(res.msg || '提交失败')
  }
}

const onReset = () => {
  // 将公司数据映射到表单字段
  const data = cloneDeep(oldCompany.value)
  enterpriseFormRef.value?.init(2, data, mallStatus.value !== 3)
}

onMounted(() => {
  const route = useRoute()
  // 等待 DOM 更新后初始化表单
  if (route.query.companyId) {
    getInfo(route.query.companyId)
  } else {
    getInfo(companyStoreObj.company.shopCompanyId)
  }
})
</script>
