<template>
  <div px-20 w-full bg-white style="position: relative">    
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="1" tab="工商信息">
        <a-form
          ref="businessFormRef"
          :rules="rules"
          :model="businessForm"
          :label-col="{ span: 8 }"
          :wrapper-col="{ span: 16 }"
          autocomplete="off"
        >
          <a-row :gutter="20">
            <a-col :span="12">
              <a-form-item name="merchantName" label="企业名称">
                <a-input v-if="isEdit" v-model:value.trim="businessForm.merchantName" :maxlength="30" />
                <span v-else>{{ businessForm.merchantName || '--' }}</span>
              </a-form-item>
              <a-form-item name="creditCode" label="统一社会信用代码">
                <a-input v-if="isEdit" v-model:value.trim="businessForm.creditCode" :maxlength="20" />
                <span v-else>{{ businessForm.creditCode || '--' }}</span>
              </a-form-item>
              <a-form-item name="residence" label="住所">
                <a-input v-if="isEdit" v-model:value.trim="businessForm.residence" :maxlength="50" />
                <span v-else>{{ businessForm.residence || '--' }}</span>
              </a-form-item>
              <a-form-item name="representative" label="法定代表人">
                <a-input v-if="isEdit" v-model:value.trim="businessForm.representative" :maxlength="20" />
                <span v-else>{{ businessForm.representative || '--' }}</span>
              </a-form-item>
              <a-form-item name="capital" label="注册资本(万元)">
                <a-input-number v-if="isEdit" v-model:value.trim="businessForm.capital" prefix="¥" :min="0" :max="99999999" style="width: 100%" />
                <span v-else>{{ businessForm.capital || '--' }}</span>
              </a-form-item>
              <a-form-item name="foundTime" label="成立日期">
                <a-date-picker v-if="isEdit" v-model:value="businessForm.foundTime" valueFormat="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
                <span v-else>{{ formatTime(businessForm.foundTime) }}</span>
              </a-form-item>
              <a-form-item name="startTime" label="营业期限">
                <a-date-picker v-if="isEdit" v-model:value="businessForm.startTime" valueFormat="YYYY-MM-DD HH:mm:ss" style="width: 45%" />
                <span v-else>{{ formatTime(businessForm.startTime) || '--' }}</span>
                <span text-center style="display: inline-block; width: 10%">~</span>
                <a-date-picker v-if="isEdit" v-model:value="businessForm.endTime" valueFormat="YYYY-MM-DD HH:mm:ss" placeholder="无固定期限" style="width: 45%" />
                <span v-else>{{ formatTime(businessForm.endTime) || '--' }}</span>
              </a-form-item>
              <a-form-item name="businessScope" label="经营范围">
                <a-textarea v-if="isEdit" v-model:value.trim="businessForm.businessScope" :maxlength="500" showCount />
                <span v-else>{{ businessForm.businessScope || '--' }}</span>
              </a-form-item>
            </a-col>
            <a-col :span="10">
              <a-form-item text-center name="businessLicense" label="营业执照电子版">
                <img-upload
                  v-if="isEdit"
                  name="businessLicense"
                  :value="businessForm.businessLicense"
                  :beforeUpload="beforeUpload"
                  @change="(v) => businessForm.businessLicense = v"
                />
                <div v-if="isEdit" sub-text>限2MB以内的jpg、jpeg、png文件</div>
                <img-show v-if="!isEdit" :src="businessForm.businessLicense" />
              </a-form-item>
              <a-form-item text-center name="identityCardFront" label="法人身份证(正面)">
                <img-upload
                  v-if="isEdit"
                  name="identityCardFront"
                  :value="businessForm.identityCardFront"
                  :beforeUpload="beforeUpload"
                  @change="(v) => businessForm.identityCardFront = v"
                />
                <div v-if="isEdit" sub-text>限2MB以内的jpg、jpeg、png文件</div>
                <img-show v-if="!isEdit" :src="businessForm.identityCardFront" />
              </a-form-item>
              <a-form-item text-center name="identityCardLater" label="法人身份证(反面)">
                <img-upload
                  v-if="isEdit"
                  name="identityCardLater"
                  :value="businessForm.identityCardLater"
                  :beforeUpload="beforeUpload"
                  @change="(v) => businessForm.identityCardLater = v"
                />
                <div v-if="isEdit" sub-text>限2MB以内的jpg、jpeg、png文件</div>
                <img-show v-if="!isEdit" :src="businessForm.identityCardLater" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>

        <a-row p-20 v-if="useAuth('set:company:edit') && mallStatus != 0">
          <a-col :span="24" text-center>
            <a-button v-if="!isEdit" type="primary" @click="isEdit = true">
              修改
            </a-button>
            <a-button v-if="isEdit" mr-20 type="primary" @click="onSubmit">
              提交审核
            </a-button>
            <a-button v-if="isEdit" mr-20 type="primary" @click="onReset">
              重置
            </a-button>
            <a-button v-if="isEdit" type="default" @click="onCancel">
              取消
            </a-button>
          </a-col>
        </a-row>
      </a-tab-pane>

      <a-tab-pane key="2" tab="企业信息">
        <a-form
          ref="basicFormRef"
          :rules="rules"
          :model="basicForm"
          :label-col="{ span: 8 }"
          :wrapper-col="{ span: 16 }"
          autocomplete="off"
        >
          <a-row :gutter="20">
            <a-col :span="12">
              <a-form-item name="merchantName" label="企业名称">
                <a-input v-if="isEdit2" v-model:value.trim="basicForm.merchantName" :maxlength="30" />
                <span v-else>{{ basicForm.merchantName || '--' }}</span>
              </a-form-item>
              <a-form-item name="merchantShortName" label="企业简称">
                <a-input v-if="isEdit2" v-model:value.trim="basicForm.merchantShortName" :maxlength="10" />
                <span v-else>{{ basicForm.merchantShortName || '--' }}</span>
              </a-form-item>
              <a-form-item name="mainProduct" label="主营">
                <a-textarea v-if="isEdit2" v-model:value.trim="basicForm.mainProduct" :maxlength="500" showCount />
                <span v-else>{{ basicForm.mainProduct || '--' }}</span>
              </a-form-item>
              <!-- <a-form-item name="userMobile" label="联系人电话">
                <a-input v-model:value.trim="basicForm.userMobile" :maxlength="11" />
              </a-form-item> -->
              <a-form-item name="merchantPhone" label="商家电话">
                <a-input v-if="isEdit2" v-model:value.trim="basicForm.merchantPhone" :maxlength="13" />
                <span v-else>{{ basicForm.merchantPhone || '--' }}</span>
              </a-form-item>
              <a-form-item name="merchantMail" label="邮箱">
                <a-input v-if="isEdit2" v-model:value.trim="basicForm.merchantMail" />
                <span v-else>{{ basicForm.merchantMail || '--' }}</span>
              </a-form-item>
              <a-form-item name="merchantWebsite" label="企业官网">
                <a-input v-if="isEdit2" v-model:value.trim="basicForm.merchantWebsite" />
                <span v-else>{{ basicForm.merchantWebsite || '--' }}</span>
              </a-form-item>
              <a-form-item name="merchantSlogan" label="企业宣传语">
                <a-textarea v-if="isEdit2" v-model:value.trim="basicForm.merchantSlogan" :maxlength="200" showCount />
                <span v-else>{{ basicForm.merchantSlogan || '--' }}</span>
              </a-form-item>
            </a-col>
            <a-col :span="10">
              <a-form-item text-center name="merchantLogo" label="企业LOGO">
                <img-upload
                  v-if="isEdit2"
                  name="merchantLogo"
                  :value="basicForm.merchantLogo"
                  :beforeUpload="beforeUpload"
                  @change="(v) => basicForm.merchantLogo = v"
                />
                <div v-if="isEdit2" sub-text>限2MB以内的jpg、jpeg、png文件</div>
                <img-show v-if="!isEdit2" :src="basicForm.merchantLogo" />
              </a-form-item>
              <a-form-item text-center name="promotionalImg" label="企业宣传图">
                <img-upload
                  v-if="isEdit2"
                  name="promotionalImg"
                  :value="basicForm.promotionalImg"
                  :beforeUpload="beforeUpload"
                  @change="(v) => basicForm.promotionalImg = v"
                />
                <div v-if="isEdit2" sub-text>限2MB以内的jpg、jpeg、png文件</div>
                <img-show v-if="!isEdit2" :src="basicForm.promotionalImg" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>

        <a-row p-20 v-if="useAuth('set:company:edit') && mallStatus != 0">
          <a-col :span="24" text-center>
            <a-button v-if="!isEdit2" type="primary" @click="isEdit2 = true">
              修改
            </a-button>
            <a-button v-if="isEdit2" mr-20 type="primary" @click="onSubmit2">
              提交
            </a-button>
            <a-button v-if="isEdit2" mr-20 type="primary" @click="onReset2">
              重置
            </a-button>
            <a-button v-if="isEdit2" type="default" @click="onCancel2">
              取消
            </a-button>
          </a-col>
        </a-row>
      </a-tab-pane>        
    </a-tabs>

    <div v-if="mallStatus != 1 && !isEdit && !isEdit2" class="in-approve">
      {{ ['审核中', '审核通过', '审核不通过'][mallStatus] }}
    </div>
  </div>
</template>

<script setup>
import { userStore } from '~/store/user'
import { companyStore } from '~/store/company'
import { putShopCompany } from '~/api/mall-manage/index'
import { getShopCompanyByCreditCode, getShopCompanyByShopCompanyId } from '~/api/mall-platform/index'

const userStoreObj = userStore()
const { user } = storeToRefs(userStoreObj)

const companyStoreObj = companyStore()
const { company } = storeToRefs(companyStoreObj)
const { setCompany } = companyStoreObj

const formatTime = (v) => {
  if (!v) return null
  const Y = new Date(v).getFullYear()
  const M = new Date(v).getMonth() + 1
  const D = new Date(v).getDate()
  return `${ Y }-${ M < 10 ? '0' + M : M }-${ D < 10 ? '0' + D : D }`
}

const mallStatus = computed(() => {
  // 0: 审核中 1: 正常 2: 审核不通过
  return company.value.status
})

const isTel = (v) => {
  // 400电话的正则
  const tel1 = /^400[0-9]{7}$/
  // 800电话正则
  const tel2 = /^800[0-9]{7}$/
  // 手机号码正则
  const tel3 = /^1[3-9]([0-9]{9})$/
  // 座机号码正则
  const tel4 = /^0\d{2,3}-\d{7,8}$/
  return (tel1.test(v) || tel2.test(v) || tel3.test(v) || tel4.test(v))
}

const validateTel = async (rule, value) => {
  if (value) {
    if (isTel(value)) {
      return Promise.resolve()
    } else {
      return Promise.reject('请输入正确的电话')
    }
  } else {
    return Promise.resolve()
  }
}

const isEmail = (s) => {
  return /^([a-zA-Z0-9_.-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})$/.test(s)
}

const validateEmail = async (rule, value) => {
  if (!value) return Promise.resolve()
  if (!isEmail(value)) {
    return Promise.reject('请输入正确的邮箱')
  } else {
    return Promise.resolve()
  }
}

const validateMobile = async (rule, value) => {
  if (!value) return Promise.resolve()
  if (value) {
    const mobile = /^(?:(?:\+|00)86)?1\d{2}([\d*]{4})\d{4}$/
    if (mobile.test(value)) {
      return Promise.resolve()
    } else {
      return Promise.reject('请输入正确的联系方式')
    }
  }
}

const vaildCreditCode = async (rule, value) => {
  if (!value) return Promise.resolve()
  const reg = /^(([0-9A-Za-z]{15})|([0-9A-Za-z]{18})|([0-9A-Za-z]{20}))$/
  if (!reg.test(value)) {
    return Promise.reject('请输入正确的统一社会信用代码')
  } else {
    const res = await getShopCompanyByCreditCode({
      creditCode: value,
      merchantId: basicForm.value.shopCompanyId
    })
    if (res.data == 'false') {
      return Promise.reject('当前信用代码已申请过会员,请勿重复申请')
    }
    return Promise.resolve()
  }
}

const activeKey = ref('1')
const isEdit = ref(false)
const isEdit2 = ref(false)
const businessForm = ref({
  creditCode: '',
  merchantName: '',
  residence: '',
  representative: '',
  capital: '',
  foundTime: '',
  startTime: '',
  endTime: '',
  businessScope: '',
  businessLicense: '',
  identityCardFront: '',
  identityCardLater: '',
  mallVersion: '',
  userMobile: '',
  shopCompanyId: ''
})
const basicForm = ref({
  merchantName: '',
  merchantShortName: '',
  mainProduct: '',
  userMobile: '',
  merchantLogo: '',
  promotionalImg: '',
  merchantSlogan: '',
  merchantWebsite: '',
  merchantMail: '',
  merchantPhone: '',
  shopCompanyId: ''
})
const rules = ref({
  merchantName: [
    { required: true, message: '企业名称不能为空', trigger: 'blur' }
  ],
  userMobile: [
    { required: true, message: '联系人电话不能为空', trigger: 'blur' },
    { validator: validateMobile, trigger: 'blur' }
  ],
  merchantPhone: [
    { validator: validateTel, trigger: 'blur' }
  ],
  merchantMail: [
    { validator: validateEmail, trigger: 'blur' }
  ],
  creditCode: [
    { required: true, message: '信用代码不能为空', trigger: 'blur' },
    { validator: vaildCreditCode, trigger: 'blur' }
  ],
  firmName: [
    { required: true, message: '企业名称不能为空', trigger: 'blur' }
  ],
  representative: [
    { required: true, message: '法定代表人不能为空', trigger: 'blur' }
  ],
  businessLicense: [
    { required: true, message: '营业执照不能为空', trigger: 'blur' }
  ],
  identityCardFront: [
    { required: true, message: '法人身份证不能为空', trigger: 'blur' }
  ],
  identityCardLater: [
    { required: true, message: '法人身份证不能为空', trigger: 'blur' }
  ]
})

const route = useRoute()
const getInfo = () => {
  getShopCompanyByShopCompanyId(route.query.companyId).then(res => {
    useMall(res, (data) => {
      if (data) {
        setCompany(JSON.parse(data))
        for (let k in businessForm.value) {
          businessForm.value[k] = company.value[k]
        }
        for (let k in basicForm.value) {
          basicForm.value[k] = company.value[k]
        }
      }
    })
  })  
}

const beforeUpload = (file) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
  if (!isJpgOrPng) {
    message.error('您只能上传jpg、jpeg和png格式的图片')
  }
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    message.error('图片大小不能超过2MB')
  }
  return isJpgOrPng && isLt2M
}

const onReset = () => {  
  for (let k in businessForm.value) {
    businessForm.value[k] = company.value[k]
  }
  // const fields = [
  //   'creditCode', 'merchantName', 'residence', 
  //   'representative', 'capital', 'foundTime', 
  //   'startTime', 'endTime', 'businessScope', 
  //   'businessLicense', 'identityCardFront', 'identityCardLater'
  // ]
  // for (let k in fields) {
  //   businessForm.value[fields[k]] = ''
  // }
}

const onReset2 = () => {
  for (let k in basicForm.value) {
    basicForm.value[k] = company.value[k]
  }
  // const fields = [
  //   'merchantName', 'merchantShortName', 'mainProduct', 
  //   'merchantLogo', 'promotionalImg', 'merchantSlogan', 
  //   'merchantWebsite', 'merchantMail', 'merchantPhone'
  // ]
  // for (let k in fields) {
  //   basicForm.value[fields[k]] = ''
  // }
}

const onCancel = () => {
  isEdit.value = false
  for (let k in businessForm.value) {
    businessForm.value[k] = company.value[k]
  }
}

const onCancel2 = () => {
  isEdit2.value = false  
  for (let k in basicForm.value) {
    basicForm.value[k] = company.value[k]
  }
}

const businessFormRef = ref(null)
const onSubmit = () => {
  businessFormRef.value?.validate().then(() => {
    putShopCompany({
      ...businessForm.value,
      userMobile: user.value.userMobile
    }).then(res => {
      getInfo()
      isEdit.value = false
    })
  })
}

const basicFormRef = ref(null)
const onSubmit2 = () => {
  basicFormRef.value?.validate().then(() => {
    putShopCompany({
      ...basicForm.value,
      userMobile: user.value.userMobile
    }).then(res => {
      getInfo()
      isEdit2.value = false
    })
  })
}

onMounted(() => {
  getInfo()
})
</script>


<style scoped>
span{
  word-break: break-all;
}

.in-approve{
  color: #c95454;
  border: 4px solid #c95454;
  border-radius: 10px;
  width: 180px;
  height: 60px;
  text-align: center;
  padding: 8px 0;
  font-size: 32px;
  font-weight: bold;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -30px;
  margin-left: -90px;
  transform: rotate(-45deg);
  z-index: 0;
}
</style>