<template>
  <div style="position: relative">
    <a-button v-if="useAuth('procure:invoice:add')" type="primary" @click="onAdd">新增</a-button>
    <a-table
      :columns="columns"
      row-key="id"
      size="middle"
      :data-source="dataSource"
      :pagination="false"
      class="mt-16px"
      bordered
      :scroll="{ x: 1500 }"
    >
      <template #bodyCell="{ column, record, value }">
        <span v-if="column.dataIndex == 'title'">
          <a-tag color="green" v-if="record.isDefault">默认</a-tag>
          {{ record.title }}
        </span>
        <template v-if="column.dataIndex == 'action'">
          <a-button
            v-if="useAuth('procure:invoice:set') && record.isDefault != 1"
            type="link"
            @click="onDeFault(record)"
          >
            设为默认
          </a-button>
          <a-button v-if="useAuth('procure:invoice:edit')" type="link" @click="onEdit(record)">编辑</a-button>
          <a-button v-if="useAuth('procure:invoice:del')" type="link" @click="onDel(record.id)">删除</a-button>
        </template>
      </template>
    </a-table>

    <a-modal
      v-model:open="visible"
      :title="title"
      :width="800"
      :maskClosable="false"
      @ok="handleOk"
      :afterClose="close1"
    >
      <a-spin :spinning="loading">
        <a-form :label-col="{ span: 4 }" :model="form" ref="formRef" :rules="rules">
          <a-form-item label="发票抬头" name="title">
            <a-input v-model:value.trim="form.title" :maxlength="200" show-count placeholder="请输入发票抬头" />
          </a-form-item>

          <a-form-item label="纳税人识别号" name="invoiceNum">
            <a-input v-model:value.trim="form.invoiceNum" :maxlength="32" show-count placeholder="请输入纳税人识别号" />
          </a-form-item>

          <a-form-item label="注册地址" name="invoiceAddr">
            <a-input v-model:value.trim="form.invoiceAddr" :maxlength="50" show-count placeholder="请输入注册地址" />
          </a-form-item>

          <a-form-item label="注册电话" name="invoiceTel">
            <a-input v-model:value.trim="form.invoiceTel" :maxlength="13" show-count placeholder="请输入注册电话" />
          </a-form-item>

          <a-form-item label="开户银行" name="invoiceBank">
            <a-input v-model:value.trim="form.invoiceBank" :maxlength="200" show-count placeholder="请输入开户银行" />
          </a-form-item>

          <a-form-item label="银行账号" name="invoiceBankNum">
            <a-input v-model:value.trim="form.invoiceBankNum" :maxlength="32" show-count placeholder="请输入银行账号" />
          </a-form-item>

          <a-form-item label="其他信息" name="otherInfo">
            <a-select v-model:value="form.extraDisplayType" placeholder="请选择">
              <a-select-option :value="1">展示地址、电话</a-select-option>
              <a-select-option :value="2">展示开户银行、银行账号</a-select-option>
              <a-select-option :value="3">展示地址、电话、开户银行及银行账号</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="备注" name="remarksType">
            <a-radio-group v-model:value="form.remarksType">
              <a-radio :value="0">无</a-radio>
              <a-radio :value="1">对账单号</a-radio>
              <a-radio :value="2">自定义</a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item name="remarks" :wrapper-col="{ offset: 4 }" v-if="form.remarksType == 2">
            <a-textarea
              placeholder="请输入备注"
              v-model:value.trim="form.remarks"
              :rows="4"
              :maxlength="200"
              show-count
            />
          </a-form-item>
        </a-form>
      </a-spin>
    </a-modal>

    <a-modal
      v-model:open="visible2"
      :title="title2"
      :width="800"
      :maskClosable="false"
      @ok="handleOk2"
      :afterClose="close2"
    >
      <a-spin :spinning="loading">
        <a-form :label-col="{ span: 4 }" :model="form" ref="formRef2" :rules="rules">
          <a-form-item label="发票抬头" name="title">
            <a-input v-model:value.trim="form.title" :maxlength="200" show-count />
          </a-form-item>

          <a-form-item label="纳税人识别号" name="invoiceNum">
            <a-input v-model:value.trim="form.invoiceNum" :maxlength="32" show-count />
          </a-form-item>
        </a-form>
      </a-spin>
    </a-modal>
  </div>
</template>

<script setup>
import { userStore } from '~/store/user'
import { companyStore } from '~/store/company'

const useUser = userStore()
const useCompany = companyStore()

const activeKey = ref('0')
const columns = [
  {
    title: '发票抬头',
    dataIndex: 'title',
    width: 200,
    ellipsis: true,
  },
  {
    title: '税号',
    dataIndex: 'invoiceNum',
    width: 150,
    ellipsis: true,
  },
  {
    title: '注册地址',
    dataIndex: 'invoiceAddr',
    width: 200,
    ellipsis: true,
  },
  {
    title: '注册电话',
    dataIndex: 'invoiceTel',
    width: 100,
    ellipsis: true,
  },
  {
    title: '开户行',
    dataIndex: 'invoiceBank',
    width: 150,
    ellipsis: true,
  },
  {
    title: '银行账户',
    dataIndex: 'invoiceBankNum',
    width: 150,
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 200,
    ellipsis: true,
  },
]

const columns2 = [
  {
    title: '发票抬头',
    dataIndex: 'title',
    width: 200,
    ellipsis: true,
  },
  {
    title: '纳税人识别号',
    dataIndex: 'invoiceNum',
    width: 150,
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 200,
    ellipsis: true,
  },
]

const isTel = (v) => {
  // 400电话的正则
  const tel1 = /^400[0-9]{7}$/
  // 800电话正则
  const tel2 = /^800[0-9]{7}$/
  // 手机号码正则
  const tel3 = /^1[3-9]([0-9]{9})$/
  // 座机号码正则
  const tel4 = /^0\d{2,3}-\d{7,8}$/
  return tel1.test(v) || tel2.test(v) || tel3.test(v) || tel4.test(v)
}

const validateTel = async (rule, value) => {
  if (value) {
    if (isTel(value)) {
      return Promise.resolve()
    } else {
      return Promise.reject('请输入正确的电话')
    }
  } else {
    return Promise.resolve()
  }
}

const dataSource = ref([])

const form = ref({})

const rules = ref({
  title: [{ required: true, message: '请输入发票抬头' }],
  invoiceNum: [{ required: true, message: '请输入税号' }],
  invoiceAddr: [{ required: true, message: '请输入注册地址' }],
  invoiceBank: [{ required: true, message: '请输入开户银行' }],
  invoiceBankNum: [{ required: true, message: '请输入银行账号' }],
  invoiceTel: [
    { required: true, message: '请输入注册电话' },
    { validator: validateTel, trigger: 'blur' },
  ],
  remarks: [
    {
      validator: (rule, value) => {
        if (form.value.remarksType === 2 && !value?.trim()) {
          return Promise.reject('请输入备注内容')
        }
        return Promise.resolve()
      },
    },
  ],
})

const visible = ref(false)
const loading = ref(false)
const title = ref('新增增值税专用发票')
const onAdd = () => {
  visible.value = true
  title.value = '新增增值税专用发票'
  form.value = {
    remarksType: 0,
  }
}

const visible2 = ref(false)
const title2 = ref('新增普通发票')

const formRef = ref(null)
const handleOk = () => {
  formRef.value.validate().then(async () => {
    submitOk()
  })
}

const close1 = () => {
  formRef.value?.clearValidate()
}

const close2 = () => {
  formRef2.value?.clearValidate()
}

const formRef2 = ref(null)
const handleOk2 = () => {
  formRef2.value.validate().then(async () => {
    submitOk()
  })
}

const submitOk = async () => {
  if (!form.value.id) {
    // 新增
    const res = await http('/mall/shop/invoice', {
      method: 'post',
      body: {
        ...form.value,
        merchantId: useCompany.company.shopCompanyId,
        type: activeKey.value,
      },
    })
    useMall(res, () => {
      message.success('操作成功')
      visible.value = false
      visible2.value = false
      getList()
    })
  } else {
    // 编辑
    const res = await http('/mall/shop/invoice', {
      method: 'put',
      body: {
        ...form.value,
      },
    })
    useMall(res, () => {
      message.success('操作成功')
      visible.value = false
      visible2.value = false
      getList()
    })
  }
}

const onDeFault = async (row) => {
  const res = await http('/mall/shop/invoice', {
    method: 'put',
    body: {
      ...row,
      isDefault: 1,
    },
  })
  useMall(res, () => {
    message.success('操作成功')
    getList()
  })
}

const onEdit = (row) => {
  visible.value = true
  title.value = '编辑增值税专用发票'
  form.value = {
    // 初始化或保留现有值
    ...row,
    otherInfo: row.otherInfo !== undefined ? row.otherInfo : null,
    remarkType: row.remarkType !== undefined ? String(row.remarkType) : '0',
    remarkContent: row.remarkContent !== undefined ? row.remarkContent : '',
  }
}

const onEdit2 = (row) => {
  visible2.value = true
  title.value = '编辑普通发票'
  form.value = {
    ...row,
  }
}

const onDel = (id) => {
  Modal.confirm({
    title: '提示',
    content: '确定要删除吗?',
    onOk: async () => {
      const res = await http('/mall/shop/invoice/' + id, {
        method: 'delete',
      })
      useMall(res, () => {
        message.success('操作成功')
        getList()
      })
    },
  })
}

const getList = async () => {
  const res = await http('/mall/shop/invoice/page', {
    params: {
      current: 1,
      size: 100,
      type: activeKey.value,
      merchantId: useCompany.company.shopCompanyId,
    },
  })
  useMall(res, (ret) => {
    const _list = ret.records || []
    const _i = _list.findIndex((d) => d.isDefault == 1)
    if (_i > -1) {
      const item = _list.splice(_i, 1)
      dataSource.value = [...item, ..._list]
    } else {
      dataSource.value = _list
    }
  })
}

const onChangeTab = () => {
  getList()
}

const route = useRoute()
onMounted(() => {
  if (route.query?.type) {
    activeKey.value = route.query.type
  }
  getList()
})
</script>

<style scoped></style>
