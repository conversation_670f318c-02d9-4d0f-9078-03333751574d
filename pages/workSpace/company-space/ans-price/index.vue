<template>
  <div class="rfq-container">
    <!-- 搜索区域 -->
    <form-wrapper>
      <a-form :model="form" layout="inline">
        <a-form-item label="物料型号">
          <a-input v-model:value="form.skuCode" placeholder="请输入型号" style="width: 180px" />
        </a-form-item>
        <a-form-item label="品牌">
          <!-- <BrandSelector v-model:value="searchForm.brandId" style="width: 180px" :api="fetchBrands" /> -->
          <a-input v-model:value="form.brandName" placeholder="请输入品牌"></a-input>
        </a-form-item>

        <a-form-item label="接受平替">
          <a-select v-model:value="form.replaceStatus" class="w-180px!" placeholder="是否平替">
            <a-select-option :value="1">是</a-select-option>
            <a-select-option :value="0">否</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="物料分类">
          <CategorySelector v-model:value="form.categoryId" :api="fetchCategories" style="width: 180px" />
        </a-form-item>
        <a-form-item label="询价单号">
          <a-input v-model:value="form.orderNo" placeholder="请输入询价单号" style="width: 180px" />
        </a-form-item>
        <a-form-item label="询价时间">
          <range-picker v-model="form.inquiryTimeFilter"></range-picker>
        </a-form-item>
        <a-form-item label="截止时间">
          <range-picker v-model="form.deadlineFilter"></range-picker>
        </a-form-item>
        <a-form-item label="询价状态" v-if="current == 0">
          <a-select v-model:value="form.status" placeholder="请选择询价状态" style="width: 180px" allowClear>
            <a-select-option v-for="item in statusOptions" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">查询</a-button>
            <a-button @click="handleReset">重置</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </form-wrapper>

    <div class="text-right">
      <view-manager :list="list" v-model="current"></view-manager>
    </div>

    <div class="mt-20px">
      <material-table ref="materialRef" v-if="current === 0" :form="form"></material-table>
      <order-table ref="orderRef" v-if="current === 1" :form="form"></order-table>
    </div>
  </div>
</template>

<script setup>
import materialTable from './components/material-table.vue'
import orderTable from './components/order-table.vue'
const current = ref(0)
const list = [
  {
    name: '物料视图',
    type: 0,
  },
  {
    name: '单据视图',
    type: 1,
  },
]

// 搜索表单
const form = ref({})

// 询价状态选项
const statusOptions = ref([
  { label: '待询价', value: 10 },
  { label: '询价中', value: 20 },
  { label: '已采纳', value: 30 },
  { label: '已截止', value: 40 },
  { label: '已取消', value: 50 },
  { label: '已过期', value: 60 },
])

const materialRef = ref()
const orderRef = ref()
const handleSearch = () => {
  if (current.value === 0) {
    materialRef.value?.search()
  } else {
    orderRef.value?.search()
  }
}
const handleReset = () => {
  form.value = {}
  nextTick(() => {
    handleSearch()
  })
}
</script>

<style scoped></style>
