<template>
  <div bg-white flex-1 px-20 p-20 overflow-x-hidden>
    <div>
      <a-descriptions :title="null" :column="4">
        <a-descriptions-item label="询价单号">
          {{ prodInfo.orderNo }}
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">
          {{ prodInfo.createdTime }}
        </a-descriptions-item>
        <a-descriptions-item label="询价时间">
          {{ prodInfo.inquiryTime }}
        </a-descriptions-item>
        <a-descriptions-item label="结束时间">
          <span v-if="[6, 7, 8].includes(prodInfo.status)">
            {{ prodInfo.updatedTime }}
          </span>
        </a-descriptions-item>
        <a-descriptions-item label="状态">
          {{ formatStatus(prodInfo.status).text }}
        </a-descriptions-item>
        <!-- <a-descriptions-item label="工程师"> -->
        <!--   {{ prodInfo.enginerName }} -->
        <!-- </a-descriptions-item> -->
        <!-- <a-descriptions-item label="采购"> -->
        <!--   {{ prodInfo.purchaseName }} -->
        <!-- </a-descriptions-item> -->
        <a-descriptions-item label="创建人">
          {{ prodInfo.createdBy }}
        </a-descriptions-item>
        <!-- <a-descriptions-item label="报价总金额"> -->
        <!--   {{ formatPrice(prodInfo.totalPrice) }} -->
        <!-- </a-descriptions-item> -->
        <a-descriptions-item label="最长交期">
          {{ formatTrade(prodInfo.tradeTerm) }}
        </a-descriptions-item>
      </a-descriptions>

      <a-table
        :loading="loading"
        :scroll="{ x: 'max-content' }"
        :columns="columns"
        :data-source="prodInfo.skuLists || []"
        :pagination="false"
      >
        <template #bodyCell="{ column, record, text }">
          <div v-if="column.dataIndex == 'prodName'" w-200 truncate :title="record.prodName">
            <!-- <a-tooltip v-if="!isValid(record)" :title="getTitle(record)"> -->
            <!--   <div class="i-mdi-alert-circle inline-block relative top-5 text-red text-20 mr-4 cursor-pointer"></div> -->
            <!-- </a-tooltip> -->
            {{ record.prodName }}
          </div>
          <!-- 数量 -->
          <template v-if="column.dataIndex == 'number'">
            <a-input-number
              v-if="[1, 2].includes(prodInfo.status)"
              :min="1"
              :step="1"
              :precision="0"
              v-model:value="record.number"
              @change="changeNumber(record)"
              @blur="changeRow(record)"
            ></a-input-number>
            <div v-else>{{ record.number }}</div>
          </template>

          <!-- <template v-if="column.dataIndex == 'skuCode'"> -->
          <!--   <a-tooltip :title="prodInfo.status == 6 ? '该商品信息已失效' : '该商品信息已失效，无法询价'"> -->
          <!--     <div -->
          <!--       class="i-mdi-alert-circle inline-block relative top-5 text-red text-20 mr-4 cursor-pointer" -->
          <!--       v-if="record.validStatus != 0" -->
          <!--     ></div> -->
          <!--   </a-tooltip> -->
          <!--   {{ record.skuCode }} -->
          <!-- </template> -->

          <!-- 客户备注 -->
          <template v-if="column.dataIndex == 'buyerRemark'">
            <div
              text-primary
              cursor-pointer
              @click="editBuyerRemark(text, record)"
              v-if="[1, 2].includes(prodInfo.status)"
            >
              <span v-if="text">{{ text }}</span>
              <span v-else>——</span>
            </div>
            <div v-else>{{ text }}</div>
          </template>

          <!-- 操作栏 -->
          <div v-if="column.dataIndex == 'action'">
            <a-button type="link" @click="goDetail(record)" v-if="isValid(record)">详情</a-button>
            <template v-if="[1, 2, 6].includes(prodInfo.status) && !isSpecial">
              <a-button v-if="useAuth('procure:price:set')" type="link" @click="openSetting(record)">备选方案</a-button>
              <a-button
                type="link"
                v-if="
                  useAuth('procure:price:del') &&
                  !isSpecial &&
                  (([1, 2].includes(prodInfo.status) && prodInfo.skuLists?.length > 1) ||
                    (prodInfo.status == 6 && record.invalidFlag))
                "
                @click="removeItem(record)"
              >
                删除
              </a-button>
            </template>
          </div>
        </template>
      </a-table>

      <div text-right mt-16>
        <a-space>
          <Price v-if="hasValid && prodInfo.status == 6" :price="getPrice(prodInfo.totalPrice)" />

          <a-button
            type="primary"
            v-if="useAuth('procure:price:receive') && showTakerOrder"
            @click="takeOrder"
            :loading="loadingOrder"
          >
            接单
          </a-button>

          <!-- <a-button -->
          <!--   type="primary" -->
          <!--   v-if="showConfirm" -->
          <!--   :loading="loadingEnginerCancel" -->
          <!--   @click="enginerCancel" -->
          <!-- > -->
          <!--   工程师取消 -->
          <!-- </a-button> -->

          <a-button
            type="primary"
            v-if="useAuth('procure:price:enginerConfirm') && showConfirm"
            :loading="loadingConfirm"
            @click="enginerConfirm"
          >
            工程师确认
          </a-button>

          <a-button
            type="primary"
            v-if="useAuth('procure:price:submitPrice') && showSubmitPrice"
            @click="submitPrice"
            :loading="loadingPrice"
          >
            提交询价
          </a-button>

          <a-popconfirm title="是否取消" @confirm="cancel">
            <a-button type="primary" v-if="useAuth('procure:price:cancel') && showCancel" :loading="loadingCancel">
              取消
            </a-button>
          </a-popconfirm>

          <a-button type="primary" v-if="[3, 5, 6, 10].includes(prodInfo.status)" @click="download">
            导出询价单
          </a-button>

          <a-button v-if="useAuth('procure:price:add') && prodInfo.status == 6" type="primary" @click="createOrder">
            创建订单
          </a-button>

          <a-button
            type="primary"
            v-if="useAuth('procure:price:again') && showRepeatPrice"
            @click="submitRePrice"
            :loading="loadingRePrice"
          >
            再次询价
          </a-button>

          <a-popconfirm
            title="确认删除?"
            @confirm="remove"
            v-if="useAuth('procure:price:del') && [6, 7, 8, 9].includes(prodInfo.status) && !isSpecial"
          >
            <a-button type="primary" danger>删除</a-button>
          </a-popconfirm>
        </a-space>
      </div>
    </div>

    <a-modal v-model:open="showEdit" title="编辑" @ok="changeBuyerRemark">
      <a-form>
        <a-form-item label="客户备注">
          <a-textarea v-model:value.trim="remark" show-count :maxlength="20" placeholder="请输入"></a-textarea>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 设置备选 -->
    <set-back ref="setRef" :hideAdd="![1, 2].includes(prodInfo.status)"></set-back>

    <before-create :total="prodInfo.totalPrice" ref="beforeCreateRef" />
  </div>
</template>

<script lang="ts" setup>
import { PriceWithCount, ProdInfo, Sku } from '~/api/ans/type'
import { omit } from 'lodash-es'
import SetBack from '~/components/SetBack/index.vue'
import beforeCreate from './components/before-create.vue'
import fileDownload from 'js-file-download'
import Price from './components/price.vue'

definePageMeta({
  name: '询价详情',
})

const { formatTrade, formatPrice, formatStatus } = useFormatter()

const route = useRoute()
const router = useRouter()
const orderNo = route.params.orderNo as string

// 是否专属客服
const isSpecial = ref(false)
const fetchIsSpecial = async () => {
  const ret = await http<boolean>('/mall/p/merchant-user/is-customer-service')
  useMall(ret, () => {
    isSpecial.value = ret.data
  })
}

// 商品详情信息
const prodInfo = ref({} as ProdInfo)
const loading = ref(false)
const _fetchData = async () => {
  loading.value = true
  const res = await http<ProdInfo>(`/mall/p/inquiry-list/info/${orderNo}`)
  loading.value = false
  useMall(res, async () => {
    prodInfo.value = res.data
  })
}

const hasValid = computed(() => {
  return (prodInfo.value.skuLists || []).some((item) => item.invalidFlag == 0 && item.validStatus == 0)
})

// 更新row数据
const showEdit = ref(false)
const row = ref({} as Sku)
const remark = ref('')
const editBuyerRemark = (_text, _row) => {
  row.value = _row
  remark.value = _text
  showEdit.value = true
}
const changeBuyerRemark = () => {
  row.value.buyerRemark = remark.value
  showEdit.value = false
}
const changeNumber = (_row) => {
  row.value = _row
}
const changeRow = async (_row) => {
  row.value = _row
  loading.value = true
  const res = await http<PriceWithCount>('/mall/p/inquiry-list/total-discount-price', {
    method: 'post',
    params: pick(row.value, ['number', 'skuId', 'prodId', 'skuCode']),
  })
  loading.value = false
  await modifyRow()
  useMall(res, () => {
    row.value.price = res.data.skuPrice
    row.value.totalPrice = res.data.oriPrice * row.value.number
  })
}

// 生成最后提交数据
const genData = () => {
  const prodIds = prodInfo.value.skuLists.map((item) => {
    const { number, skuCode, prodId, skuId, buyerRemark, alternatives } = item
    return {
      number,
      skuId,
      skuCode,
      prodId,
      buyerRemark,
      alternatives,
    }
  })
  const body = {
    ...omit(prodInfo.value, 'skuLists'),
    prodIds: JSON.stringify(prodIds),
  }
  return body
}

// 接单
const loadingOrder = ref(false)
const takeOrder = async () => {
  loadingOrder.value = true
  const res = await http('/mall/p/inquiry-list/takeOrder', {
    method: 'put',
    body: genData(),
  })
  loadingOrder.value = false
  useMall(res, () => {
    message.success('接单成功')
    _fetchData()
  })
}

// 工程师取消
const loadingEnginerCancel = ref(false)
const enginerCancel = async () => {
  loadingEnginerCancel.value = true
  const res = await http('/mall/p/inquiry-list/cancelConfirm', {
    method: 'put',
    body: {
      id: prodInfo.value.id,
    },
  })
  loadingEnginerCancel.value = false
  useMall(res, () => {
    message.success('工程师取消成功')
    _fetchData()
  })
}

// 工程师确认
const loadingConfirm = ref(false)
const enginerConfirm = async () => {
  loadingConfirm.value = true
  const res = await http('/mall/p/inquiry-list/confirm', {
    method: 'put',
    body: genData(),
  })
  loadingConfirm.value = false
  useMall(res, () => {
    message.success('工程师已确认')
    _fetchData()
  })
}

// 取消
const loadingCancel = ref(false)
const cancel = async () => {
  loadingCancel.value = true
  const res = await http('/mall/p/inquiry-list/cancel', {
    method: 'put',
    body: genData(),
  })
  loadingCancel.value = false
  useMall(res, () => {
    message.success('操作成功')
    _fetchData()
  })
}

// 提交询价
const loadingPrice = ref(false)
const submitPrice = async () => {
  loadingPrice.value = true
  const res = await http('/mall/p/inquiry-list/commitInquiryOrder', {
    method: 'post',
    body: genData(),
  })
  loadingPrice.value = false
  useMall(res, () => {
    message.success(res.msg || '提交成功')
    _fetchData()
  })
}

// 重新询价
const loadingRePrice = ref(false)
const submitRePrice = async () => {
  loadingRePrice.value = true
  const res = await http('/mall/p/inquiry-list/reInquire/' + prodInfo.value.orderNo, {
    method: 'put',
  })
  loadingRePrice.value = false
  useMall(res, () => {
    message.success('操作成功')
    router.back()
  })
}

// 显示接单
const showTakerOrder = computed(() => {
  const { status, purchaseName, enginerName } = prodInfo.value
  return [1, 2].includes(status) && (!purchaseName || !enginerName)
})

// 显示工程师确认
const showConfirm = computed(() => {
  const { status, purchaseName, enginerName } = prodInfo.value
  return [1].includes(status) && purchaseName && enginerName
})

// 显示取消
const showCancel = computed(() => [1, 2, 3, 5].includes(prodInfo.value.status) && !isSpecial.value)

// 显示询价
const showSubmitPrice = computed(() => {
  if (prodInfo.value.status == 2) return true
  if (prodInfo.value.status == 3 && isSpecial.value) return true
  return false
})

// 再次询价
const showRepeatPrice = computed(
  () => [6, 7, 8].includes(prodInfo.value.status) && !prodInfo.value.reInquiryFlag && !isSpecial.value,
)

const modifyRow = async () => {
  loading.value = true
  const res = await http('/mall/p/inquiry-list/info', {
    method: 'put',
    body: genData(),
  })
  return useMall(res, () => {
    message.success('操作成功')
    _fetchData()
  })
}

// 设置备选
const setRef = ref<InstanceType<typeof SetBack>>()
const openSetting = async (record) => {
  let list
  if (!record.alternatives) {
    list = [record]
  } else {
    list = record.alternatives
  }
  setRef.value?.open(list, record.skuId, record.number, record.prodId, async (open, data) => {
    record.alternatives = data
    const res = await modifyRow()
    if (res) {
      open.value = false
    }
  })
}

const removeItem = (record) => {
  const idx = prodInfo.value.skuLists.findIndex((item) => item.skuId == record.skuId)
  prodInfo.value.skuLists.splice(idx, 1)
  modifyRow()
}

const remove = async () => {
  const res = await http('/mall/p/inquiry-list/' + prodInfo.value.orderNo, {
    method: 'delete',
  })
  useMall(res, () => {
    message.success('删除成功')
    router.back()
  })
}

const goDetail = (row) => {
  const url = `${useRuntimeConfig().public.baseUrl.VITE_MALL_URL}/detail?prodId=${row.prodId}`
  window.open(url)
}

const beforeCreateRef = ref(null)
const createOrder = () => {
  if (!hasValid.value) {
    message.warn('商品已失效或无法询价，无法创建订单')
    return
  }
  beforeCreateRef.value?.init(prodInfo.value.orderNo, () => {
    router.push({
      path: `/order/create`,
      query: {
        orderNo: prodInfo.value.orderNo,
      },
    })
  })
}

const download = async () => {
  const res = await http('/mall/p/inquiry-list/export/' + prodInfo.value.orderNo, {
    method: 'put',
    responseType: 'blob',
  })
  fileDownload(res as unknown as Blob, prodInfo.value.orderNo + '.xlsx')
}

const columns = [
  {
    title: '商品',
    dataIndex: 'prodName',
    width: 200,
    fixed: 'left',
  },
  {
    title: '型号',
    dataIndex: 'skuCode',
  },
  {
    title: '品牌',
    dataIndex: 'brandName',
  },
  {
    title: '数量',
    dataIndex: 'number',
  },
  {
    title: 'BOM品牌',
    dataIndex: 'bomBrandName',
  },
  {
    title: '单价',
    dataIndex: 'price',
    customRender({ text, record }) {
      if (record.invalidFlag == 1 || record.validStatus != 0) return '--'
      return formatPrice(text)
    },
  },
  {
    title: '总价',
    dataIndex: 'totalPrice',
    customRender({ text, record }) {
      if (record.invalidFlag == 1 || record.validStatus != 0) return '--'
      return formatPrice(text)
    },
  },
  {
    title: '交期',
    dataIndex: 'tradeTerm',
    customRender({ text, record }) {
      if (record.invalidFlag == 1 || record.validStatus != 0) return '--'
      return formatTrade(text)
    },
  },
  {
    title: '客户备注',
    dataIndex: 'buyerRemark',
    width: 200,
  },
  {
    title: '商家备注',
    dataIndex: 'sellerRemark',
    width: 200,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
] as any

const getTitle = (record) => {
  if (record.invalidFlag) {
    return '该商品已失效'
  }
  if (record.validStatus != 0) {
    return prodInfo.value.status == 6 ? '该商品信息已失效' : '该商品信息已失效，无法询价'
  }
}

const isValid = (record) => {
  return record.invalidFlag != 1 && record.validStatus != -1
}

onMounted(() => {
  _fetchData()
  fetchIsSpecial()
})
</script>
