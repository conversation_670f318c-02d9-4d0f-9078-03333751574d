import { <PERSON><PERSON>, <PERSON><PERSON>, Typo<PERSON> } from 'ant-design-vue'
import { companyStore } from '~/store/company'
import { sideWidgetsStore } from '~/store/sideWidgets'
import { userStore } from '~/store/user'

export const useRfq = () => {
  const { isPersonal } = storeToRefs(companyStore())
  const toPurchase = async (ids) => {
    if (isPersonal.value) {
      Guide.show()
      return
    }
    const [err, res] = await try_http('/mall/p/inquiry-list-item/order-view/order', {
      method: 'post',
      body: ids,
    })
    if (!err) {
      message.success('操作成功')
      navigateTo({
        path: '/workSpace/company-space/po-detail',
        query: {
          orderId: res.data,
        },
      })
    }
  }

  const cancel = async (ids, cb) => {
    const [err] = await try_http('/mall/p/inquiry-list-item/order-view/cancel', {
      method: 'put',
      body: ids,
    })
    if (!err) {
      message.success('操作成功')
      cb?.()
    }
  }

  const remove = async (ids, cb) => {
    Modal.confirm({
      title: '提示',
      content: '是否确认删除？',
      onOk: async () => {
        const [err] = await try_http('/mall/p/inquiry-list-item/order-view', {
          method: 'delete',
          body: ids,
        })
        if (!err) {
          message.success('操作成功')
          cb?.()
        }
      },
    })
  }

  const copy = useCopy()

  const { user } = storeToRefs(userStore())
  const { company } = storeToRefs(companyStore())

  const share = async (orderNo) => {
    const [err, res] = await try_http(`/mall/p/inquiry-list-item/share-id/${orderNo}`)
    if (!err) {
      const link = `${window.location.origin}/rfq-share?shareId=${res.data}`
      const modal = Modal.confirm({
        title: '分享询价单',
        icon: null,
        closable: true,
        maskClosable: true,
        width: 600,
        content: h('div', [
          h(Typography, {}, [
            h(Typography.Paragraph, {}, [
              h('strong', user.value.realName || user.value.nickName),
              h('span', '请您查看'),
              h('strong', company.value.merchantName),
              h('span', '的询价单:'),
            ]),
            h(
              Typography.Text,
              {
                copyable: true,
                class: 'text-primary cursor-pointer',
                onClick: () => {
                  window.open(link, '_blank')
                },
              },
              link,
            ),
          ]),
          h(Alert, {
            message: `本链接为公开链接，任何研选工场注册会员均可访问，请谨慎处理。`,
            type: 'warning',
            showIcon: true,
            style: { marginTop: '20px' },
          }),
        ]),
        footer: h('div', { style: { marginTop: '12px', float: 'right', display: 'flex', gap: '8px' } }, [
          h(
            Button,
            {
              type: 'primary',
              onClick: () => {
                copy(link)
                message.success('链接已复制到剪贴板')
              },
            },
            '复制链接',
          ),
          h(
            Button,
            {
              onClick: () => {
                modal.destroy()
              },
            },
            '关闭',
          ),
        ]),
      })
    }
  }

  const outerInquiry = async (ids) => {
    const [err, res] = await try_http(`/mall/p/inquiry-list-item/share-id`, {
      method: 'post',
      body: ids,
    })
    if (!err) {
      const link = `${window.location.origin}/rfq-inquiry?shareId=${res.data}`
      const modal = Modal.confirm({
        title: '邀请报价',
        icon: null,
        closable: true,
        maskClosable: true,
        width: 600,
        content: h('div', [
          h(Typography, {}, [
            h(Typography.Paragraph, {}, [
              h('strong', user.value.realName || user.value.nickName),
              h('span', '请您加入参与'),
              h('strong', company.value.merchantName),
              h('span', '的询价单的报价:'),
            ]),
            h(
              Typography.Text,
              {
                copyable: true,
                class: 'text-primary cursor-pointer',
                onClick: () => {
                  window.open(link, '_blank')
                },
              },
              link,
            ),
          ]),
        ]),
        footer: h('div', { style: { marginTop: '12px', float: 'right', display: 'flex', gap: '8px' } }, [
          h(
            Button,
            {
              type: 'primary',
              onClick: () => {
                copy(link)
                message.success('链接已复制到剪贴板')
              },
            },
            '复制链接',
          ),
          h(
            Button,
            {
              onClick: () => {
                modal.destroy()
              },
            },
            '关闭',
          ),
        ]),
      })
    }
  }

  const { updateAnsCount } = sideWidgetsStore()
  const handleReInquiry = async (orderNo) => {
    const [err] = await try_http('/mall/p/inquiry-list-item/reinquire', {
      method: 'post',
      body: {
        orderNo,
      },
    })
    if (!err) {
      message.success('已将物料添加至询价器')
      updateAnsCount()
    }
  }

  const canMCreate = () => useAuth('ans:material:create')
  const canMBatchReInquiry = () => useAuth('ans:material:batch:reInquiry')
  const canMBatchToPo = () => useAuth('ans:material:batch:toPo')
  const canMBatchExport = () => useAuth('ans:material:batch:export')
  const canMBatchCancel = () => useAuth('ans:material:batch:cancel')
  const canMBatchDelete = () => useAuth('ans:material:batch:delete')
  const canMBatchInvite = () => useAuth('ans:material:batch:invite')
  const canMInvte = () => useAuth('ans:material:invite')
  const priceFirst = () => useAuth('ans:price:first')
  const tradeTermFirst = () => useAuth('ans:tradeterm:first')

  const canStartInquiry = (record) => eq(record.status, 10) && record.quotationItems?.length
  const canToPurchaseOrder = (status) => status === 30 && useAuth('ans:material:toPo')
  const canReInquiry = (status) => eq(status, 30, 40, 50, 60) && useAuth('ans:material:reInquiry')
  const canCancel = (status) => status === 20 && useAuth('ans:material:cancel')
  const canDelete = (status) => eq(status, 10, 40, 50, 60) && useAuth('ans:material:delete')

  const canOCreate = () => useAuth('ans:order:create')
  const canOBatchToPo = () => useAuth('ans:order:batch:toPo')
  const canOBatchCancel = () => useAuth('ans:order:batch:cancel')
  const canOBatchDelete = () => useAuth('ans:order:batch:delete')

  const canOToPo = () => useAuth('ans:order:toPo')
  const canOReInquiry = () => useAuth('ans:order:reInquiry')
  const canODelete = () => useAuth('ans:order:delete')
  const canOCancel = () => useAuth('ans:order:cancel')
  const canOShare = () => useAuth('ans:order:share')

  return {
    toPurchase,
    cancel,
    remove,
    share,
    outerInquiry,
    handleReInquiry,
    canMCreate,
    canMBatchReInquiry,
    canMBatchToPo,
    canMBatchExport,
    canMBatchCancel,
    canMBatchDelete,
    priceFirst,
    tradeTermFirst,
    canStartInquiry,
    canToPurchaseOrder,
    canReInquiry,
    canCancel,
    canDelete,
    canOCreate,
    canOBatchToPo,
    canOBatchCancel,
    canOBatchDelete,
    canOToPo,
    canOReInquiry,
    canODelete,
    canOCancel,
    canOShare,
    canMInvte,
    canMBatchInvite,
  }
}
