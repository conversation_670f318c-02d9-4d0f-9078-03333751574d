<template>
  <div>
    报价总金额：
    <span text-red>{{ price }}(运费将在创建订单时计算)</span>
    <a-popover title="联营商品运费规则:">
      <template #content>
        <a-table bordered :columns="columns" :data-source="configList" :pagination="false"></a-table>
        <p mt-10>非联营商品请参考商品详情中的运费说明。</p>
      </template>

      <span sub-text text-14 ml-10 cursor-pointer>
        <QuestionCircleOutlined />
        运费规则
      </span>
    </a-popover>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  price?: string
}>()

const { price } = toRefs(props)

const configList = ref([])

const fetchConfig = async () => {
  const res = await plat_http.get('/shop/freightConfig/page', {
    current: 1,
    size: 999,
  })
  useMall(res, () => {
    const ret = JSON.parse(res.data)
    configList.value = ret.records
  })
}

fetchConfig()

const columns = ref([
  {
    title: '单次采购联营商品总价（元）',
    customRender({ record }) {
      const { priceFloor, priceCeiling } = record
      if (has(priceCeiling)) {
        return `${priceFloor}~${priceCeiling}`
      } else {
        return `${priceFloor}以上`
      }
    },
  },
  {
    title: '运费（元）',
    dataIndex: 'freightFee',
    customRender({ text }) {
      if (text == 0) {
        return '免运费'
      } else {
        return text
      }
    },
  },
])
</script>
