<template>
  <div class="table-area">
    <div class="table-operations" v-if="isDetail">
      <a-dropdown>
        <a-button type="primary">
          批量操作
          <down-outlined />
        </a-button>
        <template #overlay>
          <a-menu @click="handleBatchOperation">
            <a-menu-item key="cancel">取消</a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
      <a-button type="primary" @click="toggle">{{ text }}</a-button>
    </div>
    <div class="table-operations" v-else>
      <a-button type="primary" class="smart-allocation-btn" @click="handleNewInquiry" v-if="canMCreate()">
        新建询价
      </a-button>

      <a-dropdown>
        <a-button type="primary">
          批量操作
          <down-outlined />
        </a-button>
        <template #overlay>
          <a-menu @click="handleBatchOperation">
            <!-- <a-menu-item key="configSupplier">配置供应商</a-menu-item> -->
            <!-- <a-menu-item key="startInquiry">启动询价</a-menu-item> -->
            <a-menu-item key="batchReInquiry" v-if="canMBatchReInquiry()">再次询价</a-menu-item>
            <a-menu-item key="toPurchaseOrder" v-if="canMBatchToPo()">转采购单</a-menu-item>
            <a-menu-item key="invite" v-if="canMBatchInvite()">邀请报价</a-menu-item>
            <a-menu-item key="export" v-if="canMBatchExport()">导出</a-menu-item>
            <a-menu-item key="cancel" v-if="canMBatchCancel()">取消</a-menu-item>
            <a-menu-item key="delete" v-if="canMBatchDelete()">删除</a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>

      <a-dropdown>
        <a-button type="primary">
          AI选价
          <down-outlined />
        </a-button>
        <template #overlay>
          <a-menu>
            <a-menu-item @click="priceType(1)" v-if="priceFirst()">价格优先</a-menu-item>
            <a-menu-item @click="priceType(2)" v-if="tradeTermFirst()">交期优先</a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
      <a-button type="primary" @click="toggle">{{ text }}</a-button>

      <div class="flex-1"></div>
      <!-- <a-button @click="showColumnConfig"> -->
      <!--   <SettingOutlined /> -->
      <!--   列配置 -->
      <!-- </a-button> -->
      <column-config @open-config="openConfig" @change-config="changeConfig"></column-config>
      <!-- <a-button type="primary" class="smart-allocation-btn" @click="handleSmartAllocation">智能分配</a-button> -->
    </div>

    <price-alert :ids="selectedRowKeys" api="/mall/p/inquiry-list-item/total-amount" class="mb-16px"></price-alert>
    <a-table
      v-model:expandedRowKeys="expandKeys"
      :columns="getColumn('material').value"
      :data-source="tableData"
      :loading="loading"
      :pagination="pagination"
      @change="pageChange"
      row-key="id"
      :row-selection="{ selectedRowKeys: selectedRowKeys, preserveSelectedRowKeys: true, onChange: onSelectChange }"
      bordered
      size="middle"
      :scroll="{ x: 1500 }"
      @resize-column="colResize"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'prodName'">
          <a-tooltip v-if="record.previousProdName" :title="getTitle(record, 'previousProdName')">
            <info-circle-filled mr-4 text-red></info-circle-filled>
          </a-tooltip>
          <a-tooltip placement="topLeft" :title="record.prodName">
            <span>{{ record.prodName }}</span>
          </a-tooltip>
        </template>
        <template v-if="column.dataIndex === 'skuCode'">
          <a-tooltip v-if="record.previousSkuCode" :title="getTitle(record, 'previousSkuCode')">
            <info-circle-filled mr-4 text-red></info-circle-filled>
          </a-tooltip>
          <a-tooltip placement="topLeft" :title="record.skuCode">
            <span>{{ record.skuCode }}</span>
          </a-tooltip>
        </template>
        <template v-if="column.dataIndex === 'categoryName'">
          <a-tooltip placement="topLeft" :title="record.categoryName">
            <span>{{ record.categoryName }}</span>
          </a-tooltip>
        </template>
        <template v-if="column.dataIndex === 'orderNo'">
          <a-tooltip placement="topLeft" :title="record.orderNo">
            <span>{{ record.orderNo }}</span>
          </a-tooltip>
        </template>
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="getStatusColor(record)">{{ getStatusText(record) }}</a-tag>
        </template>
        <template v-if="column.dataIndex === 'buyerRemark'">
          <a-tooltip placement="topLeft" :title="record.buyerRemark">
            <span>{{ record.buyerRemark }}</span>
          </a-tooltip>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <!-- <a v-if="canConfigSupplier(record)" @click="handleConfigSupplier(record)">配置供应商</a> -->
            <!-- <a v-if="canStartInquiry(record)" @click="handleStartInquiry(record)">启动询价</a> -->
            <a v-if="canToPurchaseOrder(record.status)" v-loading-click="() => handleToPurchaseOrder(record)">
              转采购单
            </a>
            <a v-if="canReInquiry(record.status)" v-loading-click="() => handleReInquiry([record.id])">再次询价</a>
            <!-- <a v-if="canReInquiry(record.status)" @click="handleReInquiry(record)">再次询价</a> -->
            <a v-if="canCancel(record.status)" v-loading-click="() => handleCancel(record)" class="danger-link">取消</a>
            <a v-if="canDelete(record.status)" v-loading-click="() => handleDelete(record)" class="danger-link">删除</a>
            <a v-if="canMInvte()" v-loading-click="() => outerInquiry([record.id])" class="danger-link">邀请报价</a>
            <!-- <a>询价历史</a> -->
          </a-space>
        </template>
      </template>

      <!-- 嵌套表格 -->
      <template #expandedRowRender="{ record }">
        <div class="m-12">
          <a-table
            size="small"
            :columns="getColumns(record).columns.value"
            :data-source="record.quotationItems || []"
            :pagination="false"
            row-key="id"
            bordered
            :scroll="{ x: 1500 }"
            :key="record.id"
          >
            <template #bodyCell="{ column, record: supplierRecord }">
              <template v-if="column.dataIndex === 'status'">
                <a-tag :color="getSupplierStatusColor(supplierRecord.status, supplierRecord)">
                  {{ getSupplierStatusText(supplierRecord.status, supplierRecord) }}
                </a-tag>
              </template>
              <template v-if="column.dataIndex === 'remark'">
                <a-tooltip placement="topLeft" :title="supplierRecord.remark">
                  <span>{{ supplierRecord.remark }}</span>
                </a-tooltip>
              </template>
              <template v-if="column.dataIndex === 'action'">
                <text-button
                  mr-8
                  v-if="has(supplierRecord.supplierId) && supplierRecord.supplierId == record.adoptedSupplierId"
                >
                  已选择
                </text-button>
                <action-button
                  v-else-if="
                    useAuth('ans:material:select') && eq(supplierRecord.status, 1) && eq(supplierRecord.type, 0)
                  "
                  :disabled="disableSelect(supplierRecord)"
                  @click="handleSelectSupplier(record.id, supplierRecord.id)"
                >
                  选择
                </action-button>
                <action-button v-if="useAuth('ans:material:supplierInfo')" @click="openDetail(supplierRecord)">
                  供应商详情
                </action-button>
              </template>
            </template>
          </a-table>
        </div>
      </template>
    </a-table>
    <supplier-selector ref="supplierSelectorRef" />

    <supplier-detail ref="supplierDetailRef"></supplier-detail>
  </div>
</template>

<script setup>
import dayjs from 'dayjs'
import SupplierSelector from './SupplierSelector.vue'
import SupplierDetail from './supplierDetail.vue'
import fileDownload from 'js-file-download'
import { sideWidgetsStore } from '~/store/sideWidgets'
import { useRfq } from './useRfq'

const {
  canMCreate,
  canMBatchReInquiry,
  canMBatchToPo,
  canMBatchExport,
  canMBatchCancel,
  canMBatchDelete,
  canMBatchInvite,
  priceFirst,
  tradeTermFirst,

  canStartInquiry,
  canToPurchaseOrder,
  canReInquiry,
  canCancel,
  canDelete,
  canMInvte,
  outerInquiry,
} = useRfq()
const props = defineProps({
  form: {
    type: Object,
    default: () => ({}),
  },
  isDetail: {
    type: Boolean,
    default: false,
  },
})
const tableData = ref([])
const route = useRoute()
const projectNo = ref(route.query.projectNo)
const { text, toggle, expandKeys } = useCollapse(tableData, 'id')
// 搜索表单
const { form } = toRefs(props)

// 表格列定义
const columns = [
  {
    title: '物料名称',
    dataIndex: 'prodName',
    key: 'prodName',
    width: 200,
    fixed: 'left',
    ellipsis: true,
    isSystem: true,
    resizable: true,
    customRender: ({ text }) => text,
  },
  {
    title: '型号',
    dataIndex: 'skuCode',
    key: 'skuCode',
    width: 180,
    fixed: 'left',
    ellipsis: true,
    isSystem: true,
    resizable: true,
  },
  {
    title: '品牌',
    dataIndex: 'brandName',
    key: 'brandName',
    width: 100,
    ellipsis: true,
    resizable: true,
  },
  {
    title: '物料分类',
    dataIndex: 'categoryName',
    key: 'categoryName',
    width: 180,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '数量',
    dataIndex: 'number',
    key: 'number',
    width: 80,
    ellipsis: true,
    resizable: true,
  },
  {
    title: '单价 (¥)',
    dataIndex: 'sellPrice',
    key: 'sellPrice',
    width: 200,
    ellipsis: true,
    resizable: true,
    customRender: ({ text, record }) => {
      return getPrice(record.sellPrice) || '-'
    },
  },
  {
    title: '总价 (¥)',
    dataIndex: 'totalPrice',
    key: 'totalPrice',
    width: 200,
    ellipsis: true,
    resizable: true,
    customRender: ({ text, record }) => {
      return getPrice(record.totalPrice) || '-'
    },
  },
  {
    title: '交期',
    dataIndex: 'tradeTerm',
    key: 'tradeTerm',
    defaultHidden: true,
    width: 100,
    ellipsis: true,
    customRender: ({ text, record }) => {
      return getTradeTerm(record.tradeTerm) || '-'
    },
  },
  {
    title: '期望交期',
    dataIndex: 'expectTerm',
    key: 'expectTerm',
    width: 100,
    ellipsis: true,
    resizable: true,
    customRender: ({ text }) => {
      return getTradeTerm(text) || '-'
    },
  },
  {
    title: '接受平替',
    dataIndex: 'replaceStatus',
    width: 100,
    resizable: true,
    customRender({ text }) {
      return text ? '是' : '否'
    },
  },
  {
    title: '平替品牌',
    dataIndex: 'replaceBrandName',
    ellipsis: true,
    width: 150,
    resizable: true,
    customRender({ record, text }) {
      return record.replaceStatus ? text : '-'
    },
  },
  {
    title: '平替型号',
    dataIndex: 'replaceSkuCode',
    ellipsis: true,
    width: 150,
    resizable: true,
    customRender({ record, text }) {
      return record.replaceStatus ? text : '-'
    },
  },
  {
    title: '询价单号',
    dataIndex: 'orderNo',
    key: 'orderNo',
    width: 230,
    ellipsis: {
      showTitle: false,
    },
    resizable: true,
  },
  {
    title: '询价时间',
    dataIndex: 'inquiryTime',
    key: 'inquiryTime',
    width: 160,
    ellipsis: true,
    resizable: true,
    customRender: ({ text }) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm') : ''
    },
  },
  {
    title: '截止时间',
    dataIndex: 'deadline',
    key: 'deadline',
    width: 160,
    ellipsis: true,
    resizable: true,
    customRender: ({ text }) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm') : ''
    },
  },
  {
    title: '询价状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    ellipsis: true,
    resizable: true,
  },
  {
    title: '备注',
    dataIndex: 'buyerRemark',
    key: 'buyerRemark',
    width: 150,
    defaultHidden: true,
    ellipsis: {
      showTitle: false,
    },
    resizable: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    fixed: 'right',
    // width: 275,
    width: 220,
    isSystem: true,
    resizable: true,
  },
]

// 供应商表格列定义
const supplierColumns = [
  {
    title: '供应商',
    dataIndex: 'supplierName',
    width: 150,
    resizable: true,
  },
  {
    title: '报价 (¥)',
    dataIndex: 'sellPrice',
    key: 'sellPrice',
    width: 100,
    ellipsis: true,
    customRender: ({ text, record }) => {
      return getPrice(record.sellPrice) || '-'
    },
  },
  {
    title: '总价 (¥)',
    dataIndex: 'totalPrice',
    key: 'totalPrice',
    width: 100,
    ellipsis: true,
    resizable: true,
    customRender: ({ text, record }) => {
      return getPrice(record.totalPrice) || '-'
    },
  },
  {
    title: '承诺交期',
    dataIndex: 'tradeTerm',
    key: 'tradeTerm',
    width: 120,
    ellipsis: true,
    customRender: ({ text }) => {
      return getTradeTerm(text) || '-'
    },
  },
  {
    title: '报价时间',
    dataIndex: 'quotationTime',
    key: 'quotationTime',
    width: 150,
    ellipsis: true,
    customRender: ({ text }) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '-'
    },
  },
  {
    title: '报价状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    ellipsis: true,
  },
  {
    title: '报价类型',
    dataIndex: 'type',
    width: 80,
    customRender({ text }) {
      return ['平台报价', '外部报价'][text] || '-'
    },
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    width: 150,
    ellipsis: {
      showTitle: false,
    },
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 160,
    fixed: 'right',
  },
]

const getColumns = (row) => {
  const columns = ref([])
  if (row.replaceStatus) {
    const [first, ...other] = supplierColumns
    columns.value = [
      first,
      {
        title: '平替品牌',
        dataIndex: 'replaceBrandName',
        width: 150,
        ellipsis: true,
      },
      {
        title: '平替型号',
        dataIndex: 'replaceSkuCode',
        width: 150,
        ellipsis: true,
      },
      ...other,
    ]
  } else {
    columns.value = [...supplierColumns]
  }
  return {
    columns,
  }
}

// 表格数据
const loading = ref(false)
// 添加选中行状态
const selectedRowKeys = ref([])

// 选中行变化处理
const selectedRows = ref([])
const onSelectChange = (newSelectedRowKeys, newSelectedRows) => {
  selectedRowKeys.value = newSelectedRowKeys
  selectedRows.value = newSelectedRows
}

// 提供完整的 handleSelectSupplier 实现
const handleSelectSupplier = async (inquiryId, supplierId) => {
  Modal.confirm({
    title: '选择供应商',
    content: '确定要选择该供应商报价吗？',
    onOk: async () => {
      loading.value = true
      // 调用接口采纳供应商报价
      const [err, res] = await try_http('/mall/p/inquiry-list-item/quotate', {
        method: 'put',
        body: {
          inquiryListItemId: inquiryId,
          supplierQuotationItemId: supplierId,
        },
      })

      if (!err) {
        message.success('已选择该供应商报价')
        // 刷新数据
        fetchData()
      }
      loading.value = false
    },
  })
}

// 状态颜色和文本
const getStatusColor = (record) => {
  const { status, deadline } = record
  if (deadline && [10, 20].includes(status)) {
    if (dayjs().isAfter(dayjs(deadline))) {
      return 'orange'
    }
  }
  const statusMap = {
    10: 'default', // 待询价
    20: 'blue', // 询价中
    30: 'green', // 已报价/采纳
    40: 'orange', // 已截止
    50: 'red', // 已取消
    60: 'gray', // 已过期
  }
  return statusMap[status] || 'default'
}

const getStatusText = (record) => {
  const { status, deadline } = record
  if (deadline && [10, 20].includes(status)) {
    if (dayjs().isAfter(dayjs(deadline))) {
      return '已截止'
    }
  }
  const statusMap = {
    10: '待询价',
    20: '询价中',
    30: '已采纳',
    40: '已截止',
    50: '已取消',
    60: '已过期',
  }
  return statusMap[status] || '未知'
}

// 供应商状态颜色和文本
const getSupplierStatusColor = (status, record) => {
  const { expiryTime } = record
  if (expiryTime) {
    if (dayjs().isAfter(dayjs(expiryTime))) {
      return 'red'
    }
  }
  const statusMap = {
    '-1': 'default', // 尚未启动询价
    0: 'blue', // 待报价
    1: 'green', // 已报价
    2: 'red', // 已拒绝
  }
  return statusMap[status] || 'default'
}

const getSupplierStatusText = (status, record) => {
  const { expiryTime } = record
  if (expiryTime) {
    if (dayjs().isAfter(dayjs(expiryTime))) {
      return '已过期'
    }
  }
  const statusMap = {
    '-1': '尚未启动询价',
    0: '待报价',
    1: '已报价',
    2: '已拒绝',
  }
  return statusMap[status] || '未知'
}

// 查询方法
const handleSearch = () => {
  // 重置分页到第一页
  page.current = 1

  // 获取数据
  fetchData()
}

// 加载数据
const fetchData = async () => {
  loading.value = true
  const [err, res] = await try_http('/mall/p/inquiry-list-item/page', {
    method: 'post',
    body: {
      ...form.value,
      projectNo: projectNo.value,
      current: page.current,
      size: page.size,
    },
  })
  if (!err) {
    tableData.value = res.data.records
    page.total = res.data.total
  }
  loading.value = false
}

const { page, pagination, pageChange } = usePage(fetchData)

const deleteInquiry = async (ids) => {
  const [err] = await try_http('/mall/p/inquiry-list-item', {
    method: 'delete',
    body: ids,
  })

  if (!err) {
    return true
  }
  return false
}
const handleDelete = (record) => {
  Modal.confirm({
    title: '删除物料',
    content: '确定要删除此物料吗？',
    onOk: async () => {
      const result = await deleteInquiry([record.id])
      if (result) {
        selectedRowKeys.value = selectedRowKeys.value.filter((item) => item !== record.id)
        selectedRows.value = selectedRows.value.filter((item) => item.id !== record.id)
        message.success('删除成功')
        fetchData()
      }
    },
  })
}

// 供应商选择器ref
const supplierSelectorRef = ref(null)

// 移除不再需要的响应式变量
// const supplierSelectorVisible = ref(false)
const currentInquiry = ref({})

// 配置供应商提交
const handleConfigSupplierCommit = async (inquiryItems, supplierIds) => {
  loading.value = true
  const [err, res] = await try_http('/mall/p/supplier-quotation/commit', {
    method: 'post',
    body: {
      inquiryItems,
      supplierIds,
    },
  })

  if (!err) {
    message.success('供应商配置成功')
    // 刷新数据
    fetchData()
  }
  loading.value = false
}

// 配置供应商
const handleConfigSupplier = async (record) => {
  currentInquiry.value = record
  // 通过ref调用open方法，使用Promise获取结果
  const keys = record.quotationItems?.map((item) => item.supplierId)
  const selectedRowKeys = await supplierSelectorRef.value.open(keys)
  if (selectedRowKeys && selectedRowKeys.length) {
    // 调用批量添加供应商到询价单的接口
    handleConfigSupplierCommit([record.id], selectedRowKeys)
  }
}

// 启动询价
const handleStartInquiry = async (record) => {
  // 调用接口启动询价
  const [err] = await try_http('/mall/p/inquiry-list-item/start', {
    method: 'post',
    body: [record.id],
  })

  if (!err) {
    message.success('询价启动成功')
    // 刷新数据
    fetchData()
  }
}

// 转采购单
const toPurchaseOrder = async (ids) => {
  const [err, res] = await try_http('/mall/p/inquiry-list-item/order', {
    method: 'post',
    body: ids,
  })

  if (!err) {
    message.success('操作成功')
    navigateTo(`/workSpace/company-space/po-detail?orderId=${res.data}`)
  }
}
const handleToPurchaseOrder = (record) => {
  toPurchaseOrder([record.id])
}

const { updateAnsCount } = sideWidgetsStore()
const handleReInquiry = async (ids) => {
  const [err] = await try_http('/mall/p/inquiry-list-item/reinquire', {
    method: 'post',
    body: {
      inquiryListItemIds: ids,
    },
  })
  if (!err) {
    message.success('已将物料添加至询价器')
    updateAnsCount()
  }
}

// 取消询价单
const cancelInquiry = async (ids) => {
  const [err] = await try_http('/mall/p/inquiry-list-item/cancel', {
    method: 'put',
    body: ids,
  })

  if (!err) {
    message.success('取消询价成功')
    // 刷新数据
    fetchData()
  }
}
const handleCancel = (record) => {
  Modal.confirm({
    title: '取消询价单',
    content: '确定要取消询价单吗？',
    onOk: () => {
      cancelInquiry([record.id])
    },
  })
}

const handleExport = async () => {
  const [err, res] = await try_http('/mall/p/inquiry-list-item/export', {
    method: 'post',
    body: selectedRowKeys.value,
    headers: {
      responseType: 'blob',
    },
  })
  if (!err) {
    selectedRowKeys.value = []
    selectedRows.value = []
    fileDownload(res, `报价单-${dayjs().format('YYYY-MM-DD HH:mm:ss')}.xlsx`)
  }
}

// 批量操作处理
const handleBatchOperation = ({ key }) => {
  if (selectedRowKeys.value.length === 0) {
    // 如果没有选中行，提示用户
    return message.warning('请先选择要操作的物料')
  }

  switch (key) {
    case 'export':
      handleExport()
      break
    case 'configSupplier':
      const list = selectedRows.value.filter((item) => [10, 20].includes(item.status)).map((item) => item.id)
      supplierSelectorRef.value.open().then((keys) => {
        if (keys && keys.length) {
          handleConfigSupplierCommit(list, keys).then(() => {
            selectedRowKeys.value = []
            selectedRows.value = []
          })
        }
      })
      break
    case 'batchReInquiry':
      const ids = selectedRowKeys.value
      if (ids.length == 0) {
        message.warning('没有可再次询价的物料')
        return
      }
      handleReInquiry(ids)

      break
    case 'toPurchaseOrder':
      // 筛选出可以转采购单的记录
      const toPurchaseList = selectedRows.value.filter((item) => canToPurchaseOrder(item.status)).map((item) => item.id)

      if (toPurchaseList.length === 0) {
        message.warning('没有可转换为采购单的物料')
        return
      }

      Modal.confirm({
        title: '批量转采购单',
        content: '确定要将选中的物料转为采购单吗？',
        onOk: async () => {
          // 调用转采购单接口
          const [err, res] = await try_http('/mall/p/inquiry-list-item/order', {
            method: 'post',
            body: toPurchaseList,
          })

          if (!err) {
            message.success('批量转采购单成功')
            selectedRowKeys.value = []
            selectedRows.value = []
            navigateTo(`/workSpace/company-space/po-detail?orderId=${res.data}`)
          }
        },
      })
      break
    case 'cancel':
      // 筛选出可以取消的记录
      const cancelList = selectedRows.value.filter((item) => canCancel(item.status)).map((item) => item.id)

      if (cancelList.length === 0) {
        message.warning('没有可取消的物料')
        return
      }

      Modal.confirm({
        title: '批量取消询价',
        content: '确定要取消选中的物料吗？',
        onOk: async () => {
          // 调用取消询价接口
          const [err] = await try_http('/mall/p/inquiry-list-item/cancel', {
            method: 'put',
            body: cancelList,
          })

          if (!err) {
            message.success('批量取消询价成功')
            selectedRowKeys.value = []
            selectedRows.value = []
            fetchData()
          }
        },
      })
      break
    case 'delete':
      Modal.confirm({
        title: '批量删除询价单',
        content: '确定要删除选中的询价单吗？',
        onOk: async () => {
          const ids = selectedRows.value.filter((item) => canDelete(item.status)).map((item) => item.id)

          if (ids.length === 0) {
            message.warning('没有可删除的询价单')
            return
          }

          const result = await deleteInquiry(ids)
          if (result) {
            message.success('删除成功')
            selectedRowKeys.value = []
            selectedRows.value = []
            fetchData()
          }
        },
      })
      break
    case 'invite':
      outerInquiry(selectedRowKeys.value)
      break
    default:
      break
  }
}

const handleNewInquiry = () => {
  navigateTo('/workSpace/company-space/quotation-tool')
}

// 智能分配处理
const handleSmartAllocation = () => {
  if (selectedRowKeys.value.length === 0) {
    return alert('请先选择要分配的询价单')
  }

  const selectedRecords = tableData.value.filter((item) => selectedRowKeys.value.includes(item.id))
  console.log('智能分配供应商', selectedRecords)
}

const fetchBrands = async (name = '') => {
  const [err, res] = await try_http('/mall/p/inquiry-list-item/related-brands', {
    params: {
      brandName: name,
    },
  })
  if (!err) {
    return res
  }
  return []
}

const fetchCategories = async (keyword = '') => {
  const [err, res] = await try_http('/mall/p/inquiry-list-item/related-categories', {
    params: {
      categoryName: keyword,
    },
  })
  return res
}

const disableSelect = (record) => {
  const { status, expiryTime } = record
  if (expiryTime) {
    return dayjs().isAfter(dayjs(expiryTime))
  }
  return status != 1
}

const { fetchConfig, columnData, getColumn, changeConfig } = useColumn({
  key: 'INQUIRY_LIST_PC',
  config: [
    {
      key: 'material',
      name: '物料配置列',
      columns,
    },
  ],
  descriptions: '企业询价列配置',
})

const openConfig = (callback) => {
  callback(columnData.value)
}
// 初始化加载
onMounted(async () => {
  // 获取询价数据
  Promise.all([fetchConfig(), handleSearch()])
})

const supplierDetailRef = ref(null)
const openDetail = (info) => {
  supplierDetailRef.value?.open(info?.supplierInfo)
}

const priceType = async (method) => {
  Modal.confirm({
    title: '提示',
    content: 'AI选价将会覆盖当前已选择的报价，是否继续？',
    okText: '是',
    cancelText: '否',
    onOk: async () => {
      const [err] = await try_http('/mall/p/inquiry-list-item/smart-quote', {
        method: 'put',
        body: {
          method,
        },
      })
      if (!err) {
        handleSearch()
      }
    },
  })
}

const getTitle = (row, key) => {
  const map = {
    previousProdName: '原物料名称',
    previousSkuCode: '原物料型号',
  }
  return `信息已变更,${map[key]}为：${row[key]}`
}

defineExpose({
  search: handleSearch,
})
</script>

<style scoped>
.table-area {
  margin-bottom: 16px;
}

.table-operations {
  margin-bottom: 16px;
  display: flex;
  gap: 8px;
}

a {
  color: var(--primary-color);
}

.danger-link {
  color: #ff4d4f;
}

/* :deep(.ant-table-thead > tr > th) {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

:deep(.ant-table) {
  overflow-x: auto;
} */
</style>
