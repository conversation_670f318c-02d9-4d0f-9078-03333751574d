<template>
  <a-drawer title="配置表格列" v-model:open="open">
    <a-spin :spinning="state.loading">
      <a-checkbox-group :value="state.displays" @change="handleChange">
        <a-row :gutter="[16, 4]">
          <a-col :span="12" v-for="item in state.columns" :key="item.dataIndex">
            <a-checkbox :value="item.dataIndex" :disabled="item.nohidden">{{ item.title }}</a-checkbox>
          </a-col>
        </a-row>
      </a-checkbox-group>
    </a-spin>
  </a-drawer>
</template>

<script setup>
import { cloneDeep } from 'lodash-es'

const open = ref(false)
const state = reactive({
  columns: [],
  displays: [],
  loading: false,
})

const show = (columns, displays) => {
  state.columns = columns
  state.displays = cloneDeep(displays)
  open.value = true
}

const emit = defineEmits(['change'])

const handleChange = async (value) => {
  state.loading = true
  const [err] = await try_http('/mall/p/config/user-form-view', {
    method: 'POST',
    body: {
      config: JSON.stringify(value),
    },
  })
  state.loading = false
  if (!err) {
    state.displays = value
    emit('change')
  }
}

defineExpose({
  show,
})
</script>
