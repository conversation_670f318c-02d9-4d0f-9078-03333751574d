<template>
  <div flex :title="formatInfo.text">
    <div v-if="showProdName" truncate>
      {{ formatInfo.text }}等共
    </div>

    <div v-if="formatInfo.len >= 1" flex-shrink-0>
      {{ formatInfo.len }} 件
    </div>
    <div v-else flex-shrink-0>
      --
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps<{
  info: string,
  showProdName: Boolean
}>()

const { info } = toRefs(props)

const formatInfo = computed(() => {
  const list = parseJson<string[]>(info.value, [])
  return {
    text: list.join(','),
    len: list.length,
  }
})
</script>
