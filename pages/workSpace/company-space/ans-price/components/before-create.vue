<template>
  <a-modal :width="1200" v-model:open="visible" title="选择商品信息" @ok="onOk">
    <a-table
      row-key="skuId"
      :scroll="{ x: 'max-content' }"
      :columns="columns"
      :data-source="dataList"
      :pagination="false"
      :row-selection="{
        selectedRowKeys: select.keys,
        onChange: changeSelect,
        getCheckboxProps: (record) => ({
          disabled: record.invalidFlag != 0 || record.validStatus != 0,
        }),
      }"
    >
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex == 'prodName'" w-200 truncate :title="record.prodName">
          <a-tooltip title="该商品已失效">
            <div
              class="i-mdi-alert-circle inline-block relative top-5 text-red text-20 mr-4 cursor-pointer"
              v-if="record.invalidFlag"
            ></div>
          </a-tooltip>
          {{ record.prodName }}
        </template>
        <template v-if="column.dataIndex == 'skuCode'">
          <a-tooltip :title="prodStatus == 6 ? '该商品信息已失效' : '该商品信息已失效，无法询价'">
            <div
              class="i-mdi-alert-circle inline-block relative top-5 text-red text-20 mr-4 cursor-pointer"
              v-if="record.validStatus != 0"
            />
          </a-tooltip>
          {{ record.skuCode }}
        </template>
        <template v-if="column.dataIndex == 'buyNum'">
          <a-input-number v-model:value="record.buyNum" @change="changeRow(record)" :precision="0" :min="1" :step="1" />
        </template>
      </template>
    </a-table>
    <template #footer>
      <template v-if="dataList.length">
        <span>订单总价：</span>
        <span text-red>{{ getPrice(total) }}（不含运费）</span>
      </template>
      <a-button @click="visible = false">取消</a-button>
      <a-button type="primary" @click="onOk" :disabled="!dataList.length">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup>
const total = ref(0)
const { formatTrade, formatPrice, formatStatus } = useFormatter()
const columns = [
  {
    title: '商品',
    dataIndex: 'prodName',
    width: 200,
    fixed: 'left',
  },
  {
    title: '型号',
    dataIndex: 'skuCode',
  },
  {
    title: '品牌',
    dataIndex: 'brandName',
  },
  {
    title: '询价数量',
    dataIndex: 'number',
  },
  {
    title: '单价',
    dataIndex: 'price',
    customRender({ text, record }) {
      if (record.invalidFlag == 1) return '--'
      return formatPrice(text)
    },
  },
  {
    title: '采购数量',
    dataIndex: 'buyNum',
  },
  {
    title: '总价',
    dataIndex: 'totalPrice',
    customRender({ text, record }) {
      if (record.invalidFlag == 1) return '--'
      return formatPrice(text)
    },
  },
  {
    title: '交期',
    dataIndex: 'tradeTerm',
    customRender({ text }) {
      return formatTrade(text)
    },
  },
  {
    title: '客户备注',
    dataIndex: 'buyerRemark',
    width: 200,
  },
  {
    title: '商家备注',
    dataIndex: 'sellerRemark',
    width: 200,
  },
]
const select = reactive({
  keys: [],
  rows: [],
})
const changeSelect = (keys, rows) => {
  select.keys = keys
  select.rows = rows
}

let orderNo = ''
let callback = null
const visible = ref(false)
const prodStatus = ref(null)
const init = (no, cb) => {
  visible.value = true
  orderNo = no
  callback = cb
  getList()
}

const dataList = ref([])
const getList = async () => {
  const res = await http(`/mall/p/inquiry-list/info/${orderNo}`)
  useMall(res, async (ret) => {
    prodStatus.value = ret.status
    const data = ret.skuLists || []
    dataList.value = data
      .filter((d) => d.invalidFlag == 0 && d.validStatus === 0)
      .map((d) => {
        d.buyNum = d.number
        return d
      })
    total.value = ret.totalPrice
    select.keys = dataList.value.map((d) => d.skuId)
    select.rows = dataList.value
  })
}

const onOk = () => {
  const postData = select.rows.map((d) => {
    return {
      orderNo,
      skuId: d.skuId,
      prodId: d.prodId,
      number: d.buyNum,
      isChecked: true,
    }
  })
  http('/mall/p/order/shopCartGenerator', {
    method: 'post',
    body: postData,
  }).then((res) => {
    useMall(res, async (ret) => {
      callback ? callback(ret) : ''
    })
  })
}

const changeRow = (row) => {
  const { price, buyNum } = row
  row.totalPrice = price * buyNum
  const totalPrices = dataList.value.map((d) => Number(d.totalPrice))
  total.value = totalPrices.reduce((a, c) => a + c, 0)
}

defineExpose({
  init,
})
</script>
