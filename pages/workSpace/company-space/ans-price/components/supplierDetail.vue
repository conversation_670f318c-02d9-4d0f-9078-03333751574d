<template>
  <a-modal v-model:open="visible" title="供应商详情" :width="800" :footer="null" @cancel="handleCancel">
    <a-descriptions bordered :column="2">
      <!-- 基础信息 -->
      <a-descriptions-item label="供应商名称">
        {{ getSupplierName(supplierInfo.merchantName) }}
      </a-descriptions-item>
      <a-descriptions-item label="供应商类型">
        {{ formatSupplierType(supplierInfo.supplierType) || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="经营品牌">
        {{ supplierInfo.brandNamesInSupplier || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="厂房产权">
        {{ formatPlantPropertyRight(supplierInfo.plantPropertyRight) || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="厂房面积">
        {{ formatPlantArea(supplierInfo.plantArea) || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="在职人数">
        {{ formatStaffAmount(supplierInfo.staffAmount) || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="年营业额">
        {{ supplierInfo.annualTurnover ? `${supplierInfo.annualTurnover} 万元` : '-' }}
      </a-descriptions-item>

      <!-- 贸易商特有信息 -->
      <template v-if="supplierInfo.supplierType === 1">
        <a-descriptions-item label="代理区域范围">
          {{ formatMultiSelect(supplierInfo.agencyAreaScope, 'agentRegion') || '-' }}
        </a-descriptions-item>
      </template>

      <!-- 加工商特有信息 -->
      <template v-else-if="supplierInfo.supplierType === 2">
        <a-descriptions-item label="核心加工能力">
          {{ formatMultiSelect(supplierInfo.coreProcessingCapability, 'coreCapabilities') || '-' }}
        </a-descriptions-item>
      </template>
    </a-descriptions>
  </a-modal>
</template>

<script setup>
const visible = ref(false)
const supplierInfo = ref({})

// 供应商类型映射
const formatSupplierType = (type) => {
  const typeMap = { 1: '贸易商', 2: '加工商', 3: '品牌商' }
  return typeMap[type] || type
}

const getSupplierName = () => {
  let name = ''
  if (supplierInfo.value.supplierType == 3) {
    name = supplierInfo.value.merchantName
  } else {
    name = '研选供应商'
  }
  return name ?? '-'
}

// 厂房产权映射
const formatPlantPropertyRight = (value) => {
  const map = { 1: '自有', 2: '租赁' }
  return map[value] || value
}

// 厂房面积映射
const formatPlantArea = (value) => {
  const map = { 1: '小于500平方米', 2: '500-2000平方米', 3: '2000以上平方米' }
  return map[value] || value
}

// 在职人数映射
const formatStaffAmount = (value) => {
  const map = { 1: '20人以下', 2: '20-50人', 3: '50人以上' }
  return map[value] || value
}

// 代理区域范围标签映射
const agentRegionLabels = {
  1: '全国',
  2: '华东',
  3: '华南',
  4: '华北',
  5: '其他',
}

// 核心加工能力标签映射
const coreCapabilityLabels = {
  1: '方件',
  2: '圆件',
  3: '大板',
  4: '钣金焊接加工',
  5: '精密机加工（精度≤±0.01mm）',
  6: '表面处理（喷涂/电镀）',
  7: '注塑成型',
  8: '铝挤型材',
  9: '铸造',
  10: '其他',
}

// 格式化多选字段（代理区域范围、核心加工能力）
const formatMultiSelect = (value, fieldType) => {
  if (!value) return '-'
  try {
    const values = typeof value === 'string' ? value.split(',').map(Number) : Array.isArray(value) ? value : [value]
    const labelMap = fieldType === 'agentRegion' ? agentRegionLabels : coreCapabilityLabels
    const labels = values.map((v) => labelMap[v] || v)
    return labels.join('、')
  } catch {
    return value // Fallback to raw value if parsing fails
  }
}

// 打开弹窗
const open = (info) => {
  supplierInfo.value = info || {}
  visible.value = true
}

// 关闭弹窗
const handleCancel = () => {
  visible.value = false
}

// 对外暴露方法
defineExpose({
  open,
})
</script>
