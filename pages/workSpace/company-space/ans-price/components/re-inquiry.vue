<template>
  <a-modal title="再次询价" v-model:open="show" @ok="submit">
    <a-form :label-col="{ flex: '80px' }" :model="form" ref="formRef">
      <!-- <a-form-item label="项目号" :rules="{ required: true }" name="projectNo"> -->
      <!--   <a-select -->
      <!--     :value="form.projectNo" -->
      <!--     disabled -->
      <!--     :loading="projectLoading" -->
      <!--     :options="projectList" -->
      <!--     :fieldNames="{ -->
      <!--       label: 'name', -->
      <!--       value: 'projectNo', -->
      <!--     }" -->
      <!--   ></a-select> -->
      <!-- </a-form-item> -->

      <a-form-item
        label="截止时间"
        name="deadline"
        :rules="{
          required: true,
          message: '请选择截止时间',
        }"
      >
        <a-date-picker
          value-format="YYYY-MM-DD HH:mm:ss"
          format="YYYY-MM-DD HH:mm"
          class="w-full"
          v-model:value="form.deadline"
          show-time
          :disabled-date="disableDate"
          :disabled-time="disableTime"
        ></a-date-picker>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import dayjs from 'dayjs'
let _resolve
const show = ref(false)

const form = ref({
  deadline: undefined,
})
const formRef = ref()

const projectList = ref([])
const projectLoading = ref(false)
const fetchProjects = async () => {
  projectLoading.value = true
  const [err, res] = await useCacheFetch('project-no', () => try_http('/mall/p/sell-project/search'))
  if (!err) {
    projectList.value = res.data
  }
  projectLoading.value = false
}

const open = async (data = {}) => {
  const _data = {}
  show.value = true
  await fetchProjects()
  if (projectList.value.length) {
    _data.projectNo = projectList.value[0].projectNo
  }
  _data.inquiryListItemIds = data.inquiryListItemIds
  form.value = _data
  return new Promise((resolve) => {
    _resolve = resolve
  })
}

const disableDate = (current) => {
  const c = dayjs(current ?? undefined)
  const now = dayjs()
  return c.isBefore(now, 'd')
}

const disableTime = (current) => {
  const c = dayjs(current ?? undefined)
  const now = dayjs()
  if (c.isBefore(now)) {
    form.value.deadline = now.format('YYYY-MM-DD HH:mm:ss')
  }
  return {}
}

const submit = async () => {
  await formRef.value?.validate()
  const [err] = await try_http('/mall/p/inquiry-list-item/reinquire', {
    method: 'post',
    body: form.value,
  })
  if (!err) {
    message.success('操作成功')
    _resolve()
    show.value = false
  }
}

defineExpose({
  open,
})
</script>
