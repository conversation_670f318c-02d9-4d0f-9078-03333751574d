<template>
  <a-modal v-model:open="visible" title="配置供应商" :width="900" :footer="null" @cancel="handleCancel">
    <div>
      <!-- 搜索区域 -->
      <div class="search-area mb-4">
        <a-form layout="inline">
          <a-form-item label="供应商名称">
            <a-input v-model:value="searchParams.keyword" placeholder="请输入供应商名称" allowClear />
          </a-form-item>
          <a-form-item label="品牌">
            <BrandSelector v-model:value="searchParams.brandId" style="width: 180px" :disabled="disableBrand" />
          </a-form-item>
          <a-form-item label="产品分类">
            <CategorySelector v-model:value="searchParams.categoryId" style="width: 180px" />
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="handleSearch">查询</a-button>
              <a-button @click="handleReset">重置</a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 表格区域 -->
      <a-table
        :columns="columns"
        :data-source="supplierList"
        :loading="loading"
        :pagination="false"
        :row-selection="{
          type: 'checkbox',
          selectedRowKeys: selectedRowKeys,
          onChange: onSelectChange,
          getCheckboxProps: (record) => ({
            disabled: disableSupplier.has(record.shopCompanyId),
          }),
          preserveSelectedRowKeys: true,
        }"
        :scroll="{ y: 500 }"
        row-key="shopCompanyId"
        size="small"
      >
        <template #expandedRowRender="{ record }">
          <div class="expanded-content">
            <a-descriptions>
              <!-- 通用信息 -->
              <a-descriptions-item label="厂房产权">
                {{ formatPlantPropertyRight(record.plantPropertyRight) || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="厂房面积">
                {{ formatPlantArea(record.plantArea) || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="在职人数">
                {{ formatStaffAmount(record.staffAmount) || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="年营业额">
                {{ record.annualTurnover ? `${record.annualTurnover} 万元` : '-' }}
              </a-descriptions-item>

              <!-- 贸易商特有信息 (supplierType === 1) -->
              <template v-if="record.supplierType === 1">
                <a-descriptions-item label="代理区域范围">
                  {{ formatMultiSelect(record.agencyAreaScope, 'agentRegion') || '-' }}
                </a-descriptions-item>
              </template>

              <!-- 加工商特有信息 (supplierType === 2) -->
              <template v-else-if="record.supplierType === 2">
                <a-descriptions-item label="核心加工能力">
                  {{ formatMultiSelect(record.coreProcessingCapability, 'coreCapabilities') || '-' }}
                </a-descriptions-item>
              </template>

              <!-- 品牌商特有信息 (supplierType === 3) -->
            </a-descriptions>
          </div>
        </template>
      </a-table>
    </div>

    <!-- 操作按钮区域 -->
    <div class="mt-4 pt-4 border-t-1 border-#eee flex justify-end">
      <a-button type="primary" @click="handleConfirm" :disabled="selectedRowKeys.length === 0">确定</a-button>
    </div>
  </a-modal>
</template>

<script setup>
const props = defineProps({
  inquiryData: {
    type: Object,
    default: () => ({}),
  },
})

// 弹窗可见状态
const visible = ref(false)

// 搜索参数
const searchParams = reactive({
  keyword: '',
  brandId: undefined,
  categoryId: undefined,
})

// 表格数据
const supplierList = ref([])
const loading = ref(false)

// 表格列定义
const columns = [
  {
    title: '供应商名称',
    dataIndex: 'merchantName',
    key: 'merchantName',
    customRender: ({ text, record }) => {
      return record.supplierType == 3 ? text : '研选供应商'
    },
  },
  {
    title: '供应商类型',
    dataIndex: 'supplierType',
    key: 'supplierType',
    customRender: ({ text }) => {
      const typeMap = { 1: '贸易商', 2: '加工商', 3: '品牌商' }
      return typeMap[text] || text
    },
    width: 150,
  },
  {
    title: '经营品牌',
    width: 200,
    dataIndex: 'brandNamesInSupplier',
    key: 'brandNamesInSupplier',
    ellipsis: true,
  },
]

// 选中行状态
const selectedRowKeys = ref([])

// 初始化数据
onMounted(() => {
  fetchSuppliers()
})

// 获取供应商列表
const fetchSuppliers = async () => {
  loading.value = true
  const [err, res] = await try_http('/mall/p/shopCompany/search', {
    method: 'get',
    params: {
      ...searchParams,
    },
  })

  if (!err) {
    supplierList.value = res.data || []
  }
  loading.value = false
}

// 格式化厂房产权
const formatPlantPropertyRight = (value) => {
  const map = { 1: '自有', 2: '租赁' }
  return map[value] || value
}

// 格式化厂房面积
const formatPlantArea = (value) => {
  const map = { 1: '小于500平方米', 2: '500-2000平方米', 3: '2000以上平方米' }
  return map[value] || value
}

// 格式化在职人数
const formatStaffAmount = (value) => {
  const map = { 1: '20人以下', 2: '20-50人', 3: '50人以上' }
  return map[value] || value
}

// 代理区域范围标签映射
const agentRegionLabels = {
  1: '全国',
  2: '华东',
  3: '华南',
  4: '华北',
  5: '其他',
}

// 核心加工能力标签映射
const coreCapabilityLabels = {
  1: '方件',
  2: '圆件',
  3: '大板',
  4: '钣金焊接加工',
  5: '精密机加工（精度≤±0.01mm）',
  6: '表面处理（喷涂/电镀）',
  7: '注塑成型',
  8: '铝挤型材',
  9: '铸造',
  10: '其他',
}

// 格式化多选字段（代理区域范围、核心加工能力）
const formatMultiSelect = (value, fieldType) => {
  if (!value) return '-'
  try {
    // 处理逗号分隔的字符串（如 '1,2'）
    const values = typeof value === 'string' ? value.split(',').map(Number) : Array.isArray(value) ? value : [value]
    const labelMap = fieldType === 'agentRegion' ? agentRegionLabels : coreCapabilityLabels
    const labels = values.map((v) => labelMap[v] || v)
    return labels.join('、')
  } catch {
    return value // Fallback to raw value if parsing fails
  }
}

// 选中行变化处理
const onSelectChange = (newSelectedRowKeys) => {
  selectedRowKeys.value = newSelectedRowKeys
}

// 搜索处理
const handleSearch = () => {
  fetchSuppliers()
}

// 重置搜索条件
const handleReset = () => {
  Object.keys(searchParams).forEach((key) => {
    searchParams[key] = undefined
  })
  handleSearch()
}

const disableSupplier = ref(new Set())
const disableBrand = ref(false)
// 打开弹窗，返回Promise
const open = (keys, brandId) => {
  // 重置选择状态
  searchParams.keyword = '' // 搜索关键字
  searchParams.brandId = undefined // 品牌ID
  searchParams.categoryId = undefined // 产品分类ID
  selectedRowKeys.value = keys || [] // 选中的行ID
  disableSupplier.value = new Set(keys || []) // 禁用供应商ID
  if (brandId) {
    searchParams.brandId = brandId // 品牌ID
  }
  disableBrand.value = !!brandId // 是否禁用品牌选择
  // 显示弹窗
  visible.value = true

  // 加载数据
  fetchSuppliers()

  // 返回Promise
  return new Promise((resolve) => {
    resolvePromise = resolve
  })
}

// Promise解析函数
let resolvePromise = null

// 关闭弹窗
const close = () => {
  visible.value = false
}

// 取消选择
const handleCancel = () => {
  close()
}

// 确认选择的供应商
const handleConfirm = () => {
  // 根据selectedRowKeys筛选出对应的供应商对象
  resolvePromise && resolvePromise(selectedRowKeys.value)
  close()
}

// 对外暴露方法
defineExpose({
  open,
  close,
})
</script>

<!--
使用示例：
<template>
  <SupplierSelector ref="supplierSelectorRef" />
  <a-button @click="openSupplierSelector">选择供应商</a-button>
</template>

<script setup>
const supplierSelectorRef = ref(null)

const openSupplierSelector = async () => {
  const suppliers = await supplierSelectorRef.value.open()
  if (suppliers) {
    // 用户点击了确定，suppliers包含选中的供应商
    console.log('选中的供应商:', suppliers)
  } else {
    // 用户点击了取消
    console.log('用户取消了选择')
  }
}
</script>
-->

<style scoped>
.search-area {
  :deep(.ant-form-item) {
    margin-bottom: 16px;
  }
}
</style>
