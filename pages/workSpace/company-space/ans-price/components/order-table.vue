<template>
  <div>
    <div class="flex gap-8">
      <a-button type="primary" @click="handleNewInquiry" v-if="canOCreate()">新建询价</a-button>
      <a-dropdown>
        <div>
          <a-button type="primary">
            批量操作
            <down-outlined />
          </a-button>
        </div>
        <template #overlay>
          <a-menu>
            <a-menu-item key="toPurchaseOrder" @click="batchToOrder" v-if="canOBatchToPo()">转采购单</a-menu-item>
            <a-menu-item key="cancel" @click="bactchCancel" v-if="canOBatchCancel()">取消</a-menu-item>
            <a-menu-item key="delete" @click="handleRemove(selectedRowKeys)" v-if="canOBatchDelete()">删除</a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>

    <a-alert class="mt-20">
      <template #message>
        <div class="flex">
          <info-circle-filled mr-6></info-circle-filled>
          <span>询价单说明</span>
          <div class="flex-1"></div>
          <div>
            已选择：
            <a-tag color="red">{{ selectedRowKeys.length }}</a-tag>
            个询价单
          </div>

          <div class="ml-16px">
            总金额：
            <a-tag color="red">{{ getPrice(total) }}</a-tag>
          </div>
        </div>
      </template>
    </a-alert>
    <div class="mt-20px">
      <a-table
        :pagination="pagination"
        @change="pageChange"
        size="small"
        :scroll="{ x: 1500 }"
        bordered
        :loading="loading"
        :data-source="data"
        :columns="columns"
        row-key="orderNo"
        :row-selection="{
          selectedRowKeys,
          onChange: onSelectChange,
          preserveSelectedRowKeys: true,
        }"
      >
        <template #bodyCell="{ column, text, record }">
          <action-button
            v-if="column.dataIndex == 'orderNo'"
            @click="
              navigateTo({
                path: '/workSpace/company-space/rfq-detail',
                query: {
                  orderNumber: text,
                },
              })
            "
          >
            {{ text }}
          </action-button>

          <div v-if="column.dataIndex == 'action'" class="flex items-center">
            <action-button v-loading-click="() => toPurchase([record.orderNo], search)">转采购单</action-button>
            <a-dropdown>
              <action-button>
                更多
                <down-outlined />
              </action-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item v-if="canOReInquiry()" @click="handleReInquiry(record.orderNo)">再次询价</a-menu-item>
                  <a-menu-item v-if="canODelete()" @click="handleRemove([record.orderNo])">删除</a-menu-item>
                  <a-menu-item v-if="canOCancel()" @click="cancel([record.orderNo], search)">取消</a-menu-item>
                  <a-menu-item v-if="canOShare()" @click="share(record.orderNo)">分享</a-menu-item>
                  <!-- <a-menu-item v-if="canOInvite()" @click="outerInquiry(record.orderNo)">邀请报价</a-menu-item> -->
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup>
import { omit } from 'lodash-es'
import { useRfq } from './useRfq'

onMounted(() => {
  fetchData()
})

const props = defineProps({
  form: {
    type: Object,
    default: () => ({}),
  },
})

const {
  toPurchase,
  cancel,
  remove,
  share,
  handleReInquiry,
  canOCreate,
  canOBatchCancel,
  canOBatchDelete,
  canOBatchToPo,
  canOReInquiry,
  canODelete,
  canOCancel,
  canOShare,
} = useRfq()

const { form } = toRefs(props)

const data = ref([])
const selectedRowKeys = ref([])
const onSelectChange = (keys) => {
  selectedRowKeys.value = keys
}
const loading = ref(false)
const fetchData = async () => {
  loading.value = true
  const [err, res] = await try_http('/mall/p/inquiry-list-item/order-view/page', {
    method: 'post',
    body: {
      ...omit(form.value, ['status']),
      ...form.value,
      current: page.current,
      size: page.size,
    },
  })
  loading.value = false
  if (!err) {
    data.value = res.data.records
    page.total = res.data.total
  }
}

const search = () => {
  page.current = 1
  fetchData()
}

const { page, pagination, pageChange } = usePage(fetchData)

const total = ref(0)

const fetchTotal = async () => {
  const [err, res] = await try_http('/mall/p/inquiry-list-item/order-view/total-amount', {
    method: 'put',
    body: selectedRowKeys.value,
  })
  if (err) return
  total.value = res.data
}

watchEffect(() => {
  if (selectedRowKeys.value.length == 0) {
    total.value = 0
  } else {
    fetchTotal()
  }
})

const columns = ref([
  { title: '询价单号', dataIndex: 'orderNo', key: 'rfqNo', width: 200, minWidth: 200, fixed: 'left' },
  { title: '物料型号数', dataIndex: 'itemCount', key: 'materialModelCount', width: 110 },
  { title: '物料总数', dataIndex: 'totalNumber', key: 'materialCount', width: 100 },
  {
    title: '物料总价',
    dataIndex: 'totalPrice',
    key: 'totalPrice',
    width: 140,
    customRender({ text }) {
      return getPrice(text)
    },
  },
  {
    title: '询价时间',
    dataIndex: 'inquiryTime',
    key: 'rfqTime',
    width: 170,
    customRender({ text }) {
      return formatTime(text)
    },
  },
  {
    title: '截止时间',
    dataIndex: 'deadline',
    key: 'deadline',
    width: 170,
    customRender({ text }) {
      return formatTime(text)
    },
  },
  { title: '操作', dataIndex: 'action', key: 'action', fixed: 'right', width: 100 },
])

const batchToOrder = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要转采购单的询价单')
    return
  }
  const result = await toPurchase(selectedRowKeys.value, () => {
    selectedRowKeys.value = []
    search()
  })
  if (result?.needMemberGuide) {
    showMemberGuide.value = true
  }
}

const bactchCancel = () => {
  cancel(selectedRowKeys.value, () => {
    selectedRowKeys.value = []
    search()
  })
}

const handleRemove = (ids) => {
  remove(ids, () => {
    const set = new Set(ids)
    selectedRowKeys.value = selectedRowKeys.value.filter((key) => !set.has(key))
    search()
  })
}

// const handleReInquiry = async (orderNo) => {
//   const [err] = await try_http('/mall/p/inquiry-list-item/reinquire', {
//     method: 'post',
//     body: {
//       orderNo,
//     },
//   })
//   if (!err) {
//     message.success('已将物料添加至询价器')
//     updateAnsCount()
//   }
// }

const handleNewInquiry = () => {
  navigateTo('/workSpace/company-space/quotation-tool')
}

defineExpose({
  search,
})
</script>
