<template>
  <div>
    <a-spin :spinning="loading">
      <a-page-header :title="title" @back="$router.go(-1)">
        <template #extra>
          <Actions @refresh="fetchInfo" :info="info" />
        </template>
      </a-page-header>

      <a-card title="基本信息" class="mt-24px detail-card">
        <a-row :gutter="24">
          <a-col :span="8">
            <div class="info-item">
              <span class="label">询价单号：</span>
              <span class="value">{{ info.orderNo }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <span class="label">创建时间：</span>
              <span class="value">{{ formatTime(info.createdTime) }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <span class="label">询价时间：</span>
              <span class="value">{{ formatTime(info.inquiryTime) }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <span class="label">截止时间：</span>
              <span class="value">{{ formatTime(info.deadline) }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <span class="label">创建人：</span>
              <span class="value">{{ info.userName }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <span class="label">联系电话：</span>
              <span class="value">{{ info.userMobile }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <span class="label">物料型号数：</span>
              <span class="value">{{ info.itemCount }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <span class="label">物料总数：</span>
              <span class="value">{{ info.totalNumber }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <span class="label">物料总价：</span>
              <span class="value important">{{ getPrice(info.totalPrice) }}</span>
            </div>
          </a-col>
        </a-row>
      </a-card>

      <a-card title="物料明细" class="mt-24px">
        <material-table :form="{ orderNo: orderNumber }" is-detail />
      </a-card>

      <div class="flex mt-24px">
        <div class="flex-1"></div>
        <Actions @refresh="fetchInfo" :info="info" />
      </div>
    </a-spin>
  </div>
</template>

<script setup>
import Actions from './actions.vue'
import materialTable from '../ans-price/components/material-table.vue'

definePageMeta({
  pageName: '询价单详情',
})

onMounted(() => {
  fetchInfo()
})

const fetchInfo = async () => {
  if (!orderNumber) {
    message.error('订单信息不存在')
    return
  }
  loading.value = true
  const [err, res] = await try_http(`/mall/p/inquiry-list-item/details/${orderNumber}`)
  loading.value = false
  if (!err) {
    info.value = res.data
  }
}

const info = ref({})
const route = useRoute()

const orderNumber = route.query.orderNumber
const loading = ref(false)

const title = computed(() => {
  return info.value.orderNumber ? `询价单详情-${info.value.orderNumber}` : '询价单详情'
})
</script>

<style lang="less" scoped>
.detail-card {
  .info-item {
    display: flex;
    margin-bottom: 16px;

    .label {
      color: rgba(0, 0, 0, 0.65);
      min-width: 90px;
      flex-shrink: 0;
    }

    .value {
      flex: 1;
      font-weight: 500;

      &.important {
        color: #f94c30;
        font-weight: 600;
      }
    }
  }
}

.summary-section {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px dashed #e8e8e8;

  .summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 0 24px;

    .label {
      color: rgba(0, 0, 0, 0.65);
    }

    .value {
      font-weight: 500;

      &.important {
        color: #f5222d;
        font-weight: 600;
        font-size: 16px;
      }
    }

    &.total {
      font-size: 16px;
      margin-top: 12px;
      padding-top: 12px;
      border-top: 1px solid #e8e8e8;
    }
  }
}
</style>
