<template>
  <div class="flex gap-8px">
    <a-button type="primary" v-loading-click="() => toPurchase([info.orderNo])" v-if="canOToPo()">转采购单</a-button>
    <a-button type="primary" v-loading-click="() => handleReInquiry(info.orderNo)" v-if="canOReInquiry()">
      再次询价
    </a-button>
    <a-button type="primary" @click="remove([info.orderNo], toAnsPrice)" v-if="canODelete()">删除</a-button>
    <a-button type="primary" @click="cancel([info.orderNo], toAnsPrice)" v-if="canOCancel()">取消</a-button>
    <a-button type="primary" @click="share(info.orderNo)" v-if="canOShare()">分享</a-button>
    <a-button type="primary" @click="outerInquiry(info.orderNo)" v-if="canOInvite()">邀请报价</a-button>
  </div>
</template>

<script setup>
import { useRfq } from '../ans-price/components/useRfq'

const props = defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['refresh'])

const { info } = toRefs(props)

const {
  toPurchase,
  remove,
  cancel,
  share,
  outerInquiry,
  handleReInquiry,
  canOToPo,
  canOReInquiry,
  canODelete,
  canOCancel,
  canOShare,
  canOInvite,
} = useRfq()

const toAnsPrice = () => {
  navigateTo({
    path: '/workSpace/company-space/ans-price',
  })
}
</script>
