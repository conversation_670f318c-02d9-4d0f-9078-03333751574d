<template>
  <div>
    <div class="flex">
      <!-- <a-dropdown> -->
      <!--   <a-button type="primary"> -->
      <!--     批量操作 -->
      <!--     <DownOutlined /> -->
      <!--   </a-button> -->
      <!--   <template #overlay> -->
      <!--     <a-menu> -->
      <!--       <a-menu-item>收货</a-menu-item> -->
      <!--       <a-menu-item>退货</a-menu-item> -->
      <!--     </a-menu> -->
      <!--   </template> -->
      <!-- </a-dropdown> -->
      <a-button type="primary" @click="toggle">{{ text }}</a-button>
      <div class="flex-1"></div>
      <column-config @open-config="(cb) => cb(columnData)" @change-config="changeConfig"></column-config>
    </div>
    <a-table
      class="mt-20px"
      :data-source="data"
      :columns="getColumn('order').value"
      :pagination="pagination"
      @change="pageChange"
      :loading="loading"
      row-key="orderId"
      v-model:expandedRowKeys="expandKeys"
      @resize-column="colResize"
      bordered
      size="small"
    >
      <template #expandedRowRender="{ record }">
        <div class="m-12px">
          <a-table
            size="small"
            :data-source="record.items || []"
            :pagination="false"
            bordered
            :columns="getColumn('material').value"
            @resize-column="colResize"
          >
            <template #bodyCell="{ column, record: data, text }">
              <action-button
                v-if="column.dataIndex == 'sellOrderNumber'"
                @click="navigateTo(`/workSpace/company-space/po-detail?orderId=${data.sellOrderNumber}`)"
              >
                {{ text }}
              </action-button>
            </template>
          </a-table>
        </div>
      </template>

      <template #bodyCell="{ record, column, text }">
        <a-tag :color="getStatusColor(record.status)" v-if="column.dataIndex == 'status'">
          {{ getStatusText(record.status) }}
        </a-tag>

        <action-button
          v-if="column.dataIndex == 'orderNumber'"
          @click="navigateTo('/workSpace/company-space/return-detail?orderNumber=' + record.orderNumber)"
        >
          {{ text }}
        </action-button>

        <div v-if="column.dataIndex == 'action'">
          <action-button v-if="canReceivue(record.status)" v-loading-click="() => handleReceive(record)">
            收货
          </action-button>
          <!-- <action-button v-if="canReturn(record.status)">退货</action-button> -->
        </div>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { useReturn } from '../useReturn'

const props = defineProps({
  form: {
    default: () => ({}),
  },
})

onMounted(() => {
  fetchConfig()
  fetchData()
})

const { getStatusText, getStatusColor, canReceivue, canReturn, receive } = useReturn()

const { form } = toRefs(props)
const data = ref([])
const { text, toggle, expandKeys } = useCollapse(data, 'orderId')
const loading = ref(false)

const search = () => {
  page.current = 1
  fetchData()
}

const fetchData = async () => {
  loading.value = true
  const [err, res] = await try_http('/mall/p/delivery-note/page', {
    method: 'post',
    body: {
      current: page.current,
      size: page.size,
      ...form.value,
    },
  })
  loading.value = false
  if (!err) {
    data.value = res.data.records
    page.total = res.data.total
  }
}
const { page, pagination, pageChange } = usePage(fetchData)

const columns = [
  {
    title: '送货单号',
    dataIndex: 'orderNumber',
    minWidth: 200,
    resizable: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 120,
    resizable: true,
  },
  {
    title: '物料数量',
    dataIndex: 'totalNumber',
    width: 120,
    resizable: true,
  },
  {
    title: '发货时间',
    dataIndex: 'deliveryTime',
    width: 160,
    resizable: true,
    customRender({ text }) {
      return formatTime(text)
    },
  },
  {
    title: '物料单数量',
    dataIndex: 'logisticsNumber',
    width: 120,
    resizable: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 200,
    resizable: true,
    isSystem: true,
  },
]

const subColumns = [
  { title: '物料名称', dataIndex: 'prodName', width: 150, resizable: true },
  {
    title: '品牌',
    width: 150,
    customRender({ record }) {
      return has(record.replaceBrandId) ? '研选' : record.brandName
    },
  },
  {
    title: '型号',
    dataIndex: 'skuCode',
    width: 180,
    customRender({ record }) {
      return has(record.replaceBrandId) ? record.replaceSkuCode : record.skuCode
    },
  },
  {
    title: '参考品牌',
    dataIndex: 'replaceBrandName',
    width: 150,
    customRender({ record }) {
      return has(record.replaceBrandId) ? record.brandName : '-'
    },
  },
  {
    title: '参考型号',
    dataIndex: 'replaceSkuCode',
    width: 180,
    customRender({ record }) {
      return has(record.replaceBrandId) ? record.skuCode : '-'
    },
  },
  { title: '数量', dataIndex: 'number', width: 80, resizable: true },
  { title: '采购单号', dataIndex: 'sellOrderNumber', width: 200, resizable: true },
]

const { fetchConfig, getColumn, columnData, changeConfig } = useColumn({
  key: 'pc-return-order',
  config: [
    {
      key: 'order',
      columns,
      name: '收货管理列配置',
    },
    {
      key: 'material',
      columns: subColumns,
      name: '物料列配置',
    },
  ],
})

const handleReceive = (row) => {
  Modal.confirm({
    title: '提示',
    content: '是否确认收货',
    onOk: async () => {
      const [err] = await receive(row.orderNumber)
      if (!err) {
        message.success('操作成功')
        fetchData()
      }
    },
  })
}

defineExpose({
  search,
})
</script>
