<template>
  <div>
    <form-wrapper>
      <a-form layout="inline">
        <a-form-item label="送货单号">
          <a-input v-model:value="form.orderNo" placeholder="请输入送货单号"></a-input>
        </a-form-item>

        <a-form-item label="发货时间">
          <range-picker v-model:value="form.deliveryTimeFilter"></range-picker>
        </a-form-item>

        <a-form-item label="状态">
          <a-select v-model:value="form.status" class="w-180px!" placeholder="请选择">
            <a-select-option v-for="key in Object.keys(statusMap)" :key="key" :value="key">
              {{ getStatusText(key) }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item>
          <a-button mr-8px type="primary" @click="search">查询</a-button>
          <a-button @click="reset">重置</a-button>
        </a-form-item>
      </a-form>
    </form-wrapper>

    <order-table ref="orderRef" :form="form"></order-table>
  </div>
</template>

<script setup>
import { useReturn } from './useReturn'
import orderTable from './components/order-table.vue'

const form = ref({})
const { statusMap, getStatusText } = useReturn()

const current = ref('order')

const orderRef = ref()
const search = () => {
  if (current.value == 'order') {
    orderRef.value.search()
  }
}

const reset = () => {
  form.value = {}
  nextTick(() => {
    search()
  })
}
</script>
