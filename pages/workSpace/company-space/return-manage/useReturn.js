export const useReturn = () => {
  const statusMap = {
    0: { text: '待发货', color: '' },
    10: { text: '已发货', color: 'blue' },
    20: { text: '已收货', color: 'green' },
    30: { text: '退货中', color: 'red' },
    40: { text: '已退货', color: 'cyan' },
  }

  const deliveryMap = {
    0: {
      text: '没有记录',
      color: 'default',
    },
    1: {
      text: '已揽收',
      color: 'blue',
    },
    2: {
      text: '运输途中',
      color: 'orange',
    },
    201: {
      text: '达到目的城市',
      color: 'purple',
    },
    3: {
      text: '已签收',
      color: 'green',
    },
    4: {
      text: '问题件',
      color: 'red',
    },
  }

  const getStatusText = (v) => statusMap[v]?.text || '-'
  const getStatusColor = (v) => statusMap[v]?.color

  const getDeliveryText = (v) => deliveryMap[v]?.text || '-'
  const getDeliveryColor = (v) => deliveryMap[v]?.color

  const canReceivue = (v) => eq(v, 10) && useAuth('return:order:receive')
  const canReturn = (v) => eq(v, 10, 20)

  const receive = async (orderNo) => {
    return await try_http(`/mall/p/delivery-note/received-confirm/${orderNo}`, {
      method: 'put',
    })
  }

  return {
    statusMap,
    getStatusText,
    getStatusColor,
    canReturn,
    canReceivue,
    receive,
    getDeliveryText,
    getDeliveryColor,
  }
}
