<template>
  <div w-full bg-white p-10 style="overflow: auto;">
    <div v-if="!dataSource.length" pt-100 style="text-align: center">
      <img h-181px src="~/assets/images/daodao_nodata.png" />
      <span text-18px color-primary font-bold>什么也没有......</span>
    </div>
    <a-table v-else rounded-4 :columns="columns" :data-source="dataSource" :pagination="false" size="middle" :scroll="{ x: '100%' }" :loading="loading" :custom-row="customRow">
      <template #headerCell="{ column, title }">
        <div v-if="column.title == 'display_name'">
          <a-checkbox v-model:checked="isHightlight">高亮显示不同</a-checkbox>
        </div>
        <div v-else relative>
          <a-button type="link" right-20 top--10 absolute @click="remove(title)">
            <close-outlined />
          </a-button>
          <img :src="getHeader(title).websiteLogo" h-20 w-auto block alt="">
          <img :src="useImage(getHeader(title).partLogo)" w-80 h-80 block mt10 alt="">
          <div text-14 mt10>{{ getHeader(title).partName }}</div>
        </div>
      </template>
      <template #bodyCell="{ record, column, index }">
        <template v-if="column.dataIndex == 'display_name'">
          {{ record.name }}
        </template>
        <template v-if="column.dataIndex == 'data_show'">
          <div v-if="getCell(record, column.title)">
            <div v-if="record.isBasisc">{{ getCell(record, column.title) }}</div>
            <template v-else>
              <div v-for="item in getCell<true>(record, column.title).options" :key="item.optionName">
                <div>{{ item.optionName }}</div>
                <template v-if="item.originSpec">
                  <div v-for="spec in item.originSpec" text-gray>
                    ({{ spec.specName }}: {{ spec.specValue }})
                  </div>
                </template>
              </div>
            </template>
          </div>
          <template v-else>
            <div>-</div>
          </template>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
import { useCompareStore } from '~/store/compare';
import { partCompare } from '~/api/search';
import { CompareResult, SpecResultItem } from '~/api/search/type';
import { isEqual, omit } from 'lodash-es'

const compareStore = useCompareStore()
const data = ref<CompareResult[]>([])
const map = ref<Map<string, CompareResult>>(new Map)
const loading = ref(false)

const fetchCompare = () => {
  loading.value = true
  partCompare(compareStore.compareList).then(res => {
    if (res.code == 'ok') {
      data.value = res.data
      res.data.forEach(item => {
        map.value.set(item.partModel, item)
      })
    } else {
      message.error(res.message)
    }
    loading.value = false
  })
}

const isHightlight = ref(false)

onMounted(() => {
  fetchCompare()
})
const columns = computed(() => {
  const arr = [
    {
      title: 'display_name',
      dataIndex: 'display_name',
      fixed: 'left',
      ellipsis: true,
      width: 300
    }
  ] as Obj[]
  return arr.concat(data.value.map(item => {
    return {
      title: item.partModel,
      dataIndex: 'data_show',
      width: '300px',
    }
  }))
})

const merge = (res, arr: SpecResultItem[], model, map) => {
  arr.forEach(item => {
    if (map.has(item.specName)) {
      const obj = map.get(item.specName)
      obj[model] = item
      if (item.platform) {
        obj.platform = true
      }
      return
    }
    const obj = { name: item.specName, platform: item.platform, [model]: item }
    map.set(item.specName, obj)
    res.push(obj)
  })
}

const dataSource = computed(() => {
  let arr: any[] = []
  const map = new Map
  data.value.forEach(item => {
    merge(arr, item.specs, item.partModel, map)
  })
  const platArr: any[] = []
  const nPlatArr: any[] = []
  arr.forEach(item => {
    if (item.platform) {
      platArr.push(item)
    } else {
      nPlatArr.push(item)
    }
  })
  arr = platArr.concat(nPlatArr)
  const category = {
    name: '类目',
    isBasisc: true
  }
  const brand = {
    name: '品牌',
    isBasisc: true
  }
  const model = {
    name: '型号',
    isBasisc: true
  }
  data.value.forEach(item => {
    category[item.partModel] = item.categoryName
    brand[item.partModel] = item.partBrand
    model[item.partModel] = item.partModel
  })
  return arr.length ? [category, brand, model].concat(arr) : []
})

const getHeader = (model: string) => {
  return map.value.get(model)!
}

const getCell = <T>(record, code) => {
  const res = record[code]
  return res as (T extends true ? SpecResultItem : string)
}

const customRow = (row, index) => {
  const style = {
    style: 'color: red'
  }
  if (isHightlight.value && index > 2) {
    const arrs = Object.values(omit(row, ['name', 'platform']))
    if (arrs.length != data.value.length) return style
    if (arrs.length > 1) {
      const priority = arrs[0]
      for (let i = 1; i < arrs.length; i++) {
        if (!isEqual(arrs[i], priority)) {
          return style
        }
      }
    }
  } else {
    if (row.platform) {
      return {
        style: 'font-weight: bold'
      }
    }
  }
  return {
    style: 'color: #999999'
  }
}

const remove = (title: string) => {
  Modal.confirm({
    title: '提示',
    content: '是否删除型号' + title,
    onOk: () => {
      compareStore.remove(title)
      fetchCompare()
    }
  })
}
</script>
