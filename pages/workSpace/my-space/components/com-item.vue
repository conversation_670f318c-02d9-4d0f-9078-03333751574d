<template>
  <div card--reactive hover:translate-y-3 justify-center text-14 overflow-hidden flex flex-col :key="item.shopId">
    <!-- shopName: 商城名称 intro: 简介 mainProduct: 主营 firmName: 企业名称 -->
    <div flex-1 px-10 text-center>
      <div h-100 my-4>
        <img w-full h-full object-scale-down :src="useImage(convertImage(item.merchantLogo))" alt="" />
      </div>
      <div font-bold truncate text-center :title="item.merchantName">
        {{ item.merchantName }}
      </div>
      <div text-center mt-8 truncate :title="item.merchantShortName">
        {{ item.merchantShortName || '--' }}
      </div>
      <div line-clamp-2 text-gray mt-8 text-left :title="item.merchantSlogan">
        {{ item.merchantSlogan }}
      </div>
      <!-- <div line-clamp-2 text-gray mt-8 text-left :title="item.mainProduct">{{ item.mainProduct }}</div> -->
    </div>

    <div h-40 leading-40 mt-16 w-full :class="disabled ? 'btn-disabled' : 'btn-primary'" @click="handleClick">
      {{ text }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { CompItem, ComResult, ComStatus } from '~/api/mall-platform/types'

const props = defineProps<{
  item: CompItem
  map: Map<number, [ComStatus, ComResult]>
}>()

const { item, map } = toRefs(props)

const emits = defineEmits<{
  click: []
}>()

const text = computed(() => {
  const status = map.value.get(item.value.shopCompanyId)
  if (status == undefined) return '申请加入'

  const [applyStatus, applyResult] = status
  if (applyStatus == ComStatus.noApply) return '已发送申请'
  else if (applyStatus == ComStatus.apply) {
    if (applyResult == ComResult.fail) {
      return '已拒绝,重新申请'
    } else {
      return '已加入'
    }
  }
  return '申请加入'
})

const disabled = computed(() => {
  if (text.value == '已拒绝,重新申请' || text.value == '申请加入') return false
  return true
})

const handleClick = () => {
  if (!disabled.value) {
    emits('click')
  }
}
</script>
