<template>
  <div text-20 bg-white py-50 flex-1>
    <div max-w="60%" mx-auto>
      <a-input-search
        v-model:value="page.merchantName"
        enter-button
        size="large"
        placeholder="搜索企业名称"
        @search="search"
        allow-clear
      ></a-input-search>
    </div>
    <img v-if="!hasSearch" :src="join" mx-auto block alt="" />
    <template v-else>
      <card-skeleton :count="page.size" v-if="loading" px-50></card-skeleton>
      <template v-if="companyList.length">
        <div grid grid-cols-4 gap="x-20 y-30" mt-20 px-50>
          <Item
            v-for="item in companyList"
            :key="item.shopCompanyId"
            :item="item"
            :map="userMap"
            @click="apply(item)"
          ></Item>
        </div>

        <a-pagination
          v-model:current="page.current"
          v-model:page-size="page.size"
          show-size-changer
          show-quick-jumper
          text-right
          m-16
          :total="page.total"
          @change="handleChange"
        ></a-pagination>
      </template>
      <Empty mt-40 v-else />
    </template>
  </div>
</template>

<script setup lang="ts">
import {
  CompBind,
  CompItem,
  ComResult,
  ComStatus,
} from '~/api/mall-platform/types'
import { Pagination } from '~/api/shop/type'
import join from '~/assets/images/join.png'
import Item from './components/com-item.vue'
import { userStore } from '~/store/user'
import { bind } from 'lodash'

const user = userStore()
const hasSearch = ref(false)

const companyList = ref<CompItem[]>([])
const page = reactive({
  current: 1,
  size: 10,
  merchantName: '',
  total: 0,
})

const userMap = ref<Map<number, [ComStatus, ComResult]>>(new Map())

const loading = ref(0)
const bindLoading = ref(false)
const fetchCompany = async () => {
  hasSearch.value = true
  const res = await plat_http.get(
    '/platform/shopCompany/page',
    pick(page, ['current', 'size', 'merchantName']),
  )

  useMall(res, () => {
    const ret = JSON.parse(res.data) as Pagination<CompItem>
    companyList.value = ret.records
    page.total = ret.total
  })
}

const fetchBindList = async () => {
  const res = await plat_http.get('/shop/merchantUser/page', {
    size: 9999,
    userMobile: user.user.userMobile,
  })
  useMall(res, () => {
    const ret = JSON.parse(res.data) as Pagination<CompBind>
    const map = new Map()
    ret.records.forEach((item) => {
      if (!map.get(item.merchantId)) {
        map.set(item.merchantId, [item.auditStatus, item.auditResult])
      }
    })
    userMap.value = map
  })
}

const apply = async (item: CompItem) => {
  if (bindLoading.value) return
  bindLoading.value = true
  const res = await plat_http.post('/shop/merchantUser', {
    merchantId: item.shopCompanyId,
    userMobile: user.user.userMobile,
    shopId: 0,
    auditStatus: ComStatus.noApply,
    auditResult: ComResult.fail,
  })
  await useMall(res, () => {
    message.success('发送申请成功')
    fetchBindList()
  })
  bindLoading.value = false
}
const fetchData = async () => {
  page.current = 1
  loading.value++
  await Promise.all([fetchBindList(), fetchCompany()])
  loading.value--
}

const search = () => {
  if (!page.merchantName) {
    return
  }
  fetchData()
}

const handleChange = () => {
  fetchData()
}

onMounted(() => {})
</script>
