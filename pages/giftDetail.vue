<template>
  <NuxtLayout name="selection">
    <div p-y-8>
      <div flex flex-row justify-start mb-8>
        <a-button type="link" size="small" mr-10 @click="() => $router.go(-1)">&lt; 返回</a-button>
        <a-breadcrumb separator=">">
          <a-breadcrumb-item>
            <NuxtLink to="/">首页</NuxtLink>
          </a-breadcrumb-item>
          <a-breadcrumb-item>
            <NuxtLink to="/scoreGift">积分商城</NuxtLink>
          </a-breadcrumb-item>
          <a-breadcrumb-item>
            {{ route.query.prodName }}
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>

      <div mt-8 flex flex-col bg-white class="content-box">
        <div flex p-20 style="border-bottom: 10px solid #eff2f7">
          <div w-200 h-200 overflow-hidden>
            <img w-full h-full object-scale-down :src="useImage(convertImage(prodInfo.pic))" alt="" />
          </div>

          <div flex flex-1 flex-col justify-around px-20 text="14 #999" class="prod-info-box">
            <div class="gift-name" :title="prodInfo.name">
              {{ prodInfo.prodName }}
            </div>
            <!-- 规格 -->
            <div v-for="(item, index) in Object.keys(propList)" :key="index">
              <span>{{ item }}：</span>
              <span
                v-for="(_item, _index) in propList[item]"
                :key="_index"
                :class="['sku-tag', defaultSku.propList.includes(`${item}:${_item}`) ? 'sku-tag-active' : '']"
                @click="onSelSku(item, _item)"
              >
                {{ _item }}
              </span>
            </div>
            <div>
              兑换数量：
              <a-button type="default" size="small" :disabled="buyNum <= 1" @click="buyNum--">
                <template #icon>
                  <MinusOutlined />
                </template>
              </a-button>
              <a-input-number :min="1" size="small" class="num-input" v-model:value="buyNum" />
              <!-- <span px-10>{{ buyNum }}</span> -->
              <a-button type="default" size="small" @click="buyNum++">
                <template #icon>
                  <PlusOutlined />
                </template>
              </a-button>
            </div>
            <div>
              所需积分：
              <span class="gift-score">{{ (defaultSku.skuScore * buyNum).toLocaleString() }}积分</span>
            </div>
            <div v-if="user.status !== 2">
              <a-button type="primary" @click="onBuy" disabled>已售罄</a-button>
            </div>
          </div>
        </div>

        <div p-20 text="15">
          <p class="row-title"><span>产品简介</span></p>

          <div p-20 style="overflow: auto" v-html="prodInfo.content"></div>
        </div>
      </div>
    </div>

    <a-modal v-model:open="visible" title="积分兑换" :width="1200" :maskClosable="false" wrapClassName="top-modal">
      <div p-20 text="15">
        <p class="row-title"><span>收货地址</span></p>

        <div flex flex-wrap>
          <div
            v-for="(item, index) in addressList"
            :key="index"
            :class="['address-item', selectIndex == index ? 'address-item-active' : '']"
            @click="selAddress(index)"
          >
            <div>
              <span class="label">联系人:</span>
              <span class="value">{{ item.receiver }}</span>
            </div>
            <div>
              <span class="label">所在地区:</span>
              <span class="value">{{ (item.province || '') + (item.city || '') + (item.area || '') }}</span>
            </div>
            <div>
              <span class="label">详细地址:</span>
              <span class="value">{{ item.addr }}</span>
            </div>
            <div>
              <span class="label">手机号:</span>
              <span class="value">{{ item.mobile }}</span>
            </div>

            <div v-if="item.commonAddr" class="default-tag">默认地址</div>
            <div v-if="selectIndex == index" class="selected-tag" text-12><CheckOutlined /></div>
          </div>
        </div>
      </div>

      <div p-20 text="15">
        <p class="row-title"><span>产品信息</span></p>

        <div flex py-10>
          <div w-120 h-120 overflow-hidden>
            <img w-full h-full object-scale-down :src="useImage(convertImage(prodInfo.pic))" alt="" />
          </div>

          <div flex flex-1 flex-col justify-around px-20 text="15 #999">
            <div class="gift-name" :title="prodInfo.name">
              {{ prodInfo.prodName }}
            </div>
            <div w-500 flex justify-between>
              <span class="gift-score">
                <b text="14 #333">兑换数量:</b>
                {{ buyNum }}
              </span>
              <span class="gift-score">
                <b text="14 #333">当前积分:</b>
                {{ score }}
              </span>
              <span class="gift-score">
                <b text="14 #333">兑换后积分:</b>
                {{ score - defaultSku.skuScore * buyNum }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div flex items-center>
          <a-checkbox v-model:checked="buyFrom.checked">
            我已阅读并同意
            <a @click.stop="readRule">《积分规则》</a>
          </a-checkbox>
          <div flex-1 text-right>
            <a-button type="default" @click="visible = false">取消</a-button>
            <a-button type="primary" :loading="submitLoading" :disabled="!buyFrom.checked" @click="onSubmit">
              兑换
            </a-button>
          </div>
        </div>
      </template>
    </a-modal>

    <a-modal v-model:open="successVisible" title="提示" :footer="null">
      <div text-center>
        <img w-150 src="~/assets/images/qr_helper.jpg" />
        <div title-text font-size-20 my-8>
          <CheckCircleFilled color-green-6 />
          兑换成功
        </div>
        <div>添加小助手，加入礼品兑换群（审核后通过）</div>
      </div>
    </a-modal>
  </NuxtLayout>
</template>

<script setup>
import { getTotalScore } from '~/api/user'
import { MemberLevel, userStore } from '~/store/user'
import { usemessageStore } from '~/store/message'

const useMessage = usemessageStore()
const router = useRouter()
const route = useRoute()

const successVisible = ref(false)

const readRule = () => {
  router.push({
    path: '/agreement/scoreConfig',
  })
}

const prodInfo = ref({})
const skuList = ref([])
const getInfo = (prodId) => {
  http('/mall/prod/prodInfo', {
    method: 'get',
    params: {
      prodId,
    },
  }).then((res) => {
    useMall(res, (ret) => {
      prodInfo.value = ret
      skuList.value = ret.skuList
      defaultSku.value = ret.skuList[0]
      if (ret.skuList.length > 1) {
        defaultSku.value.propList = defaultSku.value.properties.split(';')
        setPropList(ret.skuList)
      }
    })
  })
}

const propList = ref({})
const setPropList = (list) => {
  const ret = {}
  list.forEach((d) => {
    const _propList = d.properties.split(';')
    d.propList = _propList
    _propList.forEach((_d) => {
      const [k, v] = _d.split(':')
      if (ret[k]) {
        !ret[k].includes(v) && ret[k].push(v)
      } else {
        ret[k] = [v]
      }
    })
  })
  propList.value = ret
}

const onSelSku = (key, value) => {
  const oldPropList = defaultSku.value.propList
  const props = {}
  oldPropList.forEach((d) => {
    const [k, v] = d.split(':')
    props[k] = k == key ? value : v
  })
  const Keys = Object.keys(props)
  const findSku = skuList.value.find((d) => {
    for (let i = 0; i < Keys.length; i++) {
      const _prop = `${Keys[i]}:${props[Keys[i]]}`
      if (!d.propList.includes(_prop)) {
        return false
      }
    }
    return true
  })
  if (!findSku) {
    message.warning('此产品缺货,请选择其他规格')
    return
  }
  defaultSku.value = findSku
}

const addressList = ref([])
const getAddressList = () => {
  return http('/mall/p/address/list', {
    method: 'get',
    params: {},
  }).then((res) => {
    useMall(res, (ret) => {
      addressList.value = ret
      selectIndex.value = addressList.value.findIndex((d) => d.commonAddr)
    })
  })
}

const selectIndex = ref(0)
const selAddress = (index) => {
  selectIndex.value = index
}

const userStoreObj = userStore()
const { user } = storeToRefs(userStoreObj)
const buyNum = ref(1)
const visible = ref(false)
const buyFrom = ref({})
const onBuy = async () => {
  if (!user.value.userId) {
    loginPop.show(() => {
      // location.reload()
    })
    return
  }
  if (!useMember()) return
  await getAddressList()
  if (addressList.value.length === 0) {
    Modal.confirm({
      title: '提示',
      content: '请先完善地址信息',
      okText: '去完善',
      cancelText: '取消',
      onOk() {
        router.push('/my/address')
      },
      onCancel() {},
    })
    return
  }
  const { skuScore } = defaultSku.value
  getTotalScore().then((res) => {
    useRes(res, () => {
      score.value = res.data.score
      if (skuScore * buyNum.value - score.value > 0) {
        message.warning('积分余额不足')
        return
      }
      visible.value = true
    })
  })
}

const submitLoading = ref(false)
const onSubmit = () => {
  if (selectIndex.value < 0) {
    message.warning('请选择收货地址')
    return
  }
  const addrId = addressList.value[selectIndex.value].addrId
  submitLoading.value = true
  http('/mall/score/exchange/exchange', {
    method: 'post',
    body: {
      userId: user.value.userId,
      prodId: prodId,
      skuId: defaultSku.value.skuId,
      prodCount: buyNum.value,
      addrId,
    },
  })
    .then((res) => {
      useMall(res, (ret) => {
        successVisible.value = true
        visible.value = false
        useMessage.updatemsgCount()
      })
    })
    .finally(() => {
      submitLoading.value = false
    })
}

const score = ref(0)
let prodId = ''
const defaultSku = ref({})
onMounted(() => {
  prodId = route.query.prodId
  getInfo(prodId)
})
</script>

<style lang="less" scoped>
.content-box {
  min-height: calc(100vh - 225px);
  position: relative;

  .prod-info-box > div {
    min-height: 32px;
    display: flex;
    align-items: center;
  }

  .num-input {
    border: 0;
    outline: none;
    width: 50px;
    text-align: center;

    :deep(.ant-input-number-handler-wrap) {
      display: none;
    }

    :deep(.ant-input-number-input) {
      text-align: center;
    }
  }
}

.gift-name {
  font-size: 16px;
  font-weight: bold;
  word-break: keep-all;
  max-width: 90%;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #000;
}

.gift-score {
  font-size: 14px;
  font-weight: bold;
  color: var(--primary-color);
}

.row-title {
  padding: 0;
  margin: 0;
  line-height: 20px;

  span {
    display: inline-block;
    padding: 0 8px;
    border-left: 3px solid var(--primary-color);
  }
}

.address-item {
  min-width: 300px;
  width: 30%;
  padding: 10px;
  margin: 10px 10px 0;
  line-height: 30px;
  cursor: pointer;
  box-shadow: 0 3px 25px #ccc;
  position: relative;

  .label {
    display: inline-block;
    width: 90px;
    padding-right: 10px;
    text-align: right;
  }

  .value {
    word-break: break-all;
  }

  .default-tag {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 12px;
    background: green;
    color: #fff;
    border-radius: 3px;
    line-height: 20px;
    padding: 0 5px;
  }

  .selected-tag {
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    position: absolute;
    bottom: 0;
    left: 0;
    background: var(--primary-color);
    color: #fff;
  }
}

.address-item-active {
  border: 1px solid var(--primary-color);
}

.sku-tag {
  border: 1px solid #ccc;
  cursor: pointer;
  margin: 0 5px;
  padding: 3px 5px;

  &:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
  }
}

.sku-tag-active {
  border-color: var(--primary-color);
  color: var(--primary-color);
}
</style>
