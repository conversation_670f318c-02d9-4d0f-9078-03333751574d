<template>
  <div h-100vh flex flex-col justify-center items-center bg-powder>
    <img
      h-400
      src="~/assets/images/404.png"
      :style="{ maskImage: 'radial-gradient(circle, black 35%, transparent 65%)' }"
    />
    <div style="font-size: 20px">
      <a-button type="text" size="large" :icon="h(RollbackOutlined)" @click="() => $router.go(-1)">
        {{ message }}
      </a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { h } from 'vue'
import { RollbackOutlined } from '@ant-design/icons-vue'
definePageMeta({
  layout: false,
})

const router = useRouter()
const route = useRoute()
const message = ref(route.query.message || ('Error' as string))
</script>

<style lang="less" scoped></style>
