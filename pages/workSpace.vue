<template>
  <div class="h-full w-full">
    <div class="w-full h-full overflow-hidden flex relative" v-if="!route.meta.noSidebar">
      <div :class="[collapsed ? 'w-80px' : 'w-250px']" class="h-full transition-all bg-white z-10 relative group">
        <div
          @click="toggleCollapse"
          class="absolute cursor-pointer text-#fff text-16px overflow-hidden flex-center w-36px h-36px right--18px top-16px bg-primary rounded-full transition-opacity duration-300 opacity-0 group-hover:opacity-100 z-10"
        >
          <MenuFoldOutlined v-if="!collapsed"></MenuFoldOutlined>
          <MenuUnfoldOutlined v-else></MenuUnfoldOutlined>
        </div>
        <div class="w-full h-full overflow-y-auto">
          <a-menu
            :openKeys="openKeys"
            :selectedKeys="selectedKeys"
            :inline-collapsed="collapsed"
            mode="inline"
            @click="go"
          >
            <menu-item v-for="item in menuList" :key="item.key" :item="item"></menu-item>
          </a-menu>
        </div>

        <!-- <div class="collapse-btn" @click="toggleCollapse"> -->
        <!--   <LeftOutlined v-if="!collapsed" /> -->
        <!--   <RightOutlined v-else /> -->
        <!-- </div> -->
      </div>
      <div class="flex-1 overflow-x-hidden overflow-y-auto p-24px pt-0 relative">
        <div flex items-center h-40px>
          <a-button type="link" class="text-primary!" size="small" mr-10 @click="() => $router.go(-1)">
            &lt; 返回
          </a-button>
          <a-breadcrumb separator=">">
            <!-- <a-breadcrumb-item> -->
            <!--   <NuxtLink to="/">首页</NuxtLink> -->
            <!-- </a-breadcrumb-item> -->
            <a-breadcrumb-item>
              <nuxt-link to="/workSpace">首页</nuxt-link>
            </a-breadcrumb-item>
            <a-breadcrumb-item v-if="navName">{{ navName }}</a-breadcrumb-item>
          </a-breadcrumb>
        </div>

        <div class="p-16 bg-#fff min-h-[calc(100vh-152px)]">
          <NuxtPage :page-key="route.fullPath" />
        </div>
      </div>
    </div>

    <template v-else>
      <div flex items-center h-40px>
        <a-button type="link" class="text-primary!" size="small" mr-10 @click="() => $router.go(-1)">
          &lt; 返回
        </a-button>
        <a-breadcrumb separator=">">
          <a-breadcrumb-item>
            <NuxtLink to="/workSpace">首页</NuxtLink>
          </a-breadcrumb-item>
          <a-breadcrumb-item>
            <nuxt-link to="/workSpace">工作空间</nuxt-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item v-if="navName">{{ navName }}</a-breadcrumb-item>
        </a-breadcrumb>
      </div>
      <nuxt-page :page-key="route.fullPath"></nuxt-page>
    </template>
  </div>
</template>

<script setup>
import { MemberLevel, userStore } from '~/store/user'
import { companyStore } from '~/store/company'
import { authMenuStore } from '~/store/authMenu'
import { getMerchantByUser } from '~/api/mall-manage/index'
import MenuItem from './components/menuItem.vue'

definePageMeta({
  middleware: ['workspace'],
  layout: 'workspace',
})

const router = useRouter()
const route = useRoute()

const userStoreObj = userStore()
const { user } = storeToRefs(userStoreObj)

const companyList = ref([])
// 'my-space', 'company-edu', 'company-sale', 'company-procure', 'company-auth', 'special-customer', 'company-set'
const openKeys = ref([])
const selectedKeys = computed(() => {
  if (Object.keys(route.params).length) {
    return [route.path.split('/').at(-2)]
  }
  const list = route.path.split('/')
  if (list.length > 4) {
    return [list.at(-2)]
  } else {
    return [list.at(-1)]
  }
})

const noticeInfo = ref({
  title: '该功能需要在设备商工作台中才可使用',
  subtitle: '升级为设备商企业会员，解锁全平台功能，享受专属企业服务',
  type: 'warning',
})

const goToCompanySpace = (key) => {
  navigateTo(`/workSpace/company-space/${key}`)
}

const companyStoreObj = companyStore()
const { isPersonal } = storeToRefs(companyStoreObj)

const loadingCompany = ref(false)
const onClickChange = (v) => {
  if (!v) return
  loadingCompany.value = true
  getMerchantByUser({
    userMobile: user.value.userMobile,
  })
    .then((res) => {
      if (res.data) {
        companyList.value = JSON.parse(res.data)
      }
    })
    .finally(() => {
      loadingCompany.value = false
    })
}

onMounted(() => {
  // 普通会员不可进入工作空间
  if (user.value.level == MemberLevel.NORMAL) {
    Modal.confirm({
      title: '提示',
      content: '当前会员等级无此权限,完善信息可升级会员等级',
      okText: '去完善',
      cancelButtonProps: { style: { display: 'none' } },
      onOk() {
        Modal.destroyAll()
        router.push('/my/info')
      },
    })
  }
  onClickChange(true)
})

const useAuthMenu = authMenuStore()
const { commonMenuList, settingMenuList, menuSet } = storeToRefs(useAuthMenu)
const menuList = computed(() => {
  const menuGroup = menuSet.value.get(route.name)
  if (menuGroup == 1) return settingMenuList.value
  if (menuGroup == 0) return commonMenuList.value
  return []
})

const formatterMenu = (menuList) => {
  menuList.forEach((d) => {
    d.key = d.url
    d.children = (d.list || []).filter((d) => !d.perms)
    if (d.children.length) {
      formatterMenu(d.children)
    }
  })
  // 过滤没有子菜单的目录
  return menuList.filter((d) => d.children.length)
}

const go = ({ key }) => {
  if (isPersonal.value) {
    if (menuSet.value.has(useAuthMenu.getMenuKey(key))) {
      goToCompanySpace(key)
    } else {
      Guide.show()
    }
  } else {
    goToCompanySpace(key)
  }
}

const handleMemberUpgrade = () => {
  // 跳转到企业会员升级页面或其他相关页面
  // 这里可以根据实际需求调整跳转逻辑
  router.push('/my/info') // 或其他升级页面
}

const menuMap = computed(() => {
  openKeys.value = []
  const map = {}
  const dfs = (list) => {
    list.forEach((item) => {
      if (item.type == 0) {
        openKeys.value.push(item.key)
      }
      if (item.type == 1) {
        map[item.url] = item
      }
      if (item.children?.length) {
        dfs(item.children)
      }
    })
  }
  dfs(menuList.value)
  return map
})

const navName = computed(() => {
  return menuMap.value[selectedKeys.value[0]]?.name || route.meta.pageName || ''
})

const collapsed = ref(false)

const toggleCollapse = () => {
  collapsed.value = !collapsed.value
}
</script>

<style lang="less" scoped>
.company-box {
  min-width: 230px;
  max-height: 300px;
  overflow: auto;
}

.company-box::-webkit-scrollbar {
  width: 5px; //滚动条宽度
}
.company-box::-webkit-scrollbar-thumb {
  border-radius: 10px; //滚动条圆角
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); //滚动区域底色
  opacity: 0.2; //透明度
  background: #5cbff0; //滚动条背景色
}
.company-box::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); //滚动区域底色
  border-radius: 0;
}

.company-item:hover {
  background: #e5e7ef;
}

.company-item img {
  width: 16px;
  height: 16px;
}

.company-item b {
  vertical-align: middle;
  display: inline-block;
  width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.collapse-btn {
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 48px;
  background: #fff;
  border: 1px solid #e5e7ef;
  border-left: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 0 4px 4px 0;
  font-size: 16px;
  &:hover {
    color: #f94c30;
  }
}
</style>
