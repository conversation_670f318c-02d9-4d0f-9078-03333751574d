<template>
  <div text-center id="wx_login_container"></div>

  <div class="all-tool">
    <div style="width:250px;height:400px;" class="course-container" id="myImage">
      <div class="course">
        <img style="width:250px;height:400px;" src="~@/assets/images/invite_fg.jpg" />
      </div>
      <div style="bottom:66px;left:50px;margin-left:50px" class="code">
        <figure class="qrcode">
          <vue-qrcode :value="inviteCode" tag="svg" :size="100"></vue-qrcode>
          <img
            class="qrcode__image"
            :src="yidao"
          />
        </figure>
      </div>
    </div>
  </div>
  <div class="btn-all" type="button">
    <a-button size="mini" type="primary" @click="saveImage()">下载海报</a-button>
  </div>
  <a id="link"></a>
</template>

<script setup>
import VueQrcode from 'vue-qrcode'
import yidao from "~/assets/images/yidao.png"
import wxinit from '@/assets/js/wxLogin'
import html2canvas from "html2canvas"

const initWxCode = (el) => {
  new WxLogin({
    self_redirect: false,
    id: el,
    appid: 'wx14817ec4ef183e7b',
    scope: 'snsapi_login',
    redirect_uri: encodeURIComponent(
      `https://www.yidao.cloud/login`,
    ),
    state: Math.ceil(Math.random() * 1000),
    style: '',
    href: 'data:text/css;base64,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',
  })
}

const initWx = () => {
  // 引入 微信登录
  let script = document.createElement('script')
  script.type = 'text/javascript'
  script.src = 'https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js'
  // 引入成功
  script.onload = function () {
    console.log('wx.js资源已加载成功了')
    wxinit(window, document)
    initWxCode('wx_login_container')
  }
  // 引入失败
  script.onerror = function () {
    console.log('wx.js资源加载失败了')
  }
  document.head.appendChild(script)
}

const route = useRoute()

const saveImage = () => {
  html2canvas(document.querySelector("#myImage")).then((canvas) => {
    var image = canvas.toDataURL("image/png").replace("image/png", "image/octet-stream");
    var link = document.getElementById("link");
    link.setAttribute("download", "海报.png");
    link.setAttribute("href", canvas.toDataURL("image/png").replace("image/png", "image/octet-stream"));
    link.click();
  });
}

const inviteCode = 'http://************:3000/test'
onMounted(async () => {
  initWx()
})
</script>

<style lang="less" scoped>
.all-tool {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  justify-content: center;
  background: #fff;
  box-sizing: border-box;

  .course-container {
    height: 400px;
    width: 300px;
    position: relative;
  }

  .course {
    z-index: 1;
    position: absolute;
  }

  .code {
    z-index: 2;
    position: absolute;
  }
}

.qrcode {
  display: inline-block;
  font-size: 0;
  margin-bottom: 0;
  position: relative;
}

.qrcode__image {
  background-color: #fff;
  border: 1px solid #fff;
  width: 20%;
  height: 20%;
  position: absolute;
  top: 50%;
  left: 50%;
  overflow: hidden;
  transform: translate(-50%, -50%);
}
</style>