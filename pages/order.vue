<template>
  <NuxtLayout name="default" :width="1680">
    <div p-y-8>
      <div flex flex-row justify-start mb-8>
        <a-button type="link" size="small" mr-10 @click="() => $router.go(-1)">
          &lt; 返回
        </a-button>
        <a-breadcrumb separator=">">
          <a-breadcrumb-item>
            <NuxtLink to="/">首页</NuxtLink>
          </a-breadcrumb-item>
          <a-breadcrumb-item>{{ routePath == 'create' ? '创建订单' : '支付订单' }}</a-breadcrumb-item>
        </a-breadcrumb>
      </div>
      
      <div mt-8 flex class="content-box">
        <NuxtPage :page-key="route.fullPath" />
      </div>
    </div>
  </NuxtLayout>
</template>

<script setup>
import { MemberLevel, userStore } from '~/store/user'
import { companyStore } from '~/store/company'

definePageMeta({
  layout: false
})

const userStoreObj = userStore()
const { user } = storeToRefs(userStoreObj)
const companyStoreObj = companyStore()
const { company } = storeToRefs(companyStoreObj)
const curCompanyId = computed(() => {
  return company.value.shopCompanyId
})

const router = useRouter()
const route = useRoute()

const routePath = computed(() => {
  return route.path.split('/').pop()
})

onMounted(() => {
  // 普通会员不可进入工作空间
  if (user.value.level == MemberLevel.NORMAL) {
    Modal.confirm({
      title: '提示',
      content: '当前会员等级无此权限,完善信息可升级会员等级',
      okText: '去完善',
      cancelButtonProps: { style: { display: 'none' } },
      onOk() {
        Modal.destroyAll()
        router.push('/my/info')
      }
    })
  }
})
</script>

<style lang="less" scoped>
.content-box {
  min-height: calc(100vh - 225px);
  position: relative;

  .box-left {
    // max-height: calc(100vh - 180px)
  }
}

.company-box{
  min-width: 230px;
  max-height: 300px;
  overflow: auto;
}

.company-box::-webkit-scrollbar {
  width: 5px; //滚动条宽度
}
.company-box::-webkit-scrollbar-thumb {
  border-radius: 10px; //滚动条圆角
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); //滚动区域底色
  opacity: 0.2; //透明度
  background: #5cbff0; //滚动条背景色
}
.company-box::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); //滚动区域底色
  border-radius: 0;
}

.company-item:hover{
  background: #e5e7ef;
}

.company-item img {
  width: 16px; 
  height: 16px;
}

.company-item b {
  vertical-align: middle;
  display: inline-block;
  width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
