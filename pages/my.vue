<template>
  <NuxtLayout name="selection">
    <div p-y-8>
      <div flex flex-row justify-start mb-8>
        <a-button type="link" size="small" mr-10 @click="() => $router.go(-1)">&lt; 返回</a-button>
        <a-breadcrumb separator=">">
          <a-breadcrumb-item>
            <NuxtLink to="/">首页</NuxtLink>
          </a-breadcrumb-item>
          <a-breadcrumb-item>
            <NuxtLink to="/my/info">个人中心</NuxtLink>
          </a-breadcrumb-item>
          <a-breadcrumb-item>{{ current }}</a-breadcrumb-item>
        </a-breadcrumb>
      </div>
      <div mt-8 flex class="content-box">
        <div mr-16 w-200 flex="~ col">
          <div card mb-16 p-16 flex="~ col items-center">
            <a-avatar :src="user.pic" :size="80" class="bg-primary">
              {{ user.nickName }}
            </a-avatar>
            <span m-y-8 font-size-20 break-words text-center>
              {{ user.nickName }}
            </span>
            <div
              :style="{ background: memberLevel?.color }"
              class="py-2 px-4 mb-10 inline-block rounded-8 text-12 text-white cursor-pointer"
            >
              {{ memberLevel?.label }}
            </div>
            <span sub-text break-all>
              {{ user.userMemo || '这个人很懒，什么都没有写。' }}
            </span>
          </div>
          <div text card flex-1 p-y-8>
            <a-menu :selectedKeys="selectedKeys" mode="inline" @click="goTo">
              <a-menu-item :key="item.path" v-for="item in menuList">
                <template #icon>
                  <component pl-28 :is="item.icon"></component>
                </template>
                <span>{{ item.label }}</span>
              </a-menu-item>
            </a-menu>
          </div>
        </div>
        <div card flex="~ 1" style="position: relative">
          <water-mask
            v-if="user.status == 2"
            style="position: absolute; z-index: 10; width: 100%; height: 100%; overflow: hidden"
          >
            <!-- <img w-full h-full src="~/assets/images/invite_bg.jpg"/> -->
          </water-mask>
          <NuxtPage />
        </div>
      </div>
    </div>
  </NuxtLayout>
</template>

<script lang="ts" setup>
import {
  IdcardOutlined,
  LockOutlined,
  SketchOutlined,
  TransactionOutlined,
  SendOutlined,
  AimOutlined,
} from '@ant-design/icons-vue'
import { MemberLevel, userStore } from '~/store/user'

const router = useRouter()
const route = useRoute()
const selectedKeys = computed(() => {
  return [route.path.split('/').pop()]
})

const goTo = ({ key }) => {
  router.push({
    path: `/my/${key}`,
  })
}

const userStoreObj = userStore()
const { user } = storeToRefs(userStoreObj)
const current = computed(() => {
  const path = route.path.split('/').at(-1)
  const text = menuList.find((item) => item.path == path)?.label
  return text
})

const memberLevel = computed(() => {
  switch (user.value.level) {
    case MemberLevel.NORMAL:
      return { label: '普通会员', color: '#71c4ef' }
    case MemberLevel.AUTHENTICATION:
      return { label: '认证会员', color: '#67abff' }
    case MemberLevel.GENERALIZATION:
      return { label: '推广会员', color: '#1c344f' }
    case MemberLevel.PROFESSIONAL:
      return { label: '专业会员', color: 'var(--primary-color)' }
  }
})

let menuList = [
  { id: 1, label: '个人资料', path: 'info', icon: IdcardOutlined, show: true },
  { id: 2, label: '账户安全', path: 'security', icon: LockOutlined, show: true },
  { id: 3, label: '会员等级', path: 'member', icon: SketchOutlined, show: true },
  // { id: 4, label: '我的收藏', path: '/my/favourite', show: true },
  { id: 5, label: '我的积分', path: 'score', icon: TransactionOutlined, show: true },
  { label: '地址信息', path: 'address', icon: AimOutlined },
  // { id: 6, label: '邀请注册', path: 'invite', icon: SendOutlined, show: true },
]

const onMenuClick = (menu: any) => {
  router.push(menu.path)
}
</script>

<style lang="less" scoped>
.content-box {
  min-height: calc(100vh - 225px);
}
</style>
