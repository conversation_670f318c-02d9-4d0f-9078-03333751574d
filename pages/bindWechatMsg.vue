<template>
  <div h-100vh flex flex-col justify-center items-center>
    <img v-if="success" h-200 src="~/assets/images/daodao_on.png" :style="{ maskImage: 'radial-gradient(circle, black 35%, transparent 65%)' }" />
    <img v-else h-200 src="~/assets/images/daodao_error.jpg" :style="{ maskImage: 'radial-gradient(circle, black 35%, transparent 65%)' }" />
    <span style="font-size: 16px; margin: 10px;">{{ message }}</span>
    <div v-if="!success" style="margin: 10px;">
      <a-button 
        type="primary"
        @click="handleReScan"
      >重新扫码</a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: false
})

const route = useRoute();
const message = ref(route.query.message || ('Error' as string));
const success = ref<boolean>(false)
success.value = message.value == '绑定成功'

setTimeout(() => {
  if (success.value) {
    window.parent.postMessage('closeDialog', '*')
  }
}, 1500)

const handleReScan = () => {
  window.parent.postMessage('reScan', '*')
}
</script>

<style lang="less" scoped></style>
