<template>
  <div pb-8 class="search-page">
    <nav-list></nav-list>

    <div class="w-full p-12 bg-#fff content-box" @click.prevent text-14>
      <div flex gap-16 class="categoryTree-box">
        <div w-200 overflow-y-auto>
          <a-tree
            :tree-data="treeData as any"
            v-if="treeData.length"
            default-expand-all
            :showIcon="false"
            v-model:selected-keys="selectedKeys"
            @select="(_, e) => clickNode(e.node.dataRef)"
            :field-names="{ key: 'id' }"
          >
            <template #title="node">
              <img v-if="node.logoUrl" :src="node.logoUrl" w-20 h-20 leading-20 mr-5 />
              <span :title="node.categoryName">
                {{ node.categoryName }}
              </span>
            </template>
          </a-tree>
        </div>

        <div flex-1 overflow-y-auto style="border-left: 1px solid #eee">
          <h3 pb-4 pl-8 style="border-bottom: 1px solid #eee">
            <LeftOutlined v-if="tree.treeMap[active.parentId]" cursor-pointer @click="goPrev" />
            {{ active.categoryName }}
          </h3>
          <a-spin v-if="loading"></a-spin>
          <template v-else-if="data.length">
            <div class="flex flex-wrap pl-12px text-14">
              <div class="w-1/3 truncate relative" v-for="item in data" :key="item.id" @click="clickItem(item)">
                <div
                  v-if="item.type == 'TRUNK'"
                  flex
                  items-center
                  h-80
                  leading-25
                  rounded-8
                  cursor-pointer
                  class="hover:bg-primary px-10 hover:text-#fff w-full"
                  truncate
                  :title="item.categoryName"
                >
                  <img :src="item.categoryImgUrl || folder" w-50 h-50 mr-10 alt="" />
                  <div flex flex-col flex-1 overflow-hidden>
                    <div truncate>{{ item.categoryName }}</div>
                    <div>类目数: {{ item.categorySize }}</div>
                  </div>
                  <folder-filled
                    text-yellow
                    absolute
                    text-20
                    bottom-10
                    left-10
                    v-if="item.categoryImgUrl"
                  ></folder-filled>
                </div>

                <div
                  v-if="item.type === 'LEAF'"
                  inline-block
                  h-80
                  leading-80
                  rounded-8
                  cursor-pointer
                  class="hover:bg-primary px-10 hover:text-#fff w-full"
                  truncate
                  :title="item.categoryName"
                >
                  <img :src="item.categoryImgUrl || unknownImg" w-50 h-50 mr-10 alt="" />
                  <span>
                    {{ item.categoryName }}
                  </span>
                </div>
              </div>
            </div>
          </template>

          <Empty mt-50 v-else></Empty>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { treeStore } from '~/store/tree'
import type { Category } from '~/types/category'
import unknownImg from '~/assets/images/unknown.png'
import folder from '~/assets/images/folder.svg'
import { omit } from 'lodash-es'

const treeStoreObj = treeStore()

const emits = defineEmits<{
  toDetail: []
}>()

const max = 2
const tree = computed(() => {
  let data: Category[] = []
  const treeMap: Record<
    string,
    {
      node: Category
      list: Category[]
    }
  > = {}
  const dfs = (list, arr, level) => {
    list.forEach((item) => {
      let node
      if (level <= max) {
        node = omit(item, 'children')
        if (level < max) {
          node.children = []
        }
        // @ts-ignore
        arr && node.type !== 'LEAF' && arr.push(node)
      }
      if (item.parentId) {
        treeMap[item.id] = {
          node: item,
          list: item.children || [],
        }
      }
      if (item.children?.length) {
        dfs(item.children, node?.children, level + 1)
      }
    })
  }
  dfs(treeStoreObj.tree.data, data, 0)
  data = data.reduce((res, item) => {
    if (item.children?.length) {
      res = res.concat(item.children)
    }
    return res
  }, [] as Category[])
  return {
    data,
    treeMap,
  }
})

const treeData = computed(() => tree.value.data)

const treeMap = computed(() => tree.value.treeMap)

const loading = ref(false)

const active = ref({} as Category)
const selectedKeys = ref<string[]>([])

const router = useRouter()
const goCategory = (id) => {
  emits('toDetail')
  router.push({
    path: '/category',
    query: { id, _t: new Date().getTime() },
  })
}

const route = useRoute()
const categoryId = route.query.cid as string

const handleClickBreadcrumb = (node) => {
  // const node = titleMap.value[title]
  treeStoreObj.setActive(node.id)
  node && openCategory(node)
}

const clickNode = async (node) => {
  active.value = node
}
const openCategory = (node: Category) => {
  active.value = node
}

const goPrev = () => {
  const node = treeMap.value[active.value.parentId]
  openCategory(node.node)
}

const data = computed(() => {
  let list: Category[] = []
  const node = treeMap.value[active.value.id]
  if (node) {
    list = node.list
  }

  return list
})

const clickItem = (item: Category) => {
  if (item.type == 'LEAF') {
    goCategory(item.categoryCode)
  } else if (item.type == 'TRUNK') {
    openCategory(item)
  }
}

onMounted(() => {
  treeStoreObj.setActive(categoryId)
  setTimeout(() => {
    if (categoryId) {
      active.value = treeStoreObj.treeMap[categoryId]
    } else {
      active.value = treeStoreObj.treeMap[tree.value.data[0]?.id] || {}
    }
  }, 500)
})
</script>

<style>
#__nuxt {
  overflow: hidden;
}
</style>

<style lang="less" scoped>
.categoryTree {
  :deep(.ant-tree-treenode) {
    width: 100%;

    .ant-tree-node-content-wrapper-normal {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow-x: hidden;
    }
  }
}

.content-box {
  min-height: calc(100vh - 225px);
}

.categoryTree-box {
  height: calc(100vh - 250px);
}
</style>
