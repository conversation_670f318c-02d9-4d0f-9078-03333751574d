<template>
  <wx-open-launch-weapp
    class="absolute left-0 top-0 w-full h-full"
    id="launch-btn"
    username="gh_xxxxxxxx"
    :path="path"
    @launch="launch"
  >
    <div v-is="'script'" type="text/wxtag-template">
      <div style="position: absolute; left: 0; top: 0; width: 100%; height: 100%" class="flex-center">
        <a-button type="primary">登录小程序</a-button>
      </div>
    </div>
  </wx-open-launch-weapp>
</template>

<script setup>
definePageMeta({
  layout: false,
})
useHead({
  script: [
    {
      src: 'http://res.wx.qq.com/open/js/jweixin-1.6.0.js',
      type: 'text/javascript',
    },
  ],
})

const route = useRoute()
const path = computed(() => {
  const brandId = route.query.brandId
  console.log('%c Line:22 🥛 brandId', 'color:#6925B5', brandId)
  return `pages/brand/index?brandId=${brandId}`
})

onMounted(() => {
  setTimeout(() => {
    wx.config({
      debug: true,
      appId: 'wx8f31ece94a23eec4',
      timestamp: new Date().getTime(),
      nonceStr: genUUID(),
      signature: 'xxx',
      jsApiList: [
        'openLocation',
        'getLocation',
        'closeWindow',
        'scanQRCode',
        'chooseWXPay',
        'chooseImage',
        'uploadImage',
        'previewImage',
        'getLocalImgData',
      ],
      openTagList: ['wx-open-launch-weapp'],
    })
  })
})

const launch = (e) => {
  console.log('%c Line:53 🥛 e', 'color:#CA4673', e)
}
</script>
