<template>
  <div class="relative h-screen overflow-y-auto">
    <div class="h-64px bg-primaryBg sticky top-0 z-10">
      <div class="flex items-center max-w-1200px mx-auto h-full">
        <img
          src="~/assets/images/yanxuan_logo.png"
          class="w-150px h-auto mr-24px cursor-pointer"
          alt="logo"
          @click="goHome"
        />
        <div class="flex-1"></div>
        <template v-if="!isLogin">
          <a-button mr-8 @click="handleLogin">登录</a-button>
          <a-button type="primary" @click="handleReg">立即注册</a-button>
        </template>
        <template v-else-if="orderInfo">
          <a-button v-if="eq(orderInfo.userMerchantJoinStatus, 1)" type="primary" @click="applyToJoinMerchant">
            申请加入{{ orderInfo.merchantShortName }}
          </a-button>
          <a-button v-else-if="eq(orderInfo.userMerchantJoinStatus, 2, 3)" type="primary" @click="goAns">
            前往平台，采纳报价
          </a-button>
        </template>
      </div>
    </div>

    <div class="mt-24px p-24px max-w-1200px mx-auto">
      <div v-if="!orderInfo">
        <a-card>
          <a-skeleton></a-skeleton>
        </a-card>
        <a-card class="mt-20">
          <a-skeleton></a-skeleton>
        </a-card>
        <a-card class="mt-20">
          <a-skeleton></a-skeleton>
        </a-card>
      </div>
      <div v-else>
        <a-card>
          <div class="flex items-center">
            <a-avatar :size="40" :src="useObs(orderInfo.merchantLogo)">
              {{ orderInfo.merchantShortName.slice(0, 1) }}
            </a-avatar>
            <div class="ml-16">
              <h2 class="text-(20 #262626)">{{ orderInfo.merchantName }}</h2>
              <div class="mt-8 text-gray">{{ orderInfo.shareUserName }}邀请您查看询价单</div>
            </div>
          </div>
        </a-card>

        <a-card title="询价单详情" class="mt-24">
          <div class="grid grid-cols-3">
            <div class="info-item">
              <div>询价单号：</div>
              <div>{{ orderInfo.orderNo }}</div>
            </div>

            <div class="info-item">
              <div>创建时间：</div>
              <div>{{ formatTime(orderInfo.createdTime) }}</div>
            </div>

            <div class="info-item">
              <div>询价时间：</div>
              <div>{{ formatTime(orderInfo.inquiryTime) }}</div>
            </div>

            <div class="info-item">
              <div>截止时间：</div>
              <div>{{ formatTime(orderInfo.deadline) }}</div>
            </div>

            <div class="info-item">
              <div>创建人：</div>
              <div>{{ orderInfo.userName }}</div>
            </div>

            <div class="info-item">
              <div>联系电话：</div>
              <div>{{ orderInfo.userMobile }}</div>
            </div>

            <div class="info-item">
              <div>物料型号数：</div>
              <div>{{ orderInfo.itemCount }}</div>
            </div>

            <div class="info-item">
              <div>物料总数：</div>
              <div>{{ orderInfo.totalNumber }}</div>
            </div>

            <div class="info-item">
              <div>已采纳总价：</div>
              <div class="text-red">{{ getPrice(orderInfo.totalPrice) || '-' }}</div>
            </div>
          </div>
        </a-card>

        <a-card class="mt-24" title="物料明细">
          <div>
            <a-alert v-if="!isLogin" class="mb-20">
              <template #message>
                <info-circle-filled class="text-primary"></info-circle-filled>
                以下为物料明细，登录后可查看供应商报价详情
              </template>
            </a-alert>
            <div>
              <a-button type="primary" @click="toggle">{{ text }}</a-button>
              <a-button type="primary" :loading="exportLoading" class="ml-8" @click="handleExport">导出报价</a-button>

              <div class="mt-20">
                <a-table
                  size="small"
                  :row-selection="{
                    selectedRowKeys: checkList,
                    onChange: (keys) => (checkList = keys),
                    preserveSelectedRowKeys: true,
                  }"
                  row-key="id"
                  :scroll="{ x: 1200 }"
                  :loading="tableLoading"
                  bordered
                  :columns="columns"
                  :data-source="tableData"
                  @resize-column="colResize"
                  v-model:expandedRowKeys="expandKeys"
                  :pagination="pagination"
                  @change="pageChange"
                >
                  <template #bodyCell="{ record, column }">
                    <a-tag v-if="column.dataIndex == 'status'" :color="getStatusColor(record)">
                      {{ getStatusText(record) }}
                    </a-tag>
                  </template>
                  <template #expandedRowRender="{ record }">
                    <div class="my-12px">
                      <div>供应商报价详情</div>
                      <div class="bg-#fff flex justify-center py-12" v-if="!isLogin">
                        <div
                          class="inline-flex flex-col items-center p-24 border-1 border-solid border-#f0f0f0 rounded-8px"
                        >
                          <div class="text-16 font-500 mb-8">查看供应商报价需要登录</div>
                          <div class="text-#595959 mb-8">登录后可以查看详细的供应商报价信息</div>
                          <div>
                            <a-button @click="handleLogin" class="mr-8">去登录</a-button>
                            <a-button type="primary" @click="handleReg">立即注册</a-button>
                          </div>
                        </div>
                      </div>
                      <div class="mt-20" v-else>
                        <a-table
                          size="small"
                          :columns="subColumns"
                          :data-source="record.quotationItems || []"
                          :pagination="false"
                          row-key="id"
                          bordered
                          :scroll="{ x: 1200 }"
                          :key="record.id"
                        >
                          <template #bodyCell="{ column, record: supplierRecord }">
                            <template v-if="column.dataIndex === 'status'">
                              <a-tag :color="getSupplierStatusColor(supplierRecord.status, supplierRecord)">
                                {{ getSupplierStatusText(supplierRecord.status, supplierRecord) }}
                              </a-tag>
                            </template>
                            <template v-if="column.dataIndex === 'remark'">
                              <a-tooltip placement="topLeft" :title="supplierRecord.remark">
                                <span>{{ supplierRecord.remark }}</span>
                              </a-tooltip>
                            </template>
                          </template>
                        </a-table>
                      </div>
                    </div>
                  </template>
                </a-table>
              </div>
            </div>
          </div>
        </a-card>

        <!-- <a-card mt-20> -->
        <!--   <a-alert show-icon message="想要参与此询价单？"> -->
        <!--     <template #icon> -->
        <!--       <info-circle-outlined></info-circle-outlined> -->
        <!--     </template> -->
        <!--     <template #description>研选工场诚邀优质供应商参与报价，无需注册即可快速参与询价，获得更多商机！</template> -->
        <!--     <template #action> -->
        <!--       <a-button :loading="joinLoading" type="primary" @click="handleJoin">参与报价</a-button> -->
        <!--     </template> -->
        <!--   </a-alert> -->
        <!-- </a-card> -->
      </div>
    </div>
  </div>
</template>

<script setup>
import fileDownload from 'js-file-download'
import { userStore } from '~/store/user'
import dayjs from 'dayjs'

definePageMeta({
  layout: 'false',
})

onMounted(() => {
  if (!shareId) {
    message.error('分享的询价单不存在')
    return
  }
  fetchData()
  fetchTableData()
})

const orderInfo = ref()
const loading = ref(false)

const yanxuan = useRuntimeConfig().public.baseUrl.VITE_YANXUAN
const isLogin = useLoginState()

const route = useRoute()
const shareId = route.query.shareId

const tableData = ref()

const checkList = ref([])
const { text, toggle, expandKeys } = useCollapse(tableData, 'id')

const goHome = () => {
  window.open(yanxuan)
}

const handleLogin = usegoLogin()
const handleReg = () => {
  window.open(yanxuan + '/register')
}

const fetchData = async () => {
  loading.value = true
  const url = isLogin.value ? '/mall/p/inquiry-list-item/details' : '/mall/inquiry-list-item/details'
  const [err, res] = await try_http(url, {
    query: {
      shareId,
    },
  })
  if (err) return
  orderInfo.value = res.data
}

const tableLoading = ref(false)
const fetchTableData = async () => {
  tableLoading.value = true
  const url = isLogin.value ? '/mall/p/inquiry-list-item/page' : '/mall/inquiry-list-item/page'
  const [err, res] = await try_http(url, {
    method: 'post',
    body: {
      shareId,
      current: page.current,
      size: page.size,
    },
  })
  if (err) return
  tableData.value = res.data.records || []
  tableLoading.value = false
}

const { page, pageChange, pagination } = usePage(fetchTableData)

const getStatusColor = (record) => {
  const { status, deadline } = record
  if (deadline && [10, 20].includes(status)) {
    if (dayjs().isAfter(dayjs(deadline))) {
      return 'orange'
    }
  }
  const statusMap = {
    10: 'default', // 待询价
    20: 'blue', // 询价中
    30: 'green', // 已报价/采纳
    40: 'orange', // 已截止
    50: 'red', // 已取消
    60: 'gray', // 已过期
  }
  return statusMap[status] || 'default'
}

const getStatusText = (record) => {
  const { status, deadline } = record
  if (deadline && [10, 20].includes(status)) {
    if (dayjs().isAfter(dayjs(deadline))) {
      return '已截止'
    }
  }
  const statusMap = {
    10: '待询价',
    20: '询价中',
    30: '已采纳',
    40: '已截止',
    50: '已取消',
    60: '已过期',
  }
  return statusMap[status] || '未知'
}

// 供应商状态颜色和文本
const getSupplierStatusColor = (status, record) => {
  const { expiryTime } = record
  if (expiryTime) {
    if (dayjs().isAfter(dayjs(expiryTime))) {
      return 'red'
    }
  }
  const statusMap = {
    '-1': 'default', // 尚未启动询价
    0: 'blue', // 待报价
    1: 'green', // 已报价
    2: 'red', // 已拒绝
  }
  return statusMap[status] || 'default'
}

const getSupplierStatusText = (status, record) => {
  const { expiryTime } = record
  if (expiryTime) {
    if (dayjs().isAfter(dayjs(expiryTime))) {
      return '已过期'
    }
  }
  const statusMap = {
    '-1': '尚未启动询价',
    0: '待报价',
    1: '已报价',
    2: '已拒绝',
  }
  return statusMap[status] || '未知'
}

const { user } = storeToRefs(userStore())

const goAns = () => {
  if (orderInfo.value.userMerchantJoinStatus == 2) {
    Modal.confirm({
      title: '提示',
      content: `您当前不处于${orderInfo.value.merchantShortName}的企业空间中，是否切换?`,
      okText: '是',
      cancelText: '否',
      onOk: async () => {
        const [err] = await try_http('/mall/shop/userSpace/saveOrUpdate', {
          method: 'post',
          params: {
            userMobile: user.value.userMobile,
            merchantId: orderInfo.value.merchantId,
            merchantName: orderInfo.value.merchantShortName,
          },
        })
        if (!err) {
          message.success('切换成功，正在跳转到企业空间询价单详情...')
          setTimeout(() => {
            window.open(`/workSpace/company-space/rfq-detail?orderNumber=${orderInfo.value.orderNo}`, '_blank')
            window.location.reload()
          }, 1000)
        }
      },
    })
  } else {
    if (orderInfo.value.userMerchantJoinStatus == 3) {
      window.open(`/workSpace/company-space/rfq-detail?orderNumber=${orderInfo.value.orderNo}`, '_blank')
    }
  }
}

const applyToJoinMerchant = async () => {
  const [err] = await try_http('/mall-platform/shop/merchantUser', {
    method: 'post',
    body: {
      auditResult: 0,
      auditStatus: 0,
      merchantId: orderInfo.value.merchantId,
      shopId: 0,
      userMobile: user.value.userMobile,
    },
  })
  if (err) return
  message.success('申请已提交，请等待审核结果')
}

const exportLoading = ref(false)
const handleExport = async () => {
  if (!checkList.value.length) {
    message.warning('请先勾选需要导出的物料')
    return
  }
  exportLoading.value = true
  const [err, res] = await try_http('/mall/inquiry-list-item/export', {
    method: 'post',
    body: checkList.value,
    responseType: 'blob',
  })
  exportLoading.value = false
  if (err) return
  fileDownload(res, `询价单物料-${formatTime(new Date())}.xlsx`)
}

const columns = ref([
  {
    title: '物料名称',
    dataIndex: 'prodName',
    key: 'prodName',
    width: 200,
    fixed: 'left',
    ellipsis: true,
    isSystem: true,
    resizable: true,
    customRender: ({ text }) => text,
  },
  {
    title: '型号',
    dataIndex: 'skuCode',
    key: 'skuCode',
    width: 180,
    ellipsis: true,
    isSystem: true,
    resizable: true,
  },
  {
    title: '品牌',
    dataIndex: 'brandName',
    key: 'brandName',
    width: 100,
    ellipsis: true,
    resizable: true,
  },
  {
    title: '物料分类',
    dataIndex: 'categoryName',
    key: 'categoryName',
    width: 180,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '数量',
    dataIndex: 'number',
    key: 'number',
    width: 80,
    ellipsis: true,
    resizable: true,
  },
  {
    title: '单价 (¥)',
    dataIndex: 'sellPrice',
    key: 'sellPrice',
    width: 200,
    ellipsis: true,
    resizable: true,
    customRender: ({ text, record }) => {
      return getPrice(record.sellPrice) || '-'
    },
  },
  {
    title: '总价 (¥)',
    dataIndex: 'totalPrice',
    key: 'totalPrice',
    width: 200,
    ellipsis: true,
    resizable: true,
    customRender: ({ text, record }) => {
      return getPrice(record.totalPrice) || '-'
    },
  },
  {
    title: '交期',
    dataIndex: 'tradeTerm',
    key: 'tradeTerm',
    defaultHidden: true,
    width: 100,
    ellipsis: true,
    customRender: ({ text, record }) => {
      return getTradeTerm(record.tradeTerm) || '-'
    },
  },
  {
    title: '期望交期',
    dataIndex: 'expectTerm',
    key: 'expectTerm',
    width: 100,
    ellipsis: true,
    resizable: true,
    customRender: ({ text }) => {
      return getTradeTerm(text) || '-'
    },
  },
  {
    title: '接受平替',
    dataIndex: 'replaceStatus',
    width: 100,
    resizable: true,
    customRender({ text }) {
      return text ? '是' : '否'
    },
  },
  {
    title: '平替品牌',
    dataIndex: 'replaceBrandName',
    ellipsis: true,
    width: 150,
    resizable: true,
    customRender({ record, text }) {
      return record.replaceStatus ? text : '-'
    },
  },
  {
    title: '平替型号',
    dataIndex: 'replaceSkuCode',
    ellipsis: true,
    width: 150,
    resizable: true,
    customRender({ record, text }) {
      return record.replaceStatus ? text : '-'
    },
  },
  {
    title: '询价单号',
    dataIndex: 'orderNo',
    key: 'orderNo',
    width: 230,
    ellipsis: {
      showTitle: false,
    },
    resizable: true,
  },
  {
    title: '询价时间',
    dataIndex: 'inquiryTime',
    key: 'inquiryTime',
    width: 160,
    ellipsis: true,
    resizable: true,
    customRender: ({ text }) => {
      return formatTime(text)
    },
  },
  {
    title: '截止时间',
    dataIndex: 'deadline',
    key: 'deadline',
    width: 160,
    ellipsis: true,
    resizable: true,
    customRender: ({ text }) => {
      return formatTime(text)
    },
  },
  {
    title: '询价状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    ellipsis: true,
    resizable: true,
  },
  {
    title: '备注',
    dataIndex: 'buyerRemark',
    key: 'buyerRemark',
    width: 150,
    defaultHidden: true,
    ellipsis: {
      showTitle: false,
    },
    resizable: true,
  },
])

const subColumns = ref([
  {
    title: '报价 (¥)',
    dataIndex: 'sellPrice',
    key: 'sellPrice',
    width: 100,
    ellipsis: true,
    customRender: ({ text, record }) => {
      return getPrice(record.sellPrice) || '-'
    },
  },
  {
    title: '总价 (¥)',
    dataIndex: 'totalPrice',
    key: 'totalPrice',
    width: 100,
    ellipsis: true,
    resizable: true,
    customRender: ({ text, record }) => {
      return getPrice(record.totalPrice) || '-'
    },
  },
  {
    title: '承诺交期',
    dataIndex: 'tradeTerm',
    key: 'tradeTerm',
    width: 120,
    ellipsis: true,
    customRender: ({ text }) => {
      return getTradeTerm(text) || '-'
    },
  },
  {
    title: '报价时间',
    dataIndex: 'quotationTime',
    key: 'quotationTime',
    width: 150,
    ellipsis: true,
    customRender: ({ text }) => {
      return formatTime(text)
    },
  },
  {
    title: '报价状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    ellipsis: true,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    width: 150,
    ellipsis: {
      showTitle: false,
    },
  },
])

const joinLoading = ref(false)
const handleJoin = async () => {
  joinLoading.value = true
  const [err, res] = await try_http(`/mall/p/inquiry-list-item/share-id/${orderInfo.value.orderNo}`)
  joinLoading.value = false
  if (err) return
  navigateTo({
    path: '/rfq-inquiry',
    query: {
      shareId: res.data,
    },
  })
}
</script>

<style lang="less" scoped>
.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  & > div {
    &:nth-child(1) {
      min-width: 90px;
      color: rgba(0, 0, 0, 0.65);
      flex-shrink: 0;
    }
    &:nth-child(2) {
      font-weight: 500;
    }
  }
}
</style>
