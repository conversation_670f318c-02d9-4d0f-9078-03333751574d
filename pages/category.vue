<template>
  <div class="search-page">
    <nav-list></nav-list>

    <list-skeleton v-if="filterLoading"></list-skeleton>

    <div
      v-else
      class="card mt-8 overflow-hidden"
      :style="{ height: pageState.h }"
      :class="{
        'transition-all duration-300': pageState.isLoad,
      }"
    >
      <div filter ref="filterRef" v-if="websites.length">
        <span filter__title>品牌</span>
        <span filter__content overflow-hidden>
          <a-checkable-tag
            v-for="item in websites"
            :class="{ 'filter__option--disabled': item.disabled }"
            :key="item.websiteCode"
            :checked="set.select.has(item.websiteCode)"
            @click="clickWebsite(item)"
          >
            {{ item.websiteName }}
          </a-checkable-tag>
        </span>
      </div>

      <!-- 条件和选项 -->
      <div filter overflow-hidden ref="itemRefs" v-for="item in filterConditions" :key="item.conditionName">
        <span filter__title truncate shrink-0>{{ item.conditionName }}</span>
        <div class="flex-1 p-4 overflow-hidden" border-t="1px solid #eff2f7">
          <list-image
            v-if="imgMode(item.options)"
            link="optionValueImgUrl"
            label="optionName"
            :list="item.options"
            @update="calcH"
          >
            <template #default="{ item, hide }">
              <list-option-item
                type="img"
                :item="item as FilterOption"
                @click-item="clickItem"
                :web-set="set"
                :hide="hide"
              ></list-option-item>
            </template>
          </list-image>
          <div v-else>
            <list-option-item
              type="tag"
              :web-set="set"
              v-for="option in item.options"
              :item="option"
              :key="option.optionName"
              @click-item="clickItem"
            >
              {{ option.optionName }}
            </list-option-item>
          </div>
        </div>
      </div>
    </div>

    <div mb-8 w-full flex flex-row justify-center items-center @click="toggleFold" v-if="filterConditions.length > 4">
      <div py-6 px-20 color-white bg-primary cursor-pointer card-shadow>
        <DoubleLeftOutlined
          font-size-16
          origin-center
          transition-transform
          duration-200
          m-r-4
          :style="`transform: rotate(${showAll ? 90 : 270}deg)`"
        />
        <span text color-white>更多筛选条件</span>
      </div>
    </div>

    <div mb-8 v-if="filterList.length">
      <span
        inline-block
        mr-8
        h-24
        p-x-8
        bg-white
        line-height-22
        b-1
        b-dashed
        border-primary
        v-for="filter in filterList"
        :key="filter.conditionName"
      >
        <span sub-text>{{ filter.conditionName + '：' }}</span>
        <span text font-size-12>
          {{ filter.optionNames.map((item) => (isWebsite(item) ? item.websiteName : item.optionName)).join(',') }}
        </span>
        <CloseCircleFilled ml-4 font-size-12 color-primary @click="clearCondition(filter)" />
      </span>
      <a-button type="primary" size="small" @click="clearAll">清空筛选</a-button>
    </div>

    <div>
      <div flex items-center justify-between flex-wrap>
        <div title-text mt-8 mb-8>
          找到的产品：共
          <span text-primary>{{ page.total }}</span>
          个
        </div>
        <div mt-8 mb-8 flex items-center>
          <span
            title-text
            mr-16
            cursor-pointer
            :class="{ 'color-primary': showType === Type.card }"
            @click="changeType(Type.card)"
          >
            <AppstoreOutlined />
            卡片
          </span>
          <span
            title-text
            cursor-pointer
            :class="{ 'color-primary': showType === Type.list }"
            @click="changeType(Type.list)"
          >
            <UnorderedListOutlined />
            列表
          </span>

          <a-pagination
            v-model:current="page.page"
            v-model:page-size="page.size"
            simple
            :total="page.total"
            @change="pageChange"
          />
        </div>
      </div>
    </div>

    <template v-if="selectLoading">
      <CardSkeleton v-if="showType == Type.card"></CardSkeleton>
      <ListSkeleton v-if="showType == Type.list"></ListSkeleton>
    </template>
    <template v-else>
      <div v-if="showType == Type.card" card-list>
        <search-item
          v-for="item in categoryList"
          :key="item.id"
          type="card"
          search-type="keyword"
          :info="item"
          @click="goPart(item)"
          @after-login="afterLogin"
        ></search-item>
        <!-- <card-item -->
        <!--   v-for="item in categoryList" -->
        <!--   :id="item.id" -->
        <!--   :key="item.id" -->
        <!--   :has3-d-model="item.has3DModel" -->
        <!--   :website-img-url="item.website.websiteImgUrl" -->
        <!--   :website-name="item.website.websiteName" -->
        <!--   :image-url="useImage(item.imageUrl)" -->
        <!--   :product-name="item.productName" -->
        <!--   :page-url="item.pageUrl" -->
        <!--   :spec-pdf-url="item.specPdfUrl" -->
        <!--   :highlights="item.highLights" -->
        <!--   @click="goPart(item)" -->
        <!-- ></card-item> -->
      </div>
      <div v-if="showType == Type.list">
        <search-item
          v-for="item in categoryList"
          :key="item.id"
          type="list"
          search-type="keyword"
          :info="item"
          @click="goPart(item)"
          @after-login="afterLogin"
          mb-10
        ></search-item>
      </div>
    </template>

    <a-pagination
      mt-16
      text-right
      v-model:current="page.page"
      v-model:page-size="page.size"
      show-quick-jumper
      :total="page.total"
      @change="pageChange"
    />

    <!-- <go-register></go-register> -->
  </div>
</template>

<script setup lang="ts">
import { Condition, FilterCondition, FilterOption, SelectionItem, Website } from '~/api/search'
import { companyStore } from '~/store/company'
import { treeStore } from '~/store/tree'

type Extra = {
  selected?: boolean
  disabled?: boolean
}

const route = useRoute()
const categoryCode = computed(() => route.query.id as string)
const filterConditions = ref<FilterCondition[]>([])

const filterRef = ref<HTMLElement>()
const itemRefs = ref<HTMLElement[]>([])

const websites = ref<Array<Website & Extra>>([])

const handleFilter = () => {
  useRes(filterData.value!, (res) => {
    filterConditions.value = res.filterConditions || []
    websites.value = res.websites
    treeStoreObj.setActive(res.hancodeCategory.id)
    calcH()
  })
}

const handleSelect = () => {
  useRes(selectData.value!, (res) => {
    categoryList.value = res.BY_KEYWORD.data || []
    page.total = res.BY_KEYWORD.totalCount
  })
}

const imgMode = (list: FilterOption[]) => list.some((item) => !!item.optionValueImgUrl)

const clickWebsite = (website: Website) => {
  if (website.disabled) return
  website.selected = !website.selected
}

const set = computed(() => {
  const select: Set<string> = new Set()
  const canuse: Set<string> = new Set()
  websites.value.forEach((item) => {
    if (item.selected) {
      select.add(item.websiteCode)
    }
    if (!item.disabled) {
      canuse.add(item.websiteCode)
    }
  })
  return {
    select,
    canuse,
  }
})

const clickItem = (item: FilterOption) => {
  if (item.disabled) return
  item.selected = !item.selected
  nextTick(() => {
    fetchSelect()
  })
}

const canUseWebs = computed(() => {
  const set: Set<string> = new Set()
  filterConditions.value.forEach((condition) => {
    condition.options.forEach((option) => {
      if (option.selected) {
        const webList = JSON.parse(option.optionValueSiteCodes) as string[]
        webList.forEach((item) => set.add(item))
      }
    })
  })
  return set
})

watchEffect(() => {
  if (canUseWebs.value.size) {
    websites.value.forEach((item) => {
      if (canUseWebs.value.has(item.websiteCode)) {
        item.disabled = false
      } else {
        item.disabled = true
        if (item.selected) {
          item.selected = false
        }
      }
    })
  } else {
    websites.value.forEach((item) => {
      item.disabled = false
    })
  }
})

const showAll = ref(false)
const pageState = reactive({
  h: 'auto',
  isLoad: false,
})
const toggleFold = () => {
  showAll.value = !showAll.value
  calcH()
}
const calcH = () => {
  if (process.server) {
    return
  }
  setTimeout(() => {
    let h = 0
    if (websites.value.length && filterRef.value) {
      h += filterRef.value.offsetHeight
    }
    if (itemRefs.value?.length) {
      const els = showAll.value ? itemRefs.value : itemRefs.value.slice(0, 4)
      els.forEach((item) => {
        h += item.offsetHeight
      })
    }
    pageState.h = h + 'px'
    if (!pageState.isLoad) {
      setTimeout(() => {
        pageState.isLoad = true
      })
    }
  })
}

const filters = computed(() => {
  const sourceList = websites.value.filter((item) => item.selected)
  const conditions = filterConditions.value.reduce((res, item) => {
    const options = item.options.filter((item) => item.selected)
    if (options.length) {
      res.push({
        conditionName: item.conditionName,
        options: options,
        conditionCommonNo: item.conditionCommonNo,
      })
    }
    return res
  }, [] as FilterCondition[])

  return {
    sourceList,
    conditions,
  }
})

const isWebsite = (value): value is Website => {
  return 'websiteCode' in value
}

type FilterItem = {
  conditionName: string
  optionNames: Array<Website | FilterOption>
}
const filterList = computed(() => {
  const list: FilterItem[] = []
  if (filters.value.sourceList.length) {
    list.push({
      conditionName: '品牌',
      optionNames: filters.value.sourceList,
    })
  }
  return list.concat(
    filters.value.conditions.map((item) => ({
      conditionName: item.conditionName,
      optionNames: item.options,
    })),
  )
})

// 清空所有筛选
const clearAll = () => {
  filters.value.sourceList.forEach((item) => (item.selected = false))
  filters.value.conditions.forEach((item) => item.options.forEach((item) => (item.selected = false)))
  fetchSelect()
}

// 清空单个筛选
const clearCondition = (item: FilterItem) => {
  if (item.optionNames.some((option) => isWebsite(option))) {
    websites.value.forEach((item) => (item.selected = false))
  } else {
    const find = filterConditions.value.find((condition) => condition.conditionName == item.conditionName)
    if (find) {
      find.options.forEach((item) => (item.selected = false))
    }
  }
}

const page = reactive({
  page: 1,
  size: 50,
  total: 0,
})

enum Type {
  card,
  list,
}
const showType = ref<Type>(Type.card)
const changeType = (type: Type) => {
  showType.value = type
}

const categoryList = ref<SelectionItem[]>([])
const fetchSelect = async () => {
  // await refresh()
  handleSelect()
}
const pageChange = (pageNo: number, pageSize: number) => {
  page.page = pageNo
  page.size = pageSize
  fetchSelect()
}

const router = useRouter()
const isMetaPressed = useMeta()
const goPart = (item: SelectionItem) => {
  const path = router.resolve({ path: '/parts/' + item.id })
  if (isMetaPressed.value) {
    isMetaPressed.value = false
    window.open(path.href)
  } else {
    router.push({ path: path.path })
  }
}

const treeStoreObj = treeStore()

const body = computed(() => {
  const query = {
    brand: '',
    categoryCode: categoryCode.value,
    keyword: route.query.s || '',
    optionFilters: filters.value.conditions.map((item) => {
      const optionNames = item.options.map((option) => option.optionName)
      return {
        conditionName: item.conditionName,
        optionNames,
      }
    }),
    pageNo: page.page,
    pageSize: page.size,
    websiteCode: websites.value
      .filter((item) => item.selected)
      .map((item) => item.websiteCode)
      .join(','),
  }
  return query
})

const isload = ref(false)
const [
  { data: filterData, pending: filterLoading, execute },
  { data: selectData, pending: selectLoading, execute: exec },
] = await Promise.all([
  useCloudApi<Condition>(() => `/api/selection/part-condition/${categoryCode.value}`, {
    params: computed(() => route.query),
  }),
  useCloudApi('/api/selection/part-search', {
    method: 'post',
    body,
  }),
])
await Promise.all([handleFilter(), handleSelect()])
isload.value = true
watchEffect(() => {
  if (isload.value) {
    handleFilter()
    handleSelect()
  }
})

const afterLogin = () => {
  Promise.all([execute(), exec()])
}

// watch(
//   () => route.query,
//   () => {
//     Promise.all([execute(), refresh()]).then(() => {
//       handleFilter()
//       handleSelect()
//     })
//   },
// )
</script>
