<template>
  <div class="h-[calc(100vh-180px)]" flex flex-col>
    <!-- <div w-full p-y-16 flex justify-center bg-midnight> -->
    <!--   <div w-1280px> -->
    <!--     <NuxtLink to="/"> -->
    <!--       <img h-40 src="~/assets/images/logo_w.png" /> -->
    <!--     </NuxtLink> -->
    <!--   </div> -->
    <!-- </div> -->
    <div flex-1 flex flex-col justify-center items-center>
      <img src="~/assets/images/404.png" />
      <div class="flex gap-10px">
        <a-button @click="goHome" type="primary">回到首页</a-button>
        <a-button @click="$router.go(-1)" type="primary">返回上一页</a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
// import {} from 'vue'
// definePageMeta({
//   layout: false,
// })
const goHome = () => {
  navigateTo({ path: '/' })
}
</script>

<style scoped></style>
