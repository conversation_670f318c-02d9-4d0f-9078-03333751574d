import type { <PERSON><PERSON>arameter } from "~/api/search/type"
import Ms from 'magic-string'

const reg = /\-([a-zA-Z0-9]*?)\[(.*?)\]/g
class StrUtil {
  text: string
  list: SelectParameter[]
  steps = 0
  map: Map<string, {
    value: string
    originValue: string
    index: number
    hasKey: boolean
  }> = new Map
  matches: Map<string, SelectParameter> = new Map
  rules = {}
  form = {}
  watchKes: Set<string> = new Set
  constructor(text: string, list: SelectParameter[], form: Record<string, any>) {
    this.text = text
    this.list = list
    this.form = form
    this.init(text)
  }
  init(text: string) {
    let match: RegExpExecArray | null
    while (match = reg.exec(text)) {
      const key = match[1] || match[2]
      const value = match[2]
      this.map.set(key, {
        value,
        index: match.index,
        originValue: value,
        hasKey: match[1] != ''
      })
      this.steps += 1
    }
    this.map.forEach((v, k) => {
      let find: SelectParameter|undefined
      if (v.hasKey) {
        find = this.list.find(item => item.spec_name.toLowerCase() == k.toLowerCase())
        if (!find) {
          find = this.list.find(item => item.spec_name.toLowerCase().includes(k.toLowerCase()))
        }
      } else {
        if (k.includes('-')) {
          find = this.list.find(item => {
            if (item.spec_style == 'input') {
              const [range] = item.value_range
              const { min_value, max_value, step_value } = range
              if (k == `${min_value}-${max_value}/${step_value}`) return true
            }
          })
        } else if (k.includes(',')) {
          find = this.list.find(item => item.spec_style == 'select' && item.spec_options.map(v => v.spec_option_name).join(',') == k)
        }
      }
      if (find) {
        this.matches.set(k, find)
        this.rules[find.spec_code] = [{ required: true, message: find.spec_style == 'input' ? '请输入' : '请选择 ', trigger: 'change' }]
        this.watchKes.add(find.spec_code)
      }
    })
  }
  get currentStep() {
    let step = 0
    for (let [_k, val] of this.matches) {
      if (!!this.form[val.spec_code]) {
        step++
      }
    }
    return step
  }
  get format() {
    const s = new Ms(this.text)
    this.map.forEach((value, key) => {
      const match = this.matches.get(key)
      let start
      if (value.hasKey) {
        start = value.index + key.length + 2
      } else {
        start = value.index + 2
      }
      if (match && this.form[match.spec_code]) {
        let val = ''
        if (match.spec_options) {
          const find = match.spec_options.find(item => item.spec_option_code == this.form[match.spec_code])
          val = String(find?.spec_option_name)
        } else {
          val = this.form[match.spec_code]
        }
        s.update(start - 1, start + value.value.length + 1, `<span class="text-primary">${val}</span>`)
      } else {
        s.update(start, start + value.value.length, `<span style="color: red;word-break: break-all">${value.value}</span>`)
      }
    })
    return s.toString()
  }
  get raw() {
    const s = new Ms(this.text)
    this.map.forEach((value, key) => {
      const match = this.matches.get(key)
      let start
      if (value.hasKey) {
        start = value.index + key.length + 2
      } else {
        start = value.index + 2
      }
      if (match && this.form[match.spec_code]) {
        let val = ''
        if (match.spec_options) {
          const find = match.spec_options.find(item => item.spec_option_code == this.form[match.spec_code])
          val = String(find?.spec_option_name)
        } else {
          val = this.form[match.spec_code]
        }
        s.update(start - 1, start + value.value.length + 1, String(val))
      }
    })
    return s.toString()
  }
}

export default StrUtil