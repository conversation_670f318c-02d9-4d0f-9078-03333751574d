<template>
  <div aspect-video>
    <a-alert type="warning" message="请先完成选型" v-if="!model.isFinished"></a-alert>
    <iframe
      v-else
      :src="`/preview?path=${model.modelUrl}`"
      w-full
      aspect-ratio="4/3"
      frameborder="0"
      :key="model.modelUrl"
    ></iframe>
  </div>
</template>

<script setup lang="ts">
import SelectModel from '~/components/Select/mode'

const props = defineProps<{
  model: SelectModel
}>()
</script>
