<template>
  <div w-full aspect-square>
    <iframe :src="src" w-full aspect-ratio="4/3" frameborder="0"></iframe>
  </div>
</template>

<script setup>
const props = defineProps({
  modelFile: {
    type: String,
    default: '',
  },
})

const { modelFile } = toRefs(props)

const model = computed(() => {
  const [model] = JSON.parse(modelFile.value) || []
  return model || {}
})

const src = computed(() => {
  if (model.value.target) {
    return `/preview?path=${model.value.target}`
  } else {
    return `/preview`
  }
})

if (model.value.errorMessage) {
  message.error(model.value.errorMessage)
}
</script>
