<template>
  <a-modal v-model:open="open" width="80%" title="下载" destroy-on-close :footer="null">
    <div v-html="iframeSrc"></div>
  </a-modal>
</template>

<script lang="ts" setup>
const open = ref(false)

const iframeSrc = ref('')

const showDownload = (src: string) => {
  iframeSrc.value = `<iframe w-full h-500 src="${src}"></iframe>`
  console.log("%c Line:14 🥕 src", "color:#2eafb0", src);
  open.value = true
}

defineExpose({
  showDownload
})
</script>