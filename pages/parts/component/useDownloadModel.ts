import { userStore } from '~/store/user'

const useDownloadModel = (callback: Function) => {
  const user = userStore()

  const afterLogin = () => {
    if (!user.user.userMail) {
      return Modal.confirm({
        title: '提示',
        content: '下载3D模型需要邮箱信息，请先在个人中心进行绑定',
        okText: '去绑定',
        onOk: () => {
          navigateTo({ path: '/my/info' })
        },
      })
    }
    if (!user.user.userMobile) {
      return Modal.confirm({
        title: '提示',
        content: '下载3D模型需要绑定手机信息，请先在个人中心进行绑定',
        okText: '去绑定',
        onOk: () => {
          navigateTo({ path: '/my/info' })
        },
      })
    }
    callback()
  }

  if (!user.user.userId) {
    loginPop.show(() => {
      afterLogin()
    })
  } else {
    afterLogin()
  }
}

export default useDownloadModel
