<template>
  <div md="flex-1 ml-30" overflow-hidden relative>
    <a-affix :offset-top="0">
      <div flex items-center bg-white>
        <div text-20 mt-5 hover:text-primary cursor-pointer mr-10 @click="collapse = !collapse">
          <MenuUnfoldOutlined v-show="collapse" />
          <MenuFoldOutlined v-show="!collapse" />
        </div>
        <a-tabs flex-1 bg-white v-model:active-key="current">
          <a-tab-pane :key="Type.desc" tab="产品简介"></a-tab-pane>
          <a-tab-pane :key="Type.res" tab="产品资源" v-if="resFiles.length > 0"></a-tab-pane>
          <!-- <a-tab-pane :key="Type.pdf" tab="产品文档" v-if="pageDetail.specPdfUrl"></a-tab-pane> -->
          <a-tab-pane :key="Type.model" tab="3D模型" v-if="model.hasModel"></a-tab-pane>
        </a-tabs>
      </div>
    </a-affix>
    <div mt-10 text-14 max-w-full>
      <template v-if="current == Type.desc">
        <div v-if="pageDetail.specDetailHtml" class="prod-pdf img-full"
          v-html="dealPartHtmlDetail(pageDetail.specDetailHtml)" @contextmenu="handleRightClick"></div>
        <div v-else p-50 style="text-align: center">
          <img h-181px mb-20 src="~/assets/images/daodao_nodata.png" />
          <br />
          <span text-18px color-primary font-bold>平台数据持续更新,敬请期待 ...</span>
        </div>
        <!-- <Empty mt-100 v-else /> -->
      </template>

      <template v-if="current == Type.res">
        <div v-for="(item, i) in resFilesWithSize" :key="item.url" relative
          class="flex items-center justify-between p-10 hover:bg-gray-50 border-b border-gray-100"
          @contextmenu="handleRightClick">
          <div class="w-48 h-48 flex-center" mr-8>
            <img :src="getFileIcon(item)" class="w-full h-full" object-fit="cover">
          </div>
          <div flex-1 truncate class="text-gray-700">{{ item.name }}</div>
          <div w-100 truncate class="text-gray-500">{{ item.fileType }}</div>
          <div w-100 truncate class="text-gray-500">{{ item.fileSize }}</div>
          <div w-80 flex justify-end>
            <DownloadOutlined class="cursor-pointer hover:text-primary" @click="download(item)" />
          </div>
        </div>
      </template>

      <template v-if="current == Type.pdf">
        <div v-for="(item, i) in docs" class="sibling:mt-20" :key="item.url" relative @contextmenu="handleRightClick">
          <doc-item :item="item"></doc-item>
        </div>
      </template>

      <template v-if="current == Type.model && model.hasModel">
        <Sview v-if="isHt" :model-file="model.pageDetail!.modelFile" />
        <SviewS v-else-if="isHtMul" :model="model"></SviewS>
        <BeModel v-else-if="!isCa" :model="model"></BeModel>
        <Model v-else :model="model"></Model>
      </template>
      <!-- <BeModel v-if="model.hasModel && " :model="model"></BeModel> -->
      <!---->
      <!-- <Model -->
      <!--   v-if="model.cadenas.currentPrjPath && model.type == ModelType.model && current == Type.model" -->
      <!--   :model="model" -->
      <!-- /> -->
    </div>
  </div>
</template>

<script lang="ts" setup>
import Sview from './Sview.vue'
import SviewS from './SviewS.vue'
import BeModel from './BeModel.vue'
import Model from './model.vue'
import SelectModel from '~/components/Select/mode'
import docItem from './docItem.vue'

import unknownIcon from '~/assets/images/file_icon/unknown.png'
import pdfIcon from '~/assets/images/file_icon/pdf.png'
import wordIcon from '~/assets/images/file_icon/word.png'
import excelIcon from '~/assets/images/file_icon/excel.png'
import pptIcon from '~/assets/images/file_icon/ppt.png'
import imageIcon from '~/assets/images/file_icon/image.png'

enum Type {
  desc,
  pdf,
  model,
  res,
}

const current = ref(Type.desc)
const props = defineProps<{
  model: SelectModel
}>()

const pageDetail = computed(() => props.model.pageDetail!)

const resFiles = computed(() => pageDetail.value?.resFiles ?? [])

const resFilesWithSize = ref<any[]>([])

const collapse = defineModel({ default: false })

const isHt = computed(() => pageDetail.value.modelParameterType == 0 && pageDetail.value.modelFile)
const isHtMul = computed(() => pageDetail.value.modelParameterType == 1 && pageDetail.value.modelParameterFileId)
const isCa = computed(() => pageDetail.value.from == 'Cadenas')

// if (pageDetail.value.specDetailHtml) {
//   current.value = Type.desc
// } else if (pageDetail.value.specPdfUrl) {
//   current.value = Type.pdf
// } else {
//   current.value = Type.model
// }

const dealPartHtmlDetail = (html?: string) => {
  if (!html) return ''
  if (html.startsWith('http')) {
    html = `<img src="${html}" alt="产品详情" style="max-width: 100%">`
  }
  return html
}

const docs = computed(() => {
  const docStr = props.model.doc || pageDetail.value.specPdfUrl
  if (docStr) return JSON.parse(docStr)
  return []
})

function checkIfRightClickInSelection(event: MouseEvent) {
  const selection = window.getSelection()
  if (!selection?.rangeCount) {
    return false
  }

  const range = selection.getRangeAt(0)
  const rects = range.getClientRects()

  for (let i = 0; i < rects.length; i++) {
    const rect = rects[i]
    if (
      event.clientX >= rect.left &&
      event.clientX <= rect.right &&
      event.clientY >= rect.top &&
      event.clientY <= rect.bottom
    ) {
      return true
    }
  }

  return false
}

const handleRightClick = (e: MouseEvent) => {
  if (window.getSelection()?.isCollapsed || !checkIfRightClickInSelection(e)) {
    e.preventDefault()
  }
}

const getFileIcon = (file: any) => {
  if (!file?.name) return unknownIcon

  const ext = file.name.split('.').pop()?.toLowerCase()
  const iconMap: Record<string, string> = {
    'pdf': pdfIcon,
    'doc': wordIcon,
    'docx': wordIcon,
    'xls': excelIcon,
    'xlsx': excelIcon,
    'ppt': pptIcon,
    'pptx': pptIcon,
    'jpg': imageIcon,
    'jpeg': imageIcon,
    'png': imageIcon,
    'gif': imageIcon
  }

  return iconMap[ext] || unknownIcon
}

async function getFileSize(url: string) {
  try {
    const response = await fetch(url, { method: 'HEAD' })
    const size = response.headers.get('content-length')
    if (size) {
      const kb = Math.round(parseInt(size) / 1024)
      return kb > 1024 ? `${(kb / 1024).toFixed(2)}MB` : `${kb}KB`
    }
    return '未知大小'
  } catch (err) {
    console.error('获取文件大小失败:', err)
    return '未知大小'
  }
}

watch(resFiles, (newFiles) => {
  if (!newFiles?.length) return

  const filesWithSize = newFiles.map((file) => {
    const ext = file.name.split('.').pop()?.toLowerCase()
    let fileType = '未知类型'

    if (['doc', 'docx', 'xlsx', 'xls', 'ppt', 'pptx', 'pdf'].includes(ext)) {
      fileType = '文档'
    } else if (['jpg', 'jpeg', 'png', 'gif'].includes(ext)) {
      fileType = '图片'
    } else if (['mp4', 'avi', 'mov'].includes(ext)) {
      fileType = '视频'
    }

    // 将字节转换为合适的单位
    let fileSize = '未知大小'
    if (file.length) {
      const bytes = file.length
      if (bytes < 1024) {
        fileSize = `${bytes}B`
      } else if (bytes < 1024 * 1024) {
        fileSize = `${(bytes / 1024).toFixed(2)}KB`
      } else if (bytes < 1024 * 1024 * 1024) {
        fileSize = `${(bytes / (1024 * 1024)).toFixed(2)}MB`
      } else {
        fileSize = `${(bytes / (1024 * 1024 * 1024)).toFixed(2)}GB`
      }
    }

    return {
      ...file,
      fileSize,
      fileType
    }
  })

  resFilesWithSize.value = filesWithSize
}, { immediate: true })

// 预览文件
const preview = (item: any) => {
  window.open(item.url, '_blank')
}

// 下载文件
const download = (item: any) => {
  const a = document.createElement('a')
  a.href = item.url
  a.download = item.name
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
}

</script>

<style lang="less" scoped>
:deep(.ant-tabs-nav) {
  margin-bottom: 0 !important;
}

.prod-pdf {
  :deep(img) {
    max-width: 100%;
  }
}
</style>
