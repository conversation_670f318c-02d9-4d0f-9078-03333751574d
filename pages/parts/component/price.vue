<template>
  <div v-if="detail" inline-flex>
    <div text-primary text-14 font-bold cursor-pointer v-if="!isLogin" @click="handleLogin">
      登录后查看价格与交期 &gt;
    </div>
    <div v-else>
      <!-- <div sub-text text-14 font-bold v-if="!detail.canInquiry">该商品不支持询价</div> -->
      <!-- <template v-else> -->
      <!-- </template> -->
      <div flex items-end>
        <a-spin :spinning="loading">
          <template v-if="isUpdate">
            <div text="14 #ff0000" font-bold>价格信息更新中</div>
          </template>

          <Data v-else :data="getData()">
            <template #default="{ data }">
              <div bg-primary text="white 12" px-4 py-2 inline-block rounded-2>
                {{ data.tradeTerm }}
              </div>
              <div sub-text line-through mt-12>{{ getPrice(data.price) }}</div>
              <div text="14 #ff0000" font-bold mt-5>
                <span>{{ getPrice(data.discountedPrice) }}</span>
                <span v-if="!select.isFinished" text-10>起</span>
                <span v-if="data.packingUnit">&nbsp;/{{ data.packingUnit }}</span>
              </div>
            </template>
          </Data>
        </a-spin>
        <div flex items-center text-14 ml-40>
          <div w-32 h-32 flex-center cursor-pointer border="1px solid #d5d5d5" @click="calc(-1)">
            <MinusOutlined />
          </div>
          <div min-w-20 max-w-50>
            <a-input-number
              :controls="false"
              :min="1"
              v-model:value="count"
              :bordered="false"
              class="counter"
            ></a-input-number>
          </div>
          <!-- <div mx-10 min-w-20 text-center select-none>{{ count }}</div> -->
          <div w-32 h-32 flex-center cursor-pointer border="1px solid #d5d5d5" @click="calc(1)">
            <PlusOutlined />
          </div>
          <a-tooltip placement="topLeft" :title="!select.isFinished ? '请先完成选型' : ''" arrow-point-at-center>
            <a-button type="primary" ml-10 :disabled="!select.isFinished" @click="apply">申请询价</a-button>
          </a-tooltip>
        </div>
      </div>
    </div>
    <a-modal title="提示" v-model:open="show" centered :footer="null">
      <p mb-40>您需要处于设备制造商的企业空间下才能询价。</p>

      <div text-primary cursor-pointer v-for="item in links" :key="item.name" @click="go(item.link)">
        {{ item.name }}
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import Model from '~/components/Select/mode'
import { companyStore } from '~/store/company'
import { sideWidgetsStore } from '~/store/sideWidgets'
import { userStore } from '~/store/user'

const props = defineProps<{
  select: Model
}>()

const { select } = toRefs(props)
const detail = computed(() => select.value.pageDetail)

const user = userStore()
const isLogin = computed(() => !!user.user.userId)

const loading = ref(false)

const isUpdate = computed(() => {
  if (select.value.pageDetail) {
    if (!select.value.isFinished) {
      const { lowestDiscountedPrice, lowestTradeTerm } = select.value.pageDetail
      return !has(lowestDiscountedPrice) || !has(lowestTradeTerm)
    } else {
      const { discountedPrice, tradeTerm } = data.value
      return !has(discountedPrice) || !has(tradeTerm)
    }
  }
  return false
})

const count = ref(1)
const calc = (val: number) => {
  if (val + count.value < 1) {
    count.value = 1
  } else {
    count.value += val
  }
}

const data = ref({
  discountedPrice: '',
  price: '',
  tradeTerm: '',
  packingUnit: '',
})
const getData = () => {
  const { isFinished } = select.value
  const { lowestPrice, lowestTradeTerm, lowestDiscountedPrice, packingUnit } = detail.value!
  if (!isFinished) {
    return {
      discountedPrice: lowestDiscountedPrice,
      price: lowestPrice,
      tradeTerm: getTradeTerm(lowestTradeTerm, '最快'),
      packingUnit,
    }
  } else {
    data.value.packingUnit = packingUnit
    return data.value
  }
}

const fetchPrice = async () => {
  loading.value = true
  const { partId, skuId } = select.value
  const res = await request.get('/selection/sku-price', {
    id: partId,
    skuId,
  })
  loading.value = false
  useRes(res, () => {
    const { price, discountedPrice, tradeTerm } = res.data
    data.value.price = price
    data.value.discountedPrice = discountedPrice
    data.value.tradeTerm = getTradeTerm(tradeTerm)
    count.value = 1
  })
}

watchEffect(() => {
  if (isLogin.value && select.value.isFinished && detail.value?.canInquiry) {
    fetchPrice()
  }
})

const yanxuan = useRuntimeConfig().public.baseUrl.VITE_YANXUAN

const show = ref(false)
const company = companyStore()
const links = [
  {
    name: '开通设备制造商企业>',
    link: yanxuan + '/enterpriseCenter',
  },
  {
    name: '切换企业空间>',
    link: yanxuan + '/switch',
  },
]
const go = (link: string) => {
  window.open(link, '_target')
}

const { updateAnsCount } = sideWidgetsStore()

const applyLoading = ref(false)
const apply = () => {
  const { type } = company.company
  if (type != 1 && type != 3) {
    show.value = true
    return
  }
  if (!useAuth('procure:ansPrice:view')) {
    Modal.warn({
      title: '提示',
      content: '您所在的用户组没有询价的权限，请联系企业管理员。',
    })
  } else {
    applyLoading.value = true
    const { productName } = detail.value!
    const { partId, skuCode, skuId } = select.value
    http('/mall/p/personal-inquiry', {
      method: 'post',
      body: {
        prodName: productName,
        partId,
        skuCode,
        number: count.value,
        skuId,
        notes: '',
      },
    }).then((res) => {
      applyLoading.value = false
      useMall(res, (ret) => {
        message.success(`商品已添加到询价器`)
        updateAnsCount()
      })
    })
  }
}

const emits = defineEmits(['afterLogin'])
const handleLogin = () => {
  loginPop.show(() => {
    emits('afterLogin')
  })
}
</script>

<style lang="less" scoped>
:deep(.counter) {
  width: 100%;
  .ant-input-number-input {
    text-align: center;
  }
}
</style>
