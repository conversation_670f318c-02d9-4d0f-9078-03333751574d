<template>
  <a-spin :spinning="loading">
    <div v-html="html" class="doc"></div>
  </a-spin>
</template>

<script setup lang="ts">
import mammoth from 'mammoth'

const props = defineProps<{
  url: string
}>()
const loading = ref(false)

const html = ref('')

const loadFile = async () => {
  loading.value = true
  console.log('mmm', props.url)
  const res = await $fetch<ArrayBuffer>(props.url, {
    responseType: 'arrayBuffer',
  })
  const result = await mammoth.convertToHtml({ arrayBuffer: res })
  loading.value = false
  html.value = result.value
  console.log('%c Line:15 🥛 result', 'color:#12DD19', result)
}

loadFile()
</script>

<style lang="less" scoped>
.doc {
  :deep(img) {
    max-width: 100%;
  }
}
</style>
