<template>
  <div relative>
    <iframe
      v-if="model.modelUrl"
      :src="model.modelUrl"
      w-full
      aspect-ratio="4/3"
      frameborder="0"
      name="test"
      ref="iframeRef"
    ></iframe>
    <download-model ref="downloadRef"></download-model>
  </div>
</template>

<script lang="ts" setup>
import DownloadModel from './DownloadModel.vue'
import SelectModel from '~/components/Select/mode'

const props = defineProps<{
  model: SelectModel
}>()

const iframeRef = ref<HTMLIFrameElement>()
</script>
