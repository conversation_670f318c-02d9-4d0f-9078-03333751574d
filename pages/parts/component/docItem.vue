<template>
  <div relative @contextmenu="handleRightClick" border="1px solid #eee" ref="containerRef">
    <div min-h-screen-md v-if="item.type == 'pdf'">
      <PDF
        v-if="load"
        class="pdf"
        min-h-screen-md
        :src="item.url"
        :page="currentPage"
        @on-pdf-init="onPdfInit"
        @on-page-change="pageChange"
      />
    </div>
    <img v-else-if="item.type == 'img'" w-full :src="item.url" />

    <div p-20 min-h-screen-md v-else-if="item.type == 'docx'">
      <Doc :url="item.url" p-20></Doc>
    </div>

    <div v-else p-20>
      <hm-empty>
        <template #description>该文件不支持预览</template>
        <a-button type="primary" @click="download">点击下载</a-button>
      </hm-empty>
    </div>

    <div absolute w-full bottom-20 flex-center>
      <Control
        v-model:page="currentPage"
        :node="item"
        @page-change="pageChange"
        @to-start="currentPage = 1"
        @to-end="currentPage = total"
        @download="download"
        :type="item.type"
        v-if="['pdf', 'docxa'].includes(item.type) && visible"
      ></Control>
    </div>
  </div>
</template>

<script setup>
import PDF from 'pdf-vue3'
import Doc from './Doc.vue'
import { userStore } from '~/store/user'

const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
})

const currentPage = ref(1)
const containerRef = ref()

const load = ref(false)
const visible = useElementVisibility(containerRef)

watch(visible, () => {
  if (visible.value) {
    load.value = true
  }
})

const total = ref(0)
const pageChange = (page) => {
  if (page < 1 || page > total.value) return
  currentPage.value = page
}

const onPdfInit = (pdf) => {
  total.value = pdf.numPages
}

const store = userStore()
const download = () => {
  if (!store.user?.userId) {
    loginPop.show()
    return
  }
  const { name, type, url } = props.item
  useDownload(url, `${name}.pdf`)
}
</script>

<style lang="less" scoped>
:deep(.pdf) {
  canvas {
    box-shadow: none !important;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
  }
}
</style>
