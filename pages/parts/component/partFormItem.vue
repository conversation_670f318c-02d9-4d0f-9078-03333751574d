<template>
  <div flex items-center px-8 bg-gray text-white>{{ info.title }}</div>
  <div flex-center px-8 overflow-hidden>
    <slot></slot>
  </div>
  <div flex-center text-12>{{ info.unit }}</div>
</template>

<script lang="ts" setup>
import { SelectParameter } from '~/api/search/type'

const props = defineProps<{
  item: SelectParameter
}>()
const { item } = toRefs(props)

const info = computed(() => {
  const title = item.value.spec_name

  const reg = /(.*)\((.*)\)$/
  const match = title.match(reg)
  if (match) {
    return {
      title: match[1],
      unit: match[2]
    }
  }
  return {
    title,
    unit: ''
  }
})
</script>