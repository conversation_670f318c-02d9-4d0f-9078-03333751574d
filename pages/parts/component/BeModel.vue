<template>
  <div relative>
    <div v-if="currentType == Type.select">
      <a-alert type="warning" message="请选择参数"></a-alert>
    </div>

    <div aspect-video w-full v-if="currentType == Type.outTime">
      <a-alert type="error" message="加载3D模型超时">
        <template #action>
          <a-button type="primary" @click="getPreviewData">重新加载</a-button>
        </template>
      </a-alert>
    </div>
    <a-spin :spinning="loading" v-if="currentType == Type.preview" tip="模型加载中">
      <iframe aspect-video w-full :src="iframeUrl" frameborder="0"></iframe>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import SelectModel from '~/components/Select/mode'

const props = defineProps<{
  model: SelectModel
}>()

const iframeUrl = ref('')

const sleep = (time: number) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve('')
    }, time)
  })
}

const outTime = 30 * 1000
const startTime = ref(0)

const loading = ref(false)
enum Type {
  select,
  preview,
  outTime,
}

const currentType = ref(Type.select)

const fetchPreview = async () => {
  const { mident, isFinished, partId } = props.model
  if (!isFinished) return
  const currentTime = new Date().getTime()
  if (startTime.value + outTime <= currentTime) {
    currentType.value = Type.outTime
    return
  }
  const res = await request.post('/selection/model-review-address', {
    partId,
    extra: mident,
  })

  if (!res.data) {
    await sleep(3000)
    fetchPreview()
  } else {
    loading.value = false
    iframeUrl.value = res.data
  }
}

const getPreviewData = () => {
  loading.value = true
  currentType.value = Type.preview
  startTime.value = new Date().getTime()
  fetchPreview()
}

watch(
  () => props.model,
  (data) => {
    if (data.isFinished) {
      getPreviewData()
    } else {
      currentType.value = Type.select
    }
  },
  {
    deep: true,
    immediate: true,
  },
)
</script>
