<template>
  <div flex-1 bg-white p-40>
    <div flex text-14 justify-between p-20 style="background: #efefef">
      <img w-80 h-80 mr-30 src="@/assets/images/pay/check.png" />
      <div flex flex-col flex-1 justify-around>
        <span text-20 font-bold>
          订单提交成功,请尽快付款
        </span>
        <span text-16>
          请在 {{ timetxt }} 内付款,否则交易会被取消。
        </span>
      </div>
      <div flex items-center>
        <span text-16 font-bold px-10>应付总额:</span>
        <span text-18 font-bold style="color: red">{{ getPrice(orderInfo.totalPrice + (orderInfo.freightFee ?? 0)) }}</span>
      </div>
    </div>

    <div text-14 pt-20>
      <span 
        v-for="item in payList"
        :key="item.key"
        :class="['pay-item', item.key == selPay.key ? 'pay-item-active' : '', item.disabled ? 'pay-item-disabled' : '']"
        @click="onSelPay(item)"
      >
        <img :src="item.img" />
        {{ item.value }}
      </span>
    </div>

    <div pt-20>
      <a-radio-group v-model:value="payMethod">
        <a-radio :value="0">一次性支付</a-radio>
        <a-radio :value="1" disabled>分期付款</a-radio>
        <a-radio :value="2" disabled>账期付款</a-radio>
        <a-radio :value="3" disabled>货到付款</a-radio>
      </a-radio-group>
    </div>

    <div pt-20>
      <a-button type="primary" size="large" @click="onCompletePay">
        已完成支付
      </a-button>
    </div>
  </div>
</template>

<script setup>
import money from '@/assets/images/pay/money.png'
import yhk from '@/assets/images/pay/yhk.png'
import zfb from '@/assets/images/pay/zfb.png'
import wx from '@/assets/images/pay/wx.png'
import money1 from '@/assets/images/pay/money1.png'

import { companyStore } from '~/store/company'
const route = useRoute()
const router = useRouter()

const payList = [
  {
    key: 1,
    value: '线下支付',
    img: money
  },
  {
    key: 2,
    value: '银行转账',
    img: yhk,
    disabled: true
  },
  {
    key: 3,
    value: '支付宝',
    img: zfb,
    disabled: true
  },
  {
    key: 4,
    value: '微信支付',
    img: wx,
    disabled: true
  },
  {
    key: 5,
    value: '其他方式',
    img: money1,
    disabled: true
  }
]

const payMethod = ref(0)
const selPay = ref(payList[0])
const onSelPay = (data) => {
  if (data.disabled) return
  selPay.value = data
}

const onCompletePay = () => {
  if (orderNumbers.value.length == 1) {
    router.replace({
      path: `/workSpace/company-space/procure-order/${orderNumbers.value[0]}`
    })
  } else {
    router.replace({
      path: `/workSpace/company-space/procure-order`
    })
  }
}

const time = ref(null)
const timetxt = ref('')
const timeFun = (diffTime) => {
  if (diffTime > 0) {
    time.value = setInterval(() => {
      let diffH = Math.floor(diffTime / 3600) > 9 ? Math.floor(diffTime / 3600) : `0${Math.floor(diffTime / 3600)}`;
      let diffM = Math.floor(diffTime / 60 % 60) > 9 ? Math.floor(diffTime / 60 % 60) : `0${Math.floor(diffTime / 60 % 60)}`;
      let diffS = Math.floor(diffTime % 60) > 9 ? Math.floor(diffTime % 60) : `0${Math.floor(diffTime % 60)}`;
      timetxt.value = `${diffH}:${diffM}:${diffS}`;
      diffTime--;
      if (diffTime < 0) {
        clearInterval(time);
        Modal.confirm({
          title: '提示',
          content: '您的订单已过期,请重新提交',
          okText: '去提交',
          cancelButtonProps: { style: { display: 'none' } },
          onOk() {
            Modal.destroyAll()
            router.replace({
              path: `/workSpace/company-space/ans-price`
            })
          }
        })
        return;
      }
    }, 1000);
  }
}

const orderNumbers = ref([])
const orderInfo = ref({})

const getOrderInfo = async() => {
  const res = await http(`/mall/p/inquiry-list/info/${route.query?.inquiryOrderNo}`)
  useMall(res, () => {
    orderInfo.value = res.data
  })
}
onMounted(() => {
  getOrderInfo()
  const { orderNo } = route.query
  orderNumbers.value = orderNo.split(',')
  timeFun(86400)
})
</script>

<style lang="less" scoped>
.pay-item{
  margin-right: 10px;
  padding: 5px 10px;
  border: 1px solid #ccc;
  border-radius: 3px;
  cursor: pointer;

  img{
    width: 20px;
    height: 20px;
    margin-right: 5px;
  }
}

.pay-item-active{
  border-color: #f94c30;
}

.pay-item-disabled{
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
