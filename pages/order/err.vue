<template>
  <div flex-1 bg-white p-40>
    <div flex text-14 justify-between p-20 style="background: #efefef">
      <img w-80 h-80 mr-30 src="@/assets/images/pay/err.png" />
      <div flex flex-col flex-1 justify-around>
        <span text-20 font-bold>
          订单提交失败
        </span>
        <span text-16>
          部分商品库存不足,请修改商品数量并重新下单
        </span>
      </div>
    </div>

    <div v-if="err" text-16 mt-20>
      {{ err }}
    </div>

    <div pt-20>
      <a-button type="primary" size="large" @click="goToInq">
        返回询价管理
      </a-button>
    </div>
  </div>
</template>

<script setup>
const route = useRoute()
const router = useRouter()

const goToInq = () => {
  router.replace({
    path: `/workSpace/company-space/ans-price`
  })
}

const err = ref('')
onMounted(() => {
  const { errmsg } = route.query
  err.value = errmsg
})
</script>

<style lang="less" scoped>
.pay-item{
  margin-right: 10px;
  padding: 5px 10px;
  border: 1px solid #ccc;
  border-radius: 3px;
  cursor: pointer;

  img{
    width: 20px;
    height: 20px;
    margin-right: 5px;
  }
}

.pay-item-active{
  border-color: #f94c30;
}

.pay-item-disabled{
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
