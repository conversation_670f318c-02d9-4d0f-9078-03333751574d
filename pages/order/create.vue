<template>
  <div flex-1 bg-white px-20 pb-60 style="position: relative">
    <div class="btn">
      <span text-18 font-bold pr-20>
        总计:
        <b style="color: red">
          {{ getPrice(totalPrice + (orderInfo.freightFee || 0))
          }}{{
            orderInfo.freightFee == 0 || !has(orderInfo.freightFee)
              ? '（免运费）'
              : `（含运费 ${getPrice(orderInfo.freightFee)}）`
          }}
        </b>
      </span>
      <a-button type="primary" size="large" :loading="submitLoading" style="height: 45px" @click="onSubmit">
        提交订单
      </a-button>
    </div>

    <div class="selections">
      <div class="title">
        收货地址
        <a-button
          type="link"
          @click="
            navigateTo({
              path: '/workSpace/company-space/addr-info',
              query: {
                type: 0,
              },
            })
          "
        >
          管理
        </a-button>
      </div>
      <div flex flex-wrap>
        <div
          v-for="(item, index) in addressList"
          :key="index"
          :class="['card-shadow', 'address-item', selectIndex == index ? 'address-item-active' : '']"
          @click="onSelAddr(item, index)"
        >
          <div>
            <span class="label">联系人:</span>
            <span class="value">{{ item.receiver }}</span>
          </div>
          <div>
            <span class="label">所在地区:</span>
            <span class="value">{{ (item.province || '') + (item.city || '') + (item.area || '') }}</span>
          </div>
          <div>
            <span class="label">详细地址:</span>
            <span class="value">{{ item.addr }}</span>
          </div>
          <div>
            <span class="label">手机号:</span>
            <span class="value">{{ item.mobile }}</span>
          </div>

          <div v-if="item.commonAddr" class="default-tag">默认地址</div>
          <div v-if="selectIndex == index" class="selected-tag" text-12><CheckOutlined /></div>
        </div>
      </div>
    </div>

    <div class="selections">
      <div class="title">
        发票信息
        <a-button
          type="link"
          @click="
            navigateTo({
              path: '/workSpace/company-space/invoice-info',
              query: {
                type: invoiceType,
              },
            })
          "
        >
          管理
        </a-button>
      </div>
      <div p-10>
        <a-radio-group v-model:value="needInvoice">
          <a-radio-button :value="1">电子发票</a-radio-button>
          <a-radio-button :value="0">不开发票</a-radio-button>
        </a-radio-group>
        <div py-10 v-if="needInvoice == 1">
          <a-radio-group v-model:value="invoiceType">
            <a-radio :value="0">增值税专用发票</a-radio>
            <a-radio :value="1">普通发票</a-radio>
          </a-radio-group>
        </div>
        <div v-if="needInvoice == 1">
          <a-radio-group v-model:value="invoiceId">
            <a-radio
              v-for="item in invoiceList.filter((d) => d.type == invoiceType)"
              :key="item.id"
              :value="item.id"
              style="display: flex"
            >
              <span pr-20 w-300 class="text-ellipsis" :title="item.title">发票抬头: {{ item.title }}</span>
              <span pr-20 w-300 class="text-ellipsis">纳税人识别号: {{ item.invoiceNum }}</span>
              <a-tag v-if="item.isDefault == 1" color="var(--primary-color)">默认</a-tag>
            </a-radio>
          </a-radio-group>
        </div>
      </div>
    </div>

    <div class="selections" v-if="false">
      <div class="title">
        收票地址
        <a-button
          type="link"
          @click="
            navigateTo({
              path: '/workSpace/company-space/addr-info',
              query: {
                type: 1,
              },
            })
          "
        >
          管理
        </a-button>
      </div>
      <div flex flex-wrap>
        <div
          v-for="(item, index) in addressList1"
          :key="index"
          :class="['address-item', selectIndex1 == index ? 'address-item-active' : '']"
          @click="selectIndex1 = index"
        >
          <div>
            <span class="label">联系人:</span>
            <span class="value">{{ item.receiver }}</span>
          </div>
          <div>
            <span class="label">所在地区:</span>
            <span class="value">{{ (item.province || '') + (item.city || '') + (item.area || '') }}</span>
          </div>
          <div>
            <span class="label">详细地址:</span>
            <span class="value">{{ item.addr }}</span>
          </div>
          <div>
            <span class="label">手机号:</span>
            <span class="value">{{ item.mobile }}</span>
          </div>

          <div v-if="item.commonAddr" class="default-tag">默认地址</div>
          <div v-if="selectIndex1 == index" class="selected-tag" text-12><CheckOutlined /></div>
        </div>
      </div>
    </div>

    <div class="selections">
      <div class="title">商品明细</div>
      <div v-for="item in goods" :key="item.shopId" p-10>
        <div flex items-center text-16>
          <shop-outlined color="#f94c30" text-20 pr-10 />
          <span font-bold>{{ item.shopName }}</span>
          <a-tag v-if="item.isSelf" color="var(--primary-color)" ml-10>联营</a-tag>
        </div>
        <a-table :columns="columns" row-key="skuId" size="middle" :data-source="item.goods" :pagination="false">
          <template #bodyCell="{ column, record, value }">
            <span v-if="column.dataIndex == 'tradeTerm'">
              {{ value ? (value > 1 ? `${value}日发货` : '当日发货') : '--' }}
            </span>
            <span v-if="column.dataIndex == 'price'">{{ getPrice(value) }}/个</span>
            <span v-if="column.dataIndex == 'totalPrice'">
              {{ getPrice(value) }}
            </span>
          </template>
        </a-table>
        <div my-20>
          <a-input
            v-model:value="item.remark"
            placeholder="订单备注"
            :maxlength="100"
            show-count
            @blur="
              () => {
                remarks[item.shopId] = item.remark
              }
            "
          />
        </div>
      </div>
    </div>

    <div class="selections">
      <div class="title">配送信息</div>
      <div px-20 pt-10>
        <span mr-10>发货方式:</span>
        <a-radio-group v-model:value="sendMethod">
          <a-radio :value="1" disabled>统一发货</a-radio>
          <a-radio :value="2">拆单发货</a-radio>
        </a-radio-group>
      </div>
    </div>

    <div class="selections">
      <div class="title">其他信息</div>
      <div px-20 pt-10>
        <div py-5>询价单号: {{ orderInfo.orderNo }}</div>
        <div py-5>项目编号: {{ orderInfo.projectNo }}</div>
        <div py-5>
          <span>采购合同号:</span>
          <a-input placeholder="采购合同号" w-250 ml-10 v-model:value="orderInfo.thirdOrderNumber"></a-input>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { companyStore } from '~/store/company'
const route = useRoute()
const router = useRouter()

let uuid = ''
const genUUID = () => {
  const s = []
  const hexDigits = '0123456789abcdef'
  for (let i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
  }
  s[14] = '4' // bits 12-15 of the time_hi_and_version field to 0010
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1) // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23] = '-'

  return s.join('')
}

const columns = [
  {
    title: '商品名称',
    dataIndex: 'prodName',
    ellipsis: true,
  },
  {
    title: '型号',
    dataIndex: 'partyCode',
    ellipsis: true,
  },
  {
    title: '交期',
    dataIndex: 'tradeTerm',
    width: 150,
    ellipsis: true,
  },
  {
    title: '数量',
    dataIndex: 'prodCount',
    width: 150,
    ellipsis: true,
  },
  {
    title: '折后单价',
    dataIndex: 'price',
    width: 150,
    ellipsis: true,
  },
  {
    title: '小计',
    dataIndex: 'totalPrice',
    width: 150,
    ellipsis: true,
  },
]

const useCompany = companyStore()
const addressList = ref([])
const addressList1 = ref([])
const selectIndex = ref(0)
const selectIndex1 = ref(0)
// 地址
const getAddress = async () => {
  const res = await http('/mall/shop/merchantAddr/page', {
    params: {
      merchantId: useCompany.company.shopCompanyId,
    },
  })
  useMall(res, (ret) => {
    const _list = (ret.records || []).filter((d) => d.type == 0)
    const _list1 = (ret.records || []).filter((d) => d.type == 1)
    const _i = _list.findIndex((d) => d.commonAddr == 1)
    const _i1 = _list1.findIndex((d) => d.commonAddr == 1)
    if (_i > -1) {
      const item = _list.splice(_i, 1)
      addressList.value = [...item, ..._list]
    } else {
      addressList.value = _list
    }
    if (_i1 > -1) {
      const item = _list1.splice(_i, 1)
      addressList1.value = [...item, ..._list1]
    } else {
      addressList1.value = _list1
    }
    if (addressList.value.length) {
      onConfirmOrder(addressList.value[0].id)
    } else {
      Modal.confirm({
        title: '提示',
        content: '请先完善收货地址',
        okText: '去完善',
        cancelButtonProps: { style: { display: 'none' } },
        onOk() {
          Modal.destroyAll()
          navigateTo({
            path: '/workSpace/company-space/addr-info',
            query: {
              type: 0,
            },
          })
        },
      })
    }
  })
}

const onSelAddr = (item, index) => {
  selectIndex.value = index
  onConfirmOrder(item.id)
}

const needInvoice = ref(1)
const invoiceList = ref([])
const invoiceId = ref(null)
// 发票
const getInvoice = async () => {
  const res = await http('/mall/shop/invoice/page', {
    params: {
      current: 1,
      size: 100,
      merchantId: useCompany.company.shopCompanyId,
    },
  })
  useMall(res, (ret) => {
    const _list = (ret.records || []).filter((d) => d.isDefault == 1)
    const _list1 = (ret.records || []).filter((d) => d.isDefault != 1)
    invoiceList.value = [..._list, ..._list1]

    // 默认一个发票
    invoiceId.value = invoiceList.value.filter((d) => d.type == invoiceType.value)[0]?.id
  })
}

const onConfirmOrder = async (addrId) => {
  const res = await http(`/mall/p/order/confirm`, {
    method: 'post',
    body: {
      addrId,
      uuid,
      projectNo: orderInfo.value.projectNo,
      inquiryOrderNo: orderInfo.value.orderNo,
    },
  })
  useMall(res, async (ret) => {
    const { shopCartOrders } = ret
    // sendMethod.value = shopCartOrders.length > 1 ? 2 : 1
    getGoodsList(shopCartOrders)
  })
}

const remarks = ref({})
const goods = ref([])
const totalPrice = ref(0)
const getGoodsList = (data) => {
  totalPrice.value = 0
  goods.value = []
  data.forEach((d) => {
    const { shopCartItems } = d.shopCartItemDiscounts[0]
    const goodChildren = []
    let isSelf = false
    shopCartItems.forEach((t) => {
      isSelf = t.isSelf == 1
      t.totalPrice = Number(t.price * t.prodCount)
      totalPrice.value += t.totalPrice
      goodChildren.push(t)
    })
    goods.value.push({
      isSelf,
      shopId: d.shopId,
      shopName: d.shopName,
      goods: goodChildren,
      remark: remarks.value[d.shopId] || '',
    })
  })
}

const sendMethod = ref(2)
const invoiceType = ref(0)
watch(invoiceType, () => {
  invoiceId.value = null
})

const submitLoading = ref(false)
const onSubmit = async () => {
  const postData = {
    uuid,
    orderShopParams: goods.value.map((d) => {
      return {
        shopId: d.shopId,
        remarks: d.remark,
      }
    }),
    orderInvoiceList: [],
    thirdOrderNumber: orderInfo.value.thirdOrderNumber,
  }
  // 发票
  if (needInvoice.value == 1) {
    if (!invoiceId.value) {
      message.warning('请选择发票')
      return
    }
    const item = invoiceList.value.find((d) => d.id == invoiceId.value)
    postData.orderInvoiceList = goods.value.map((d) => {
      return {
        headerName: item.title,
        headerType: 1,
        invoiceContext: 1,
        invoiceType: 1,
        invoiceTaxNumber: item.invoiceNum,
        shopId: d.shopId,
      }
    })
  }
  submitLoading.value = true
  const res = await http('/mall/p/order/submit', {
    method: 'post',
    body: postData,
  })
  submitLoading.value = false
  useMall(
    res,
    async (ret) => {
      router.replace({
        path: `/order/pay`,
        query: {
          orderNo: ret.orderNumbers,
          inquiryOrderNo: route.query?.orderNo,
        },
      })
    },
    (ret) => {
      router.replace({
        path: `/order/err`,
        query: {
          errmsg: ret.msg,
        },
      })
    },
  )
}

const orderInfo = ref({})
onMounted(async () => {
  const res = await http(`/mall/p/inquiry-list/info/${route.query?.orderNo}`)
  useMall(res, async () => {
    orderInfo.value = res.data
  })
  uuid = genUUID()
  getAddress()
  getInvoice()
})
</script>

<style lang="less" scoped>
.selections {
  padding: 20px 10px 20px;
  font-size: 14px;

  &:not(:last-child) {
    border-bottom: 1px solid #ccc;
  }

  .title {
    font-size: 16px;
    font-weight: bold;
  }

  .address-item {
    min-width: 300px;
    width: 23%;
    padding: 10px;
    margin: 10px 1%;
    line-height: 30px;
    cursor: pointer;
    position: relative;

    .label {
      display: inline-block;
      width: 90px;
      padding-right: 10px;
      text-align: right;
    }

    .value {
      word-break: break-all;
    }

    .default-tag {
      position: absolute;
      top: 10px;
      right: 10px;
      font-size: 12px;
      background: green;
      color: #fff;
      border-radius: 3px;
      line-height: 20px;
      padding: 0 5px;
    }

    .selected-tag {
      width: 20px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      position: absolute;
      bottom: 0;
      left: 0;
      background: var(--primary-color);
      color: #fff;
    }
  }

  .address-item-active {
    border: 2px solid var(--primary-color);
  }
}

.btn {
  height: 50px;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  text-align: right;
  border-top: 5px solid #eff2f7;
}
</style>
