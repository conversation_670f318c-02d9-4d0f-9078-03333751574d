<template>
  <div>
    <Nav>
      <nuxt-link to="/">首页</nuxt-link>
      {{ '搜索“' + searchText + '"的结果' }}
    </Nav>

    <div v-if="localType == 'skuCode'">
      <div flex items-center justify-between flex-wrap>
        <div title-text mt-8 mb-8>
          找到的型号：共
          <span text-primary>{{ typeTotalCount }}</span>
          个
        </div>
        <div v-if="typeList.length > 0" mt-8 mb-8 flex items-center>
          <span
            title-text
            mr-16
            cursor-pointer
            :class="{ 'color-primary': showType === 'card' }"
            @click="onShowTypeClick('card')"
          >
            <AppstoreOutlined />
            卡片
          </span>
          <span
            title-text
            cursor-pointer
            :class="{ 'color-primary': showType === 'list' }"
            @click="onShowTypeClick('list')"
          >
            <UnorderedListOutlined />
            列表
          </span>

          <a-pagination
            v-model:current="typeCurrentPage"
            v-model:page-size="typeCurrentPageSize"
            simple
            :total="typeTotalCount"
            @change="onTypePageChange"
          />
        </div>
      </div>
      <div v-show="showType === 'card' && typeLoading">
        <card-skeleton />
      </div>
      <div
        v-show="showType === 'card' && !typeLoading"
        class="grid gap-20 grid-cols-1"
        sm="grid-cols-2"
        md="grid-cols-3"
        lg="grid-cols-4"
        xl="grid-cols-5"
      >
        <search-item
          @click="goPart(item)"
          v-for="item in typeList"
          :key="item.id"
          :info="item"
          type="card"
          :searchType="localType"
        ></search-item>
      </div>
      <div v-show="showType === 'list' && typeLoading">
        <list-skeleton mb-16 v-for="index in 20" :key="index" />
      </div>
      <div v-show="showType === 'list' && !typeLoading">
        <search-item
          @click="goPart(item)"
          mb-16
          v-for="item in typeList"
          :key="item.id"
          :info="item"
          :search-type="localType"
          :type="showType"
        ></search-item>
      </div>
      <a-pagination
        mt16
        v-if="typeList.length > 0"
        text-right
        v-model:current="typeCurrentPage"
        v-model:page-size="typeCurrentPageSize"
        show-size-changer
        show-quick-jumper
        :total="typeTotalCount"
        @change="onTypePageChange"
      />
    </div>
    <a-divider v-if="typeTotalCount" />

    <template v-if="localType == 'keyword'">
      <div>
        <div title-text mt-8 mb-8>
          找到的类目：共
          <span text-primary>{{ categoryList.length }}</span>
          个
        </div>
        <list-skeleton v-if="categoryLoading" />
        <div v-else flex flex-wrap gap-12>
          <div
            card--reactive
            w100
            h100
            text-center
            v-for="item in categoryList"
            :key="item.id"
            @click="onCategoryItemClick(item.categoryCode)"
            :title="item.categoryName"
          >
            <img w-64 h-64 mb-4 mt10 :src="item.categoryImgUrl || unknownImg" />
            <div text truncate>{{ item.categoryName }}</div>
          </div>
        </div>
      </div>
      <a-divider />

      <list-skeleton v-if="filterLoading" />
      <div v-if="!filterLoading && selectionList.length > 0" card mb-8>
        <div filter>
          <span filter__title>类目</span>
          <span filter__content>
            <a-checkable-tag
              v-for="option in filters.categories"
              :key="option.value"
              :checked="option.selected"
              @change="(checked) => onFilterChange('categories', option.value, checked)"
            >
              {{ option.label }}
            </a-checkable-tag>
          </span>
        </div>
        <div filter>
          <span filter__title>品牌</span>
          <span filter__content>
            <a-checkable-tag
              v-for="option in filters.websites"
              :key="option.value"
              :checked="option.selected"
              @change="(checked) => onFilterChange('websites', option.value, checked)"
            >
              {{ option.label }}
            </a-checkable-tag>
          </span>
        </div>
      </div>
    </template>
    <div mb-8>
      <span
        inline-block
        mr-8
        h-24
        p-x-8
        bg-white
        line-height-22
        b-1
        b-dashed
        border-primary
        v-show="filter.options.length > 0"
        v-for="filter in selectedFilters"
        :key="filter.id"
      >
        <span sub-text>{{ filter.name + '：' }}</span>
        <span text font-size-12>{{ filter.options.join('，') }}</span>
        <CloseCircleFilled ml-4 font-size-12 color-primary @click="onClearFilterClick(filter.id)" />
      </span>
      <a-button
        v-show="selectedFilters.some((ele) => ele.options.length > 0)"
        type="primary"
        size="small"
        @click="onClearAllClick"
      >
        清空筛选
      </a-button>
    </div>
    <div v-if="localType == 'keyword'">
      <div flex items-center justify-between flex-wrap>
        <div title-text mt-8 mb-8>
          找到的商品：共
          <span text-primary>{{ totalCount }}</span>
          个
        </div>
        <div v-if="selectionList.length > 0" mt-8 mb-8 flex items-center>
          <span
            title-text
            mr-16
            cursor-pointer
            :class="{ 'color-primary': showType === 'card' }"
            @click="onShowTypeClick('card')"
          >
            <AppstoreOutlined />
            卡片
          </span>
          <span
            title-text
            cursor-pointer
            :class="{ 'color-primary': showType === 'list' }"
            @click="onShowTypeClick('list')"
          >
            <UnorderedListOutlined />
            列表
          </span>

          <a-pagination
            v-model:current="currentPage"
            v-model:page-size="currentPageSize"
            simple
            :total="totalCount"
            @change="onPageChange"
          />
        </div>
      </div>
      <div v-show="showType === 'card' && selectionLoading">
        <card-skeleton />
      </div>
      <div
        v-show="showType === 'card' && !selectionLoading"
        class="grid gap-20 grid-cols-1"
        sm="grid-cols-2"
        md="grid-cols-3"
        lg="grid-cols-4"
        xl="grid-cols-5"
      >
        <search-item
          @click="goPart(item)"
          v-for="item in selectionList"
          :key="item.id"
          :info="item"
          :type="showType"
          :searchType="localType"
        ></search-item>
      </div>
      <div v-show="showType === 'list' && selectionLoading">
        <list-skeleton mb-16 v-for="index in 20" :key="index" />
      </div>
      <div v-show="showType === 'list' && !selectionLoading">
        <search-item
          @click="goPart(item)"
          v-for="item in selectionList"
          :key="item.id"
          :info="item"
          :type="showType"
          :searchType="localType"
          mb-16
        ></search-item>
      </div>
      <a-pagination
        mt-16
        v-if="selectionList.length > 0"
        text-right
        v-model:current="currentPage"
        v-model:page-size="currentPageSize"
        show-size-changer
        show-quick-jumper
        :total="totalCount"
        @change="onPageChange"
      />
    </div>
    <div v-if="!categoryList.length && !totalCount && !typeTotalCount" w-full h-640 flex justify-center items-center>
      <img
        h-300
        src="~/assets/images/daodao_error.jpg"
        :style="{
          maskImage: 'radial-gradient(circle, black 35%, transparent 65%)',
        }"
      />
    </div>
    <!-- <go-register></go-register> -->
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { reactive } from 'vue'
import { getCategoryList, getFilterList, getSelect, getSelectionList } from '~/api/search'
import { highlightExp } from '~/utils/regex'
import { isImageValid } from '~/utils/util'
import unknownImg from '~/assets/images/unknown.png'
import { useSearch } from '~/store/useSearch'
import type { SearchItem } from '~/components/searchItem/type'

interface CategoryItem {
  categoryCode: string
  categoryDesc: string
  categoryImgUrl: string
  categoryName: string
  categoryPath: string
  categorySiteCode: string
  categoryUrl: string
  createTime: string
  id: number
  updateTime: string
}

interface FilterOption {
  label: string
  value: string
  selected?: boolean
}

type SelectionItem = SearchItem

interface Filters {
  categories: Array<FilterOption>
  websites: Array<FilterOption>
  brands: Array<FilterOption>
}

const router = useRouter()
const route = useRoute()
const searchText = computed(() => route.query.s as string)

const typeLoading = ref<boolean>(true)
const categoryLoading = ref<boolean>(true)
const filterLoading = ref<boolean>(true)
const selectionLoading = ref<boolean>(true)

const showType = ref<'card' | 'list'>('card')

const { currentType } = storeToRefs(useSearch())
const localType = ref(currentType.value.type)

const filters: Filters = reactive({
  categories: [],
  websites: [],
  brands: [],
})

const selectedFilters = computed(() => {
  return [
    {
      id: 'categories',
      name: '类目',
      options: filters.categories.filter((ele) => ele.selected).map((ele) => ele.label),
    },
    {
      id: 'websites',
      name: '品牌',
      options: filters.websites.filter((ele) => ele.selected).map((ele) => ele.label),
    },
    // {
    //   id: 'brands',
    //   name: '品牌',
    //   options: filters.brands
    //     .filter((ele) => ele.selected)
    //     .map((ele) => ele.label),
    // },
  ]
})

const typeList = ref<SelectionItem[]>([])
const categoryList = ref<CategoryItem[]>([])
const selectionList = ref<SelectionItem[]>([])

const currentPage = ref<number>(1)
const currentPageSize = ref<number>(50)
const totalCount = ref<number>(0)

const typeCurrentPage = ref<number>(1)
const typeCurrentPageSize = ref<number>(50)
const typeTotalCount = ref<number>(0)

const onFilterChange = (filterName: string, value: string, checked: boolean) => {
  // 重置页码
  currentPage.value = 1
  typeCurrentPage.value = 1

  const filter = filters[filterName]
  const nextFilter = filter.map((ele) => {
    if (ele.value === value) {
      ele.selected = checked
    }
    return ele
  })
  filters[filterName] = nextFilter
  getSelectionListCaller()
}

const onShowTypeClick = (type: string) => {
  showType.value = type
  getSelectionListCaller()
}

const onCategoryItemClick = (id: string | number) => {
  router.push({
    path: '/category',
    query: { id, s: searchText.value },
  })
}

const onClearFilterClick = (id: string) => {
  const nextFilters = filters[id].map((ele) => {
    ele.selected = false
    return ele
  })
  filters[id] = nextFilters
  getSelectionListCaller()
}

const onClearAllClick = () => {
  ;['categories', 'websites', 'brands'].forEach((filter) => {
    const nextFilter = filters[filter].map((ele) => {
      ele.selected = false
      return ele
    })
    filters[filter] = nextFilter
  })
  getSelectionListCaller()
}

const onPageChange = (page: number, pageSize: number) => {
  currentPage.value = page
  currentPageSize.value = pageSize
  getSelectionListCaller()
}

const onTypePageChange = (page: number, pageSize: number) => {
  typeCurrentPage.value = page
  typeCurrentPageSize.value = pageSize
  getSelectionListCaller('TYPE')
}

/**
 * 检查图片地址是否有效
 * @param category
 */
const checkImageValidity = async (category: CategoryItem): Promise<CategoryItem> => {
  const isValid = await isImageValid(category.categoryImgUrl)
  if (!isValid) {
    category.categoryImgUrl = ''
  }
  return category
}

const getCategoryListCaller = async () => {
  categoryLoading.value = true
  const { data } = await getCategoryList({ keyword: searchText.value })
  categoryLoading.value = false
  categoryList.value = data
  Promise.all(data.map(checkImageValidity))
    .then((updatedArray) => {
      categoryList.value = updatedArray
    })
    .catch((error) => {
      console.error('检查图片地址时发生错误:', error)
    })
}

const getFilterListCaller = async () => {
  filterLoading.value = true
  const {
    data: { categories, websites, brands },
  } = await getFilterList({ keyword: searchText.value })
  filterLoading.value = false
  filters.categories =
    categories?.map((ele: any) => ({
      label: ele.categoryName,
      value: ele.categoryCode,
    })) ?? []
  filters.websites =
    websites?.map((ele: any) => ({
      label: ele.websiteName,
      value: ele.websiteCode,
    })) ?? []
  // filters.brands = brands?.map((ele: any) => ({ label: ele, value: ele })) ?? []
}

const generateQuery = () => {
  const query = {
    keyword: searchText.value,
    websiteCode: filters.websites
      .filter((ele) => ele.selected)
      .map((ele) => ele.value)
      .join(','),
    brand: filters.brands
      .filter((ele) => ele.selected)
      .map((ele) => ele.value)
      .join(','),
    categoryCode: filters.categories
      .filter((ele) => ele.selected)
      .map((ele) => ele.value)
      .join(','),
    pageNo: currentPage.value,
    pageSize: currentPageSize.value,
    optionFilters: [],
  }

  return query
}

const generateTypeQuery = () => {
  const query = {
    keyword: searchText.value,
    websiteCode: '',
    brand: '',
    categoryCode: '',
    pageNo: typeCurrentPage.value,
    pageSize: typeCurrentPageSize.value,
    optionFilters: [],
  }

  return query
}

const handleHightLight = (data = []) => {
  data.map((ele: any) => {
    if (Array.isArray(ele.highLights) && !!ele.highLights[0]) {
      ele.highlights = highlightExp(ele.highLights[0])
    }
    return ele
  })
}

const fetchTypeData = async () => {
  typeLoading.value = true
  const query = generateTypeQuery()
  const res = await getSelect('type', query)
  typeLoading.value = false
  const data = res.data
  // handleHightLight(data.data)
  typeList.value = data?.data || []
  typeTotalCount.value = data.totalCount
}

const fetchSelectionData = async () => {
  selectionLoading.value = true
  const query = generateQuery()
  const res = await getSelect('kw', query)
  selectionLoading.value = false
  const data = res.data
  // handleHightLight(data.data)
  selectionList.value = data?.data || []
  totalCount.value = data?.totalCount || 0
}

const getSelectionListCaller = async (by?: string) => {
  if (localType.value == 'keyword') {
    fetchSelectionData()
  }
  if (localType.value == 'skuCode') {
    fetchTypeData()
  }
}

const heightlight = (text: string) => {
  // 根据搜索关键字用 <em> 标签包裹 忽略大小写
  const reg = new RegExp(searchText.value, 'ig')
  return text.replace(reg, '<em>$&</em>')
}

if (!searchText.value) {
  console.log('搜索关键字为空，跳转到首页')
  router.push('/')
}

onMounted(() => {
  search()
})

watch(
  () => route.query,
  () => {
    search()
  },
)

const search = () => {
  localType.value = currentType.value.type
  if (localType.value == 'keyword') {
    getFilterListCaller()
    getCategoryListCaller()
    getSelectionListCaller()
  } else if (localType.value == 'skuCode') {
    getSelectionListCaller()
  }
}

// watch(
//   () => currentType.value.type,
//   () => {
//     if (searchText.value) {
//       getSelectionListCaller()
//     }
//   },
// )

const isMetaPressed = useMeta()
const goPart = (item: SelectionItem) => {
  const _path = {
    path: `/parts/${item.id}`,
    query: {} as Obj,
  }
  if (localType.value == 'skuCode') {
    _path.query.typeCode = item.partCode
  }
  const path = router.resolve(_path)
  if (isMetaPressed.value) {
    window.open(path.href)
  } else {
    router.push(_path)
  }
}
</script>
