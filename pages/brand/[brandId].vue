<template>
  <div class="text-14">
    <div v-if="agent" class="fixed right-10 z-10 top-1/2 translate-y--1/2 cursor-pointer group" @click="showChat">
      <img
        :src="useImage(useObs(agent.agentImg))"
        class="w-80 h-80 rounded-full overflow-hidden shadow-md transition-all"
        alt=""
      />
      <div
        class="absolute left-0 top-full mt-2 bg-white p-4 rounded-4 card-shadow w-max opacity-0 group-hover:opacity-100 transition-opacity truncate"
      >
        {{ agent.agentName }}
      </div>
    </div>
    <a-skeleton v-if="pending" p-10></a-skeleton>
    <template v-else>
      <a-empty description="品牌信息未找到" mt-40 v-if="!data"></a-empty>
      <div v-else>
        <div class="bg-powder-blue h-200">
          <div
            class="flex w-full max-w-1440 h-full px-50 py-30 bg-white items-center justify-between content-width border-b border-solid border-#eee"
          >
            <div flex-1 flex items-center>
              <div class="flex-shrink-0">
                <img
                  class="h-120 w-120 cursor-pointer object-contain"
                  :src="useImage(useObs(data.shopLogo))"
                  alt=""
                  @click="goHome"
                />
              </div>
              <div class="ml-20 flex flex-col">
                <div class="flex items-center">
                  <span class="text-24 font-bold color-#333">{{ data.shopName }}</span>
                  <a-button v-if="!isCollect" type="link" @click="handleCollect">
                    <StarOutlined />
                    收藏
                  </a-button>
                  <a-button v-else type="link" @click="handleCollect">
                    <StarFilled />
                    已收藏
                  </a-button>
                </div>
                <div class="mt-8 text-14 text-gray-500 line-clamp-2" line-height-24 :title="data.intro">
                  {{ data.intro }}
                </div>
                <div flex items-center mt-16>
                  <div flex flex-col items-center mr-20>
                    <span class="text-18 line-height-30 font-bold color-primary text-center">
                      {{ data.prodTotalCount || 0 }}
                    </span>
                    <div flex items-center justify-center>
                      <span class="text-12 color-#999 text-center">产品数</span>
                    </div>
                  </div>

                  <div flex flex-col items-center mr-20>
                    <span class="text-18 line-height-30 font-bold color-primary text-center">
                      {{ data.visits || 0 }}
                    </span>
                    <div flex items-center justify-center>
                      <span class="text-12 color-#999 text-center">浏览数</span>
                    </div>
                  </div>
                  <div flex flex-col items-center>
                    <span class="text-18 line-height-30 font-bold color-primary text-center">
                      {{ data.userCollect || 0 }}
                    </span>
                    <div flex items-center justify-center>
                      <span class="text-12 color-#999 text-center">收藏数</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="w-150 h-150">
              <img class="w-full h-full" :src="code" alt="" />
            </div>
          </div>
        </div>

        <nav class="h-50 bg-powder-blue">
          <div class="bg-white content-width flex justify-between relative">
            <div class="flex">
              <span
                class="mx-30 leading-50 cursor-pointer text-16 font-bold"
                :class="{ 'color-primary border-b-2 border-primary': $route.path === `/brand/${shopId}` }"
                @click="$router.push(`/brand/${shopId}`)"
              >
                首页
              </span>
              <span
                class="mx-30 leading-50 cursor-pointer text-16 font-bold"
                :class="{ 'color-primary border-b-2 border-primary': $route.path.includes('product') }"
                @click="$router.push(`/brand/${shopId}/product`)"
              >
                全部产品
              </span>
              <span class="mx-30 leading-50 cursor-pointer text-16 font-bold" @click="showCategory" ref="categoryRef">
                产品分类
                <CaretDownOutlined />
                <KeepAlive>
                  <Category v-if="show" :brand-id="shopId" @handle-category="clickCategory" z-10 />
                </KeepAlive>
              </span>
              <span
                class="mx-30 leading-50 cursor-pointer text-16 font-bold"
                :class="{ 'color-primary border-b-2 border-primary': $route.path.includes('news') }"
                @click="$router.push(`/brand/${shopId}/news`)"
              >
                新闻动态
              </span>
            </div>
            <div class="flex items-center mr-30">
              <a-input-search
                v-model:value="searchKeyword"
                placeholder="搜索产品、资源、新闻"
                enter-button
                style="width: 250px"
                @search="handleSearch"
              />
            </div>
          </div>
        </nav>

        <!-- <nav class="h-40 bg-primary">
          <div class="content-width text-white leading-40 relative cursor-pointer">
            <span ml-16 @click="showCategory" ref="categoryRef">
              全部分类
              <DownOutlined />
              <KeepAlive>
                <Category v-if="show" :brand-id="shopId" @handle-category="clickCategory" z-10 />
              </KeepAlive>
            </span>

            <span mx-20 cursor-pointer>方案案例</span>
          </div>
        </nav> -->

        <div class="content-width">
          <router-view :key="$route.fullPath"></router-view>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import Category from './components/category.vue'
definePageMeta({
  layout: 'full',
})

const show = ref(false)
const categoryRef = ref()
const isLogin = useLoginState()
const base = useRuntimeConfig().public.baseUrl.VITE_API_BASE

const showCategory = () => {
  if (!show.value) {
    show.value = true
  }
}
onClickOutside(categoryRef, () => {
  show.value = false
})

// 从路由获取品牌id
const route = useRoute()
const shopId = computed(() => route.params.brandId)
const code = computed(() => {
  if (import.meta.env.DEV) {
    console.log('mmm', import.meta.env)
    return `${base}/mall/shop/wx-miniapp/qrcode?params=${shopId.value}&envVersion=trial`
  }
  return `${base}/mall/shop/wx-miniapp/qrcode?params=${shopId.value}`
})
// 获取品牌信息
const { data, pending } = await useCloudApi('/mall/shop/info', {
  query: {
    shopId: shopId.value,
  },
  transform: (res) => {
    console.log('%c Line:168 🥛 res', 'color:#CB9C1A', res)
    if (res.success) {
      return res.data
    }
    return null
  },
})

const agent = ref()
const loadCustomer = async () => {
  const [err, res] = await useCacheFetch(`agent-${shopId.value}`, () =>
    try_http('/mall/p/llm/agent/getConfigByAgentId', {
      query: getshopsign(
        {
          agentId: shopId.value,
        },
        'get'
      ),
      headers: {
        grantType: 'sign',
      },
    })
  )
  if (!err) {
    agent.value = JSON.parse(res.data)
  }
}

// 搜索功能
const router = useRouter()
const searchKeyword = ref('')

const handleSearch = (value) => {
  if (!value || !value.trim()) {
    message.warning('请输入搜索关键词')
    return
  }

  // 跳转到搜索结果页
  router.push({
    path: `/brand/${shopId.value}/search`,
    query: {
      keyword: value.trim(),
    },
  })
}

const showChat = () => {
  chatPop.show(shopId.value)
}

const goHome = () => {
  navigateTo(`/brand/${shopId.value}`)
}

const clickCategory = (node) => {
  show.value = false
  navigateTo({
    path: `/brand/${shopId.value}/product`,
    query: {
      categoryId: node.categoryId,
    },
  })
}

const handleCollect = async () => {
  if (!isLogin.value) {
    loginPop.show(() => {})
    return
  }
  if (isCollect.value) {
    const [err, res] = await try_http('/mall/p/shop/collection/' + shopId.value, {
      method: 'delete',
    })
    if (!err) {
      isCollect.value = false
      message.success('取消收藏成功')
    }
  } else {
    const [err, res] = await try_http('/mall/p/shop/collection/' + shopId.value, {
      method: 'post',
    })
    if (!err) {
      isCollect.value = true
      message.success('收藏成功')
    }
  }
}

const isCollect = ref(false)
const getCollectStatus = async () => {
  const [err, res] = await try_http('/mall/p/shop/collection/isCollection', {
    method: 'get',
    query: {
      shopId: shopId.value,
    },
  })
  if (!err) {
    isCollect.value = res.data
  }
}

const visitShop = async () => {
  const [err, res] = await try_http('/mall/shop/visit/' + shopId.value, {
    method: 'post',
  })
  if (!err) {
    // message.success('浏览成功')
  }
}

onMounted(() => {
  loadCustomer()
  visitShop()
  if (isLogin.value) {
    getCollectStatus()
  }
})
</script>
