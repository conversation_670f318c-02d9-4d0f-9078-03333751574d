<template>
  <div class="w-full max-w-1680 mx-auto py-20" max-sm="overflow-hidden">
    <div flex bg-white p-16 card-shadow rounded-2 min-h-182 flex-wrap>
      <template v-if="pageDetail">
        <div w-150 h-150 relative>
          <swiper-image :links="select.skuImage" w-150 h-150></swiper-image>
          <!-- <div -->
          <!--   v-if="select.noSkuImage" -->
          <!--   absolute -->
          <!--   left-0 -->
          <!--   bottom-0 -->
          <!--   w-full -->
          <!--   h-20 -->
          <!--   z-10 -->
          <!--   flex-center -->
          <!--   text="14 #fff" -->
          <!--   bg="black/30" -->
          <!-- > -->
          <!--   该型号未配置图片 -->
          <!-- </div> -->
        </div>
        <div ml-30 flex-1 flex flex-col>
          <div text-20 font-bold mt-10>{{ pageDetail.productName }}</div>
          <div text-14 text-gray mt-10>品牌: {{ pageDetail.website.websiteName }}</div>
          <div flex-1></div>
          <div flex items-end>
            <Price :select="select" @afterLogin="afterLogin" />
            <Consult />
          </div>
        </div>
        <div flex flex-col justify-between>
          <img :src="pageDetail.website.websiteImgUrl" h-50 self-end w-auto alt="" />
          <a-space self-end>
            <a-button type="primary" v-if="select.hasModel" @click="download" :loading="downloadLoding">
              <CodeSandboxOutlined />
              下载模型
            </a-button>
            <!-- <a-button type="primary" :disabled="!pageDetail.specPdfUrl" @click="open(pageDetail.specPdfUrl)"><FilePdfOutlined />查看 PDF 文档</a-button> -->
            <a-button type="primary" @click="goSource" v-if="!partId.startsWith('mall')">
              <ArrowRightOutlined />
              跳转到源网址
            </a-button>
          </a-space>
        </div>
      </template>
    </div>

    <div flex bg-white px-16 py-20 pt-0 card-shadow mt-20 rounded-2 w-full flex-wrap>
      <div
        flex-shrink-0
        relative
        :class="{
          'w-full md:w-500': !collapse,
          hidden: collapse,
        }"
      >
        <fix-wrapper :offset="0" ref="fixRef">
          <title-dec>型号筛选</title-dec>
          <Select ref="selectRef" :model="select">
            <!-- <a-button -->
            <!--   :loading="loadingPrice" -->
            <!--   v-if="!!company.shopCompanyId && company.tradeVersion >= 2" -->
            <!--   type="primary" -->
            <!--   @click="onPrice" -->
            <!-- > -->
            <!--   <ShoppingOutlined /> -->
            <!--   申请询价 -->
            <!-- </a-button> -->

            <!-- <a-popover placement="rightBottom">
              <template #content>
                <a-menu style="border: 0" :selectedKeys="[]">
                  <a-menu-item key="selection-library" @click="onFav">
                    <template #icon>
                      <InboxOutlined />
                    </template>
                    <span>选型库</span>
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="primary" :loading="loadingFav">添加到</a-button>
            </a-popover> -->

            <a-button v-if="!!favInfo" type="link" @click="onUnFav">
              <StarFilled />
              <span>已收藏</span>
            </a-button>
            <a-button v-else type="link" @click="onFav">
              <StarOutlined />
              <span>收藏</span>
            </a-button>
          </Select>
        </fix-wrapper>
      </div>

      <Right v-if="pageDetail" :model="select" v-model="collapse" ref="rightRef" />
    </div>

    <div class="recommend">
      <div max-w-1440 mx-auto>
        <Recommend max-sm="hidden" />
      </div>
    </div>
    <DownloadModel :select="select" ref="downloadRef"></DownloadModel>
  </div>
</template>

<script lang="ts" setup>
import Model from '~/components/Select/mode'
import Consult from '../components/consult.vue'

import Right from '~/pages/parts/component/right.vue'
import { MemberLevel, userStore } from '~/store/user'
import useDownloadModel from '~/pages/parts/component/useDownloadModel'
// import DownloadModel from './component/DownloadModel.vue'
import { companyStore } from '~/store/company'
import { sideWidgetsStore } from '~/store/sideWidgets'
import Price from '~/pages/parts/component/price.vue'

const route = useRoute()
const partId = computed(() => (route.params.id as string) || '')
const select = ref(new Model(partId.value))
const skuCode = computed(() => select.value.skuCode)
const favInfo = ref(null)

const collapse = ref(false)

const pageDetail = computed(() => select.value.pageDetail)

const showForm = computed(() => !!pageDetail.value?.selectionParameters.length)

const goSource = () => {
  if (!showForm.value) {
    window.open(pageDetail.value?.pageUrl)
    return
  }
  select.value.goSource()
}

watch(skuCode, async (newVal) => {
  if (newVal) {
    if (!!newVal && isLogin.value) {
      favInfo.value = await select.value.getFavInfo()
    } else {
      favInfo.value = null
    }
  } else {
    favInfo.value = null
  }
})

onMounted(() => {
  if (partId.value.includes('misumi')) {
    loadjscssfile('https://www.misumi.com.cn/vcommon/detail/css/style_complex_rev_2402021252.css', 'css')
  }
})

function loadjscssfile(filename, filetype) {
  let fileref
  if (filetype == 'js') {
    //if filename is a external JavaScript file
    fileref = document.createElement('script')
    fileref.setAttribute('type', 'text/javascript')
    fileref.setAttribute('src', filename)
  } else if (filetype == 'css') {
    //if filename is an external CSS file
    fileref = document.createElement('link')
    fileref.setAttribute('rel', 'stylesheet')
    fileref.setAttribute('type', 'text/css')
    fileref.setAttribute('href', filename)
  }
  if (typeof fileref != 'undefined') document.getElementsByTagName('head')[0].appendChild(fileref)
}

const userStoreObj = userStore()
const { user } = storeToRefs(userStoreObj)
const companyStoreObj = companyStore()
const { company } = storeToRefs(companyStoreObj)

const isLogin = computed(() => !!user.value.userId)

const loadingFav = ref(false)
const onFav = async () => {
  const { skuCode, skuId } = select.value
  if (!isLogin.value) {
    loginPop.show(() => {})
    return
  }
  if (user.value.level == MemberLevel.NORMAL) {
    message.warning('当前会员等级无此权限,完善信息可升级会员等级')
    return
  }
  if (!skuCode) {
    message.warning('请先完成选型')
    return
  }
  const merchantId = company.value.shopCompanyId || 0
  loadingFav.value = true

  // 收藏
  const res = await http('/mall/p/my-favorites', {
    method: 'post',
    body: {
      userId: user.value.userId,
      prodName: pageDetail.value?.productName,
      categoryName: pageDetail.value?.category?.categoryName,
      brandName: pageDetail.value?.website?.websiteName || '无',
      model: skuCode,
      partId: partId.value,
      folderId: 0,
    },
  })
  useMall(res, async (ret) => {
    message.success('收藏成功')
    favInfo.value = await select.value.getFavInfo()
  })
  loadingFav.value = false
}

const onUnFav = async () => {
  const { skuCode, skuId } = select.value
  if (!user.value.userId) {
    loginPop.show(() => {})
    return
  }
  const res = await http('/mall/p/my-favorites', {
    method: 'delete',
    query: {
      userId: user.value.userId,
      id: favInfo.value?.id,
    },
  })
  useMall(res, (ret) => {
    message.success('已取消收藏')
    favInfo.value = null
  })
}

const downloadLoding = ref(false)
const downloadRef = ref()
const download = () => {
  useDownloadModel(async () => {
    if (!select.value.isFinished) {
      message.warning('请先完成选型')
      return
    }
    downloadRef.value.showDownload()
  })
}

const { updateAnsCount } = sideWidgetsStore()
const loadingPrice = ref(false)
const onPrice = () => {
  const { skuCode, skuId } = select.value
  if (!user.value.userId) {
    loginPop.show()
    return
  }
  if (!skuCode) {
    message.warning('请先完成选型')
    return
  }
  loadingPrice.value = true
  http('/mall/p/personal-inquiry', {
    method: 'post',
    body: {
      prodName: pageDetail.value?.productName,
      partId: partId.value,
      skuCode,
      number: 1,
      skuId,
      notes: '',
    },
  }).then((res) => {
    loadingPrice.value = false
    useMall(res, (ret) => {
      message.success(`商品已添加到询价器`)
      updateAnsCount()
    })
  })
  // message.info(`该商品已经在询价器中,无法重复添加`)
}

const rightRef = ref()
const fixRef = ref()
useResizeObserver(rightRef, () => {
  fixRef.value?.update()
})

const selectRef = ref()
const afterLogin = () => {
  selectRef.value?.load()
}
</script>

<style lang="less" scoped>
#part-detail-html-container {
  img {
    max-width: 100%;
  }
}

.recommend {
  position: fixed;
  width: 100%;
  left: 0;
  bottom: 0;
  overflow: hidden;
}
</style>
