<template>
    <div>
        <div class="py-20 min-h-screen">
            <div class="w-full max-w-1440 mx-auto h-full">
                <div class="flex gap-30 h-[calc(100vh-450px)]">
                    <!-- 左侧分类树 -->
                    <div class="w-250 bg-white rounded-2 card-shadow p-20 h-full overflow-y-auto">
                        <h3 class="text-18 mb-20 pb-10 border-b border-solid border-[#eee]">产品分类</h3>
                        <a-skeleton v-if="categoryLoading" :paragraph="{ rows: 10 }" active />
                        <ul v-else class="list-none" pl-0>
                            <template v-for="(category, index) in categories" :key="index">
                                <li class="mb-10">
                                    <!-- TRUNK节点样式 -->
                                    <template v-if="category.type === 'TRUNK'">

                                        <a href="javascript:void(0)" :class="[
                                            'flex justify-between items-center text-[#333] no-underline p-10 rounded-4 transition-colors duration-300 hover:bg-[#f5f5f5]'
                                        ]" @click="toggleCategory(category)">
                                            <span font-bold class="truncate w-full" :title="category.categoryName">
                                                <FolderOpenOutlined v-if="category.expanded" mr-4 />
                                                <FolderOutlined v-else mr-4 />{{ category.categoryName }}
                                            </span>
                                        </a>
                                        <!-- 递归渲染子节点 -->
                                        <ul v-show="category.expanded"
                                            v-if="category.children && category.children.length"
                                            :class="['list-none pl-20 mt-5']">
                                            <template v-for="(child, childIndex) in category.children"
                                                :key="childIndex">
                                                <li class="mb-5">
                                                    <template v-if="child.type === 'TRUNK'">
                                                        <a href="javascript:void(0)" :class="[
                                                            'flex justify-between items-center text-[#666] no-underline p-8 rounded-4 transition-colors duration-300 hover:bg-[#f5f5f5]',
                                                            { 'bg-primary text-white': activeCategory === child.key }
                                                        ]" @click="toggleCategory(child)">
                                                            <span font-bold class="truncate w-full"
                                                                :title="child.categoryName">
                                                                <FolderOpenOutlined v-if="child.expanded" mr-4 />
                                                                <FolderOutlined v-else mr-4 />
                                                                {{ child.categoryName }}
                                                            </span>
                                                        </a>
                                                        <ul v-show="child.expanded"
                                                            v-if="child.children && child.children.length"
                                                            :class="['list-none pl-15 mt-5']">
                                                            <li v-for="(grandChild, grandChildIndex) in child.children"
                                                                :key="grandChildIndex" class="mb-5">
                                                                <a href="javascript:void(0)" :class="[
                                                                    'block text-[#666] no-underline p-8 rounded-4 transition-colors duration-300 hover:bg-[#f5f5f5]',
                                                                    { 'text-primary font-bold': activeSubcategory === grandChild.key }
                                                                ]" @click="selectSubcategory(grandChild)">
                                                                    <span class="truncate block">{{
                                                                        grandChild.categoryName }}</span>
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </template>
                                                    <!-- LEAF节点样式 -->
                                                    <template v-else>
                                                        <a href="javascript:void(0)" :class="[
                                                            'block text-[#666] no-underline p-8 rounded-4 transition-colors duration-300 hover:bg-[#f5f5f5]',
                                                            { 'text-primary font-bold': activeSubcategory === child.key }
                                                        ]" @click="selectSubcategory(child)">
                                                            <span class="truncate block" :title="child.categoryName">
                                                                {{ child.categoryName }}
                                                            </span>
                                                        </a>
                                                    </template>
                                                </li>
                                            </template>
                                        </ul>
                                    </template>
                                    <!-- 顶层LEAF节点样式 -->
                                    <template v-else>
                                        <a href="javascript:void(0)" :class="[
                                            'block text-[#666] no-underline p-8 rounded-4 transition-colors duration-300 hover:bg-[#f5f5f5]',
                                            { 'text-primary font-bold': activeSubcategory === category.key }
                                        ]" @click="selectSubcategory(category)">
                                            <span class="truncate block" :title="category.categoryName">
                                                {{ category.categoryName }}
                                            </span>
                                        </a>
                                    </template>
                                </li>
                            </template>
                        </ul>
                    </div>

                    <!-- 右侧产品内容 -->
                    <div class="flex-1">
                        <!-- <div
                            class="flex justify-between items-center bg-white p-15 px-20 rounded-2 mb-20 shadow-[0_2px_10px_rgba(0,0,0,0.05)]">
                            <div class="flex gap-15">
                                <div v-for="(filter, index) in filters" :key="index" :class="[
                                    'cursor-pointer px-10 py-5 rounded-4 transition-all duration-300 hover:text-primary',
                                    { 'bg-primary text-white': activeFilter === filter.key }
                                ]" @click="setActiveFilter(filter.key)">
                                    {{ filter.name }}
                                </div>
                            </div>

                            <div class="flex items-center gap-10">
                                <span>排序：</span>
                                <a-select v-model:value="sortOption" style="width: 120px" @change="handleSortChange">
                                    <a-select-option v-for="option in sortOptions" :key="option.value"
                                        :value="option.value">
                                        {{ option.label }}
                                    </a-select-option>
                                </a-select>

                                <div class="flex gap-10 ml-15">
                                    <div :class="['cursor-pointer text-[#777] p-5', { 'text-primary': viewMode === 'grid' }]"
                                        @click="viewMode = 'grid'">
                                        <i class="fas fa-th-large"></i>
                                    </div>
                                    <div :class="['cursor-pointer text-[#777] p-5', { 'text-primary': viewMode === 'list' }]"
                                        @click="viewMode = 'list'">
                                        <i class="fas fa-list"></i>
                                    </div>
                                </div>
                            </div>
                        </div> -->

                        <card-skeleton v-if="loading || categoryLoading" :count="8"></card-skeleton>

                        <div v-else-if="products && products.length > 0"
                            class="grid grid-cols-[repeat(auto-fill,minmax(250px,1fr))] gap-20 mb-30">
                            <search-item search-type="keyword" :info="item" v-for="item in products" type="card"
                                :key="item.id" :shop-id="shopId" @click="goPart(item)"></search-item>
                        </div>

                        <div v-else class="flex justify-center items-center py-50">
                            <a-empty description="暂无数据" />
                        </div>

                        <!-- 分页 -->
                        <a-pagination v-if="products && products.length > 0" v-model:current="currentPage"
                            :total="total" :pageSize="pageSize" @change="handlePageChange"
                            class="flex justify-center mt-30" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const shopId = route.params.brandId
const urlCategoryId = route.query.categoryId // 获取URL中的categoryId参数

// 分类数据
const categories = ref([])
const categoryLoading = ref(true)

// 筛选和排序
const filters = ref([
    { name: '全部', key: 'all' },
    { name: '新品', key: 'new' },
    { name: '热销', key: 'hot' },
    { name: '促销', key: 'sale' }
])

const sortOptions = ref([
    { label: '默认', value: 'default' },
    { label: '价格从低到高', value: 'price_asc' },
    { label: '价格从高到低', value: 'price_desc' },
    { label: '销量优先', value: 'sales' },
    { label: '好评优先', value: 'rating' }
])

// 状态变量
const activeCategory = ref('')
const activeSubcategory = ref('')
const activeFilter = ref('all')
const sortOption = ref('default')
const viewMode = ref('grid')
const currentPage = ref(1)
const pageSize = ref(12)
const total = ref(0)
const loading = ref(false)
const products = ref([])

// 计算当前子分类名称
const currentSubcategoryName = computed(() => {
    const category = categories.value.find(c => c.key === activeCategory.value)
    if (!category) return ''

    const subcategory = category.children.find(s => s.key === activeSubcategory.value)
    return subcategory ? subcategory.name : ''
})

// 切换分类展开/折叠
const toggleCategory = (category) => {
    if (category.type === 'TRUNK') {
        category.expanded = !category.expanded
        // activeCategory.value = category.key
    }
}

// 选择分类
const selectSubcategory = (category) => {
    if (category.type === 'LEAF') {
        activeSubcategory.value = category.key
        fetchProducts()
    }
}

// 设置筛选条件
const setActiveFilter = (filterKey) => {
    activeFilter.value = filterKey
    fetchProducts()
}

// 处理排序变化
const handleSortChange = (value) => {
    sortOption.value = value
    fetchProducts()
}

// 处理分页变化
const handlePageChange = (page) => {
    currentPage.value = page
    fetchProducts()
}

// 跳转到产品详情
const goToProduct = (product) => {
    router.push({
        path: `/brand/${shopId}/${product.id}`
    })
}

// 根据categoryId查找并设置对应的分类
const findAndSelectCategory = (categoryId) => {
    if (!categoryId) return false

    // 递归查找分类
    const findCategoryPath = (categories, targetId, path = []) => {
        for (const category of categories) {
            const currentPath = [...path, category]

            if (category.key.toString() === targetId.toString()) {
                return currentPath
            }

            if (category.children && category.children.length) {
                const result = findCategoryPath(category.children, targetId, currentPath)
                if (result) return result
            }
        }

        return null
    }

    const categoryPath = findCategoryPath(categories.value, categoryId)

    if (categoryPath) {
        // 找到了匹配的分类，设置激活状态
        const parentCategory = categoryPath[0]
        const leafCategory = categoryPath[categoryPath.length - 1]

        // 设置父分类
        activeCategory.value = parentCategory.key

        // 确保所有父分类都展开
        categoryPath.forEach(cat => {
            if (cat.type === 'TRUNK') {
                cat.expanded = true
            }
        })

        // 如果是叶子节点，设置为活跃的子分类
        if (leafCategory.type === 'LEAF') {
            activeSubcategory.value = leafCategory.key
        } else if (leafCategory.children && leafCategory.children.length) {
            // 如果不是叶子节点但有子节点，选择第一个子节点
            activeSubcategory.value = leafCategory.children[0].key
        }

        return true
    }

    return false
}

// 递归获取子分类
const getCategories = async (parentId) => {
    const res = await http('/mall/category/categoryInfo', {
        query: {
            shopId,
            parentId,
        },
    })

    if (res.success && res.data.categoryInfo) {
        const list = res.data.categoryInfo
        const result = []

        for (const item of list) {
            const category = {
                ...item,
                key: item.categoryId,
                expanded: true,
                children: []
            }

            if (item.type !== 'LEAF') {
                const children = await getCategories(item.categoryId)
                category.children = children
            }

            result.push(category)
        }

        return result
    }

    return []
}

// 获取分类第一层列表并递归获取子分类
const getCategoryInfo = async () => {
    categoryLoading.value = true
    loading.value = true

    try {
        const res = await http('/mall/category/categoryInfo', {
            method: 'get',
            query: {
                shopId,
                parentId: 0,
            },
        })
        useMall(res, async () => {
            const list = res.data.categoryInfo || []
            const result = []

            for (const item of list) {
                const category = {
                    ...item,
                    key: item.categoryId,
                    expanded: true,
                    children: []
                }

                if (item.type !== 'LEAF') {
                    const children = await getCategories(item.categoryId)
                    category.children = children
                }

                result.push(category)
            }

            categories.value = result

            // 检查URL中是否有categoryId，如果有则选中对应分类
            if (urlCategoryId) {
                const found = findAndSelectCategory(urlCategoryId)

                // 如果找到并选中了分类，获取对应产品
                if (found) {
                    fetchProducts()
                }
            } else {
                // 如果没有URL参数，使用默认选择逻辑
                if (categories.value?.length) {
                    const firstCategory = categories.value[0]
                    activeCategory.value = firstCategory.key

                    if (firstCategory.children.length) {
                        activeSubcategory.value = firstCategory.children[0].key
                    }

                    fetchProducts()
                }
            }

            // 所有递归操作完成后再设置loading状态
            categoryLoading.value = false
        })
    } catch (error) {
        console.error('获取分类失败:', error)
        categories.value = []
        categoryLoading.value = false
    } finally {
        if (loading.value) {
            loading.value = false
        }
    }
}

// 获取产品数据
const fetchProducts = async () => {
    if (!activeSubcategory.value) return

    loading.value = true
    try {
        const [err, res] = await try_http('/api/selection/search-by-keyword', {
            method: 'post',
            body: {
                websiteCode: shopId,
                shopCategoryCode: activeSubcategory.value,
                pageNo: currentPage.value,
                pageSize: pageSize.value,
            },
        })

        if (!err) {
            products.value = res.data.data || []
            // totalItems.value = res.data.totalCount
        } else {
            products.value = []
            total.value = 0
        }
    } catch (error) {
        console.error('获取产品列表失败:', error)
        products.value = []
        total.value = 0
    } finally {
        loading.value = false
    }
}

// 跳转产品详情
const goPart = (item) => {
    router.push({
        path: `/brand/${shopId}/${item.id}`
    })
}

// 监听路由变化
watch(() => route.params.brandId, (newId) => {
    if (newId) {
        shopId.value = newId
        getCategoryInfo()
    }
})

// 监听路由中的categoryId变化
watch(() => route.query.categoryId, (newCategoryId) => {
    if (categories.value.length > 0) {
        if (newCategoryId) {
            const found = findAndSelectCategory(newCategoryId)
            if (found) {
                fetchProducts()
            }
        }
    }
})

// 页面加载时获取数据
onMounted(() => {
    getCategoryInfo()
})
</script>