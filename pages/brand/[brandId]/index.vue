<template>
  <div py-20>
    <div v-if="swiperList.length > 0" w-full h-400 relative>
      <Swiper :list="swiperList" prop="imgUrl" @click-item="handleClickSwiper"></Swiper>
      <!-- <img w-full h-full src="@/assets/images/seidal_bg_temp.jpg" object-scale-cover /> -->
    </div>
    <div class="flex py-25 items-center">
      <div w-5 h-20 bg-primary rounded-2 mr-12></div>
      <div class="text-20 font-bold">最新上架</div>
      <div class="flex-1"></div>
      <div class="text-#666 cursor-pointer" @click="goProduct">更多 > </div>
    </div>
    <card-skeleton v-if="loading" :count="10"></card-skeleton>
    <div class="grid grid-cols-[repeat(auto-fill,minmax(240px,1fr))] gap-25">
      <search-item search-type="keyword" :info="item" v-for="item in products" type="card" :key="item.id"
        :shop-id="shopId" @click="goPart(item)"></search-item>
    </div>
  </div>
</template>

<script setup>
const route = useRoute()
const shopId = route.params.brandId
// 获取产品列表
const products = ref([])
const loading = ref(false)
const getProducts = async () => {
  loading.value = true
  const res = await http('/api/selection/part-search', {
    method: 'post',
    body: {
      websiteCode: shopId,
      pageNo: 1,
      pageSize: 10,
    },
  })
  loading.value = false
  products.value = res.data.BY_KEYWORD?.data ?? []
  console.log('%c Line:70 🥛 res', 'color:#FCE877', res)
}

getProducts()

const router = useRouter()
const isMetaPressed = useMeta()
const goPart = (item) => {
  const path = router.resolve({
    path: `/brand/${shopId}/${item.id}`,
  })

  if (isMetaPressed.value) {
    isMetaPressed.value = false
    window.open(path.href)
  } else {
    router.push({
      ...path,
    })
  }
}

const goChat = () => {
  const yanxuan = useRuntimeConfig().public.baseUrl.VITE_YANXUAN
  window.open(`${yanxuan}/chat?shop_id=${shopId}`)
}

const swiperList = ref([])

const goProduct = () => {
  router.push({
    path: `/brand/${shopId}/product`,
  })
}

onMounted(() => {
  loadSwiper()
})

const loadSwiper = async () => {
  const [err, res] = await useCacheFetch(`swiper-${shopId}`, () =>
    try_http('/mall/shop-decoration/carousel/list', {
      query: {
        shopId: shopId,
        satus: 1,
        sort: 'seq',
      },
    })
  )
  if (!err) {
    console.log('res', res)
    swiperList.value = res.data
  }
}

const handleClickSwiper = (item) => {
  if (item.type == 1) {
    navigateTo({
      path: `/brand/${shopId}/mall-${item.subjectId}`,
      query: {
        _t: new Date().getTime(),
      },
    })
  }
}
</script>
