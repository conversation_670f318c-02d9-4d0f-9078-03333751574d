<template>
    <div class="py-20 min-h-screen">

        <div class="w-full max-w-1440 mx-auto">
            <!-- 搜索结果头部 -->
            <div class="bg-white rounded-2 card-shadow p-20 mb-20">
                <div class="flex items-center mb-15">
                    <div class="w-4 h-20 bg-primary rounded-2 mr-10"></div>
                    <span class="text-20 font-bold">搜索结果</span>
                </div>

                <!-- 搜索过滤器 -->
                <div class="flex mt-20 border-b border-solid border-[#eee]">
                    <div v-for="tab in tabs" :key="tab.key" class="px-15 py-10 cursor-pointer relative mr-20"
                        :class="activeTab === tab.key ? 'text-primary font-bold' : 'text-[#666] hover:text-primary'"
                        @click="switchTab(tab.key)">
                        {{ tab.name }} ({{ tab.count }})
                        <div v-if="activeTab === tab.key" class="absolute bottom-0 left-0 w-full h-2 bg-primary">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 产品搜索结果 -->
            <div v-if="activeTab === 'products'" class="bg-white rounded-2 card-shadow p-20 mb-20">
                <div class="flex justify-between items-center mb-20">
                    <h3 class="text-18 font-bold flex items-center">
                        <i class="fas fa-box-open mr-8 text-primary"></i>
                        产品搜索结果
                    </h3>

                    <!-- 产品筛选器 -->
                    <!-- <div class="flex items-center">
                        <div class="mr-15">
                            <a-select v-model:value="productFilter.category" style="width: 120px" placeholder="产品分类">
                                <a-select-option value="">全部分类</a-select-option>
                                <a-select-option v-for="cat in productCategories" :key="cat.id" :value="cat.id">
                                    {{ cat.name }}
                                </a-select-option>
                            </a-select>
                        </div>
                        <div class="mr-15">
                            <a-select v-model:value="productFilter.sort" style="width: 120px" placeholder="排序方式">
                                <a-select-option value="relevance">相关度</a-select-option>
                                <a-select-option value="newest">最新上架</a-select-option>
                                <a-select-option value="price_asc">价格从低到高</a-select-option>
                                <a-select-option value="price_desc">价格从高到低</a-select-option>
                            </a-select>
                        </div>
                        <a-button type="primary" @click="applyProductFilter">筛选</a-button>
                    </div> -->
                </div>

                <!-- 产品列表 -->
                <div v-if="loading.products" class="py-30 text-center">
                    <a-spin />
                </div>

                <div v-else-if="products.length === 0" class="py-30 text-center text-[#999]">
                    暂无相关产品
                </div>

                <div v-else class="grid grid-cols-[repeat(auto-fill,minmax(240px,1fr))] gap-20">
                    <div v-for="product in products" :key="product.id"
                        class="bg-white rounded-2 overflow-hidden transition-all duration-300 hover:translate-y-[-5px] hover:shadow-md cursor-pointer border border-solid border-[#eee]"
                        @click="goToProduct(product)">
                        <div class="w-full h-200 flex justify-center items-center overflow-hidden">
                            <img v-if="product.imageUrl" :src="product.imageUrl" :alt="product.productName"
                                class="w-full h-full object-contain">
                            <i v-else class="fas fa-box text-[#ddd] text-40"></i>
                        </div>
                        <div class="p-15">
                            <h4 class="text-16 font-bold mb-10 line-clamp-2 h-48">{{ product.productName }}</h4>
                            <div class="text-primary text-18 font-bold mb-8">¥{{ product.price }}</div>
                            <!-- <div class="flex justify-between text-[#999] text-12">
                                <span><i class="fas fa-shopping-cart mr-5"></i>{{ formatNumber(product.salesCount || 0)
                                }}</span>
                                <span><i class="fas fa-star mr-5"></i>{{ product.rating || '4.5' }}</span>
                            </div> -->
                        </div>
                    </div>
                </div>

                <!-- 产品分页 -->
                <div class="flex justify-center mt-30" v-if="products.length > 0">
                    <a-pagination v-model:current="pagination.products.current" :total="pagination.products.total"
                        :pageSize="pagination.products.pageSize"
                        @change="(page) => handlePageChange('products', page)" />
                </div>
            </div>

            <!-- 资源搜索结果 -->
            <div v-if="activeTab === 'resources'" class="bg-white rounded-2 card-shadow p-20 mb-20">
                <div class="flex justify-between items-center mb-20">
                    <h3 class="text-18 font-bold flex items-center">
                        <i class="fas fa-file-alt mr-8 text-primary"></i>
                        资源搜索结果
                    </h3>

                    <!-- 资源筛选器 -->
                    <!-- <div class="flex items-center">
                        <div class="mr-15">
                            <a-select v-model:value="resourceFilter.type" style="width: 120px" placeholder="资源类型">
                                <a-select-option value="">全部类型</a-select-option>
                                <a-select-option v-for="type in resourceTypes" :key="type.id" :value="type.id">
                                    {{ type.name }}
                                </a-select-option>
                            </a-select>
                        </div>
                        <div class="mr-15">
                            <a-select v-model:value="resourceFilter.sort" style="width: 120px" placeholder="排序方式">
                                <a-select-option value="relevance">相关度</a-select-option>
                                <a-select-option value="newest">最新发布</a-select-option>
                                <a-select-option value="downloads">下载量</a-select-option>
                            </a-select>
                        </div>
                        <a-button type="primary" @click="applyResourceFilter">筛选</a-button>
                    </div> -->
                </div>

                <!-- 资源列表 -->
                <div v-if="loading.resources" class="py-30 text-center">
                    <a-spin />
                </div>

                <div v-else-if="resources.length === 0" class="py-30 text-center text-[#999]">
                    暂无相关资源
                </div>

                <div v-else class="flex flex-col gap-15">
                    <div v-for="resource in resources" :key="resource.id"
                        class="flex p-15 bg-[#f9f9f9] rounded-2 transition-all duration-300">
                        <div class="w-60 h-60 bg-[#eee] rounded-2 flex-shrink-0 flex justify-center items-center mr-15">
                            <img :src="getFileIcon(resource)" class="w-full h-full" object-fit="cover">
                        </div>
                        <div class="flex-1">
                            <h4 class="text-16 font-bold mb-8 line-clamp-1">{{ resource.name }}</h4>
                            <p class="text-14 text-[#666] mb-8 line-clamp-2">{{ resource.description }}</p>
                            <div class="flex text-[#999] text-12">
                                <!-- <span class="mr-15"><i class="far fa-calendar-alt mr-5"></i>{{
                                    formatDate(resource.publishDate) }}</span> -->
                                <!-- <span class="mr-15"><i class="fas fa-download mr-5"></i>{{
                                    formatNumber(resource.downloads) }} 下载</span> -->
                                <span class="mr-15"><i class="far fa-file mr-5"></i>{{ resource.fileSize }}</span>
                                <span><i class="fas fa-tag mr-5"></i>{{ resource.typeName }}</span>
                            </div>
                        </div>
                        <div class="flex-shrink-0 ml-15 flex justify-center items-center">
                            <a-button type="link" size="small" @click.stop="downloadResource(resource)">
                                下载
                            </a-button>
                        </div>
                    </div>
                </div>

                <!-- 资源分页 -->
                <div class="flex justify-center mt-30" v-if="resources.length > 0">
                    <a-pagination v-model:current="pagination.resources.current" :total="pagination.resources.total"
                        :pageSize="pagination.resources.pageSize"
                        @change="(page) => handlePageChange('resources', page)" />
                </div>
            </div>

            <!-- 新闻搜索结果 -->
            <div v-if="activeTab === 'news'" class="bg-white rounded-2 card-shadow p-20 mb-20">
                <div class="flex justify-between items-center mb-20">
                    <h3 class="text-18 font-bold flex items-center">
                        <i class="fas fa-newspaper mr-8 text-primary"></i>
                        新闻搜索结果
                    </h3>

                    <!-- 新闻筛选器 -->
                    <!-- <div class="flex items-center">
                        <div class="mr-15">
                            <a-select v-model:value="newsFilter.category" style="width: 120px" placeholder="新闻分类">
                                <a-select-option value="">全部分类</a-select-option>
                                <a-select-option v-for="cat in newsCategories" :key="cat.id" :value="cat.id">
                                    {{ cat.name }}
                                </a-select-option>
                            </a-select>
                        </div>
                        <div class="mr-15">
                            <a-select v-model:value="newsFilter.sort" style="width: 120px" placeholder="排序方式">
                                <a-select-option value="relevance">相关度</a-select-option>
                                <a-select-option value="newest">最新发布</a-select-option>
                                <a-select-option value="views">浏览量</a-select-option>
                            </a-select>
                        </div>
                        <a-button type="primary" @click="applyNewsFilter">筛选</a-button>
                    </div> -->
                </div>

                <!-- 新闻列表 -->
                <div v-if="loading.news" class="py-30 text-center">
                    <a-spin />
                </div>

                <div v-else-if="news.length === 0" class="py-30 text-center text-[#999]">
                    暂无相关新闻
                </div>

                <div v-else class="flex flex-col gap-15">
                    <div v-for="item in news" :key="item.id"
                        class="flex gap-20 p-15 bg-[#f9f9f9] rounded-10 transition-all duration-300 hover:bg-[#f5f5f5] cursor-pointer"
                        @click="viewNewsDetail(item)">
                        <div
                            class="w-200 h-120 bg-[#eee] rounded-5 flex-shrink-0 flex justify-center items-center overflow-hidden">
                            <img v-if="item.articleCover" :src="useImage(convertImage(item.articleCover))"
                                :alt="item.title" class="w-full h-full object-cover">
                            <i v-else class="fas fa-newspaper text-[#999] text-40"></i>
                        </div>
                        <div class="flex-1 flex flex-col justify-between">
                            <div>
                                <h4 class="text-18 font-bold mb-10 line-clamp-1">{{ item.title }}</h4>
                                <p class="text-14 text-[#666] leading-normal line-clamp-2">{{ item.subTitle }}</p>
                            </div>
                            <div class="flex justify-between mt-15 text-[#999] text-12">
                                <span><i class="far fa-clock mr-5"></i>{{ formatDate(item.publishTime) }}</span>
                                <!-- <span><i class="far fa-eye mr-5"></i>{{ formatNumber(item.viewCount) }} 浏览</span> -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 新闻分页 -->
                <div class="flex justify-center mt-30" v-if="news.length > 0">
                    <a-pagination v-model:current="pagination.news.current" :total="pagination.news.total"
                        :pageSize="pagination.news.pageSize" @change="(page) => handlePageChange('news', page)" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import unknownIcon from '~/assets/images/file_icon/unknown.png'
import pdfIcon from '~/assets/images/file_icon/pdf.png'
import wordIcon from '~/assets/images/file_icon/word.png'
import excelIcon from '~/assets/images/file_icon/excel.png'
import pptIcon from '~/assets/images/file_icon/ppt.png'
import imageIcon from '~/assets/images/file_icon/image.png'

const route = useRoute()
const router = useRouter()
const shopId = computed(() => route.params.brandId)
const searchKeyword = ref('')

// 热门搜索标签
const hotSearchTags = ref(['电机', '传感器', '控制器', '工业机器人', '自动化设备', '伺服电机'])

// 搜索历史
const SEARCH_HISTORY_KEY = 'brand_search_history'
const searchHistory = ref([])

// 加载搜索历史
const loadSearchHistory = () => {
    try {
        const history = localStorage.getItem(SEARCH_HISTORY_KEY)
        if (history) {
            searchHistory.value = JSON.parse(history)
        }
    } catch (error) {
        console.error('加载搜索历史失败:', error)
        searchHistory.value = []
    }
}

// 保存搜索历史
const saveSearchHistory = (keyword) => {
    if (!keyword || !keyword.trim()) return

    // 移除已存在的相同关键词
    const index = searchHistory.value.findIndex(item => item === keyword)
    if (index !== -1) {
        searchHistory.value.splice(index, 1)
    }

    // 添加到历史记录开头
    searchHistory.value.unshift(keyword)

    // 限制历史记录数量
    if (searchHistory.value.length > 10) {
        searchHistory.value = searchHistory.value.slice(0, 10)
    }

    // 保存到本地存储
    try {
        localStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(searchHistory.value))
    } catch (error) {
        console.error('保存搜索历史失败:', error)
    }
}

const getFileIcon = (file) => {
    if (!file?.name) return unknownIcon

    const ext = file.name.split('.').pop()?.toLowerCase()
    const iconMap = {
        'pdf': pdfIcon,
        'doc': wordIcon,
        'docx': wordIcon,
        'xls': excelIcon,
        'xlsx': excelIcon,
        'ppt': pptIcon,
        'pptx': pptIcon,
        'jpg': imageIcon,
        'jpeg': imageIcon,
        'png': imageIcon,
        'gif': imageIcon
    }

    return iconMap[ext] || unknownIcon
}


// 移除搜索历史
const removeHistory = (index) => {
    searchHistory.value.splice(index, 1)
    localStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(searchHistory.value))
}

// 清空搜索历史
const clearHistory = () => {
    searchHistory.value = []
    localStorage.removeItem(SEARCH_HISTORY_KEY)
}

// 快速搜索
const quickSearch = (keyword) => {
    searchKeyword.value = keyword
    handleSearch(keyword)
}

// 处理搜索
const handleSearch = (value) => {
    if (!value || !value.trim()) {
        message.warning('请输入搜索关键词')
        return
    }

    // 保存到搜索历史
    saveSearchHistory(value)

    // 更新URL参数但不重新加载页面
    router.replace({
        path: `/brand/${shopId.value}/search`,
        query: {
            ...route.query,
            keyword: value.trim()
        }
    })

    // 执行搜索
    initSearch()
}

// 标签页配置
const tabs = ref([
    { key: 'products', name: '产品', count: 0 },
    { key: 'resources', name: '资源', count: 0 },
    { key: 'news', name: '新闻', count: 0 }
])
const activeTab = ref('products')

// 加载状态
const loading = ref({
    products: false,
    resources: false,
    news: false
})

// 分页配置
const pagination = ref({
    products: { current: 1, total: 0, pageSize: 10 },
    resources: { current: 1, total: 0, pageSize: 10 },
    news: { current: 1, total: 0, pageSize: 5 }
})

// 筛选器配置
const productFilter = ref({ category: '', sort: 'relevance' })
const resourceFilter = ref({ type: '', sort: 'relevance' })
const newsFilter = ref({ category: '', sort: 'relevance' })

// 分类数据
const productCategories = ref([
    { id: 'electronics', name: '电子产品' },
    { id: 'mechanical', name: '机械设备' },
    { id: 'automation', name: '自动化控制' },
    { id: 'tools', name: '工具配件' }
])

const resourceTypes = ref([
    { id: 'manual', name: '产品手册' },
    { id: 'datasheet', name: '技术规格书' },
    { id: 'software', name: '软件驱动' },
    { id: 'cad', name: 'CAD图纸' },
    { id: 'video', name: '教学视频' }
])

const newsCategories = ref([
    { id: 'company', name: '公司动态' },
    { id: 'product', name: '产品资讯' },
    { id: 'industry', name: '行业新闻' },
    { id: 'tech', name: '技术前沿' },
    { id: 'event', name: '活动公告' }
])

// 数据列表
const products = ref([])
const resources = ref([])
const news = ref([])

// 计算总结果数
const totalResults = computed(() => {
    return tabs.value.reduce((sum, tab) => sum + tab.count, 0)
})

// 切换标签页
const switchTab = (tabKey) => {
    activeTab.value = tabKey
    // 如果该标签页数据为空，则加载数据
    if ((tabKey === 'products' && products.value.length === 0) ||
        (tabKey === 'resources' && resources.value.length === 0) ||
        (tabKey === 'news' && news.value.length === 0)) {
        fetchData(tabKey)
    }
}

// 应用产品筛选
const applyProductFilter = () => {
    pagination.value.products.current = 1
    fetchData('products')
}

// 应用资源筛选
const applyResourceFilter = () => {
    pagination.value.resources.current = 1
    fetchData('resources')
}

// 应用新闻筛选
const applyNewsFilter = () => {
    pagination.value.news.current = 1
    fetchData('news')
}

// 处理分页变化
const handlePageChange = (type, page) => {
    pagination.value[type].current = page
    fetchData(type)
}

// 获取资源图标
const getResourceIcon = (type) => {
    const iconMap = {
        'manual': 'fas fa-book',
        'datasheet': 'fas fa-file-alt',
        'software': 'fas fa-download',
        'cad': 'fas fa-drafting-compass',
        'video': 'fas fa-video'
    }
    return iconMap[type] || 'fas fa-file'
}

// 跳转到产品详情
const goToProduct = (product) => {
    router.push({
        path: `/brand/${shopId.value}/${product.id}`
    })
}

// 查看资源详情
const viewResource = (resource) => {
    router.push({
        path: `/brand/${shopId.value}/resource/${resource.id}`,
        query: { name: resource.name }
    })
}

// 下载资源
const downloadResource = (resource) => {
    window.open(resource.url, '_blank')
}

// 查看新闻详情
const viewNewsDetail = (news) => {
    router.push({
        path: `/brand/${shopId.value}/news/${news.id}`,
        query: { title: news.title }
    })
}

// 格式化日期
const formatDate = (dateStr) => {
    return dateStr
}

// 格式化数字（添加千位分隔符）
const formatNumber = (num) => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
}

// 获取产品数据
const fetchProductData = async () => {
    loading.value.products = true

    try {
        const res = await http('/api/selection/search-by-keyword', {
            method: 'post',
            body: {
                websiteCode: shopId.value,
                keyword: searchKeyword.value,
                pageNo: pagination.value.products.current,
                pageSize: pagination.value.products.pageSize,
            },
        })

        if (res.code === 'ok') {
            products.value = res.data.data || []
            pagination.value.products.total = res.data.totalCount || 0
            tabs.value[0].count = res.data.totalCount || 0
        } else {
            // 如果API调用失败或返回空数据，使用模拟数据
            fallbackToMockData('products')
        }
    } catch (error) {
        console.error('产品搜索API调用失败:', error)
        fallbackToMockData('products')
    } finally {
        loading.value.products = false
    }
}

// 获取资源数据
const fetchResourceData = async () => {
    loading.value.resources = true

    try {
        const res = await http('/mall/prod/res-file/search', {
            method: 'get',
            params: {
                shopId: shopId.value,
                keyword: searchKeyword.value,
                current: pagination.value.resources.current,
                size: pagination.value.resources.pageSize,
            },
        })

        if (res.data) {
            resources.value = res.data.records.map(item => {
                // 将字节转换为合适的单位
                let fileSize = '未知大小'
                if (item.length) {
                    const bytes = item.length
                    if (bytes < 1024) {
                        fileSize = `${bytes}B`
                    } else if (bytes < 1024 * 1024) {
                        fileSize = `${(bytes / 1024).toFixed(2)}KB`
                    } else if (bytes < 1024 * 1024 * 1024) {
                        fileSize = `${(bytes / (1024 * 1024)).toFixed(2)}MB`
                    } else {
                        fileSize = `${(bytes / (1024 * 1024 * 1024)).toFixed(2)}GB`
                    }
                }

                return {
                    ...item,
                    fileSize
                }
            }) || []
            pagination.value.resources.total = res.data.total || 0
            tabs.value[1].count = res.data.total || 0
        } else {
            fallbackToMockData('resources')
        }
    } catch (error) {
        console.error('资源搜索API调用失败:', error)
        fallbackToMockData('resources')
    } finally {
        loading.value.resources = false
    }
}

// 获取新闻数据
const fetchNewsData = async () => {
    loading.value.news = true

    try {
        const res = await http('/mall/article/page-search', {
            method: 'get',
            query: {
                shopId: shopId.value,
                keyword: searchKeyword.value,
                current: pagination.value.news.current,
                size: pagination.value.news.pageSize,
            },
        })

        if (res.data) {
            news.value = res.data.records || []
            pagination.value.news.total = res.data.total || 0
            tabs.value[2].count = res.data.total || 0
        } else {
            fallbackToMockData('news')
        }
    } catch (error) {
        console.error('新闻搜索API调用失败:', error)
        fallbackToMockData('news')
    } finally {
        loading.value.news = false
    }
}

// 根据类型获取数据
const fetchData = (type) => {
    if (type === 'products') {
        fetchProductData()
    } else if (type === 'resources') {
        fetchResourceData()
    } else if (type === 'news') {
        fetchNewsData()
    }
}

// 使用模拟数据作为后备
const fallbackToMockData = (type) => {
    if (type === 'products') {
        // 模拟产品数据
        const mockProducts = Array(20).fill().map((_, i) => ({
            id: `p${i + 1}`,
            name: `搜索结果产品 ${i + 1} - ${searchKeyword.value}`,
            price: Math.floor(Math.random() * 10000) + 999,
            salesCount: Math.floor(Math.random() * 1000),
            rating: (Math.random() * 1 + 4).toFixed(1),
            imgUrl: ''
        }))

        products.value = mockProducts.slice(0, 12)
        pagination.products.total = mockProducts.length
        tabs.value[0].count = mockProducts.length
    }
    else if (type === 'resources') {
        // 模拟资源数据
        const mockResources = Array(15).fill().map((_, i) => ({
            id: `r${i + 1}`,
            name: `${searchKeyword.value} 相关资源文档 ${i + 1}`,
            description: `这是关于${searchKeyword.value}的详细资源文档，包含技术规格、使用说明和常见问题解答等内容。`,
            type: resourceTypes.value[i % resourceTypes.value.length].id,
            typeName: resourceTypes.value[i % resourceTypes.value.length].name,
            publishDate: `2025-0${Math.floor(Math.random() * 9) + 1}-${Math.floor(Math.random() * 28) + 1}`,
            downloads: Math.floor(Math.random() * 5000),
            fileSize: `${Math.floor(Math.random() * 100) + 1}MB`
        }))

        resources.value = mockResources.slice(0, 10)
        pagination.resources.total = mockResources.length
        tabs.value[1].count = mockResources.length
    }
    else if (type === 'news') {
        // 模拟新闻数据
        const mockNews = Array(12).fill().map((_, i) => ({
            id: `n${i + 1}`,
            title: `${searchKeyword.value}相关新闻 ${i + 1}: 行业最新动态与发展趋势`,
            description: `这是一篇关于${searchKeyword.value}的详细新闻报道，内容涵盖了最新的技术进展、市场动态以及未来发展趋势的分析。本文由行业专家撰写，提供了深入的见解和专业的观点。`,
            publishDate: `2025-0${Math.floor(Math.random() * 9) + 1}-${Math.floor(Math.random() * 28) + 1}`,
            viewCount: Math.floor(Math.random() * 10000),
            categoryId: newsCategories.value[i % newsCategories.value.length].id,
            imgUrl: ''
        }))

        news.value = mockNews.slice(0, 5)
        pagination.news.total = mockNews.length
        tabs.value[2].count = mockNews.length
    }
}

// 初始化搜索
const initSearch = () => {
    searchKeyword.value = route.query.keyword || ''

    if (!searchKeyword.value) {
        return
    }
    // 重置分页
    pagination.value.products.current = 1
    pagination.value.resources.current = 1
    pagination.value.news.current = 1

    // 默认加载产品数据
    fetchProductData()
}

// 监听路由变化
watch(() => [route.params.brandId, route.query.keyword], () => {
    initSearch()
})

// 页面加载时初始化
onMounted(() => {
    loadSearchHistory()
    initSearch()
})
</script>

<style scoped>
.card-shadow {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* 确保图标库已引入 */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');
</style>