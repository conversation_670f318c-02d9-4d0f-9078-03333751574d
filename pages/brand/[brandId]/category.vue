<template>
  <div>
    <div class="flex h-50 items-center">
      <div w-8 h-32 bg-primary mr-12></div>
      <div class="text-20 font-bold">{{ categoryName }}</div>
      <div class="flex-1"></div>
      <!-- <div class="text-#333">更多></div> -->
    </div>
    <div class="pb-20px">
      <card-skeleton v-if="loading" :count="10"></card-skeleton>
      <div class="grid grid-cols-[repeat(auto-fill,minmax(240px,1fr))] gap-20" v-else>
        <search-item
          search-type="keyword"
          :info="item"
          v-for="item in products"
          type="card"
          :key="item.id"
          :shop-id="shopId"
          @click="goPart(item)"
        ></search-item>
      </div>
      <div class="text-right mt-20px">
        <a-pagination
          v-if="!loading"
          :current="currentPage"
          :total="totalItems"
          :page-size="pageSize"
          @change="handlePageChange"
          class="pagination"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
const route = useRoute()

const categoryName = computed(() => route.query.categoryName)
const categoryId = computed(() => route.query.categoryId)
const shopId = computed(() => route.params.brandId)
console.log('%c Line:28 🥛 shopId', 'color:#D8E256', shopId.value)

const loading = ref(false)
const products = ref([])
const currentPage = ref(1)
const totalItems = ref(0)
const pageSize = ref(10)

onMounted(() => {
  fetchProducts()
})

const fetchProducts = async () => {
  loading.value = true
  const [err, res] = await try_http('/api/selection/search-by-keyword', {
    method: 'post',
    body: {
      websiteCode: shopId.value,
      shopCategoryCode: categoryId.value,
      pageNo: currentPage.value,
      pageSize: pageSize.value,
    },
  })
  loading.value = false
  if (!err) {
    products.value = res.data.data || []
    totalItems.value = res.data.totalCount
  }
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchProducts()
}

const goPart = (item) => {
  navigateTo({
    path: `/brand/${shopId.value}/${item.id}`,
  })
}

watch(
  () => [shopId.value, categoryId.value],
  () => {
    currentPage.value = 1
    fetchProducts()
  }
)
</script>
