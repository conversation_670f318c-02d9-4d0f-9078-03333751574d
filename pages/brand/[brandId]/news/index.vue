<template>
    <div class="py-20 min-h-screen">
        <div class="w-full max-w-1440 mx-auto h-full">
            <!-- 新闻分类标签 -->
            <div class="bg-white rounded-2 card-shadow p-20 mb-20">
                <div class="flex flex-wrap gap-15">
                    <div v-for="(category, index) in newsCategories" :key="index"
                        class="px-15 py-8 rounded-full cursor-pointer transition-all duration-300"
                        :class="activeCategory === category.id ? 'bg-primary text-white' : 'bg-[#f5f5f5] hover:bg-[#e5e5e5]'"
                        @click="setActiveCategory(category.id)">
                        <i :class="[category.icon, 'mr-8']"></i>
                        {{ category.name }}
                    </div>
                </div>
            </div>

            <!-- 新闻动态区域 -->
            <div class="bg-white rounded-2 card-shadow p-20">
                <div class="flex justify-between items-center mb-20 pb-10 border-b border-solid border-[#eee]">
                    <h3 class="text-20 font-bold flex items-center">
                        <div class="w-4 h-20 bg-primary rounded-2 mr-10"></div>
                        {{ currentCategory.name || '新闻动态' }}
                    </h3>
                    <!-- <a href="javascript:void(0)"
                        class="text-[#666] hover:text-primary transition-colors duration-300 text-14"
                        @click="viewAllNews">
                        查看全部 <i class="fas fa-angle-right"></i>
                    </a> -->
                </div>

                <div v-if="loading" class="py-30 text-center">
                    <a-spin />
                </div>

                <div v-else-if="newsList.length === 0" class="py-30 text-center text-[#999]">
                    暂无相关新闻
                </div>

                <div v-else class="flex flex-col gap-15">
                    <div v-for="(news, index) in newsList" :key="index"
                        class="flex gap-20 p-15 bg-[#f9f9f9] rounded-10 transition-all duration-300 hover:bg-[#f5f5f5] cursor-pointer"
                        @click="viewNewsDetail(news)">
                        <div
                            class="w-200 h-120 bg-[#eee] rounded-5 flex-shrink-0 flex justify-center items-center overflow-hidden">
                            <img v-if="news.articleCover" :src="useImage(convertImage(news.articleCover))"
                                :alt="news.title" class="w-full h-full object-cover">
                            <i v-else class="fas fa-newspaper text-[#999] text-40"></i>
                        </div>
                        <div class="flex-1 flex flex-col justify-between">
                            <div>
                                <h4 class="text-18 font-bold mb-10 line-clamp-1">{{ news.title }}</h4>
                                <p class="text-14 text-[#666] leading-normal line-clamp-2">{{ news.subTitle }}</p>
                            </div>
                            <div class="flex justify-between mt-15 text-[#999] text-12">
                                <span><i class="far fa-clock mr-5"></i>{{ formatDate(news.publishTime) }}</span>
                                <!-- <span><i class="far fa-eye mr-5"></i>{{ formatNumber(news.viewCount) }} 浏览</span> -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分页 -->
                <div class="flex justify-center mt-30" v-if="newsList.length > 0">
                    <a-pagination v-model:current="currentPage" :total="total" :pageSize="pageSize"
                        @change="handlePageChange" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const shopId = computed(() => route.params.brandId)

// 新闻分类
const newsCategories = ref([
    { id: null, name: '全部新闻', icon: 'fas fa-newspaper' },
    { id: '0', name: '产品资讯', icon: 'fas fa-building' },
    { id: '1', name: '解决方案', icon: 'fas fa-box-open' },
    { id: '2', name: '成功案例', icon: 'fas fa-industry' },
    { id: '3', name: '企业宣传', icon: 'fas fa-microchip' },
])

const activeCategory = ref(null)
const currentCategory = computed(() => {
    return newsCategories.value.find(item => item.id === activeCategory.value) || {}
})

// 分页相关
const currentPage = ref(1)
const pageSize = ref(5)
const total = ref(0)
const loading = ref(false)

// 新闻列表
const newsList = ref([

])

// 设置当前分类
const setActiveCategory = (categoryId) => {
    activeCategory.value = categoryId
    currentPage.value = 1
    fetchNewsList()
}

// 查看全部新闻
const viewAllNews = () => {
    activeCategory.value = 'all'
    fetchNewsList()
}

// 查看新闻详情
const viewNewsDetail = (news) => {
    router.push({
        path: `/brand/${shopId.value}/news/${news.id}`,
        query: {
            title: news.title
        }
    })
}

// 获取新闻列表
const fetchNewsList = async () => {
    loading.value = true
    try {
        const [err, res] = await try_http('/mall/article/page-search', {
            method: 'get',
            query: {
                current: 1,
                size: 10,
                sortType: 'desc',
                sort: 'created_time',
                articleTags: '',
                articleType: activeCategory.value || null,
                shopId: shopId.value // 修复: 使用 .value 获取响应式数据的值
            },
        })
        if (!err) {
            newsList.value = res.data.records || []
            // totalItems.value = res.data.totalCount
        } else {
            newsList.value = []
            total.value = 0
        }
    } catch (error) {
        console.error('获取产品列表失败:', error)
        newsList.value = []
        total.value = 0
    } finally {
        loading.value = false
    }
}

// 处理分页变化
const handlePageChange = (page) => {
    currentPage.value = page
    fetchNewsList()
}

// 格式化日期
const formatDate = (dateStr) => {
    return dateStr
}

// 格式化数字（添加千位分隔符）
const formatNumber = (num) => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
}

// 监听路由变化
watch(() => route.params.brandId, (newId) => {
    if (newId) {
        fetchNewsList()
    }
})

// 页面加载时获取数据
onMounted(() => {
    fetchNewsList()
})
</script>
