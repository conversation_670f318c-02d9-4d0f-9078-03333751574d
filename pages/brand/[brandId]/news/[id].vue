<template>
    <div class="py-20 min-h-screen">
        <div class="w-full max-w-1440 mx-auto">
            <!-- 面包屑导航 -->
            <!-- <div class="bg-white rounded-2 card-shadow p-20 mb-20">
                <div class="flex items-center text-14">
                    <a href="javascript:void(0)" class="text-[#666] hover:text-primary transition-colors duration-300"
                        @click="goBack">
                        <i class="fas fa-arrow-left mr-5"></i> 返回
                    </a>
                    <span class="mx-10 text-[#999]">/</span>
                    <a href="javascript:void(0)" class="text-[#666] hover:text-primary transition-colors duration-300"
                        @click="goHome">
                        首页
                    </a>
                    <span class="mx-10 text-[#999]">/</span>
                    <a href="javascript:void(0)" class="text-[#666] hover:text-primary transition-colors duration-300"
                        @click="goNewsList">
                        新闻动态
                    </a>
                    <span class="mx-10 text-[#999]">/</span>
                    <span class="text-[#999] line-clamp-1">{{ newsDetail.title }}</span>
                </div>
            </div> -->

            <!-- 新闻详情内容 -->
            <div class="bg-white rounded-2 card-shadow p-30">
                <div v-if="loading" class="py-50 text-center">
                    <a-spin />
                </div>

                <div v-else-if="!newsDetail.id" class="py-50 text-center text-[#999]">
                    <a-empty description="新闻内容不存在或已被删除" />
                </div>

                <div v-else>
                    <!-- 新闻标题 -->
                    <h1 class="text-28 font-bold text-center mb-15">{{ newsDetail.title }}</h1>

                    <!-- 新闻元信息 -->
                    <div
                        class="flex justify-center items-center text-[#999] text-14 mb-30 pb-20 border-b border-solid border-[#eee]">
                        <span class="mx-10"><i class="far fa-clock mr-5"></i>{{ formatDate(newsDetail.publishDate)
                        }}</span>
                        <!-- <span class="mx-10"><i class="far fa-eye mr-5"></i>{{ formatNumber(newsDetail.viewCount) }}
                            浏览</span> -->
                        <span class="mx-10"><i class="fas fa-tag mr-5"></i>{{ getCategoryName(newsDetail.categoryId)
                        }}</span>
                        <span class="mx-10"><i class="far fa-user mr-5"></i>{{ newsDetail.author || '品牌官方' }}</span>
                    </div>

                    <!-- 新闻封面图 -->
                    <div v-if="newsDetail.coverImage" class="w-full mb-30 flex justify-center">
                        <img :src="newsDetail.coverImage" :alt="newsDetail.title" class="max-w-800 rounded-2 shadow-sm">
                    </div>

                    <!-- 新闻摘要 -->
                    <div v-if="newsDetail.summary"
                        class="bg-[#f9f9f9] p-20 rounded-2 mb-30 text-16 text-[#666] leading-normal">
                        <p>{{ newsDetail.summary }}</p>
                    </div>

                    <!-- 新闻正文 -->
                    <div class="news-content text-16 leading-loose text-[#333]" v-html="newsDetail.content"></div>

                    <!-- 文章标签 -->
                    <div v-if="newsDetail.tags && newsDetail.tags.length > 0"
                        class="mt-30 pt-20 border-t border-solid border-[#eee]">
                        <span class="text-[#999] mr-10">标签:</span>
                        <a-tag v-for="(tag, index) in newsDetail.tags" :key="index" class="mr-10 ">
                            {{ tag }}
                        </a-tag>
                    </div>

                    <!-- 分享按钮 -->
                    <!-- <div class="flex justify-between items-center mt-30 pt-20 border-t border-solid border-[#eee]">
                        <div class="flex items-center">
                            <span class="text-[#999] mr-10">分享到:</span>
                            <div class="flex gap-15">
                                <a href="javascript:void(0)" class="text-20 text-[#1DA1F2] hover:opacity-80"
                                    @click="shareToWeixin">
                                    <i class="fab fa-weixin"></i>
                                </a>
                                <a href="javascript:void(0)" class="text-20 text-[#1DA1F2] hover:opacity-80"
                                    @click="shareToWeibo">
                                    <i class="fab fa-weibo"></i>
                                </a>
                                <a href="javascript:void(0)" class="text-20 text-[#0077B5] hover:opacity-80"
                                    @click="shareToQQ">
                                    <i class="fab fa-qq"></i>
                                </a>
                                <a href="javascript:void(0)" class="text-20 text-[#333] hover:opacity-80"
                                    @click="copyLink">
                                    <i class="fas fa-link"></i>
                                </a>
                            </div>
                        </div>
                        <div>
                            <a-button type="primary" @click="likeNews" :disabled="isLiked">
                                <template #icon><i
                                        :class="isLiked ? 'fas fa-thumbs-up' : 'far fa-thumbs-up'"></i></template>
{{ isLiked ? '已点赞' : '点赞' }} ({{ newsDetail.likes || 0 }})
</a-button>
</div>
</div> -->
                </div>
            </div>

            <!-- 相关新闻 -->
            <!-- <div class="bg-white rounded-2 card-shadow p-20 mt-20">
                <div class="flex justify-between items-center mb-20 pb-10 border-b border-solid border-[#eee]">
                    <h3 class="text-18 font-bold flex items-center">
                        <div class="w-4 h-16 bg-primary rounded-2 mr-10"></div>
                        相关新闻
                    </h3>
                </div>

                <div v-if="relatedNews.length === 0" class="py-20 text-center text-[#999]">
                    暂无相关新闻
                </div>

                <div v-else class="grid grid-cols-3 gap-20">
                    <div v-for="(news, index) in relatedNews" :key="index"
                        class="bg-white rounded-2 overflow-hidden transition-all duration-300 hover:translate-y-[-5px] hover:shadow-md cursor-pointer border border-solid border-[#eee]"
                        @click="viewNewsDetail(news)">
                        <div class="w-full h-150 bg-[#eee] flex justify-center items-center overflow-hidden">
                            <img v-if="news.imgUrl" :src="news.imgUrl" :alt="news.title"
                                class="w-full h-full object-cover">
                            <i v-else class="fas fa-newspaper text-[#999] text-40"></i>
                        </div>
                        <div class="p-15">
                            <h4 class="text-16 font-bold mb-10 line-clamp-2 h-48">{{ news.title }}</h4>
                            <div class="flex justify-between text-[#999] text-12">
                                <span><i class="far fa-clock mr-5"></i>{{ formatDate(news.publishDate) }}</span>
                                <span><i class="far fa-eye mr-5"></i>{{ formatNumber(news.viewCount) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'

const route = useRoute()
const router = useRouter()
const shopId = computed(() => route.params.brandId)
const newsId = computed(() => route.params.id)

// 加载状态
const loading = ref(true)
const isLiked = ref(false)

// 新闻详情
const newsDetail = ref({
    id: '',
    title: '',
    summary: '',
    content: '',
    publishDate: '',
    viewCount: 0,
    likes: 0,
    author: '',
    categoryId: '',
    coverImage: '',
    tags: []
})

// 相关新闻
const relatedNews = ref([])

// 新闻分类
const newsCategories = ref([
    { id: 'all', name: '全部新闻', icon: 'fas fa-newspaper' },
    { id: 'company', name: '公司动态', icon: 'fas fa-building' },
    { id: 'product', name: '产品资讯', icon: 'fas fa-box-open' },
    { id: 'industry', name: '行业新闻', icon: 'fas fa-industry' },
    { id: 'tech', name: '技术前沿', icon: 'fas fa-microchip' },
    { id: 'event', name: '活动公告', icon: 'fas fa-bullhorn' }
])

// 获取分类名称
const getCategoryName = (categoryId) => {
    const category = newsCategories.value.find(item => item.id === categoryId)
    return category ? category.name : '未分类'
}

// 获取新闻详情
const fetchNewsDetail = async () => {
    loading.value = true
    try {
        // 实际项目中应该调用API
        const [err, res] = await try_http('/mall/article/info/' + newsId.value, {
            method: 'get'
        })

        if (!err && res.data) {
            newsDetail.value = {
                id: res.data.id || newsId.value,
                title: res.data.title || route.query.title || '新闻详情',
                summary: res.data.summary || '',
                content: res.data.articleContent || generateMockContent(),
                publishDate: res.data.publishTime || '1970-01-01',
                viewCount: res.data.viewCount || Math.floor(Math.random() * 10000),
                likes: res.data.likes || Math.floor(Math.random() * 500),
                author: res.data.updatedBy || '品牌官方',
                categoryId: res.data.categoryId || 'company',
                coverImage: useImage(convertImage(res.data.articleCover)) || '',
                tags: res.data.articleTags.split(',') || []
            }

            // 更新浏览量
            // updateViewCount()

            // 获取相关新闻
            // fetchRelatedNews()
        } else {
            // 如果API调用失败，使用模拟数据
            useMockData()
        }
    } catch (error) {
        console.error('获取新闻详情失败:', error)
        useMockData()
    } finally {
        loading.value = false
    }
}

// 使用模拟数据
const useMockData = () => {
    newsDetail.value = {
        id: newsId.value,
        title: route.query.title || '品牌新品发布会即将举行，多款重磅产品蓄势待发',
        summary: '据悉，品牌将于下月举行年度新品发布会，届时将发布多款创新科技产品，包括全新一代智能手机、笔记本电脑以及多款智能家居设备。本次发布会主题为"连接未来"，将展示品牌最新科技成果和创新理念。',
        content: generateMockContent(),
        publishDate: '2025-03-15',
        viewCount: 12345,
        likes: 256,
        author: '品牌官方',
        categoryId: 'product',
        coverImage: '',
        tags: ['新品发布', '科技创新', '智能设备']
    }

    // 获取相关新闻
    fetchRelatedNews()
}

// 生成模拟内容
const generateMockContent = () => {
    return `
        <p>据悉，品牌将于下月举行年度新品发布会，届时将发布多款创新科技产品，包括全新一代智能手机、笔记本电脑以及多款智能家居设备。本次发布会主题为"连接未来"，将展示品牌最新科技成果和创新理念。</p>
        
        <h2 class="text-20 font-bold mt-30 mb-15">发布会亮点前瞻</h2>
        
        <p>根据业内消息，此次发布会将重点展示品牌在人工智能、可持续发展和用户体验三大领域的突破性进展。新一代旗舰智能手机预计将搭载自研芯片，在性能和能效方面有显著提升，同时在拍照、续航等用户关注的核心功能上也有重大改进。</p>
        
        <p>轻薄笔记本电脑系列将推出全新设计，采用环保材料制造，并提供更长的电池续航时间和更强的性能表现。智能家居设备则将进一步完善品牌的生态系统，提供更加智能化和个性化的用户体验。</p>
        
        <h2 class="text-20 font-bold mt-30 mb-15">行业影响与市场前景</h2>
        
        <p>分析师认为，此次发布会对品牌在全球市场的竞争地位具有重要意义。随着消费电子行业竞争日益激烈，品牌通过持续创新和提升产品品质，有望进一步巩固其市场领导地位。</p>
        
        <p>市场研究机构预测，新产品发布后，品牌在高端智能手机和笔记本电脑市场的份额有望提升3-5个百分点，智能家居领域也将迎来快速增长。</p>
        
        <h2 class="text-20 font-bold mt-30 mb-15">用户期待与反响</h2>
        
        <p>在社交媒体上，众多消费者已经表达了对此次发布会的期待。根据在线调查，超过70%的品牌现有用户计划关注此次发布会，其中约40%的用户表示有意购买新产品。</p>
        
        <p>品牌忠实用户李先生表示："我已经使用这个品牌的产品超过5年了，每一代产品都带来惊喜。特别期待这次的新款智能手机，希望在拍照和电池续航方面有更大突破。"</p>
        
        <h2 class="text-20 font-bold mt-30 mb-15">发布会详情</h2>
        
        <p>发布会将于2025年4月15日在国家会议中心举行，同时通过官方网站和社交媒体平台进行全球直播。感兴趣的消费者可以通过官方渠道预约观看。</p>
        
        <p>品牌发言人表示："我们致力于通过创新科技改善人们的生活。此次发布会将展示我们在过去一年中的研发成果，我们相信这些产品将为用户带来全新的体验和价值。"</p>
    `
}

// 获取相关新闻
const fetchRelatedNews = async () => {
    try {
        // 实际项目中应该调用API
        const [err, res] = await try_http('/mall/article/related', {
            method: 'get',
            query: {
                id: newsId.value,
                shopId: shopId.value,
                categoryId: newsDetail.value.categoryId,
                limit: 3
            },
        })

        if (!err && res.data) {
            relatedNews.value = res.data || []
        } else {
            // 如果API调用失败，使用模拟数据
            relatedNews.value = [
                {
                    id: 'r1',
                    title: '品牌荣获2025年度消费者信赖大奖，品质获权威认证',
                    description: '在近日举办的2025年度消费电子行业峰会上，品牌凭借优质的产品品质和出色的用户体验，荣获"消费者最信赖品牌"大奖。',
                    publishDate: '2025-02-28',
                    viewCount: 8765,
                    categoryId: 'company',
                    imgUrl: ''
                },
                {
                    id: 'r2',
                    title: '品牌启动"绿色科技"环保计划，推进产品可持续发展',
                    description: '品牌近日宣布启动"绿色科技"环保计划，承诺到2030年实现产品生产全过程碳中和。',
                    publishDate: '2025-02-15',
                    viewCount: 6543,
                    categoryId: 'company',
                    imgUrl: ''
                },
                {
                    id: 'r3',
                    title: '品牌技术研发中心揭秘：下一代产品核心技术曝光',
                    description: '在近期举办的技术开放日活动中，品牌技术研发中心首次对外展示了多项正在研发的核心技术。',
                    publishDate: '2025-01-28',
                    viewCount: 9876,
                    categoryId: 'tech',
                    imgUrl: ''
                }
            ]
        }
    } catch (error) {
        console.error('获取相关新闻失败:', error)
        // 使用模拟数据
        relatedNews.value = [
            {
                id: 'r1',
                title: '品牌荣获2025年度消费者信赖大奖，品质获权威认证',
                description: '在近日举办的2025年度消费电子行业峰会上，品牌凭借优质的产品品质和出色的用户体验，荣获"消费者最信赖品牌"大奖。',
                publishDate: '2025-02-28',
                viewCount: 8765,
                categoryId: 'company',
                imgUrl: ''
            },
            {
                id: 'r2',
                title: '品牌启动"绿色科技"环保计划，推进产品可持续发展',
                description: '品牌近日宣布启动"绿色科技"环保计划，承诺到2030年实现产品生产全过程碳中和。',
                publishDate: '2025-02-15',
                viewCount: 6543,
                categoryId: 'company',
                imgUrl: ''
            },
            {
                id: 'r3',
                title: '品牌技术研发中心揭秘：下一代产品核心技术曝光',
                description: '在近期举办的技术开放日活动中，品牌技术研发中心首次对外展示了多项正在研发的核心技术。',
                publishDate: '2025-01-28',
                viewCount: 9876,
                categoryId: 'tech',
                imgUrl: ''
            }
        ]
    }
}

// 更新浏览量
const updateViewCount = async () => {
    try {
        // 实际项目中应该调用API
        await http('/mall/article/view', {
            method: 'post',
            body: {
                id: newsId.value,
                shopId: shopId.value
            },
        })
    } catch (error) {
        console.error('更新浏览量失败:', error)
    }
}

// 点赞新闻
const likeNews = async () => {
    if (isLiked.value) return

    try {
        // 实际项目中应该调用API
        const [err, res] = await try_http('/mall/article/like', {
            method: 'post',
            body: {
                id: newsId.value,
                shopId: shopId.value
            },
        })

        if (!err) {
            isLiked.value = true
            newsDetail.value.likes++
            message.success('点赞成功')
        }
    } catch (error) {
        console.error('点赞失败:', error)
        // 模拟点赞成功
        isLiked.value = true
        newsDetail.value.likes++
        message.success('点赞成功')
    }
}

// 分享功能
const shareToWeixin = () => {
    message.success('已复制链接，请在微信中粘贴分享')
    copyLink()
}

const shareToWeibo = () => {
    const url = encodeURIComponent(window.location.href)
    const title = encodeURIComponent(newsDetail.value.title)
    window.open(`http://service.weibo.com/share/share.php?url=${url}&title=${title}`)
}

const shareToQQ = () => {
    const url = encodeURIComponent(window.location.href)
    const title = encodeURIComponent(newsDetail.value.title)
    window.open(`http://connect.qq.com/widget/shareqq/index.html?url=${url}&title=${title}`)
}

const copyLink = () => {
    const url = window.location.href
    navigator.clipboard.writeText(url).then(() => {
        message.success('链接已复制到剪贴板')
    }).catch(() => {
        message.error('复制失败，请手动复制')
    })
}

// 通过标签搜索
const searchByTag = (tag) => {
    router.push({
        path: `/brand/${shopId.value}/search`,
        query: {
            keyword: tag,
            type: 'news'
        }
    })
}

// 查看新闻详情
const viewNewsDetail = (news) => {
    router.push({
        path: `/brand/${shopId.value}/news/${news.id}`,
        query: {
            title: news.title
        }
    })
}

// 导航功能
const goBack = () => {
    router.back()
}

const goHome = () => {
    router.push(`/brand/${shopId.value}`)
}

const goNewsList = () => {
    router.push(`/brand/${shopId.value}/news`)
}

// 格式化日期
const formatDate = (dateStr) => {
    return dateStr
}

// 格式化数字（添加千位分隔符）
const formatNumber = (num) => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
}

// 监听路由变化
watch(() => [route.params.brandId, route.params.id], () => {
    fetchNewsDetail()
})

// 页面加载时获取数据
onMounted(() => {
    fetchNewsDetail()
})
</script>

<style scoped>
.card-shadow {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.news-content {
    line-height: 1.8;
}

.news-content h2 {
    margin-top: 30px;
    margin-bottom: 15px;
    font-size: 20px;
    font-weight: bold;
}

.news-content p {
    margin-bottom: 15px;
}

.news-content img {
    max-width: 100%;
    margin: 20px 0;
    border-radius: 4px;
}

/* 确保图标库已引入 */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');
</style>
