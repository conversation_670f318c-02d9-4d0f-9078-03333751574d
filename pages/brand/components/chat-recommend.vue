<template>
  <div class="flex flex-wrap gap-16px bg-primaryBg pl-64px pr-16px pt-0 pb-12px bg-opacity-75">
    <div
      class="flex h-32px items-center rounded-10px px-8px overflow-hidden cursor-pointer text-(#fff 14px) transition-all duration-200 hover:(color-#f94c30 border-#f94c30)"
      border="1px solid #A3A3A4"
      v-for="item in recommends"
      :key="item"
      @click="emits('ask', item)"
    >
      <div>{{ item }}</div>
      <div class="i-a-arrow-right-outlined text-18px"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
withDefaults(
  defineProps<{
    recommends: string[]
  }>(),
  {
    recommends: () => [],
  }
)

const emits = defineEmits<{
  ask: [string]
}>()
</script>
