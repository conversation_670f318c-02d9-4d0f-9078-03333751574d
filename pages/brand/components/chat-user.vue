<template>
  <div class="px-16px py-12px bg-darkBg bg-opacity-75 w-full overflow-hidden" flex>
    <div w-32px h-32px mr-16px>
      <a-avatar :src="user.pic">
        {{ isLogin ? user.nickName : '游客' }}
      </a-avatar>
    </div>
    <div class="text-#fff line-height-28px font-size-14px" v-if="ask.type == 'text'">
      <span class="mr-16px">{{ ask.text }}</span>
    </div>
    <card-form v-else-if="ask.type == 'form'" v-bind="ask.attrs"></card-form>
  </div>
</template>

<script lang="ts" setup>
import { userStore } from '~/store/user'
import cardForm from './card-form.vue'
const store = userStore()
const user = computed(() => store.user)
const isLogin = computed(() => user.value.userId)

const props = defineProps<{
  ask: string
}>()

const ask = computed(() => {
  let text = props.ask
  text = text.replace(/^<selected_prods>.*<\/selected_prods>(.*)/s, '$1')
  if (text.startsWith('```selection-form-snapshot')) {
    try {
      const attrs = JSON.parse(text.replace(/```selection-form-snapshot(.*)```/s, '$1'))
      return {
        type: 'form',
        attrs,
      }
    } catch (err) {
      console.log('%c Line:36 🥛 err', 'color:#1DC867', err)
    }
    return {
      type: 'text',
      text,
    }
  } else {
    return {
      type: 'text',
      text,
    }
  }
})
</script>
