<template>
    <div class="w-300px h-80px overflow-hidden bg-#32323E card-shadow m-3 cursor-pointer flex" @click="downloadFile">
        <div class="flex-1 p-3 flex">
            <div class="w-60px h-60px flex-center" mr-2>
                <img :src="fileIcon" class="w-full h-full" object-fit="cover">
            </div>
            <div class="flex-1 flex flex-col justify-between">
                <div class="text-14px truncate" :title="file?.file_name">{{ file?.file_name + file?.file_type }}
                </div>
                <div class="text-12px text-gray">{{ fileSize }}</div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import unknownIcon from '~/assets/images/file_icon/unknown.png'
import pdfIcon from '~/assets/images/file_icon/pdf.png'
import wordIcon from '~/assets/images/file_icon/word.png'
import excelIcon from '~/assets/images/file_icon/excel.png'
import pptIcon from '~/assets/images/file_icon/ppt.png'
import imageIcon from '~/assets/images/file_icon/image.png'

interface FileInfo {
    file_name: string
    file_type: string
    file_url: string
}

const props = defineProps<{
    file?: FileInfo
}>()

const fileSize = ref('文件大小未知')
const fileIcon = computed(() => {
    if (!props.file?.file_type) return unknownIcon

    const type = props.file.file_type.toLowerCase().replace('.', '')
    const iconMap: Record<string, string> = {
        'pdf': pdfIcon,
        'doc': wordIcon,
        'docx': wordIcon,
        'xls': excelIcon,
        'xlsx': excelIcon,
        'ppt': pptIcon,
        'pptx': pptIcon,
        'jpg': imageIcon,
        'jpeg': imageIcon,
        'png': imageIcon,
        'gif': imageIcon
    }

    return iconMap[type] || unknownIcon
})

async function getFileSize() {
    if (!props.file?.file_url) return
    try {
        const response = await fetch(props.file.file_url, { method: 'HEAD' })
        const size = response.headers.get('content-length')
        if (size) {
            const kb = Math.round(parseInt(size) / 1024)
            fileSize.value = kb > 1024 ? `${(kb / 1024).toFixed(2)}MB` : `${kb}KB`
        }
    } catch (err) {
        console.error('获取文件大小失败:', err)
        fileSize.value = '未知大小'
    }
}

onMounted(() => {
    getFileSize()
})

const downloadFile = () => {
    if (!props.file?.file_url) return
    window.open(props.file.file_url, '_blank')
}
</script>
