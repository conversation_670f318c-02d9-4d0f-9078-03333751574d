<template>
  <div class="w-250px h-250px overflow-hidden bg-#32323E card-shadow m-12px cursor-pointer flex" @click="toBrand">
    <div flex-1 flex-center v-if="loading">
      <a-spin :spinning="true"></a-spin>
    </div>
    <div class="flex-1 p-16px flex flex-col items-center" v-else>
      <img class="h-90px w-90px object-scale-down" :src="useImage(convertImage(brand.shopLogo))" alt="" />
      <div class="my-8px">{{ brand.shopName }}</div>
      <div class="text-(sm gray) line-clamp-16px">{{ brand.intro }}</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const brand = ref<obj>({})

const props = defineProps<{
  site?: string
}>()

const loading = ref(false)
async function getBrandInfo() {
  const shopId = props.site
  if (!shopId) return
  loading.value = true
  const [err, res] = await try_http('/mall/shop/info', {
    params: {
      shopId,
    },
  })
  loading.value = false
  if (!err) {
    brand.value = res.data
    console.log('%c Line:31 🥛 res', 'color:#2FBCAA', res.data)
  }
}

function toBrand() {
  message.info('品牌即将开放，敬请期待')
}

onMounted(() => {
  getBrandInfo()
})
</script>
