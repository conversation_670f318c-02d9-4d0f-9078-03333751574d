<template>
  <div class="flex flex-wrap">
    <template v-for="id in ids" :key="id">
      <card-brand v-if="type == 'brands'" :site="id"></card-brand>
      <card-product v-else-if="type == 'prods'" :id="id"></card-product>
      <card-file v-else-if="type == 'files'" :file="id"></card-file>
    </template>
  </div>
</template>

<script lang="ts" setup>
import cardBrand from './card-brand.vue'
import cardProduct from './card-product.vue'
import cardFile from './card-file.vue'
defineProps<{
  type: string
  ids: string[]
}>()
</script>
