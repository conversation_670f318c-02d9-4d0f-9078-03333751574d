<template>
  <card-link v-if="type == 'category'" :code="code" :content="content"></card-link>
  <card-product v-else-if="type == 'product'" :id="id"></card-product>
  <card-brand v-else-if="type == 'brand'" :site="site"></card-brand>
</template>

<script setup lang="ts">
import cardLink from './card-link.vue'
import cardProduct from './card-product.vue'
import cardBrand from './card-brand.vue'
defineProps<{
  type: string
  content?: string
  code?: string
  id?: string
  site?: string
}>()
</script>
