<template>
  <a-drawer
    width="50%"
    :title="null"
    v-model:open="open"
    :closable="false"
    :bodyStyle="{
      padding: 0,
    }"
  >
    <div class="w-full h-full flex-center bg-primaryBg" v-if="loading">
      <a-spin></a-spin>
    </div>
    <iframe v-show="!loading" :src="url" frameborder="0" class="w-full h-100vh bg-#2A2A35" ref="iframeRef"></iframe>
  </a-drawer>
</template>

<script setup lang="ts">
const yanxuan = useRuntimeConfig().public.baseUrl.VITE_YANXUAN

const currentShopId = ref('')

const open = ref(false)

const url = ref('')

const loading = ref(true)
let _resolve
const loadIframe = new Promise((resolve) => {
  _resolve = resolve
})

const iframeRef = ref<HTMLIFrameElement>()
const show = async (shopId, tag) => {
  url.value = `${yanxuan}/chat?shop_id=${shopId}`
  open.value = true
  currentShopId.value = shopId

  loadIframe.then(() => {
    iframeRef.value?.contentWindow?.postMessage(
      {
        type: 'setTag',
        shopId: currentShopId.value,
        from: 'bomai',
        tag,
      },
      '*'
    )
  })
}

useEventListener(window, 'message', (e) => {
  if (typeof e.data == 'object' && e.data.from == 'chat' && e.data.shop_id == currentShopId.value) {
    console.log('%c Line:105 🎂 e', 'color:#33a5ff', e)
    handleMessage(e.data)
  }
})

const goLogin = usegoLogin()

const handleMessage = (data) => {
  const { type } = data
  if (type == 'login') {
    goLogin()
  } else if (type == 'loaded') {
    loading.value = false
    _resolve()
  }
}

defineExpose({
  show,
})
</script>
