<template>
  <div
    class="w-250 h-80 px-10 flex items-center hover:bg-primary text-#333 cursor-pointer"
    hover="text-white"
    rounded-4
    @click.stop="
      emits('clickNode', {
        isProduct,
        data,
      })
    "
  >
    <img :src="imgUrl" w-50 h-50 mr-10 object-scale-down alt="" />
    <div flex-1 truncate>{{ isProduct ? data.prodName : data.categoryName }}</div>
  </div>
</template>

<script setup>
import folder from '~/assets/images/folder.svg'
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

const emits = defineEmits(['clickNode'])

const imgUrl = computed(() => {
  if (isProduct.value) {
    return useImage(useObs(props.data.pic))
  } else {
    return props.data.pic ? useObs(props.data.pic) : folder
  }
})
const isProduct = computed(() => has(props.data.prodId))
</script>
