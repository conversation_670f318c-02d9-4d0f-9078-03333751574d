<template>
  <div class="px-16px py-12px bg-primaryBg bg-opacity-75 w-full overflow-x-hidden">
    <div flex>
      <div w-32px h-32px mr-16px bg-white rounded-full>
        <a-avatar :src="chat.agent.agentImg"></a-avatar>
      </div>
      <div class="text-#fff line-height-28px font-size-14px flex-1 overflow-x-auto">
        <Render :children="nodeList" />
        <span class="tail" v-if="!item.isFinished"></span>
      </div>
    </div>
    <div flex text-white h-30px items-center v-if="item.isFinished && item.message_id">
      <div class="flex-1"></div>
      <img
        v-if="item.fav == 'like'"
        :src="likeFill"
        @click="setFav()"
        class="w-20px h-20px mr-16px cursor-pointer"
        alt=""
      />
      <img v-else :src="like" @click="setFav('like')" class="w-20px h-20px mr-16px cursor-pointer" alt="" />
      <img
        v-if="item.fav == 'dislike'"
        :src="dislikeFill"
        @click="setFav()"
        class="w-20px h-20px cursor-pointer"
        alt=""
      />
      <img v-else :src="dislike" @click="setFav('dislike')" class="w-20px h-20px cursor-pointer" alt="" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import like from '~/assets/images/thumb-up-line.svg'
import likeFill from '~/assets/images/thumb-up-fill.svg'
import dislike from '~/assets/images/thumb-down-line.svg'
import dislikeFill from '~/assets/images/thumb-down-fill.svg'
import markdownit from 'markdown-it'
import { tokensToAST } from './type'
import Render from './render.vue'
import type Chat from '~/utils/chat'
const props = defineProps<{
  item: Ans
  chat: Chat
}>()

const { item } = toRefs(props)
const emits = defineEmits(['ok'])

const md = markdownit({
  html: true,
  linkify: true,
  typographer: true,
})

const nodeList = computed(() => {
  const parse = md.parse(item.value.answer, {})
  return tokensToAST(parse)
})

const setFav = async (fav?: string) => {
  const data: obj = {
    message_id: item.value.message_id,
    user: item.value.user,
    agentId: props.chat.shop_id,
  }

  if (fav) {
    data.rating = fav
  }
  const body = getshopsign(data, 'post')
  const [err, res] = await try_http('/mall/p/llm/agent/feedbacks', {
    method: 'post',
    body,
    headers: {
      grantType: 'sign',
    },
  })
  if (!err) {
    item.value.fav = fav
    console.log('res', JSON.parse(res.data))
  }
}
</script>

<style lang="less" scoped>
.tail::after {
  animation: blink 1s steps(5, start) infinite;
  content: '_';
  font-weight: 700;
  margin-left: 3px;
  vertical-align: baseline;
}

@keyframes blink {
  to {
    visibility: hidden;
  }
}
</style>
