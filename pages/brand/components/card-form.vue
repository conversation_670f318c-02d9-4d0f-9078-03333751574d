<template>
  <div class="w-full mt-20px">
    <a-table
      :pagination="false"
      :columns="columns"
      :data-source="dataSource"
      size="small"
      :scroll="{ y: 500 }"
      bordered
    >
      <template #bodyCell="{ column, record }">
        <form-item :item="record" :form="form" v-if="column.dataIndex == 'name'"></form-item>
      </template>
    </a-table>
    <div class="text-right mt-20px" v-if="!value">
      <a-button @click="form = {}" class="mr-10px">重置</a-button>
      <a-button type="primary" @click="submit">提交</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import emitter from './mitt'
import formItem from './form-item.vue'
const props = defineProps<{
  form_id?: string
  value?: obj
}>()

onMounted(() => {
  getSelect()
})

const form = ref<obj>({})
const dataSource = ref<obj[]>([])
const getSelect = async () => {
  if (!props.form_id) return
  const [err, res] = await try_http('/mall/p/llm/agent/getForm', {
    query: getshopsign(
      {
        supplierTag: props.form_id,
      },
      'get',
    ),
    headers: {
      grantType: 'sign',
    },
  })
  if (!err) {
    const data = JSON.parse(res.data)
    if (props.value) {
      data.forEach((item) => {
        form.value[item.name] = props.value![item.name]
        item.disabled = true
      })
    } else {
      data.forEach((item) => {
        form.value[item.name] = item.default
      })
    }
    dataSource.value = data
  }
}

const columns = [
  {
    title: '序号',
    customRender({ index }) {
      return index + 1
    },
  },
  {
    title: '规格名',
    dataIndex: 'title',
  },
  {
    title: '规格说明',
    dataIndex: 'description',
  },
  {
    title: '规格值',
    dataIndex: 'name',
  },
]

const submit = async () => {
  const errList: obj[] = []
  dataSource.value.forEach((item, index) => {
    if (item.required && !has(form.value[item.name])) {
      errList.push({ name: item.title, index: index + 1 })
    }
  })
  if (errList.length) {
    Modal.info({
      title: '提示',
      content: () =>
        h(
          'div',
          errList.map((item) => {
            const { name, index } = item
            return h('div', `第${index}行缺少规格${name}`)
          }),
        ),
    })
  } else {
    const str = `\`\`\`selection-form-snapshot
${JSON.stringify({
  form_id: props.form_id,
  value: form.value,
})}
\`\`\``
    emitter.emit('addForm', str)
  }
}
</script>
