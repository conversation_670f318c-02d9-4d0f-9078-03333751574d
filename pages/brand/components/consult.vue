<template>
  <a-button ml-10 type="primary" @click="productConSul" :loading="loading">产品咨询</a-button>
</template>

<script setup lang="ts">
const route = useRoute()
const loading = ref(false)

const shopId = (route.params.brandId as string) ?? '0'
const productConSul = async () => {
  loading.value = true
  const [err, res] = await try_http('/api/selection/part-details', {
    params: {
      id: route.params.id,
    },
  })
  loading.value = false
  if (!err) {
    chatPop.show(shopId, {
      _id: res.data.id,
      ...res.data,
    })
  }
}
</script>
