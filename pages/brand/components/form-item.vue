<template>
  <div class="flex gap-10px">
    <div class="flex-1">
      <a-input
        :disabled="item.disabled"
        v-if="item.field_type == 'string'"
        v-model:value="form[item.name]"
        :status="status"
      ></a-input>
      <a-input-number
        :disabled="item.disabled"
        v-if="item.field_type == 'integer'"
        v-model:value="form[item.name]"
        :precision="0"
        :step="1"
        :status="status"
        class="w-full"
      ></a-input-number>
      <a-input-number
        :disabled="item.disabled"
        v-if="item.field_type == 'float'"
        v-model:value="form[item.name]"
        :status="status"
        class="w-full"
      ></a-input-number>

      <a-select
        :disabled="item.disabled"
        v-if="item.field_type == 'enum'"
        v-model:value="form[item.name]"
        :status="status"
        class="w-full"
      >
        <a-select-option v-for="val in item.values" :key="val" :value="val">{{ val }}</a-select-option>
      </a-select>
    </div>
    <div class="w-30px">{{ item.unit }}</div>
  </div>
</template>

<script setup>
const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
  form: {
    type: Object,
    default: () => ({}),
  },
})

const status = computed(() => {
  if (props.item.required && !has(props.form[props.item.name])) return 'error'
  return ''
})
</script>
