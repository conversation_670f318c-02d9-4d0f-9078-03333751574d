export type AstNode = {
  type: string
  tag: string
  content: string
  children: AstNode[]
  attrs: Obj
  custom: string
}

export const tokensToAST = (tokens: obj[]) => {
  const ast: AstNode[] = []
  const stack: AstNode[] = []

  const genAst = (tokens: Obj[]) => {
    tokens.forEach((token) => {
      if (token.hidden) return
      const node: AstNode = {
        type: token.type,
        tag: token.tag,
        content: token.content || '',
        children: [],
        attrs: {},
        custom: token.info || '',
      }
      if (token.attrs) {
        for (const item of token.attrs) {
          const [key, val] = item
          node.attrs[key] = val
        }
      }

      if (token.nesting === 1) {
        // 开始节点，入栈
        if (stack.length > 0) {
          stack[stack.length - 1].children.push(node)
        } else {
          ast.push(node)
        }
        stack.push(node)
      } else if (token.nesting === -1) {
        // 结束节点，出栈
        stack.pop()
      } else {
        if (token.children?.length) {
          genAst(token.children)
        } else {
          // 普通节点，直接添加
          if (stack.length > 0) {
            const last = stack.at(-1)!
            if (last.type == 'link_open') {
              last.content = node.content
            } else {
              last.children.push(node)
            }
          } else {
            ast.push(node)
          }
        }
      }
    })
  }

  genAst(tokens)

  return ast
}
