<template>
  <div class="absolute w-full h-500 flex overflow-hidden text-14 bg-white card-shadow left-0 top-55">
    <div class="w-250 shrink-0 bg-[#2a2a35] overflow-y-auto">
      <div
        v-for="item in categoryList"
        :key="item.categoryId"
        class="px-16 truncate cursor-pointer text-white"
        :class="{
          'bg-white text-primary': item.categoryId == root.categoryId,
        }"
        hover="text-primary bg-white"
        :title="item.categoryName"
        @click.stop="changeActive(item)"
      >
        {{ item.categoryName }}
      </div>
    </div>

    <div class="flex-1 flex flex-col">
      <div class="text-18 font-bold px-10 text-#333">
        <span v-if="active.parentId == 0">{{ active.categoryName }}</span>

        <span v-else cursor-pointer hover-link @click="back(active)">
          <LeftOutlined />
          {{ active.categoryName }}
        </span>
      </div>
      <div class="flex-1 overflow-y-auto">
        <div class="flex-center w-full h-full" v-if="loading">
          <a-spin></a-spin>
        </div>
        <template v-else>
          <div class="flex flex-wrap p-20" v-if="currentList.length">
            <Item v-for="item in currentList" :data="item" @click-node="handleClickNode"></Item>
          </div>

          <Empty class="text-#333" v-else />
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
import Item from './Item.vue'
const props = defineProps({
  brandId: String,
})

const emits = defineEmits(['handleCategory'])

onMounted(() => {
  getCategoryInfo()
})

const shopId = props.brandId

const loading = ref(false)

// 当前激活项
const active = ref({})

// 缓存 categoryId -> list
const cache = ref(new Map())
// 记录所有节点路径
const paths = ref(new Map())
const addPath = (list) => {
  for (const item of list) {
    if (!paths.value.has(item.categoryId)) {
      paths.value.set(item.categoryId, item)
    }
  }
}

// 获取分类第一层列表
const categoryList = ref([])
const getCategoryInfo = async () => {
  const res = await http('/mall/category/categoryInfo', {
    method: 'get',
    query: {
      shopId,
      parentId: 0,
    },
  })
  useMall(res, () => {
    categoryList.value = res.data.categoryInfo
    if (categoryList.value?.length) {
      active.value = categoryList.value[0]
      addPath(categoryList.value)

      getListByActive()
    }
  })
}

// 根据当前激活分类，获取右侧内容
// 1. 分类 2。叶子节点
const getListByActive = async () => {
  const categoryId = active.value.categoryId
  if (cache.value.has(categoryId)) return
  loading.value = true
  await getCategories()
  // if (active.value.type == 'LEAF') {
  //   // await getProducts()
  //   console.log('clickleaf')
  // } else if (active.value.type == 'TRUNK') {
  // }

  loading.value = false
}

const getProducts = async () => {
  const res = await http('/mall/search/page', {
    query: {
      shopId,
      current: 1,
      size: 100,
      isActive: 1,
      shopCategoryId: active.value.categoryId,
      isAllProdType: true,
      sort: 21,
    },
  })
  useMall(res, () => {
    const [cate] = res.data.records
    cache.value.set(active.value.categoryId, cate.products)
  })
}

const getCategories = async () => {
  const res = await http('/mall/category/categoryInfo', {
    query: {
      shopId,
      parentId: active.value.categoryId,
    },
  })
  useMall(res, () => {
    const list = res.data.categoryInfo ?? []
    addPath(list)
    cache.value.set(active.value.categoryId, list)
  })
}

// 切换激活项
const changeActive = (node) => {
  if (node.type != 'LEAF') {
    active.value = node
    getListByActive()
  } else {
    emits('handleCategory', node)
  }
}

const handleClickNode = ({ isProduct, data }) => {
  if (!isProduct) {
    changeActive(data)
  }
}

const back = () => {
  const preNode = paths.value.get(active.value.parentId)
  if (preNode) {
    changeActive(preNode)
  }
}

const currentList = computed(() => {
  return cache.value.get(active.value.categoryId) ?? []
})

const root = computed(() => {
  let current = active.value.categoryId
  while (current) {
    const node = paths.value.get(current)
    if (!node) return {}
    if (node.parentId == 0) return node
    current = node.parentId
  }
  return {}
})
</script>
