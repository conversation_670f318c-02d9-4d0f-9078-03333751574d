<template>
  <div class="h-[calc(100vh-180px)]" flex flex-col>
    <div flex-1 flex flex-col justify-center items-center>
      <img src="~/assets/images/404.png" />
      <div class="text-16 text-gray mb-20px">对不起，您没有权限访问此页面</div>
      <div class="flex">
        <a-button @click="navigateTo('/')" type="primary" class="mr-8px">返回首页</a-button>
        <a-button @click="goBack" type="primary">返回上一页</a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

definePageMeta({
  layout: 'workspace',
  noSidebar: 1,
})

const router = useRouter()

const goBack = () => {
  router.back()
}
</script>
