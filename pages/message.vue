<template>
  <NuxtLayout name="selection">
    <div p-y-8>
      <div flex flex-row justify-start mb-8>
        <a-button type="link" size="small" mr-10 @click="() => $router.go(-1)">&lt; 返回</a-button>
        <a-breadcrumb separator=">">
          <a-breadcrumb-item>
            <NuxtLink to="/">首页</NuxtLink>
          </a-breadcrumb-item>
          <a-breadcrumb-item>消息中心</a-breadcrumb-item>
        </a-breadcrumb>
      </div>
      <div mt-8 flex class="content-box">
        <div mr-16 w-200 flex="~ col" style="background: #fff">
          <a-menu :selectedKeys="selectedKeys" mode="inline" @click="goTo">
            <a-menu-item key="platform">
              <template #icon>
                <NotificationOutlined pl-28 />
              </template>
              <span>平台公告</span>
            </a-menu-item>
          </a-menu>

          <a-menu v-if="user.userId" :selectedKeys="selectedKeys" mode="inline" @click="goTo">
            <a-menu-item key="system" :disabled="!user.userId">
              <template #icon>
                <BellOutlined pl-28 />
              </template>
              <span>系统通知</span>
              <a-badge ml-10 mb-5 :count="messages.unReadNum"></a-badge>
            </a-menu-item>
          </a-menu>

          <a-menu v-if="user.userId" :selectedKeys="selectedKeys" mode="inline" @click="goTo">
            <a-menu-item key="interaction" :disabled="!user.userId">
              <template #icon>
                <UserSwitchOutlined pl-28 />
              </template>
              <span>叨友互动</span>
            </a-menu-item>
          </a-menu>

          <a-menu v-if="user.userId" :selectedKeys="selectedKeys" mode="inline" @click="goTo">
            <a-menu-item key="customer-service" :disabled="!user.userId">
              <template #icon>
                <MessageOutlined pl-28 />
              </template>
              <span>客服消息</span>
            </a-menu-item>
          </a-menu>
        </div>
        <div flex="~ 1">
          <NuxtPage />
        </div>
      </div>
    </div>
  </NuxtLayout>
</template>

<script setup>
import {} from 'vue'
import { userStore } from '~/store/user'
import { usemessageStore } from '~/store/message'

const userStoreObj = userStore()
const { user } = storeToRefs(userStoreObj)
const messageStoreObj = usemessageStore()
const { messages } = messageStoreObj

const router = useRouter()
const route = useRoute()
const selectedKeys = computed(() => {
  return [route.path.split('/').pop()]
})

const goTo = ({ key }) => {
  router.push({
    path: `/message/${key}`,
  })
}
</script>

<style scoped>
.content-box {
  min-height: calc(100vh - 225px);
}

.badge {
  display: inline-block;
  width: 20px;
  height: 20px;
  vertical-align: bottom;
}
</style>
