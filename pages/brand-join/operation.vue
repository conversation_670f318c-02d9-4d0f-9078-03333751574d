<template>
  <div class="w-full flex py-20 justify-center">
    <div class="card w-1440 p-30 overflow-auto" style="max-height: calc(100vh - 200px)">
      <a-alert
        v-if="formData.status == 3"
        text-center
        message="您的企业正在审核中，请耐心等待审核结果"
        type="warning"
      />

      <a-alert
        v-else-if="formData.status === 70"
        text-center
        message="您的企业未能通过审核，请在消息中心中查看详情"
        type="error"
        closable
      />
      <h1 class="text-24 font-bold my-30 text-center">研选工场装备制造业AI供应链服务平台供应商经营信息</h1>

      <div class="min-h-500 overflow-x-hidden mb-30">
        <Step4OperationInfo ref="step4Ref" :formData="formData" />
      </div>

      <div class="mt-30 border-t border-#eee pt-20">
        <div class="flex justify-center gap-16">
          <!-- <a-button @click="onBack">返回</a-button> -->
          <a-button type="primary" @click="onSubmit">提交</a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import Step4OperationInfo from '~/components/settle/Step4OperationInfo.vue'
import { getCompany } from '~/api/common'
import { putShopCompany } from '~/api/mall-manage'
import { omit } from 'lodash-es'

const route = useRoute()
const router = useRouter()
const companyId = route.query.companyId

// 表单数据
const formData = ref({
  supplierType: 1, // 供应商类型
  mainProductCategories: [], // 主营产品分类
  agencyBrands: [], // 代理品牌
  agencyAuthorizationLetter: '', // 代理授权书
  agencyAreaScope: [], // 代理区域
  coreProcessingCapability: [], // 核心加工能力
  maxOrderAmount: null, // 最大订单量
  inspectionList: '', // 质检清单
  equipmentList: '', // 设备清单
  ownBrandName: '', // 自有品牌名称
  ownBrandList: [], // 自有品牌列表
  trademarkRegistrationCertificate: '', // 商标注册证书
  patentCertificate: '', // 专利证书
  companyAddress: '', // 企业地址
  plantPropertyRight: 1, // 厂房产权
  plantArea: '', // 厂房面积
  staffAmount: '', // 在职人员数
  annualTurnover: '', // 年营业额
  qualityAuthentication: '', // 质量认证
  customerCase: '', // 客户合作案例
  plantVideo: '', // 厂房视频
  brandCategorySignings: [], // 品牌类目签约参数
  selectBrands: [], // 选择的品牌
  penaltyAgreement: '', // 假一罚十协议
  status: null,
})

// 组件引用
const step4Ref = ref(null)

// 返回
const onBack = () => {
  router.back()
}

// 提交
const onSubmit = async () => {
  try {
    await step4Ref.value.validate()

    const body = {
      ...omit(formData.value, ['ownBrandList']),
      agencyAreaScope: formData.value.agencyAreaScope.join(','),
      coreProcessingCapability: formData.value.coreProcessingCapability.join(','),
      shopCompanyId: companyId,
      applyType: 7,
    }

    const res = await putShopCompany(body)
    if (res.success) {
      message.success('提交成功, 稍后将跳转至首页')
      setTimeout(() => {
        navigateTo('/')
      }, 3000)
    }
  } catch (error) {
    console.error('提交失败:', error)
  }
}

// 回显数据
onMounted(async () => {
  window._data = formData
  if (companyId) {
    const data = await getCompany(companyId)

    // 经营信息回显
    const { supplierType, status } = data
    formData.value.supplierType = supplierType
    formData.value.status = status

    // 根据供应商类型处理不同的回显逻辑
    if (supplierType === 1) {
      // 贸易商回显
      formData.value.agencyAreaScope = data.agencyAreaScope?.split(',').map(Number) || []
      formData.value.penaltyAgreement = data.penaltyAgreement || ''

      // 处理品牌授权和产品分类
      if (data.brandCategorySignings) {
        formData.value.brandAuthorizations = {}
        formData.value.brandProductCategories = {}
        formData.value.selectBrands = []
        formData.value.agencyBrands = []

        data.brandCategorySignings.forEach((item) => {
          if (item.brandId) {
            formData.value.agencyBrands.push(item.brandId)
            formData.value.selectBrands.push({
              brandId: item.brandId,
              name: item.brandName || '',
            })
            formData.value.brandAuthorizations[item.brandId] = item.qualifications || ''
            formData.value.brandProductCategories[item.brandId] = item.categories || ''
          }
        })
      }
    } else if (supplierType === 2) {
      // 加工商回显
      formData.value.coreProcessingCapability = data.coreProcessingCapability?.split(',').map(Number) || []
      formData.value.maxOrderAmount = data.maxOrderAmount || null
      formData.value.equipmentList = data.equipmentList || ''
      formData.value.inspectionList = data.inspectionList || ''
      const [categorySign] = data.brandCategorySignings
      if (categorySign) {
        formData.value.mainProductCategories = categorySign.categories || []
      }
    } else if (supplierType === 3) {
      // 品牌商回显
      formData.value.patentCertificate = data.patentCertificate || ''
      formData.value.equipmentList = data.equipmentList || ''
      formData.value.inspectionList = data.inspectionList || ''

      if (data.brandCategorySignings) {
        formData.value.ownBrandList = data.brandCategorySignings.map((item) => ({
          brandId: item.brandId || genUUID(),
          name: item.brandName || '',
          type: item.brandId ? 'dict' : 'custom',
          categoryIds: item.categories || '',
          trademarkRegistrationCertificate: item.qualifications || '',
          logo: item.brandLogo || '',
          replaceBrands: item.replaceBrands,
        }))
      }
    }

    // 通用字段回显
    formData.value.companyAddress = data.companyAddress || ''
    formData.value.plantPropertyRight = Number(data.plantPropertyRight) || 1
    formData.value.plantArea = Number(data.plantArea) || 1
    formData.value.staffAmount = Number(data.staffAmount) || 1
    formData.value.annualTurnover = Number(data.annualTurnover) || null
    formData.value.qualityAuthentication = data.qualityAuthentication || ''
    formData.value.customerCase = data.customerCase || ''
    formData.value.plantVideo = data.plantVideo || ''
  }
})
</script>
