<template>
  <div class="text-14 max-w-auto!">
    <div class="h-350 text-center pt-100 banner">
      <h1 class="text-48 text-white">
        <span>加入研选工场，成为</span>
        <span class="text-primary">严选</span>
        <span>供应商</span>
      </h1>
      <a-button type="primary" class="mt-50 w-175 h-50" size="large" @click="handleJoin" v-if="showJoin">
        立即加入
      </a-button>
    </div>

    <div class="px-20">
      <div
        w-full
        max-w-1240
        mx-auto
        flex
        items-center
        justify-between
        py-50
        gap-x-20
        v-for="(item, i) in list"
        :key="i"
      >
        <div class="max-w-400 w-full">
          <div class="text-36 font-bold">{{ item.title }}</div>
          <div class="text-16 leading-30 mt-30 text-#555">{{ item.content }}</div>
        </div>

        <div class="max-w-650 w-full aspect-650/350">
          <img w-full h-full :src="`/images/join/${item.img}`" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { companyStore } from '~/store/company'
import { userStore } from '~/store/user'
import { getOneByCond } from '~/api/mall-platform/index'
import { getCompany } from '~/api/common'

definePageMeta({
  layout: 'full',
})

const showJoin = computed(() => {
  const { type, status } = companyStore().company
  if ((type == 2 || type == 3) && status == 1) return false
  return true
})

const list = [
  {
    title: 'AI营销，降本增效',
    content:
      '品牌资料和产品信息将自动进入品牌专属知识库，为AI客服所学，快速响应下游客户需求，提供技术支持和实时商务信息，减少人力成本，提升服务效率。',
    img: 'join1.jpg',
  },
  {
    title: '数据驱动，高效协同',
    content: '系统自动预处理和分析产品数据，优化库存管理，预测市场需求，助力您与下游客户高效协同，提升供应链灵活性。',
    img: 'join2.jpg',
  },
  {
    title: '精准曝光，拓展商机',
    content: '平台通过智能匹配，将您的产品精准推送给下游客户，帮助您触达更多优质客户，提升品牌曝光度和市场占有率。',
    img: 'join3.jpg',
  },
]

const user = computed(() => userStore().user)
const company = computed(() => companyStore().company)
const isLogin = useLoginState()

const handleJoin = async () => {
  // 未登录
  if (!isLogin.value) {
    loginPop.show(() => {
      // location.reload()
    })
    // router.push({
    //   path: '/login'
    // })
    return
  }
  // 已登录
  const { userMobile } = user.value
  // 普通会员 - 无手机号
  if (!userMobile) {
    Modal.confirm({
      title: '提示',
      content: '请绑定手机号',
      okText: '去完善',
      cancelText: '取消',
      onOk() {
        const yanxuan = useRuntimeConfig().public.baseUrl.VITE_YANXUAN
        window.open(`${yanxuan}/userCenter/account`)
      },
      onCancel() {},
    })
    return
  }
  // 是否在企业空间
  const companyId = company.value.shopCompanyId

  if (companyId) {
    const company = await getCompany(companyId)
    const { type, status } = company
    if ((type == 2 || type == 3) && status == 1) {
      message.warn('您已经是研选供应商了，无需重复申请')
      return
    }

    if (status === 2) {
      // 正在审核中
      message.warning('企业正处于审核状态,请耐心等待审核完成')
      return
    }
    const res = await getOneByCond({
      merchantId: companyId,
      userMobile,
    })
    await useMall(res)
    const _data = JSON.parse(res.data)
    if (_data.isAdmin != 1) {
      message.warning('您不是该企业的管理员,请联系企业管理员开通服务')
      return
    }
    navigateTo('/brand-join/settle?companyId=' + companyId)
  } else {
    navigateTo('/brand-join/settle')
    message.warning('您当前处于个人空间，将创建新的企业')
  }
}
</script>

<style lang="less" scoped>
.banner {
  background: url('/images/join/joinbanner.jpg') no-repeat center;
  background-size: cover;
}
</style>
