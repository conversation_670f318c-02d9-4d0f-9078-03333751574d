<template>
  <div class="w-full py-20">
    <div class="card w-1440 p-30 overflow-auto" style="max-height: calc(100vh - 200px)">
      <a-alert
        v-if="formData.status == 3"
        text-center
        message="您的企业正在审核中，请耐心等待审核结果"
        type="warning"
      />

      <a-alert
        v-else-if="formData.status === 80"
        text-center
        message="您的企业未能通过审核，请在消息中心中查看详情"
        type="error"
        closable
      />

      <h1 class="text-24 font-bold my-30 text-center">研选工场装备制造业AI供应链服务平台供应商财务信息</h1>

      <div class="min-h-500 overflow-x-hidden mb-30">
        <Step5FinanceInfo ref="step5Ref" :formData="formData" hideSkip />
      </div>

      <div class="mt-30 border-t border-#eee pt-20">
        <div class="flex justify-center gap-16">
          <!-- <a-button @click="onBack">返回</a-button> -->
          <a-button type="primary" @click="onSubmit">提交</a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import Step5FinanceInfo from '~/components/settle/Step5FinanceInfo.vue'
import { message } from 'ant-design-vue'
import { getCompany } from '~/api/common'
import { putShopCompany } from '~/api/mall-manage'

const route = useRoute()
const router = useRouter()
const companyId = route.query.companyId

// 表单数据
const formData = ref({
  status: null,
  paymentTerms: '预付', // 支付方式
  paymentTermsRemark: '', // 支付方式备注
  billingType: null, // 发票类型
  invoiceTypeRemark: '', // 发票类型备注
  isSkipBank: false, // 是否跳过银行信息
  depositBank: '', // 开户银行
  bankAccount: '', // 银行账号
  accountName: '', // 账户名称
  certifyDoc: '', // 证明文件
  billingTypeNotes: '', // 发票类型备注
})

// 组件引用
const step5Ref = ref(null)

// 返回
const onBack = () => {
  router.back()
}

// 提交
const onSubmit = async () => {
  try {
    await step5Ref.value.validate()

    const body = {
      ...formData.value,
      shopCompanyId: companyId,
      applyType: 8,
    }

    const res = await putShopCompany(body)
    if (res.success) {
      message.success('提交成功, 稍后将跳转至首页')
      setTimeout(() => {
        navigateTo('/')
      }, 3000)
    }
  } catch (error) {}
}

// 回显数据
onMounted(async () => {
  if (companyId) {
    const data = await getCompany(companyId)

    formData.value.status = data.status

    // 财务信息回显
    formData.value.billingType = data.billingType || null
    formData.value.invoiceTypeRemark = data.invoiceTypeRemark || ''

    formData.value.depositBank = data.depositBank || ''
    formData.value.bankAccount = data.bankAccount || ''
    formData.value.accountName = data.accountName || ''
    formData.value.certifyDoc = data.certifyDoc || ''
    formData.value.billingTypeNotes = data.billingTypeNotes || ''
  }
})
</script>
