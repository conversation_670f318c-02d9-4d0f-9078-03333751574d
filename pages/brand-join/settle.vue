<template>
  <div class="w-full flex justify-center py-20">
    <div class="card w-1440 p-30 overflow-auto" style="max-height: calc(100vh - 200px)">
      <h1 class="text-24 font-bold mb-30 text-center">研选工场装备制造业AI供应链服务平台供应商入驻</h1>

      <a-steps :current="step" class="mb-40" :key="step">
        <a-step title="入驻协议" />
        <a-step title="工商信息" />
        <a-step title="企业信息" />
        <a-step title="经营信息" />
        <a-step title="财务信息" />
        <a-step title="诚信承诺书" />
      </a-steps>

      <div class="min-h-500 overflow-x-hidden mb-30">
        <!-- 第一步：入驻协议 -->
        <Step1Agreement v-if="step === 0" ref="currentStepRef" :formData="settleFrom" />

        <!-- 第二步：工商信息 -->
        <Step2BusinessInfo v-if="step === 1" ref="currentStepRef" :formData="settleFrom" :editable="editable" />

        <!-- 第三步：企业信息 -->
        <Step3CompanyInfo v-if="step === 2" ref="currentStepRef" :formData="settleFrom" :editable="editable" />

        <!-- 第四步：经营信息 -->
        <Step4OperationInfo v-if="step === 3" ref="currentStepRef" :formData="settleFrom" />

        <!-- 第五步：财务信息 -->
        <Step5FinanceInfo v-if="step === 4" ref="currentStepRef" :formData="settleFrom" @skip="onSkip" />

        <!-- 第六步：诚信承诺书 -->
        <Step6Commitment v-if="step === 5" ref="currentStepRef" :formData="settleFrom" />
      </div>

      <div class="mt-30 border-t border-#eee pt-20">
        <div class="flex justify-center gap-16">
          <a-button v-if="step > 0" @click="onPrev">上一步</a-button>
          <a-button v-if="step < 5" :disabled="!settleFrom.checked && step === 0" type="primary" @click="onNext">
            下一步
          </a-button>
          <a-button type="primary" v-if="step === 4" @click="onSkip">跳过</a-button>
          <a-button
            v-if="step === 5"
            :loading="loading"
            type="primary"
            :disabled="!settleFrom.integrityCommitment"
            @click="onFinalSubmit"
          >
            提交
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 导入步骤组件
import { getCompany } from '~/api/common'
import Step1Agreement from '~/components/settle/Step1Agreement.vue'
import Step2BusinessInfo from '~/components/settle/Step2BusinessInfo.vue'
import Step3CompanyInfo from '~/components/settle/Step3CompanyInfo.vue'
import Step4OperationInfo from '~/components/settle/Step4OperationInfo.vue'
import Step5FinanceInfo from '~/components/settle/Step5FinanceInfo.vue'
import Step6Commitment from '~/components/settle/Step6Commitment.vue'
import { companyStore } from '~/store/company'
import { userStore } from '~/store/user'

const useUser = userStore()

// 当前步骤
const step = ref(0)
// 是否可编辑
const editable = ref(true)
// 当前步骤组件引用
const currentStepRef = ref(null)

// 表单数据
const settleFrom = ref({
  checked: false, // 同意入驻协议
  integrityCommitment: false, // 同意诚信承诺书
  merchantName: '', // 企业名称
  creditCode: '', // 统一社会信用代码
  residence: '', // 企业地址
  representative: '', // 企业法人
  capital: null, // 注册资本
  foundTime: null, // 成立时间
  startTime: null, // 营业期限开始时间
  endTime: null, // 营业期限结束时间
  businessScope: '', // 经营范围
  businessLicense: '', // 营业执照
  identityCardFront: '', // 法人身份证正面
  identityCardLater: '', // 法人身份证反面
  merchantShortName: '', // 企业简称
  mainProduct: '', // 主营产品
  merchantPhone: '', // 联系电话
  merchantMail: '', // 邮箱
  merchantWebsite: '', // 企业网站
  merchantSlogan: '', // 企业口号
  merchantLogo: '', // 企业logo
  promotionalImg: '', // 宣传图片
  supplierType: 1, // 供应商类型
  mainProductCategories: [], // 主营产品分类
  agencyBrands: [], // 代理品牌
  agencyAuthorizationLetter: '', // 代理授权书
  agencyAreaScope: [], // 代理区域
  coreProcessingCapability: [], // 核心加工能力
  maxOrderAmount: null, // 最大订单量
  inspectionList: '', // 质检清单
  equipmentList: '', // 设备清单
  ownBrandName: '', // 自有品牌名称
  ownBrandList: [], // 自有品牌列表
  trademarkRegistrationCertificate: '', // 商标注册证书
  patentCertificates: '', // 专利证书
  companyAddress: '', // 企业地址
  plantPropertyRight: 1, // 厂房产权
  plantArea: 1, // 厂房面积
  staffAmount: 1, // 在职人员数
  annualTurnover: '', // 年营业额
  qualityAuthentication: '', // 质量认证
  customerCase: '', // 客户合作案例
  plantVideo: '', // 厂房视频
  paymentTerms: '预付', // 支付方式
  paymentTermsRemark: '', // 支付方式备注
  billingType: null,
  invoiceTypeRemark: '', // 发票类型备注
  brandCategorySignings: [], // 品牌类目签约参数
  isSkipBank: false, // 是否跳过银行信息
  selectBrands: [], // 选择的品牌
  patentCertificate: '', // 专利证书
  fakePenaltyAgreement: '', // 假一罚十协议
  depositBank: '', // 开户银行
  bankAccount: '', // 银行账号
  accountName: '', // 账户名称
  certifyDoc: '', // 证明文件
  billingTypeNotes: '',
})

// 上一步
const onPrev = () => {
  if (step.value > 0) {
    step.value--
  }
}

// 下一步
const onNext = async () => {
  if (step.value === 0) {
    if (!settleFrom.value.checked) {
      message.error('请阅读并同意入驻协议')
      return
    }
    step.value++
    return
  }

  try {
    // 获取当前步骤组件的验证方法
    if (currentStepRef.value) {
      await currentStepRef.value.validate()
      step.value++
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 跳过财务信息步骤
const onSkip = () => {
  settleFrom.value.isSkipBank = true
  step.value++
}

const getData = () => {
  const {
    supplierType,
    merchantName,
    creditCode,
    residence,
    representative,
    capital,
    foundTime,
    startTime,
    endTime,
    businessScope,
    businessLicense,
    identityCardFront,
    identityCardLater,
    merchantShortName,
    mainProduct,
    merchantPhone,
    merchantMail,
    merchantWebsite,
    merchantSlogan,
    merchantLogo,
    promotionalImg,
    brandCategorySignings,
    coreProcessingCapability,
    agencyAreaScope,
    penaltyAgreement,
    maxOrderAmount,
    billingType,

    companyAddress,
    plantPropertyRight,
    plantArea,
    staffAmount,
    annualTurnover,
    qualityAuthentication,
    customerCase,
    plantVideo,
    ownBrandList,
    equipmentList,
    inspectionList,
    patentCertificate,
    isSkipBank,
    depositBank,
    bankAccount,
    accountName,
    certifyDoc,
    billingTypeNotes,
  } = settleFrom.value
  const data = {
    userMobile: useUser.user.userMobile,

    supplierType,
    // 工商信息
    merchantName,
    creditCode,
    residence,
    representative,
    capital,
    foundTime,
    startTime,
    endTime,
    businessScope,
    businessLicense,
    identityCardFront,
    identityCardLater,

    // 企业信息
    merchantShortName,
    mainProduct,
    merchantPhone,
    merchantMail,
    merchantWebsite,
    merchantSlogan,
    merchantLogo,
    promotionalImg,
    // 经营信息
    patentCertificate,
    equipmentList,
    inspectionList,
    ownBrandList,
    companyAddress,
    plantPropertyRight,
    plantArea,
    staffAmount,
    annualTurnover,
    qualityAuthentication,
    customerCase,
    plantVideo,
    brandCategorySignings,
    type: 2,

    // 财务信息
    // bankName,
    // bankAccount,
    // accountName,
    // bankProof,
  }

  if (companyStore().company.shopCompanyId != 0) {
    data.shopCompanyId = companyStore().company.shopCompanyId
  }

  if (supplierType === 1) {
    data.agencyAreaScope = agencyAreaScope.join(',')
    data.penaltyAgreement = penaltyAgreement
  } else if (supplierType === 2) {
    data.coreProcessingCapability = coreProcessingCapability.join(',')
    data.maxOrderAmount = maxOrderAmount
    data.equipmentList = equipmentList
    data.inspectionList = inspectionList
  } else if (supplierType === 3) {
    data.equipmentList = equipmentList
    data.inspectionList = inspectionList
    data.patentCertificate = patentCertificate
    // data.brandCategorySignings = ownBrandList.map((item) => {
    //   const { brandId, categoryIds, trademarkRegistrationCertificate, logo, type, name } = item
    //   if (type === 'dict') {
    //     return {
    //       brandId,
    //       categoryIds,
    //       qualifications: trademarkRegistrationCertificate,
    //       logo,
    //     }
    //   } else if (type === 'custom') {
    //     return {
    //       brandName: name,
    //       categoryIds,
    //       qualifications: trademarkRegistrationCertificate,
    //       brandLogo: logo,
    //     }
    //   }
    // })
  }

  if (!isSkipBank) {
    data.depositBank = depositBank
    data.bankAccount = bankAccount
    data.accountName = accountName
    data.certifyDoc = certifyDoc
    data.billingType = billingType
    data.billingTypeNotes = billingTypeNotes
  }
  return data
}

// 最终提交
const loading = ref(false)
const onFinalSubmit = async () => {
  if (!settleFrom.value.integrityCommitment) {
    message.error('请阅读并同意诚信承诺书')
    return
  }

  try {
    // 提交表单数据
    loading.value = true
    const body = getData()
    const [err, res] = await try_http('/mall/p/shopCompany/storage', {
      method: 'post',
      body,
    })
    if (!err) {
      message.success('提交成功, 稍后将跳转至首页')
      setTimeout(() => {
        navigateTo('/')
      }, 3000)
    }
    loading.value = false
  } catch (error) {
    console.error('提交失败:', error)
  }
}

const route = useRoute()
const companyId = route.query.companyId
const type = route.query.type

onMounted(async () => {
  if (companyId) {
    const data = await getCompany(companyId)

    // 基础信息回显
    settleFrom.value.merchantName = data.merchantName || ''
    settleFrom.value.creditCode = data.creditCode || ''
    settleFrom.value.residence = data.residence || ''
    settleFrom.value.representative = data.representative || ''
    settleFrom.value.capital = data.capital || null
    settleFrom.value.foundTime = data.foundTime || null
    settleFrom.value.startTime = data.startTime || null
    settleFrom.value.endTime = data.endTime || null
    settleFrom.value.businessScope = data.businessScope || ''
    settleFrom.value.businessLicense = data.businessLicense || ''
    settleFrom.value.identityCardFront = data.identityCardFront || ''
    settleFrom.value.identityCardLater = data.identityCardLater || ''
    settleFrom.value.merchantShortName = data.merchantShortName || ''
    settleFrom.value.mainProduct = data.mainProduct || ''
    settleFrom.value.merchantPhone = data.merchantPhone || ''
    settleFrom.value.merchantMail = data.merchantMail || ''
    settleFrom.value.merchantWebsite = data.merchantWebsite || ''
    settleFrom.value.merchantSlogan = data.merchantSlogan || ''
    settleFrom.value.merchantLogo = data.merchantLogo || ''
    settleFrom.value.promotionalImg = data.promotionalImg || ''

    if (data.type == 1 || data.type == 3) {
      editable.value = false
    }

    if (type != 'edit') return

    // 经营信息回显
    const { supplierType } = data
    settleFrom.value.supplierType = supplierType

    // 根据供应商类型处理不同的回显逻辑
    if (supplierType === 1) {
      // 贸易商回显
      settleFrom.value.agencyAreaScope = data.agencyAreaScope?.split(',').map(Number) || []
      settleFrom.value.penaltyAgreement = data.penaltyAgreement || ''

      // 处理品牌授权和产品分类
      if (data.brandCategorySignings) {
        // 初始化数据结构
        settleFrom.value.brandAuthorizations = {}
        settleFrom.value.brandProductCategories = {}
        settleFrom.value.selectBrands = []
        settleFrom.value.agencyBrands = []

        // 处理每个品牌的数据
        data.brandCategorySignings.forEach((item) => {
          if (item.brandId) {
            // 添加到代理品牌列表
            settleFrom.value.agencyBrands.push(item.brandId)

            // 添加到已选品牌列表
            settleFrom.value.selectBrands.push({
              brandId: item.brandId,
              name: item.brandName || '',
            })

            // 设置品牌授权和产品分类
            settleFrom.value.brandAuthorizations[item.brandId] = item.qualifications || ''
            settleFrom.value.brandProductCategories[item.brandId] = item.categories || ''
          }
        })

        // 更新品牌类目签约参数
        // settleFrom.value.brandCategorySignings = settleFrom.value.selectBrands.map((brand) => ({
        //   brandId: brand.brandId,
        //   categories: settleFrom.value.brandProductCategories[brand.brandId],
        //   qualifications: settleFrom.value.brandAuthorizations[brand.brandId],
        // }))
      }
    } else if (supplierType === 2) {
      // 加工商回显
      settleFrom.value.coreProcessingCapability = data.coreProcessingCapability?.split(',').map(Number) || []
      settleFrom.value.maxOrderAmount = data.maxOrderAmount || null
      settleFrom.value.equipmentList = data.equipmentList || ''
      settleFrom.value.inspectionList = data.inspectionList || ''
      const [categorySign] = data.brandCategorySignings
      if (categorySign) {
        settleFrom.value.mainProductCategories = categorySign.categories || []
      }

      // 更新品牌类目签约参数
      // settleFrom.value.brandCategorySignings = [
      //   {
      //     categories: settleFrom.value.mainProductCategories,
      //   },
      // ]
    } else if (supplierType === 3) {
      // 品牌商回显
      settleFrom.value.patentCertificate = data.patentCertificate || ''
      settleFrom.value.equipmentList = data.equipmentList || ''
      settleFrom.value.inspectionList = data.inspectionList || ''

      // 处理自有品牌数据
      if (data.brandCategorySignings) {
        settleFrom.value.ownBrandList = data.brandCategorySignings.map((item) => ({
          brandId: item.brandId || genUUID(),
          name: item.brandName || '',
          type: item.brandId ? 'dict' : 'custom',
          categoryIds: item.categories || '',
          trademarkRegistrationCertificate: item.qualifications || '',
          logo: item.brandLogo || '',
          replaceBrands: item.replaceBrands,
        }))

        // 更新品牌类目签约参数
        // settleFrom.value.brandCategorySignings = settleFrom.value.ownBrandList.map((brand) => {
        //   if (brand.type === 'dict') {
        //     return {
        //       brandId: brand.brandId,
        //       categories: brand.categoryIds,
        //       qualifications: brand.trademarkRegistrationCertificate,
        //     }
        //   } else if (brand.type === 'custom') {
        //     return {
        //       brandName: brand.name,
        //       categories: brand.categoryIds,
        //       qualifications: brand.trademarkRegistrationCertificate,
        //       brandLogo: brand.logo,
        //     }
        //   }
        // })
      }
    }

    settleFrom.value.depositBank = data.depositBank || ''
    settleFrom.value.bankAccount = data.bankAccount || ''
    settleFrom.value.accountName = data.accountName || ''
    settleFrom.value.certifyDoc = data.certifyDoc || ''
    settleFrom.value.billingType = data.billingType
    settleFrom.value.billingTypeNotes = data.billingTypeNotes

    // 通用字段回显
    settleFrom.value.companyAddress = data.companyAddress || ''
    settleFrom.value.plantPropertyRight = Number(data.plantPropertyRight)
    settleFrom.value.plantArea = Number(data.plantArea)
    settleFrom.value.staffAmount = Number(data.staffAmount)
    settleFrom.value.annualTurnover = Number(data.annualTurnover)
    settleFrom.value.qualityAuthentication = data.qualityAuthentication || ''
    settleFrom.value.customerCase = data.customerCase || ''
    settleFrom.value.plantVideo = data.plantVideo || ''
  }

  if (type == 'edit') {
    step.value = 3
    settleFrom.value.checked = true
  }
})

useEventListener('beforeunload', (e) => {
  e.preventDefault()
  e.returnValue = '您确定要离开吗？未保存的更改可能会丢失。'
})
</script>

<style lang="less" scoped>
/* Custom styling for specific elements that can't be handled with UnoCSS */
:deep(.ant-input-number-disabled) {
  background: #f5f5f5 !important;
}

:deep(.ant-input-number .ant-input-number-input) {
  background: inherit !important;
  color: inherit !important;
}

.protocol {
  .textAlign {
    text-align: center;
  }

  .textRight {
    text-align: right;
  }
}

.uploaded-files {
  margin-top: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  margin-bottom: 4px;
  background: #f5f5f5;
  border-radius: 4px;
}

.file-name {
  margin: 0 8px;
  flex: 1;
}

.delete-icon {
  cursor: pointer;
  color: #999;
}

.delete-icon:hover {
  color: #ff4d4f;
}
</style>
