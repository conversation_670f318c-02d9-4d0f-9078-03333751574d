<template>
  <div class="relative h-screen overflow-y-auto">
    <div class="h-64px bg-primaryBg sticky top-0 z-10">
      <div class="flex items-center max-w-1200px mx-auto h-full">
        <img
          src="~/assets/images/yanxuan_logo.png"
          class="w-150px h-auto mr-24px cursor-pointer"
          alt="logo"
          @click="goHome"
        />
      </div>
    </div>

    <div class="mt-24px p-24px max-w-1200px mx-auto">
      <div v-if="!list">
        <a-card>
          <a-skeleton></a-skeleton>
        </a-card>
        <a-card class="mt-20">
          <a-skeleton></a-skeleton>
        </a-card>
      </div>

      <div v-else>
        <!-- <a-card> -->
        <!--   <div class="flex items-center"> -->
        <!--     <a-avatar :size="40" :src="useObs(orderInfo.merchantLogo)"> -->
        <!--       {{ orderInfo.merchantShortName.slice(0, 1) }} -->
        <!--     </a-avatar> -->
        <!--     <div class="ml-16"> -->
        <!--       <h2 class="text-(20 #262626)">{{ orderInfo.merchantName }}</h2> -->
        <!--       <div class="mt-8 text-gray">询价单号：{{ orderInfo.orderNo }}</div> -->
        <!--     </div> -->
        <!--     <div class="flex-1"></div> -->
        <!--     <div class="text-gray"> -->
        <!--       截止时间: -->
        <!--       <span class="text-primary">{{ formatTime(orderInfo.deadline) }}</span> -->
        <!--     </div> -->
        <!--   </div> -->
        <!--   <a-divider></a-divider> -->
        <!--   <div class="grid grid-cols-4 gap-24px"> -->
        <!--     <div :class="cardCls"> -->
        <!--       <div class="text-#8c8c8c">物料种类</div> -->
        <!--       <div class="font-500 text-(16 #262626) mt-8">{{ orderInfo.itemCount || 0 }}种</div> -->
        <!--     </div> -->
        <!--     <div :class="cardCls"> -->
        <!--       <div class="text-#8c8c8c">总数量</div> -->
        <!--       <div class="font-500 text-(16 #262626) mt-8">{{ orderInfo.totalNumber }}</div> -->
        <!--     </div> -->
        <!--     <div :class="cardCls"> -->
        <!--       <div class="text-#8c8c8c">联系人</div> -->
        <!--       <div class="font-500 text-(16 #262626) mt-8">{{ orderInfo.userName }}</div> -->
        <!--     </div> -->
        <!--     <div :class="cardCls"> -->
        <!--       <div class="text-#8c8c8c">联系电话</div> -->
        <!--       <div class="font-500 text-(16 #262626) mt-8">{{ orderInfo.userMobile }}</div> -->
        <!--     </div> -->
        <!--   </div> -->
        <!-- </a-card> -->

        <a-card class="mt-20" title="供应商信息">
          <div class="bg-#fafafa p-20 rounded-8">
            <h3>供应商信息</h3>
            <a-form :rules="rules" :model="form" layout="vertical" ref="formRef">
              <div class="grid grid-cols-3 gap-x-24">
                <a-form-item label="供应商类型" name="supplierType">
                  <a-select placeholder="请选择供应商" v-model:value="form.supplierType">
                    <a-select-option v-for="item in supplierTypeList" :key="item.value" :value="item.value">
                      {{ item.name }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
                <a-form-item label="企业名称" name="supplierName">
                  <bussiness
                    label-in-value
                    allow-clear
                    v-model="form.supplierName"
                    placeholder="请输入企业名称"
                  ></bussiness>
                </a-form-item>
                <a-form-item label="联系人姓名" name="contactName">
                  <a-input v-model:value="form.contactName" placeholder="请输入联系人姓名"></a-input>
                </a-form-item>
                <a-form-item label="联系电话" name="contactMobile">
                  <a-input v-model:value="form.contactMobile" placeholder="请输入联系电话"></a-input>
                </a-form-item>
                <a-form-item label="自有品牌" name="customBrand" v-if="form.supplierType == 3">
                  <a-auto-complete
                    :options="brandOptions"
                    @search="onSearchBrand"
                    @select="onSelectBrand"
                    v-model:value="brand"
                    allow-clear
                  ></a-auto-complete>
                </a-form-item>
                <a-form-item label="主营品牌" name="mainBrand" v-if="form.supplierType == 1">
                  <use-brand
                    :remote-method="fetchBrandOptions"
                    label="name"
                    value="brandId"
                    v-model="form.mainBrand"
                    placeholder="请输入主营品牌"
                    allow-clear
                    mode="1"
                  ></use-brand>
                </a-form-item>
                <a-form-item label="主营品牌分类" name="mainBrandCategory" v-if="eq(form.supplierType, 1, 3)">
                  <use-category
                    mode="1"
                    v-model="form.mainBrandCategory"
                    label="categoryName"
                    value="categoryId"
                    :remote-method="fetchCategoryOptions"
                    allow-clear
                    placeholder="请选择主营品牌分类"
                  ></use-category>
                </a-form-item>
              </div>
            </a-form>
          </div>
          <a-divider></a-divider>
          <h3>物料报价</h3>
          <a-dropdown :disabled="!selects.keys.length">
            <div class="inline-flex items-center">
              <a-button>
                批量操作
                <down-outlined></down-outlined>
              </a-button>
              <span v-if="selects.keys.length" text-primary ml-8>已选择{{ selects.keys.length }}项</span>
            </div>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="handleReject">拒绝</a-menu-item>
                <a-menu-item @click="handleReset">重置</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>

          <div class="mt-20">
            <a-table
              size="small"
              bordered
              :columns="columns"
              :data-source="list"
              :pagination="false"
              :row-selection="{
                selectedRowKeys: selects.keys,
                onChange: (selectedRowKeys, selectedRows) => {
                  selects.keys = selectedRowKeys
                  selects.list = selectedRows
                },
              }"
              :scroll="{ x: 1500 }"
              row-key="id"
            >
              <template #bodyCell="{ record: row, column, text }">
                <template v-if="column.dataIndex == 'prodName'">
                  <a-tooltip :title="getContent(row.status)" v-if="eq(row.status, 40, 50)">
                    <info-circle-filled class="text-red mr-4"></info-circle-filled>
                  </a-tooltip>
                  {{ text }}
                </template>
                <a-input-number
                  placeholder="请输入"
                  class="w-full"
                  v-model:value="row.price"
                  v-if="column.dataIndex == 'price'"
                  :precision="2"
                  :disabled="!canEdit(row)"
                  :min="0"
                ></a-input-number>

                <a-input-number
                  class="w-full"
                  v-if="column.dataIndex == 'totalPrice'"
                  disabled
                  :value="row.price ? row.price * row.number : 0"
                  placeholder="自动计算"
                  :precision="2"
                ></a-input-number>

                <a-input-number
                  class="w-full"
                  v-if="column.dataIndex == 'tradeTerm'"
                  v-model:value="row.tradeTerm"
                  placeholder="请输入天数"
                  :disabled="!canEdit(row)"
                  :min="0"
                ></a-input-number>

                <a-input
                  class="w-full"
                  v-if="column.dataIndex == 'replaceSkuCode'"
                  placeholder="请输入平替型号"
                  v-model:value="row.replaceSkuCode"
                  :disabled="!canEdit(row) || row.replaceStatus == 0"
                ></a-input>

                <a-input
                  class="w-full"
                  v-if="column.dataIndex == 'remark'"
                  placeholder="可选"
                  v-model:value="row.remark"
                  :disabled="!canEdit(row)"
                ></a-input>

                <a-tag v-if="column.dataIndex == 'status'" :color="getStatusColor(row)">
                  {{ getStatusText(row) }}
                </a-tag>
              </template>
            </a-table>

            <div class="text-center mt-20">
              <a-button type="primary" @click="handleSubmit">提交报价</a-button>
            </div>
          </div>
        </a-card>
      </div>
    </div>

    <a-modal
      v-model:open="open"
      title="提示"
      ok-text="知道了"
      :cancel-button-props="{ style: { display: 'none' } }"
      @ok="handleOk"
      :width="600"
    >
      <div>
        <div class="mb-16px flex items-center rounded-4px bg-orange-50 p-12px text-orange-500">
          <close-circle-outlined class="mr-8px" />
          <span>以下物料无法报价，已跳过</span>
        </div>
        <a-table size="small" bordered :columns="errColumns" :data-source="errList" :pagination="false"></a-table>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import dayjs from 'dayjs'
import { cloneDeep } from 'lodash-es'
import st from 'store2'

definePageMeta({
  layout: 'false',
})

const route = useRoute()
const shareId = route.query.shareId
const yanxuan = useRuntimeConfig().public.baseUrl.VITE_YANXUAN
const orderInfo = ref()
const form = ref({})
const formRef = ref()
const rules = {}
const KEY = 'YX_RFQ_FORM'
const goHome = () => {
  window.open(yanxuan)
}

const cardCls = 'bg-#fafafa text-center p-12px rounded-6px'

watch(
  () => form.value,
  (newVal) => {
    st.set(KEY, newVal)
  },
  { deep: true },
)

onMounted(() => {
  form.value = st.get(KEY, {})

  if (!shareId) {
    message.error('分享的询价单不存在')
    return
  }
  fetchData()
})

const list = ref()
const listMap = computed(() => {
  if (!list.value) return {}
  return list.value.reduce((res, item) => {
    res[item.id] = item
    return res
  }, {})
})
const fetchData = async () => {
  const [err, res] = await try_http('/mall/inquiry-list-item/list', {
    query: {
      shareId,
    },
  })
  if (err) return
  list.value = res.data
}

const supplierTypeList = [
  { name: '贸易商', value: 1 },
  { name: '加工商', value: 2 },
  { name: '品牌商', value: 3 },
]

Object.assign(rules, {
  supplierType: [{ required: true, message: '请选择供应商类型', trigger: 'change' }],
  supplierName: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
  contactName: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
  contactMobile: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
  mainBrand: [{ required: true, message: '请输入主营品牌', trigger: 'blur' }],
  customBrand: [{ required: true, message: '请输入自有品牌', trigger: 'blur' }],
  mainBrandCategory: [{ required: true, message: '请选择主营品牌分类', trigger: 'change' }],
})

const selects = reactive({
  keys: [],
  list: [],
})

const columns = ref([
  {
    title: '物料名称',
    dataIndex: 'prodName',
    key: 'itemName',
    fixed: 'left',
    width: 200,
  },
  { title: '型号', dataIndex: 'skuCode', key: 'itemModel', width: 200 },
  { title: '品牌', dataIndex: 'brandName', key: 'itemBrand', width: 150 },
  { title: '数量', dataIndex: 'number', key: 'itemCount', width: 120 },
  {
    title: '期望交期',
    dataIndex: 'expectTerm',
    key: 'expectedDeliveryDate',
    width: 120,
    customRender({ text }) {
      return getTradeTerm(text)
    },
  },
  {
    title: '接受平替',
    dataIndex: 'replaceStatus',
    key: 'canReplace',
    width: 80,
    customRender({ text }) {
      return text == 1 ? '是' : '否'
    },
  },
  { title: '备注', dataIndex: 'buyerRemark', key: 'remark', width: 200, ellipsis: true },
  { title: '单价 (¥)', dataIndex: 'price', key: 'unitPrice', width: 150 },
  { title: '总价 (¥)', dataIndex: 'totalPrice', key: 'totalPrice', width: 150 },
  {
    title: '承诺交期（天）',
    dataIndex: 'tradeTerm',
    key: 'promiseDeliveryDays',
    width: 150,
  },
  {
    title: '平替型号',
    dataIndex: 'replaceSkuCode',
    width: 150,
  },
  {
    title: '报价备注',
    dataIndex: 'remark',
    width: 150,
  },
  {
    title: '报价状态',
    dataIndex: 'status',
    width: 80,
  },
])

const canEdit = (row) => !!!row.isReject && !eq(row.status, 40, 50)
const getStatusColor = (row) => (row.isReject ? 'red' : 'blue')
const getStatusText = (row) => (row.isReject ? '已拒绝' : '询价中')

const handleReject = () => {
  selects.list.forEach((item) => {
    item.isReject = true
  })
  message.success('操作成功')
  selects.keys = []
  selects.list = []
}

const brand = computed({
  get() {
    return form.value.brandName
  },
  set(val) {
    form.value.brandName = val
    form.value.brandId = undefined
  },
})

const brandOptions = ref([])
const onSearchBrand = async (val) => {
  const list = await fetchBrandOptions(val)
  brandOptions.value = list.map((item) => {
    return { value: item.name, text: item.brandId }
  })
}
const onSelectBrand = (_, option) => {
  form.value.brandName = option.value
  form.value.brandId = option.text
}

const handleReset = () => {
  list.value = cloneDeep(orderInfo.value.inquiryListItems || [])
  message.success('操作成功')
  selects.keys = []
  selects.list = []
}

const fetchBrandOptions = async (searchText) => {
  const [err, res] = await try_http('/mall/brand/list', {
    query: {
      name: searchText,
    },
  })
  if (err) return []
  return res.data || []
}

const fetchCategoryOptions = async (keyword = '') => {
  const [err, res] = await try_http('/mall/category/search', {
    query: {
      keyword,
    },
  })
  if (err) return []
  return res.data || []
}

const open = ref(false)
const errColumns = [
  {
    title: '物料名称',
    dataIndex: 'prodName',
  },
  {
    title: '型号',
    dataIndex: 'skuCode',
  },
  {
    title: '品牌',
    dataIndex: 'brandName',
  },
  {
    title: '原因',
    dataIndex: 'reason',
  },
]
const errList = ref([])

const handleOk = () => {
  open.value = false
}

const getContent = (v) => {
  const map = {
    40: '该物料已截止报价',
    50: '该物料已取消报价',
  }
  return map[v]
}

const handleSubmit = async () => {
  await formRef.value.validate()
  // if (isDeadlinePassed(orderInfo.value.deadline)) {
  //   message.error('询价单已过期，无法提交报价')
  //   submitLoading.value = false
  //   return
  // }
  const quotationItems = list.value
    .filter((item) => has(item.price) && has(item.tradeTerm) && !item.isReject)
    .map((item) => {
      return pick(item, ['id', 'price', 'tradeTerm', 'replaceSkuCode', 'remark', 'miniOrderQuantity', 'expiryTime'])
    })
  if (!quotationItems.length) {
    message.error('请至少为一个物料填写完整的报价信息（单价、承诺交期）')
    return
  }
  const { supplierType, supplierName, contactName, contactMobile, mainBrand, customBrand, mainBrandCategory } =
    form.value
  const supplierInfo = {
    supplierType,
    supplierName: supplierName.value,
    creditCode: supplierName.option.creditCode,
    contactName,
    contactMobile,
  }
  if (supplierType == 1) {
    supplierInfo.brandCategorySignings = mainBrand.map((item) => {
      return {
        brandId: item.brandId,
        brandName: item.name,
        categories: mainBrandCategory,
      }
    })
  } else if (supplierType == 3) {
    const { brandName, brandId } = form.value
    const brand = {
      brandName,
      categories: mainBrandCategory,
    }
    if (brandId) {
      brand.brandId = brandId
    }
    supplierInfo.brandCategorySignings = [brand]
  }

  Modal.confirm({
    title: '确认提交',
    content: '报价提交之后不可修改，是否继续？',
    okText: '确认提交',
    cancelText: '取消',
    onOk: async () => {
      const res = await http('/mall/inquiry-list-item/quote', {
        method: 'put',
        body: {
          shareId,
          supplierInfo,
          quotationItems,
        },
      })
      if (res.data.status) {
        message.success('报价提交成功')
      } else {
        open.value = true
        errList.value = res.data.errors.map((item) => {
          const row = listMap.value[item.id] || {}
          return { ...row, reason: item.reason }
        })
      }
    },
  })
}
</script>
