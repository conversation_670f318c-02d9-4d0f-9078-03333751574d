import unkownImage from '~/assets/images/unknown.png'

type Fn = <T extends boolean = false>(imageUrl?: string, isArray?: T) => T extends true ? string[] : string

const useImage: Fn =(imageUrl, isArray) => {
  let _res: string = ''
  if (imageUrl) {
    if (imageUrl.includes('[')) {
      try {
        const res = JSON.parse(imageUrl)
        return isArray ? res : res[0]
      } catch (error) {
        
      }
    }
    _res = imageUrl
  }
  if (!_res) {
    _res = unkownImage
  }
  return isArray ? [_res] : _res
}

export default useImage