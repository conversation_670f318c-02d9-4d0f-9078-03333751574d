export const useTimeRange = (target, key) => {
  const value = computed({
    get() {
      if (target[key]) {
        const { beginTime, endTime } = target[key]
        return [beginTime, endTime]
      }
      return undefined
    },
    set(range) {
      if (range) {
        const [beginTime, endTime] = range
        target[key] = {
          beginTime,
          endTime,
        }
      } else {
        target[key] = undefined
      }
    },
  })

  return value
}
