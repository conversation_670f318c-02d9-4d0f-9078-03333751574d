export default () => {
  function formatStatus(statu: number) {
    switch (statu) {
      case 1:
        return {
          text: '待询价',
          color: 'text-yellow',
        }
      case 2:
        return {
          text: '待询价',
          color: 'text-yellow',
        }
      case 3:
        return {
          text: '待专属客服确认',
          color: 'text-orange',
        }
      case 5:
        return {
          text: '报价中',
          color: 'text-primary',
        }
      case 6:
        return {
          text: '已报价',
          color: 'text-green',
        }
      case 7:
        return {
          text: '已过期',
          color: 'text-red',
        }
      case 8:
        return {
          text: '已取消',
          color: 'text-gray',
        }
      case 9:
        return {
          text: '已失效',
          color: 'text-black',
        }
      case 10:
        return {
          text: '待确认',
          color: 'text-primary',
        }
      default:
        return {
          text: '',
          color: '',
        }
    }
  }

  function formatTrade(day: number) {
    if (!has(day)) return ''
    if (day == 0) return '当日发货'
    return `${day}日发货`
  }

  function formatPrice(price?: number) {
    if (!price) return ''
    return `${getPrice(price)}`
  }

  return {
    formatTrade,
    formatStatus,
    formatPrice,
  }
}
