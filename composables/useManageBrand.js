import { getShopsByCompanyId, _http } from '~/api/mall-platform'
import { companyStore } from '~/store/company'
import { userStore } from '~/store/user'

export const useManageBrand = () => {
  const getRootUrl = (toPort) => {
    const { origin, port } = window.location
    if (port && process.env.NODE_ENV == 'development') {
      return origin.replace(port, toPort) + '/vendor'
    }
    return useRuntimeConfig().public.baseUrl.VITE_MALL_URL + '/vendor'
  }
  const user = computed(() => userStore().user)
  const company = computed(() => companyStore().company)
  const onManage = ({ shopId, shopCompanyId, userMobile }) => {
    const url = getRootUrl('9527')
    window.open(`${url}/login?type=signstore&phone=${userMobile}&shopId=${shopId}&companyId=${shopCompanyId}`)
  }

  const yanxuan = useRuntimeConfig().public.baseUrl.VITE_YANXUAN

  const manageBrand = async () => {
    if (company.value.creditCode == '91320594MAE5L8PD96') {
      window.open(`${yanxuan}/enterpriseCenter/library`)
    } else {
      try {
        const res = await getShopsByCompanyId({
          merchantId: company.value.shopCompanyId,
        })
        const data = JSON.parse(res.data)
        if (data.records.length > 0) {
          const [err, res] = await try_http('/mall/p/shop-employee/login-check', {
            params: {
              shopId: data.records[0].shopId,
            },
          })
          if (!err) {
            if (res.data == 1) {
              onManage({
                shopId: data.records[0].shopId,
                shopCompanyId: company.value.shopCompanyId,
                userMobile: user.value.userMobile,
              })
            } else {
              switch (res.data) {
                case 0:
                  return message.error('请联系企业管理员为您开通权限')
                case 2:
                  return message.error('用户被禁用登录')
                case 3:
                  return message.error('企业信息有误')
                case 4:
                  return message.error('店铺未找到')
              }
            }
          }
        }
      } catch (error) {
        console.log('%c Line:191 🥛 error', 'color:#E0AB81', error)
      }
    }
  }

  return {
    manageBrand,
  }
}
