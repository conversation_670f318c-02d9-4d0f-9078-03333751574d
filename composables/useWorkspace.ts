import { companyStore } from '~/store/company'

interface WorkSpaceFn {
  (f: Function): void
  (f: Function, f2: Function): void
}

// 检测是否处于个人空间，如果是就跳转研选
const useWorkspace: WorkSpaceFn = (a: Function, b?: Function) => {
  const isEnterprise = useIsenterprise()
  const yanxuan = useRuntimeConfig().public.baseUrl.VITE_YANXUAN
  if (a && b) {
    if (!isEnterprise) {
      a()
      setTimeout(() => {
        window.open(yanxuan + '/enterpriseCenter')
      }, 3000)
    } else {
      b()
    }
  } else {
    if (!isEnterprise) {
      window.open(yanxuan + '/enterpriseCenter')
    } else {
      a()
    }
  }
}

export default useWorkspace

// 是否是企业空间
export const useIsenterprise = () => {
  const company = companyStore()
  return company.company.shopCompanyId != 0
}
