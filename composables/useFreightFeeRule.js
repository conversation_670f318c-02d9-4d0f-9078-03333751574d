export const useFreightFeeRule = () => {
  const rules = ref([])
  const getRule = async () => {
    const [err, res] = await useCacheFetch('freightFee', () =>
      _try(() =>
        plat_http.get('/shop/freightConfig/page', {
          size: 999,
        }),
      ),
    )
    if (!err) {
      const data = parseJson(res.data, {})
      rules.value = data.records || []
    }
  }

  const getFee = (fee) => {
    if (has(fee)) {
      return `运费${getPrice(fee)}`
    }
    return '免运费'
  }

  const ruleMsg = computed(() => {
    const list = []
    rules.value.forEach((item) => {
      const { priceFloor, priceCeiling, freightFee } = item
      if (!priceCeiling) {
        list.unshift(`订单金额￥${priceFloor}以上，${getFee(freightFee)}`)
        return
      }
      list.unshift(`订单总金额￥${priceFloor}-￥${priceCeiling}，${getFee(freightFee)}`)
    })
    return list
  })

  return {
    getRule,
    ruleMsg,
  }
}
