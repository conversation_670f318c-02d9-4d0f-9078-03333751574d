export const useColumn = ({ key, config, descriptions = '' }) => {
  const defaultConfig = {}
  config.forEach((item) => {
    defaultConfig[item.key] = {
      key: item.key,
      name: item.name,
      defaultDisplay: item.columns.filter((item) => !item.defaultHidden).map((item) => item.dataIndex),
      columns: item.columns,
    }
  })

  const remoteConfig = ref()

  const fetchConfig = async () => {
    const [err, res] = await try_http('/mall/p/config/user-form-view/my', {
      params: {
        key,
      },
    })

    if (!err) {
      try {
        if (!res.data) return
        remoteConfig.value = JSON.parse(res.data)
      } catch (err) {
        remoteConfig.value = undefined
      }
    }
  }

  const columnData = computed(() => {
    const map = {}
    Object.keys(defaultConfig).forEach((key) => {
      const item = defaultConfig[key]
      const displays = remoteConfig.value?.[key] || item.defaultDisplay
      const set = new Set(displays)
      const filterColumns = item.columns.filter((col) => set.has(col.dataIndex))
      map[key] = {
        name: item.name,
        columns: item.columns,
        displays,
        key,
        filterColumns,
      }
    })
    return {
      key,
      descriptions,
      map,
    }
  })

  const getColumn = (key, fn) => {
    const columns = ref([])
    columns.value = columnData.value.map[key].filterColumns || []
    if (fn) {
      columns.value = columns.filter((item) => fn(item))
    }
    return columns
  }

  const changeConfig = () => {
    fetchConfig()
  }

  return {
    fetchConfig,
    columnData,
    getColumn,
    changeConfig,
  }
}
