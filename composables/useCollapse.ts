export const useCollapse = (list: Ref<Obj[]>, rowkey: string) => {
  const expandKeys = ref<obj[]>([])
  const flag = ref(false)
  const text = computed(() => (flag.value ? '全部收起' : '全部展开'))
  const toggle = () => {
    if (flag.value) {
      expandKeys.value = []
    } else {
      expandKeys.value = list.value.map((item) => item[rowkey])
    }
    flag.value = !flag.value
  }

  return {
    text,
    toggle,
    expandKeys,
  }
}
