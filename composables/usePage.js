export const usePage = (fn, opts = {}) => {
  /**
   * 分页
   */
  const page = reactive({
    current: 1,
    size: opts.size || 10,
    total: 0,
  })

  /**
   * 分页切换
   */
  const pageChange = ({ current, pageSize }) => {
    page.current = current
    page.size = pageSize
    fn()
  }

  /**
   *  表格分页
   */
  const pagination = computed(() => ({
    current: page.current,
    pageSize: page.size,
    total: page.total,
    showSizeChanger: true,
    showQuickJumper: true,
    pageSizeOptions: ['10', '20', '50', '100'],
    size: 'middle',
  }))

  return {
    page,
    pageChange,
    pagination,
  }
}
