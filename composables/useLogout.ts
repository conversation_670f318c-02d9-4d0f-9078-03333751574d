import { userStore } from '~/store/user'

const starts = ['/my', '/workSpace', '/message', '/ans-price']

export const useLogout = () => {
  const store = userStore()
  const config = useRuntimeConfig().public.baseUrl
  const logout = async () => {
    http('/mall/p/logOut', {
      method: 'post',
    }).then((res) => {
      useMall(res, () => {
        const pathname = window.location.pathname
        if (starts.some((item) => pathname.startsWith(item))) {
          message.destroy()
          message.success('已退出登录，即将跳转到登录页...')
          setTimeout(() => {
            store.clearUser()
            window.location.href = config.VITE_YANXUAN + `/login?redirect=${encodeURIComponent(location.href)}`
          }, 1000)
        } else {
          store.clearUser()
          message.destroy()
          message.success('已退出登录')
        }
      })
    })
  }
  return logout
}
