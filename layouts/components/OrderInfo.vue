<template>
  <div class="w-250 bg-white py-20 rounded-4">
    <div flex w-full justify-around>
      <div class="text-center cursor-pointer" @click="go('/workSpace/company-space/procure-order?status=1')">
        <div class="text-primary text-20" font-bold>{{ orderInfo[1] }}</div>
        <div class="text-13">待支付</div>
      </div>
      <div class="text-center cursor-pointer" @click="go('/workSpace/company-space/procure-order?status=2')">
        <div class="text-primary text-20" font-bold>{{ orderInfo[2] }}</div>
        <div class="text-13">待发货</div>
      </div>
      <div class="text-center cursor-pointer" @click="go('/workSpace/company-space/procure-order?status=3')">
        <div class="text-primary text-20" font-bold>{{ orderInfo[3] }}</div>
        <div class="text-13">待收货</div>
      </div>
    </div>

    <div class="mt-36 w-full grid grid-cols-2 gap-y-20">
      <div
        class="text-center text-14 cursor-pointer transition-colors hover:text-primary"
        @click="go('/workSpace/company-space/ans-price')"
      >
        <transaction-outlined text-24 />
        <div mt-10>企业询价单</div>
      </div>
      <div
        class="text-center text-14 cursor-pointer transition-colors hover:text-primary"
        @click="go('/workSpace/company-space/procure-order')"
      >
        <file-text-outlined text-24 />
        <div mt-10>企业采购单</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { companyStore } from '~/store/company'

const company = computed(() => companyStore().company)

// 获取企业订单详情
type Order = Record<number, number>
const orderInfo = ref<Order>({})
const getOrderInfo = async () => {
  const res = await http<Order>('/mall/p/myOrder/myOrderSearchConut', {
    params: {
      merchantId: company.value.shopCompanyId,
    },
  })
  useMall(res, () => {
    orderInfo.value = res.data
  })
}

const isLogin = useLoginState()
const goLogin = usegoLogin()

const go = (path) => {
  if (!isLogin.value) {
    goLogin()
  } else {
    useWorkspace(() => {
      navigateTo(path)
    })
  }
}

getOrderInfo()
</script>
