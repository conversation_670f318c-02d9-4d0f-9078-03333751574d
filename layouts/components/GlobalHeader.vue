<template>
  <div class="bg-primaryBg text-#fff text-14 header-fixed" :class="{ 'header-shrinked': isShrinked }">
    <div
      v-if="agent && !path.startsWith('/brand') && !path.startsWith('/workSpace')"
      class="fixed text-black right-10 z-999 top-1/2 translate-y--1/2 cursor-pointer group"
      @click="showChat"
    >
      <div class="relative">
        <img :src="useImage(agent.agentImg)" class="w-80 h-80 rounded-full overflow-hidden card-shadow" alt="" />
        <div
          class="absolute left-0 -top-40 w-max opacity-0 group-hover:opacity-100 bg-white p-8 rounded-4 card-shadow text-center transition-opacity duration-200"
        >
          <div truncate>{{ agent.agentName }}</div>
        </div>
      </div>
    </div>
    <div class="max-w-1440 w-full mx-auto">
      <div class="h-40 flex items-center">
        <nuxt-link :to="yanxuan" ml-4>
          <HomeOutlined class="text-20" />
          研选首页
        </nuxt-link>
        <div flex-1></div>

        <nuxt-link to="/" class="mr-50">零部件库</nuxt-link>

        <nuxt-link :to="yanxuan + '/message-center'" class="mr-50 relative" v-if="isLogin">
          <BellOutlined class="text-20" />
          <div
            v-if="unreadCount > 0"
            class="absolute -top-8px -right-8px min-w-16px h-16px px-1 rounded-full bg-red-500 text-white text-12px flex items-center justify-center"
          >
            {{ unreadCount > 99 ? '99+' : unreadCount }}
          </div>
        </nuxt-link>

        <a-dropdown v-if="isLogin">
          <div
            class="cursor-pointer mr-50"
            @click="goWorkSpace"
            v-if="[1, 3].includes(company.type) || companyStore().isPersonal"
          >
            <span>工作台</span>
            <DownOutlined ml-4 />
          </div>
          <template #overlay>
            <OrderInfo />
          </template>
        </a-dropdown>

        <a-dropdown v-if="isLogin">
          <div>
            <span cursor-pointer>{{ userText }}</span>
            <DownOutlined ml-4 />
          </div>
          <template #overlay>
            <div class="bg-white p-10 rounded-4 w-200">
              <div class="flex items-center">
                <a-avatar class="border-#797979 shrink-0" :src="user.pic">{{ user.nickName }}</a-avatar>
                <div ml-10 overflow-hidden>
                  <div class="text-16 font-bold">{{ user.nickName }}</div>
                  <div class="text-gray mt-4 truncate" :title="company.merchantName">
                    {{ company.merchantName }}
                  </div>
                </div>
              </div>
              <a-divider class="my-10"></a-divider>
              <div class="text-right">
                <div cursor-pointer hover="text-primary" @click="logout">
                  <LogoutOutlined />
                  <span ml-4>退出登录</span>
                </div>
              </div>
            </div>
            <!-- <a-menu> -->
            <!--   <a-menu-item @click="goWorkSpace">工作空间</a-menu-item> -->
            <!--   <a-menu-item @click="logout">退出登录</a-menu-item> -->
            <!-- </a-menu> -->
          </template>
        </a-dropdown>

        <div cursor-pointer v-else @click="login">登录</div>

        <!-- <a-dropdown>
          <div>
            <span ml-40>手机研选</span>
            <DownOutlined ml-4 />
          </div>
        </a-dropdown> -->
      </div>

      <div class="flex items-center header-content">
        <nuxt-link to="/" class="w-250 h-40">
          <img :src="Logo" class="h-full" alt="" />
        </nuxt-link>

        <div class="flex-1 mx-30 relative">
          <a-input-search
            v-model:value="value"
            size="large"
            :placeholder="placeholder"
            @search="onSearch"
            @click.stop="onFocus"
            @change="onChange"
          >
            <template #enterButton>
              <div>
                <SearchOutlined mr-8 />
                <span>搜索</span>
              </div>
            </template>
            <template #suffix>
              <div flex items-center text="primary" cursor-pointer @click="changeType">
                <div mr-8 text-14>{{ currentType.name }}</div>
                <OrderedListOutlined />
              </div>
            </template>
          </a-input-search>
          <div v-show="showSearchRecommend" class="selection-list-container shadow-md bg-white">
            <div
              class="selection-item h-32 flex items-center justify-between"
              v-for="(item, index) in searchHistory"
              :key="index"
              @click="onSelectionItemClick(item)"
            >
              <span text>{{ item.keyword }}</span>
              <a
                v-show="item.type == Type.history"
                font-size-14
                href="javascript:void(0)"
                class="text-primary"
                @click.stop="onDeleteHistoryClick(item)"
              >
                删除
              </a>
              <img v-if="item.brandLogo" h-full :src="item.brandLogo" alt="" />
            </div>
            <div h-32 flex-center v-if="!has(value) && searchHistory.length" @click.stop>
              <a-button type="link" @click.stop="clearHistory">清空搜索记录</a-button>
            </div>
          </div>
        </div>

        <!-- <a-button type="primary" ghost size="large">
          <div class="flex-center">
            <img class="mr-5" :src="Bot" alt="" />
            <span>智能导购</span>
          </div>
        </a-button> -->

        <!-- <a-button type="primary" size="large" ml-30 @click="goAns">
          <div class="flex-center">
            <PayCircleOutlined font-size-16 mr-8 />
            <span>询价器</span>
          </div>
        </a-button> -->
        <a-button type="primary" size="large" ml-30 @click="showChat">
          <div class="flex-center">
            <i class="i-a-robot-outlined" mr-8></i>
            <span>AI助手</span>
          </div>
        </a-button>
      </div>

      <div v-show="!isShrinked" mt-10 z-99 flex items-center relative @click.prevent>
        <a-button type="primary" class="w-250 h-50 text-18" @click.stop="toggleShow">
          <UnorderedListOutlined />
          <span>全部产品分类</span>
        </a-button>
        <div class="absolute flex top-50 h-430 z-10 w-[calc(100%-250px)]" v-show="show" ref="wrapRef">
          <category-tree v-model:show="showSide" @out-click="outClick"></category-tree>
        </div>
        <!-- <nuxt-link class="link! text-20 mx-25" to="/brand-join">加入研选工场</nuxt-link> -->
        <!-- <nuxt-link class="link! text-20 mx-25" to="/plugin">插件下载</nuxt-link> -->
      </div>
    </div>
    <SideWidget v-if="!path.startsWith('/workSpace') && !path.startsWith('/ans-price')" />
  </div>
  <slot></slot>
</template>

<script setup>
import Logo from '~/assets/images/yanxuan_logo.png'
import { userStore } from '~/store/user'
import { companyStore } from '~/store/company'
import { BellOutlined } from '@ant-design/icons-vue'
import { usemessageStore } from '~/store/message'
import SideWidget from './SideWidget.vue'
import usePart, { SearchType as Type } from '~/components/SearchBar/usePart'
import useLoginPop from '~/composables/useLoginPop'
import OrderInfo from './OrderInfo.vue'

const store = userStore()
const user = computed(() => store.user)
const { company } = companyStore()
const isLogin = computed(() => !!user.value.userId)

const userText = computed(() =>
  !!company.shopCompanyId
    ? `${user.value.nickName}@${company.merchantShortName || company.merchantName}`
    : user.value.nickName
)

const yanxuan = useRuntimeConfig().public.baseUrl.VITE_YANXUAN

const login = usegoLogin()
const logout = useLogout()

const goWorkSpace = () => {
  navigateTo('/workSpace/company-space/ans-price')
}

const show = ref(false)
const showSide = ref(true)
const route = useRoute()
const path = computed(() => route.path)

const toggleShow = () => {
  // if (route.path == '/') {
  //   return
  // }
  if (!show.value) {
    show.value = true
    showSide.value = true
  } else {
    show.value = false
  }
}

watch(
  () => route.path,
  () => {
    show.value = false
  }
)

// watchEffect(() => {
//   if (route.path == '/') {
//     show.value = true
//     showSide.value = true
//   } else {
//     show.value = false
//   }
// })

const outClick = () => {
  if (route.path == '/') {
    showSide.value = false
  }
}

const wrapRef = ref()
onClickOutside(
  wrapRef,
  () => {
    if (show.value) {
      show.value = false
    }
    // if (route.path != '/') {
    // }
  },
  {
    capture: false,
  }
)

const goAns = () => {
  useLoginPop(() => {
    useWorkspace(
      () => {
        message.error('个人会员无法询价，页面即将跳转到企业中心......')
      },
      () => {
        navigateTo('/ans-price')
      }
    )
  })
}

const value = ref(route.query.s || '')

const {
  placeholder,
  onSearch,
  onFocus,
  onChange,
  currentType,
  changeType,
  showSearchRecommend,
  searchHistory,
  clearHistory,
  onSelectionItemClick,
  onDeleteHistoryClick,
} = usePart(value)

const isShrinked = ref(false)

const messageStore = usemessageStore()
const unreadCount = computed(() => messageStore.msgCount)

// 定时刷新未读消息数
let timer

onMounted(() => {
  // 每分钟刷新一次
  timer = setInterval(() => {
    messageStore.updatemsgCount()
  }, 60000)

  window.addEventListener('scroll', handleScroll)
  loadAgent()
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
  window.removeEventListener('scroll', handleScroll)
})

const handleScroll = () => {
  isShrinked.value = window.scrollY > 160
}

const agent = ref()
const loadAgent = async () => {
  const [err, res] = await useCacheFetch('agent-0', () =>
    try_http('/mall/p/llm/agent/getConfigByAgentId', {
      query: getshopsign(
        {
          agentId: 0,
        },
        'get'
      ),
      headers: {
        grantType: 'sign',
      },
    })
  )
  if (!err) {
    agent.value = JSON.parse(res.data)
  }
}

const showChat = () => {
  chatPop.show('0')
}
</script>

<style lang="less" scoped>
.selection-list-container {
  border-bottom-left-radius: 2px;
  border-bottom-right-radius: 2px;
  text-align: left;
  background-color: #fff;
  position: absolute;
  z-index: 999;
  display: block;
  width: calc(100% - 1px);
  top: v-bind('offset');
  left: 0;

  .selection-item {
    padding: 8px 8px;
    cursor: pointer;

    &:hover {
      background-color: #e6f4ff;
    }
  }
}

.header-content {
  transition: all 0.3s ease;
  height: 60px;
}

.header-fixed {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  // .header-content {
  //   height: 60px;
  // }

  // .h-40 {
  //   height: 30px;
  // }
}
</style>
