<template>
  <div
    class="side-widget fixed left-4 top-1/2 -translate-y-1/2 z-50 flex flex-col items-center justify-between bg-white shadow-md"
  >
    <a-tooltip placement="right">
      <template #title>
        <span>研选首页</span>
      </template>
      <div w-32 h-50 bg-white rounded-full overflow-hidden flex items-center justify-center @click="onHome">
        <img w-32 h-32 src="~/assets/images/logo_squa_dark.png" cursor-pointer />
      </div>
    </a-tooltip>
    <div class="upper-buttons flex flex-col items-center">
      <div
        v-for="(item, index) in displayMenuItems"
        :key="index"
        class="menu-button w-48px h-50px flex items-center justify-center cursor-pointer"
        @click="navigateToPath(item.path)"
      >
        <div class="flex flex-col items-center">
          <a-tooltip :title="item.name" placement="right">
            <div class="relative">
              <div
                :class="item.icon"
                class="text-32"
                color-gray-9
                hover:color-primary
                transition-colors
                cursor-pointer
              ></div>
              <div
                v-if="item.path === '/message-center' && unreadCount > 0"
                class="absolute -top-2 -right-2 bg-red-500 text-white text-10px rounded-full min-w-16px h-16px flex items-center justify-center px-1"
              >
                {{ unreadCount > 99 ? '99+' : unreadCount }}
              </div>
            </div>
          </a-tooltip>
        </div>
      </div>
    </div>

    <div v-if="isLogin" class="avatar-container mb-2 h-50px flex items-center justify-center">
      <a-dropdown :trigger="['click']" placement="right">
        <div class="cursor-pointer p-2 hover:bg-gray-100 rounded-full transition-colors">
          <a-avatar :size="30" :src="userAvatar">
            {{ userInitial }}
          </a-avatar>
        </div>
        <template #overlay>
          <a-menu>
            <a-menu-item key="logout" @click="handleLogout">
              <logout-outlined />
              退出登录
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
    <div v-else h-50px flex items-center justify-center>
      <div cursor-pointer w-32 h-32 text-center line-height-32 rounded-full text-12px bg-gray-200 @click="onLogin">
        登录
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { UserOutlined, SettingOutlined, LogoutOutlined } from '@ant-design/icons-vue'
import { userStore } from '~/store/user'
import { companyStore } from '~/store/company'
import useLoginPop from '~/composables/useLoginPop'
import { usemessageStore } from '~/store/message'

const userStoreObj = userStore()
const { user } = storeToRefs(userStoreObj)
const { company } = companyStore()
const yanxuan = useRuntimeConfig().public.baseUrl.VITE_YANXUAN

const menuItems = [
  {
    name: '零部件库',
    icon: 'i-a-shop-outlined',
    path: '/',
  },
  {
    name: '设备商工作台',
    icon: 'i-a-appstore-outlined',
    path: '/workSpace',
  },
  {
    name: '消息通知',
    icon: 'i-a-bell-outlined',
    path: '/message-center',
  },
]

const userAvatar = computed(() => user.value.pic || '')
const userInitial = computed(() => user.value.nickName?.charAt(0) || '')

const login = usegoLogin()
const logout = useLogout()
const router = useRouter()
const isLogin = computed(() => !!user.value.userId)

const messageStore = usemessageStore()
const unreadCount = computed(() => messageStore.msgCount)

const displayMenuItems = computed(() => {
  return menuItems.filter((item) => {
    // For workSpace, require login AND company.type is 1 or 3
    if (item.path === '/workSpace') {
      return isLogin.value && (company.type === 1 || company.type === 3)
    }
    // For message-center, require login
    if (item.path === '/message-center') {
      return isLogin.value
    }
    // Show other items always
    return true
  })
})

const onHome = () => {
  if (yanxuan) {
    window.open(yanxuan, '_blank')
  } else {
    return
  }
}

const navigateToPath = (path: string) => {
  if (path === '/message-center') {
    window.open(yanxuan + '/message-center', '_blank')
  } else if (path === '/workSpace') {
    goWorkSpace()
  } else {
    router.push(path)
  }
}

const goWorkSpace = () => {
  if (!isLogin.value) {
    login()
    return
  }

  useWorkspace(() => {
    window.open('/workSpace/company-space/ans-price', '_blank')
  })
}

const onLogin = usegoLogin()

const handleLogout = () => {
  logout()
}
</script>

<style scoped>
.side-widget {
  font-size: 4px; /* 根据要求设置根字体大小 */
}
</style>
