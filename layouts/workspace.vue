<template>
  <div class="h-screen w-full overflow-hidden">
    <div class="h-64px bg-primaryBg px-24px flex items-center">
      <div class="flex items-end">
        <img
          src="~/assets/images/yanxuan_logo.png"
          class="w-150px h-auto mr-24px cursor-pointer"
          alt="logo"
          @click="goHome"
        />
        <nuxt-link to="/workSpace" class="text-18px font-bold text-#bbb!">
          {{ isPersonal ? '个人工作台' : '设备商工作台' }}
        </nuxt-link>
      </div>

      <div class="flex-1">
        <!-- <div class="max-w-800px w-full mx-auto"> -->
        <!--   <global-search :is-outer="true"></global-search> -->
        <!-- </div> -->
      </div>

      <a-tooltip placement="bottom" title="询价器">
        <a-badge class="mr-36px cursor-pointer" :count="store.ansPriceCount" :overflow-count="999">
          <MoneyCollectOutlined
            style="color: white; font-size: 24px"
            @click="navigateTo('/workSpace/company-space/quotation-tool')"
          />
        </a-badge>
      </a-tooltip>
      <a-tooltip placement="bottom" title="消息通知">
        <a-badge class="mr-36px cursor-pointer" :count="msgStore.msgCount" :overflow-count="999">
          <BellOutlined
            style="color: white; font-size: 24px"
            @click="navigateTo('/workSpace/company-space/notify-center')"
          />
        </a-badge>
      </a-tooltip>

      <a-tooltip title="联系客服" v-if="!isPersonal">
        <CustomerServiceOutlined
          style="margin-right: 36px; color: white; font-size: 24px"
          @click="navigateTo('/workSpace/company-space/customer')"
        />
      </a-tooltip>
      <!-- <a-tooltip placement="bottom" title="AI助手" @click="showAiChat"> -->
      <!--   <RobotOutlined style="margin-right: 36px; color: white; font-size: 24px" /> -->
      <!-- </a-tooltip> -->
      <!-- <a-tooltip placement="bottom" title="品牌馆"> -->
      <!--   <AppstoreOutlined style="margin-right: 36px; color: white; font-size: 24px" @click="navigateTo('/')" /> -->
      <!-- </a-tooltip> -->

      <a-dropdown trigger="click">
        <div class="flex items-center cursor-pointer">
          <a-avatar
            :src="useObs(user.pic)"
            size="large"
            :style="{ backgroundColor: 'white', color: '#1a1a2b', marginRight: '8px' }"
          >
            <template #icon>
              <UserOutlined />
            </template>
          </a-avatar>
          <div class="text-14px max-w-200px overflow-hidden">
            <div class="font-bold text-white">{{ user.nickName }}</div>
            <div class="text-#ccc truncate">{{ company.merchantShortName }}</div>
          </div>
        </div>
        <template #overlay>
          <a-menu>
            <template v-if="!isPersonal">
              <a-menu-item
                v-if="!isCompanySetting"
                @click="
                  navigateTo({
                    name: 'workSpace-settings',
                  })
                "
              >
                企业设置
              </a-menu-item>
              <a-menu-item
                v-else
                @click="
                  navigateTo({
                    name: 'workSpace',
                  })
                "
              >
                回到首页
              </a-menu-item>
              <a-menu-item v-if="eq(company.type, 2, 3)" @click="toVendor">供应商后台</a-menu-item>
              <a-menu-item v-else @click="joinVendor">成为供应商</a-menu-item>
            </template>
            <a-menu-item @click="logout">退出登录</a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>

    <div class="w-full h-[calc(100vh-64px)] overflow-hidden">
      <slot />
    </div>
  </div>
</template>

<script setup>
import { getCompany } from '~/api/common'
import { authMenuStore } from '~/store/authMenu'
import { companyStore } from '~/store/company'
import { messageStore } from '~/store/message'
import { sideWidgetsStore } from '~/store/sideWidgets'
import { userStore } from '~/store/user'

const user = computed(() => userStore().user)
const { company, isAdmin, isPersonal } = storeToRefs(companyStore())
const store = sideWidgetsStore()
const msgStore = messageStore()
const authStore = authMenuStore()

const yanxuan = useRuntimeConfig().public.baseUrl.VITE_YANXUAN
const route = useRoute()

const isCompanySetting = computed(() => {
  return authStore.menuSet.get(route.name) == 1
})

const goHome = () => {
  window.open(yanxuan)
}

const goMsg = () => {
  window.open(`${yanxuan}/message-center`)
}

const showAiChat = () => {
  chatPop.show('0')
}

const logout = useLogout()

const { manageBrand } = useManageBrand()
const toVendor = () => {
  manageBrand()
}

const joinVendor = async () => {
  if (!isAdmin.value) {
    message.error('您不是企业管理员，无法进行此操作')
    return
  }
  const companyInfo = await getCompany(company.value.shopCompanyId)
  if (companyInfo.status == 1 && (companyInfo.type == 3 || companyInfo.type == 2)) {
    message.warn('企业已开通供应商，无需重复开通')
    return
  }
  if (companyInfo.status == 3) {
    message.warn('当前企业正在审核中')
    return
  }
  if (companyInfo.status == 40) {
    Modal.confirm({
      title: '提示',
      content: '您有审核失败的企业，是否前往修改审核信息',
      onOk: async () => {
        window.open(`/brand-join/settle?companyId=${companyInfo.shopCompanyId}&type=edit`, '_blank')
      },
    })
    return
  }

  window.open(`/brand-join/settle?companyId=${companyInfo.shopCompanyId}`, '_blank')
}
</script>
