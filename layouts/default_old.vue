<template>
  <div class="selection-layout">
    <div h-132 v-if="fix"></div>
    <div :class="{
      'fixed w-full top-0 z-999': fix
    }">
      <global-header theme="dark" :fix="fixHeader"></global-header>
      <div class="banner" :class="{
        'transition-all': fix
      }" :style="{ height: h + 'px'}">
        <div class="banner__inner relative transition-all" :style="{
          top: top+ 'px'
        }">
          <NuxtLink to="/" class="logo-container">
            <img src="~/assets/images/logo_w.png" />
            <!-- <span inline-block color-white font-size-14 p-l-8 p-b-4>装备产业互联网平台</span> -->
          </NuxtLink>
          <div class="search-container mx-auto">
            <div class="search-container__inner">
              <div class="input-search-top">
              </div>
              <search-bar/>
              <div v-show="showSearchRecommend" class="selection-list-container">
                <div class="selection-item" v-for="item in mockRecommendList" :key="item.id" @click="onSelectionItemClick(item.text)">{{ item.text }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div flex items-center justify-center bg-white card>
      <div max-w-1280 w-full flex xl:w-full>
        <!-- <a-dropdown>
          <div inline-block p-y-8 p-x-12 title-text color-white font-size-16 mr-24 cursor-pointer bg-primary>
            <UnorderedListOutlined />
            商品类目
          </div>
          <template #overlay>
            <a-menu @click="onCategoryMenuClick">
              <CategoryMenuItem v-for="item in categoryTreeList" :key="item.id" :menuItem="item" />
            </a-menu>
          </template>
        </a-dropdown> -->
        <!-- <a-popover>
          <template #content>
            <p>该功能尚在开发中，敬请期待:)</p>
          </template>
          <div flex items-center title-text font-size-16 cursor-pointer hover:color-primary>
            <img h-24 mr-4 src="~/assets/images/daodao_normal.png" />
            AI选型
          </div>
        </a-popover> -->
      </div>
    </div>
    <div v-if="!err" class="page-container" :class="{ large: size === 'large' }" :style="{
      maxWidth: width + 'px'
    }">
      <slot />
    </div>
    <div v-else flex flex-col items-center justify-center :style="{ background: 'linear-gradient(to bottom, #E9F2FF, #89B6FF)' }">
      <img h-640 src="~/assets/images/daodao_error.jpg" :style="{ maskImage: 'radial-gradient(circle, black 35%, transparent 65%)' }" />
      <span font-size-40 color-primary mb-48>糟糕，系统似乎出了点问题……</span>
    </div>
    <div v-show="showScrollButton" class="scroll-button" :class="{ clicked: scrollToTopClicked }" @click="onScrollToTopClick">
      <ArrowUpOutlined :style="{ fontSize: '16px' }" />
      回顶部
    </div>
    <side-widgets/>

    <global-footer v-if="!err"></global-footer>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { ArrowUpOutlined } from '@ant-design/icons-vue';
import { mockRecommendList } from '~/mocks/selection';
import { treeStore } from '~/store/tree';

const props = withDefaults(defineProps<{
  fix?: boolean
}>(), {
  fix: true
})

const layoutCustomProps = useAttrs();
const size = ref<string>(layoutCustomProps.size as string);
const width = computed(() => layoutCustomProps.width || 1280)
const err = computed(() => {
  return layoutCustomProps.error;
});
interface TagItem {
  key: string;
  name: string;
  defaultPlaceholder: string;
}
const route = useRoute();
const searchText = ref(route.query.s as string);
const scrollToTopClicked = ref(false);

// watch(
//   () => route.query,
//   (to, from) => {
//     // 判断 query 参数是否发生变化
//     if (JSON.stringify(to) !== JSON.stringify(from)) {
//       // 执行重新加载页面的操作
//       window.location.reload();
//     }
//   }
// );

const showScrollButton = ref(false);

const showSearchRecommend = ref(false);

const tagList = ref<TagItem[]>([
  {
    key: 'mechanicalParts',
    name: '机械件',
    defaultPlaceholder: '线性导向轴',
  },
  {
    key: 'electricalPars',
    name: '气动件',
    defaultPlaceholder: '针型气缸',
  },
  {
    key: 'drawing',
    name: '图纸',
    defaultPlaceholder: '空气压缩机',
  },
]);
const currentTag = ref('mechanicalParts');

const computedCurrentTagItem = computed<TagItem | undefined>(() => {
  return tagList.value.find((ele) => ele.key === currentTag.value);
});

/**
 * 成为搜索字符串的placeholder
 */
const computedPurePlaceholder = computed(() => {
  return computedCurrentTagItem.value?.defaultPlaceholder;
});

/**
 * 显示在界面上的placeholder
 */
const computedShowPlaceholder = computed(() => {
  return `请输入关键词查找${computedCurrentTagItem.value?.name}，如“${computedPurePlaceholder.value}”`;
});

const fixHeader = ref(false)
const h = ref(100)
const top = ref(20)
const handleScroll = useRafFn(() => {
  showScrollButton.value = window.scrollY > window.innerHeight / 2;
  if (!props.fix) return
  fixHeader.value = window.scrollY > 0
  if (window.scrollY < 40) {
    h.value = 100 - window.scrollY
    top.value = 20 - window.scrollY
  } else {
    h.value = 60
    top.value = -20
  }
});

const onScrollToTopClick = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth',
  });
  scrollToTopClicked.value = true;
  setTimeout(() => {
    scrollToTopClicked.value = false;
  }, 1000);
};

const onSelectionItemClick = (text: string) => {};

onMounted(() => {
  
});

// @ts-ignore
useEventListener('scroll', handleScroll)
</script>

<style lang="less" scoped>
.selection-layout {
  position: relative;
  background-color: #eff2f7;
  min-height: 100vh;
  padding-bottom: 30px;

  // .global-header-component {
  //   position: absolute;
  //   width: 100%;
  //   top: 0;
  //   z-index: 1;
  //   background-color: transparent;
  // }
  .banner {
    background-color: #1c344f;
    &__inner {
      @apply w-full xl:w-1280 left-0 mx-auto;
      // text-align: center;

      .logo-container {
        @apply max-xl:hidden;
        position: absolute;
        left: 0;
        top: 0px;
        z-index: 1;
        img {
          height: 35px;
          vertical-align: bottom;
        }
      }
      .search-container {
        @apply w-600 max-xl:w-full max-xl:px-20;
        // width: 600px;
        &__inner {
          .input-search-top {
            display: flex;
            align-items: flex-end;
            .daodao {
              display: inline-block;
              width: 40px;
              margin: 0 20px 0 16px;
            }
            .search-tag-list {
              text-align: left;
              margin-bottom: 8px;
              .search-tag {
                padding: 3px 4px;
                font-size: 12px;
                color: #fff;
                margin-right: 32px;
                border-radius: 2px;
                cursor: pointer;
                &.selected {
                  position: relative;
                  background-color: #fff;
                  color: #1c344f;
                  &::after {
                    content: '';
                    position: absolute;
                    bottom: -5px;
                    left: 50%;
                    transform: translateX(-50%);
                    border-left: 5px solid transparent;
                    border-right: 5px solid transparent;
                    border-top: 5px solid #fff;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  .page-container {
    @apply w-full mx-auto;
    &.large {
      margin-left: 64px;
      width: calc(100vw - 128px);
    }
  }

  .global-footer-component {
    position: absolute;
    width: 100%;
    bottom: 0;
  }

  .scroll-button {
    position: fixed;
    display: flex;
    padding: 6px 0;
    width: 50px;
    height: 50px;
    bottom: 5px;
    right: 5px;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 3px 25px #1c344f1a;
    background-color: #fff;
    border-radius: 2px;
    font-size: 12px;
    z-index: 999;
    cursor: pointer;
    transition: box-shadow 0.2s ease;

    /* 样式其他属性 */
    &:hover {
      box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.3);
    }

    &.clicked {
      animation: rocket 1s forwards;
    }

    @keyframes rocket {
      0% {
        transform: translateY(0);
        opacity: 1;
      }
      50% {
        transform: translateY(-300px);
        opacity: 0.5;
      }
      100% {
        transform: translateY(-600px);
        opacity: 0;
      }
    }
  }

  .badge {
    transition: all 0.5s;
  }

  .explode {
    transform: scale(5);
    opacity: 0.5;
  }
}
</style>
