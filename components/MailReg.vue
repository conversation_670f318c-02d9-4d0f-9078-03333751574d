<template>
  <a-form w="80%" :wrapper-col="{ span: 24 }">
    <a-config-provider component-size="large">
      <a-form-item v-bind="validateInfos.name">
        <a-input v-model:value="form.name" placeholder="用户名"></a-input>
      </a-form-item>

      <a-form-item v-bind="validateInfos.email">
        <a-input v-model:value="form.email" placeholder="邮箱"></a-input>
      </a-form-item>

      <a-form-item v-bind="validateInfos.verifyCode">
        <msg-input
          v-model="form.verifyCode"
          :send-api="sendMsg"
          placeholder="验证码"
        ></msg-input>
      </a-form-item>

      <a-form-item v-bind="validateInfos.password">
        <a-input-password
          v-model:value="form.password"
          placeholder="密码"
          autocomplete="new-password"
        ></a-input-password>
      </a-form-item>

      <a-form-item v-bind="validateInfos.rePassword">
        <a-input-password
          v-model:value="form.rePassword"
          placeholder="确认密码"
          autocomplete="new-password"
        ></a-input-password>
      </a-form-item>
    </a-config-provider>

    <a-form-item class="mb-12!" v-bind="validateInfos.identity">
      <a-select
        v-model:value="form.identity"
        size="large"
        placeholder="身份"
        allowClear
      >
        <a-select-option v-for="item in identities" :key="item" :value="item">
          {{ item }}
        </a-select-option>
      </a-select>
    </a-form-item>

    <a-form-item v-bind="validateInfos.checked">
      <a-checkbox size="small" v-model:checked="form.checked">
        <span>
          同意
          <NuxtLink :to="{ path: '/agreement/termsOfService' }" target="_blank">
            《用户协议》
          </NuxtLink>
          <NuxtLink :to="{ path: '/agreement/disclaimer' }" target="_blank">
            《免责声明》
          </NuxtLink>
          <NuxtLink :to="{ path: '/agreement/privacyPolicy' }" target="_blank">
            《隐私政策》
          </NuxtLink>
          <NuxtLink :to="{ path: '/agreement/scoreConfig' }" target="_blank">
            《积分规则》
          </NuxtLink>
        </span>
      </a-checkbox>
    </a-form-item>

    <a-button
      w-full
      type="primary"
      size="large"
      mb-12
      @click="register"
      :loading="regLoading"
    >
      注册
    </a-button>
  </a-form>
</template>

<script setup lang="ts">
import { registerCommit, registerInviteCommit, sendEmailReg } from '~/api/user'
import { Form } from 'ant-design-vue'
import { pick } from 'lodash-es'

const { useForm } = Form
const props = defineProps<{
  inviteCode?: string
}>()

const identities = ['工程师', '采购', '供应商', '其他']

const form = ref({
  identity: undefined,
  name: '',
  email: '',
  password: '',
  rePassword: '',
  verifyCode: '',
  checked: false,
})

const rules = reactive({
  identity: [{ required: true, message: '请选择身份' }],
  name: [
    {
      required: true,
      message: '请输入用户名',
    },
    {
      pattern: USER_NAME_REG.reg,
      message: USER_NAME_REG.msg,
    },
  ],
  email: [
    {
      required: true,
      message: '请输入邮箱',
    },
    {
      pattern: EMAIL_REG.reg,
      message: EMAIL_REG.msg,
    },
  ],
  password: [
    { required: true, message: '请输入密码' },
    {
      pattern: PASSWORD_REG.reg,
      message: PASSWORD_REG.msg,
    },
  ],
  verifyCode: [{ required: true, message: '请输入验证码' }],
  rePassword: [
    { required: true, message: '请再输入一次密码' },
    {
      validator(_, value, callback) {
        console.log('%c Line:129 🥛 value', 'color:#2628C1', value)
        if (value != form.value.password) {
          callback(new Error('密码输入不一致'))
        } else {
          callback()
        }
      },
    },
  ],
  checked: [
    {
      validator: (_rule, value, callback) => {
        if (!value) {
          callback(new Error('请点击同意用户协议'))
        } else {
          callback()
        }
      },
    },
  ],
})

const { validateInfos, validate } = useForm(form, rules)

const sendMsg = async () => {
  try {
    await validate('email')
    const res = await sendEmailReg({
      email: form.value.email,
    })
    return await useRes(res)
  } catch (error) {
    return false
  }
}

const regLoading = ref(false)

const register = () => {
  validate().then(async () => {
    const data = pick(form.value, [
      'email',
      'name',
      'verifyCode',
      'password',
      'identity',
    ])
    let registerApi = registerCommit
    if (props.inviteCode) {
      // @ts-ignore
      data.inviteCipher = props.inviteCode
      registerApi = registerInviteCommit
    }

    regLoading.value = true
    const res = await registerApi(data)
    regLoading.value = false
    useRes(res, () => {
      message.success('注册成功，请登录')
      navigateTo({
        path: '/login',
        query: {
          type: 'LOGIN',
        },
      })
    })
  })
}
</script>

<style lang="less" scoped>
:deep(.ant-select-selection-placeholder){
  color: #d9d9d9 !important;
  font-weight: 100;
}
</style>