<template>
  <a-range-picker v-model:value="time" show-time format="YYYY-MM-DD HH:mm" value-format="YYYY-MM-DD HH:mm:ss"
    :placeholder="[startPlaceholder, endPlaceholder]" style="width: 100%"
    :show-time-options="{ defaultValue: [dayjs('00:00:00'), dayjs('23:59:59')] }" />
</template>

<script setup>
import dayjs from 'dayjs';

const value = defineModel()

defineProps({
  startPlaceholder: {
    type: String,
    default: '开始时间',
  },
  endPlaceholder: {
    type: String,
    default: '结束时间',
  },
})

const time = computed({
  get() {
    if (value.value) {
      const { beginTime, endTime } = value.value
      return [beginTime, endTime]
    } else {
      return undefined
    }
  },
  set(val) {
    if (val) {
      const [beginTime, endTime] = val
      value.value = {
        beginTime,
        endTime,
      }
    } else {
      value.value = undefined
    }
  },
})
</script>
