<template>
  <a-select
    label-in-value
    mode="multiple"
    v-model:value="selects"
    @search="fetchBrands"
    @dropdown-visible-change="changeVisible"
    :filter-option="false"
    :not-found-content="loading ? undefined : null"
    :getPopupContainer="(el) => el.parentNode"
    placeholder="请选择"
  >
    <template #notFoundContent v-if="loading">
      <a-spin size="small"></a-spin>
    </template>
    <a-select-option
      v-for="item in brandList"
      :key="item.brandId"
      :value="item.brandId"
      :label="item.name"
      :disabled="disableSet.has(item.brandId)"
    >
      {{ item.name }}
    </a-select-option>
  </a-select>
</template>

<script setup>
const value = defineModel({ default: () => [] })
const brandList = ref([])

const loading = ref(false)
let lastId = 0

defineProps({
  disableSet: {
    type: Set,
    default: () => new Set(),
  },
})

const getBrands = async (kw = '') => {
  lastId++
  const currentId = lastId
  brandList.value = []
  loading.value = true
  const [err, res] = await try_http('/mall/brand/list', {
    params: {
      status: 1,
      name: kw,
    },
  })
  loading.value = false
  if (!err) {
    if (currentId != lastId) return
    brandList.value = res.data
  }
}

const fetchBrands = useDebounceFn(async (kw = '') => {
  getBrands(kw)
}, 500)

const selects = computed({
  get() {
    if (!value.value) return []
    return value.value.map((item) => {
      return {
        value: item.brandId,
        label: item.brandName,
      }
    })
  },
  set(list) {
    value.value = list.map((item) => {
      return {
        brandName: item.option?.label ?? item.label,
        brandId: item.value,
      }
    })
  },
})

const changeVisible = (open) => {
  if (open) {
    getBrands('')
  }
}

// onMounted(() => {
//   fetchBrands('')
// })
</script>
