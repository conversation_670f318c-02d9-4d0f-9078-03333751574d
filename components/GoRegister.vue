<template>
  <a-modal v-model:open="show" :footer="null" :width="400" class="register-m" relative>
    <template v-if="type == TYPE.register">
      <img w-full src="~@/assets/images/tour.jpg" alt="" />
      <a-button absolute bottom-20 right-20 size="large" type="primary" @click="goRegister">立即注册</a-button>
    </template>

    <template v-if="type == TYPE.member">
      <img w-full src="/special.jpg" alt="" />
      <a-button absolute bottom-20 right-20 size="large" type="primary">
        <nuxt-link to="/my/member">了解详情</nuxt-link>
      </a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { MemberLevel, userStore } from '~/store/user'

enum TYPE {
  register,
  member,
}

const props = defineProps<{
  immediate?: boolean
}>()

const duration = 60 * 60 * 1000

const user = userStore()
const show = ref(false)

const [registerTime, setRegisterTime] = useTime('_show_register_time')
const [memberTime, setMemberTime] = useTime('_show_member_time')

const isLogin = computed(() => !!user.user.userId)

const time = computed(() => {
  if (!isLogin.value) return registerTime.value
  return memberTime.value
})

const type = computed(() => {
  if (!isLogin.value) return TYPE.register
  return TYPE.member
})

onMounted(() => {
  if (props.immediate) {
    if (!time.value || new Date().getTime() - time.value >= duration) {
      show.value = true
      if (type.value == TYPE.register) {
        setRegisterTime(new Date().getTime())
      } else if (type.value == TYPE.member) {
        setMemberTime(new Date().getTime())
      }
    }
  }
})

useEventListener('scroll', () => {
  if (type.value == TYPE.register) {
    const isShow = window.scrollY > window.innerHeight / 2

    if (!time.value) {
      if (props.immediate || isShow) {
        show.value = true
        setRegisterTime(new Date().getTime())
      }
    } else {
      const currentTime = new Date().getTime()
      if (currentTime - time.value >= duration && (props.immediate || isShow)) {
        show.value = true
        setRegisterTime(currentTime)
      }
    }
  } else {
    const isPro = isLevel(user.user.level, MemberLevel.PROFESSIONAL)
    if (!isPro) {
      const isShow = window.scrollY > window.innerHeight / 2

      if (!time.value) {
        if (props.immediate || isShow) {
          show.value = true
          setMemberTime(new Date().getTime())
        }
      } else {
        const currentTime = new Date().getTime()
        if (currentTime - time.value >= duration && (props.immediate || isShow)) {
          show.value = true
          setMemberTime(currentTime)
        }
      }
    }
  }
})

const goRegister = () => {
  navigateTo({
    path: '/login',
    query: {
      type: 'REGISTER',
    },
  })
}
</script>
