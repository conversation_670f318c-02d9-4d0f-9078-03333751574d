<template>
  <div
    v-if="type == 'img'"
    relative
    :title="item.optionName"
    cursor-pointer
    @click="emits('clickItem', item)"
    border-2
    border-transparent
    w-110
    h-110
    flex
    flex-col
    items-center
    hover="border-primary rounded-4"
    :class="{
      'filter__image-option--disabled!': item.disabled,
      'border-primary!': item.selected,
      'hidden!': hide,
    }"
  >
    <div w-70 h-70 mt-5>
      <img
        w-full
        h-full
        object-scale-down
        :src="useImage(item.optionValueImgUrl)"
        :class="{
          'image-disabled': item.disabled,
        }"
        alt=""
      />
    </div>
    <div
      line-clamp-2
      w-90
      text-center
      :class="{
        'color-#00000040': item.disabled,
      }"
    >
      {{ item.optionName }}
    </div>

    <div
      absolute
      left-0
      bottom-0
      w-16
      h-16
      color-white
      bg-primary
      line-height-16
      text-center
      font-size-12
      v-show="item.selected"
    >
      <CheckOutlined />
    </div>
  </div>
  <a-checkable-tag
    v-else
    :checked="item.selected"
    :key="item.optionName"
    :class="{ 'filter__option--disabled! bg-transparent!': item.disabled }"
    @click="emits('clickItem', item)"
  >
    {{ item.optionName }}
  </a-checkable-tag>
</template>

<script lang="ts" setup>
import { FilterOption } from '~/api/search'

const emits = defineEmits<{
  clickItem: [item: FilterOption]
}>()

const props = defineProps<{
  item: FilterOption
  type: 'img' | 'tag'
  webSet: {
    select: Set<string>
    canuse: Set<string>
  }
  hide?: boolean
}>()

const { item, webSet } = toRefs(props)

const websites = computed(() => {
  const webList = JSON.parse(item.value.optionValueSiteCodes) as string[]
  return webList
})

const disabled = computed(() => {
  const { select, canuse } = webSet.value
  if (select.size == 0) {
    if (websites.value.some((item) => canuse.has(item))) return false
    return true
  }
  if (websites.value.some((item) => select.has(item))) return false
  return true
})

watchEffect(() => {
  item.value.disabled = disabled.value
  if (disabled.value && item.value.selected) {
    item.value.selected = false
  }
})
</script>
