<template>
  <div w-full overflow-hidden relative ref="rootRef">
    <div
      class="flex gap-10 text-12 transition-all"
      :class="{ 'flex-wrap': expand }"
    >
      <template v-for="(item, idx) in list" :key="idx">
        <slot
          v-if="$slots.default"
          :item="item"
          :hide="max && idx >= max && !expand && init"
        ></slot>

        <div
          relative
          :title="item[label]"
          cursor-pointer
          @click="emits('clickItem', item)"
          border-2
          border-transparent
          w-110
          h-110
          flex
          flex-col
          items-center
          v-else
          hover="border-primary rounded-4"
          :class="{
            'filter__image-option--disabled!': disableFn
              ? disableFn(item)
              : item.disabled,
            'border-primary!': item.selected,
            'hidden!': max && idx >= max && !expand && init,
          }"
        >
          <div w-70 h-70 mt-5>
            <img
              w-full
              h-full
              object-scale-down
              :src="useImage(item[link])"
              :class="{
                'image-disabled': disableFn ? disableFn(item) : item.disabled,
              }"
              alt=""
            />
          </div>
          <div
            line-clamp-2
            w-90
            text-center
            :class="{
              'color-#00000040': disableFn ? disableFn(item) : item.disabled,
            }"
          >
            {{ item[label] }}
          </div>

          <div
            absolute
            left-0
            bottom-0
            w-16
            h-16
            color-white
            bg-primary
            line-height-16
            text-center
            font-size-12
            v-show="item.selected"
          >
            <CheckOutlined />
          </div>
        </div>
      </template>
      <a-button
        absolute
        right-0
        top-40
        type="link"
        self-center
        @click="expand = !expand"
        v-if="needExpand"
      >
        <template v-if="expand">
          <UpOutlined />
          收起
        </template>
        <template v-else="expand">
          <DownOutlined />
          展开
        </template>
      </a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = withDefaults(
  defineProps<{
    list: Obj[]
    label?: string
    link?: string
    disableFn?: (item: any) => boolean
  }>(),
  {
    list: () => [],
    label: 'label',
    link: 'link',
  },
)

const emits = defineEmits<{
  clickItem: [value: Obj]
  update: []
}>()

const W = 110

const rootRef = ref(null)
const expand = ref(false)
const needExpand = ref(false)
const max = ref()
const init = ref(false)
const len = computed(() => props.list.length)
useResizeObserver(rootRef, (entries) => {
  const entry = entries[0]
  const { width } = entry.contentRect
  const n = (((width - W - 8) / (W + 10)) >> 0) + 1
  const divide = width - 8 + 10 - (W + 10) * n
  if (n < len.value) {
    needExpand.value = true
    if (divide < 60) {
      max.value = n - 1
    } else {
      max.value = n
    }
  } else {
    needExpand.value = false
    expand.value = false
  }
  init.value = true
  emits('update')
})
</script>
