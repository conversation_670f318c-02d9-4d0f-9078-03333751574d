<template>
  <a-modal
    v-model:open="visible"
    :footer="null"
    :width="modalWidth"
    class="member-guide-modal"
    centered
    :z-index="2000"
    :mask-closable="false"
    @cancel="handleClose"
  >
    <div class="member-guide-container">
      <!-- 动态提示区域 -->
      <div class="notice-section">
        <div class="notice-header" :class="`notice-warning`">
          <div class="notice-icon-wrapper">
            <component
              :is="getNoticeIcon(noticeInfo.type)"
              class="notice-icon"
              :class="`notice-icon-${noticeInfo.type}`"
            />
          </div>
          <div class="notice-content">
            <h2 class="notice-title">{{ noticeInfo.title }}</h2>
            <p v-if="noticeInfo.subtitle" class="notice-subtitle">{{ noticeInfo.subtitle }}</p>
          </div>
        </div>

        <!-- 详细内容区域 -->
        <div v-if="noticeInfo.content" class="notice-body">
          <div class="notice-text" v-html="noticeInfo.content"></div>
        </div>
      </div>

      <!-- 功能介绍区域 -->
      <div v-if="showFeatures" class="features-section">
        <div
          v-for="(group, index) in featureGroups"
          :key="index"
          class="feature-group"
          :class="{ 'feature-group-left': index % 2 === 0, 'feature-group-right': index % 2 === 1 }"
        >
          <div class="feature-header">
            <div class="icon-wrapper bg-gradient-to-br" :class="group.gradientClass">
              <component :is="getIconComponent(group.icon)" class="text-white text-20px" />
            </div>
            <div class="header-content">
              <h3>{{ group.title }}</h3>
              <p>{{ group.subtitle }}</p>
            </div>
          </div>

          <div v-for="(item, itemIndex) in group.items" :key="itemIndex" class="feature-item">
            <component :is="getIconComponent(item.icon)" class="feature-icon" />
            <div class="feature-content">
              <h4>{{ item.title }}</h4>
              <p>{{ item.description }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作区域 -->
      <div class="footer-section">
        <a-button type="primary" size="large" class="upgrade-btn" @click="handleUpgrade">{{ buttonText }}</a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import {
  CloseOutlined,
  SettingOutlined,
  TeamOutlined,
  FileTextOutlined,
  MessageOutlined,
  CreditCardOutlined,
  SyncOutlined,
  CheckCircleOutlined,
  RobotOutlined,
  UserOutlined,
  InfoCircleOutlined,
  ExclamationCircleOutlined,
  CheckCircleFilled,
  WarningFilled,
} from '@ant-design/icons-vue'

// 定义数据结构 (使用JSDoc注释)
/**
 * @typedef {Object} NoticeInfo
 * @property {string} title - 标题
 * @property {string} [subtitle] - 副标题
 * @property {string} [content] - 内容
 * @property {'info'|'warning'|'success'|'error'} [type] - 类型
 * @property {string} [icon] - 图标
 */

/**
 * @typedef {Object} FeatureItem
 * @property {string} icon - 图标名称
 * @property {string} title - 标题
 * @property {string} description - 描述
 */

/**
 * @typedef {Object} FeatureGroup
 * @property {string} icon - 图标名称
 * @property {string} title - 标题
 * @property {string} subtitle - 副标题
 * @property {FeatureItem[]} items - 功能项列表
 * @property {string} gradientClass - 渐变类名
 */
const visible = ref(false)
const noticeInfo = ref({
  title: '',
  subtitle: '升级为设备商企业会员，解锁全平台功能，享受专属企业服务',
  type: 'warning',
})
const show = (title = '该功能需要在设备商工作台中才可使用') => {
  noticeInfo.value.title = title
  visible.value = true
}

defineExpose({
  show,
})

const props = defineProps({
  // 提示信息配置
  // 是否显示功能介绍区域
  showFeatures: {
    type: Boolean,
    default: true,
  },
  // 自定义功能组配置
  featureGroups: {
    type: Array,
    default: () => [
      {
        icon: 'SettingOutlined',
        title: '全流程采购管理能力',
        subtitle: '从需求到交付，打造完整的采购管理闭环',
        gradientClass: 'from-orange-400 to-red-500',
        items: [
          { icon: 'FileTextOutlined', title: '采购BOM管理', description: '支持物料清单的创建与管理' },
          { icon: 'MessageOutlined', title: '询价 & 下单', description: '不限次数发起询价并完成采购订单' },
          { icon: 'CreditCardOutlined', title: '对账 & 付款', description: '提供财务对账和在线支付功能' },
        ],
      },
      {
        icon: 'TeamOutlined',
        title: '企业级平台支持服务',
        subtitle: '专业服务团队，为企业采购保驾护航',
        gradientClass: 'from-yellow-400 to-orange-500',
        items: [
          { icon: 'CheckCircleOutlined', title: '账期结算', description: '灵活的企业账期支付，优化现金流' },
          { icon: 'RobotOutlined', title: 'AI客服', description: '智能解答采购问题' },
          { icon: 'UserOutlined', title: '专属采购助理', description: '人工服务支持复杂需求' },
        ],
      },
    ],
  },
  // 按钮文本
  buttonText: {
    type: String,
    default: '立即升级',
  },
  // 模态框宽度
  modalWidth: {
    type: String,
    default: '800px',
  },
})

const emit = defineEmits(['update:modelValue', 'upgrade'])

// 图标映射函数
const getIconComponent = (iconName) => {
  const iconMap = {
    SettingOutlined,
    TeamOutlined,
    FileTextOutlined,
    MessageOutlined,
    CreditCardOutlined,
    SyncOutlined,
    CheckCircleOutlined,
    RobotOutlined,
    UserOutlined,
    InfoCircleOutlined,
    ExclamationCircleOutlined,
    CheckCircleFilled,
    WarningFilled,
  }
  return iconMap[iconName] || InfoCircleOutlined
}

// 根据提示类型获取对应图标
const getNoticeIcon = (type) => {
  const iconMap = {
    info: InfoCircleOutlined,
    warning: ExclamationCircleOutlined,
    success: CheckCircleFilled,
    error: WarningFilled,
  }
  return iconMap[type || 'info'] || InfoCircleOutlined
}

const handleClose = () => {
  visible.value = false
}

const handleUpgrade = () => {
  emit('upgrade')
  visible.value = false
}
</script>

<style lang="less" scoped>
.member-guide-modal {
  :deep(.ant-modal-content) {
    padding: 0 !important;
    background: white !important;
    border-radius: 16px !important;
    overflow: hidden !important;
  }

  :deep(.ant-modal-body) {
    padding: 0 !important;
    background: transparent !important;
  }

  :deep(.ant-modal-mask) {
    background: rgba(0, 0, 0, 0.6) !important;
  }
}

.member-guide-container {
  position: relative;
  padding: 20px 8px;
  color: #333 !important;
  background: white;
  min-height: 500px;

  .close-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;

    &:hover {
      background: rgba(0, 0, 0, 0.2);
    }

    .anticon {
      color: #666 !important;
    }
  }

  // 动态提示区域样式
  .notice-section {
    margin-bottom: 32px;

    .notice-header {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      padding: 20px;
      border-radius: 12px;
      border-left: 4px solid;
      margin-bottom: 16px;
      transition: all 0.3s ease;

      &.notice-info {
        background: linear-gradient(135deg, #e3f2fd 0%, #f0f9ff 100%);
        border-left-color: #2196f3;
      }

      &.notice-warning {
        background: linear-gradient(135deg, #fff8e1 0%, #fffbf0 100%);
        border-left-color: #ff9800;
      }

      &.notice-success {
        background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
        border-left-color: #4caf50;
      }

      &.notice-error {
        background: linear-gradient(135deg, #ffebee 0%, #fdf2f2 100%);
        border-left-color: #f44336;
      }

      .notice-icon-wrapper {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        margin-top: 4px;

        .notice-icon {
          font-size: 24px;

          &.notice-icon-info {
            color: #2196f3;
          }

          &.notice-icon-warning {
            color: #ff9800;
          }

          &.notice-icon-success {
            color: #4caf50;
          }

          &.notice-icon-error {
            color: #f44336;
          }
        }
      }

      .notice-content {
        flex: 1;

        .notice-title {
          font-size: 24px;
          font-weight: 700;
          margin: 0 0 8px 0;
          color: #333 !important;
          line-height: 1.3;
        }

        .notice-subtitle {
          font-size: 16px;
          color: #666 !important;
          margin: 0;
          line-height: 1.5;
        }
      }
    }

    .notice-body {
      padding: 16px 20px;
      background: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;

      .notice-text {
        font-size: 14px;
        line-height: 1.6;
        color: #555 !important;

        :deep(p) {
          margin: 0 0 12px 0;

          &:last-child {
            margin-bottom: 0;
          }
        }

        :deep(ul),
        :deep(ol) {
          margin: 8px 0 12px 0;
          padding-left: 20px;
        }

        :deep(li) {
          margin-bottom: 4px;
        }

        :deep(strong) {
          color: #333 !important;
          font-weight: 600;
        }

        :deep(em) {
          color: #666 !important;
          font-style: italic;
        }
      }
    }
  }

  .features-section {
    display: flex;
    gap: 40px;
    flex-wrap: wrap;

    .feature-group {
      flex: 1;
      min-width: 300px;
    }

    .feature-header {
      margin-bottom: 24px;
      display: flex;
      align-items: flex-start;
      gap: 16px;

      .icon-wrapper {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        margin-top: 4px;
      }

      .header-content {
        flex: 1;

        h3 {
          font-size: 20px;
          font-weight: 600;
          color: #333 !important;
          margin: 0 0 8px 0;
        }

        p {
          font-size: 14px;
          color: #666 !important;
          margin: 0;
        }
      }
    }

    .feature-item {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      margin-bottom: 20px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 12px;
      border: 1px solid #e9ecef;
      transition: all 0.3s ease;

      &:hover {
        background: #f1f3f4;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .feature-icon {
        font-size: 20px;
        color: #666;
        margin-top: 2px;
        flex-shrink: 0;
      }

      .feature-content {
        h4 {
          font-size: 16px;
          font-weight: 600;
          color: #333 !important;
          margin: 0 0 4px 0;
        }

        p {
          font-size: 13px;
          color: #666 !important;
          margin: 0;
          line-height: 1.4;
        }
      }
    }
  }

  .footer-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;

    .upgrade-btn {
      background: linear-gradient(45deg, #f94c30, #ff6b4a);
      border: none;
      border-radius: 24px;
      height: 48px;
      padding: 0 32px;
      font-size: 16px;
      font-weight: 600;
      box-shadow: 0 4px 15px rgba(249, 76, 48, 0.4);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(249, 76, 48, 0.6);
        background: linear-gradient(45deg, #ff5a38, #ff7652);
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .member-guide-container {
    padding: 16px;

    .notice-section {
      .notice-header {
        flex-direction: column;
        gap: 12px;
        padding: 16px;

        .notice-icon-wrapper {
          align-self: center;
          width: 40px;
          height: 40px;
        }

        .notice-content {
          text-align: center;

          .notice-title {
            font-size: 24px;
          }

          .notice-subtitle {
            font-size: 14px;
          }
        }
      }

      .notice-body {
        padding: 12px 16px;

        .notice-text {
          font-size: 13px;
        }
      }
    }

    .features-section {
      flex-direction: column;
      gap: 24px;

      .feature-group {
        min-width: unset;
      }

      .feature-header {
        .header-content h3 {
          font-size: 18px;
        }

        .header-content p {
          font-size: 13px;
        }
      }

      .feature-item {
        padding: 12px;

        .feature-content h4 {
          font-size: 15px;
        }

        .feature-content p {
          font-size: 12px;
        }
      }
    }

    .footer-section {
      flex-direction: column;
      gap: 16px;
      text-align: center;

      .upgrade-btn {
        height: 44px;
        font-size: 15px;
      }
    }
  }

  .member-guide-modal {
    :deep(.ant-modal-content) {
      margin: 16px !important;
    }
  }
}
</style>
