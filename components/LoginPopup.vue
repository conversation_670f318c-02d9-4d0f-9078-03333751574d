<template>
  <transition name="login">
    <div
      class="loginMask"
      flex
      flex-col
      justify-center
      items-center
      id="loginMask"
      @click="hideLoginPopup"
      v-show="show"
    >
      <div class="relative w-566 h-412">
        <iframe :src="loginurl" frameborder="0" class="w-full h-full overflow-hidden"></iframe>
        <CloseOutlined
          class="absolute top-16 right-16 font-size-20 text-white cursor-pointer"
          @click="hideLoginPopup"
        />
      </div>
    </div>
  </transition>
</template>

<script lang="ts" setup>
import { message } from 'ant-design-vue'

import { storeToRefs } from 'pinia'
import { userStore } from '~/store/user'
import { getUserDetail } from '~/api/user'
import { companyStore } from '~/store/company'
import { authMenuStore } from '~/store/authMenu'
import { getMerchantByMerchantId } from '~/api/mall-manage/index'
const config = useRuntimeConfig()

const loginurl = computed(() => config.public.baseUrl.VITE_YANXUAN + '/login?isIframe=1')

const userStoreObj = userStore()
const { user } = storeToRefs(userStoreObj)
const { setUser, clearUser } = userStoreObj

const show = ref(false)
const afterLogin = ref<Function>()

const showLoginPopup = (callback?: Function) => {
  message.destroy()
  message.info('请先登录后再进行操作')
  show.value = true
  if (callback) {
    afterLogin.value = callback
  }
}

const hideLoginPopup = () => {
  show.value = false
}

const useAuthMenu = authMenuStore()
const loginFinish = () => {
  getUserDetail()
    .then((res) => {
      useMall(res, async () => {
        setUser(res.data)
        hideLoginPopup()

        const _com = await getCompany(res.data.userMobile)
        await useMall(_com, async (ret) => {
          if (ret && ret.merchantId) {
            // 查询企业
            const __com = await getMerchantByMerchantId({ merchantId: ret.merchantId })
            await useMall(__com, async (ret) => {
              const _ret = JSON.parse(ret)
              company.setCompany({ ..._ret })
              // 更新菜单
              if (_ret.shopCompanyId) {
                const menuData = await getAuthMenu(_ret.shopCompanyId)
                await useMall(menuData, async (ret) => {
                  useAuthMenu.setAuthMenu({
                    ...ret,
                    loadedMenu: true,
                  })
                })
              }
            })
          } else {
            company.setCompany({
              shopCompanyId: 0, // 个人空间
            })
          }
        })

        afterLogin.value?.()
      })
    })
    .catch((err) => {
      const { status } = err
      console.log(err)
    })
}

const company = companyStore()
const getCompany = async (userMobile) => {
  const res = await http('/mall/shop/userSpace/getOneByCond', {
    method: 'get',
    params: {
      userMobile,
    },
  })
  return res
}

const getAuthMenu = async (enterpriseId) => {
  const res = await http(`/mall/p/sys/enterpriseMenu/auth/${enterpriseId}`, {
    method: 'get',
  })
  return res
}

useEventListener('message', (e) => {
  if (e.data == 'remote-login') {
    loginFinish()
  }
})

defineExpose({
  showLoginPopup,
  hideLoginPopup,
})
</script>

<style lang="less" scoped>
.login-enter-from,
.login-leave-to {
  opacity: 0;
}
.login-enter-active,
.login-leave-active {
  transition: opacity 0.5s;
}
.loginMask {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}
</style>
