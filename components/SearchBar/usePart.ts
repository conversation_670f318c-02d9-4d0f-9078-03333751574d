/**
 * 零部件查找逻辑
 */
import { getSearchSuggestion } from '~/api/search'
import { debounce } from 'lodash-es'
import { useSearch } from '~/store/useSearch'

const MAX_SEARCH_HISTORY = 100
const MAX_SHOW_SEARCH_RECOMMEND = 10
const SEARCH_KEY = 'searchHistory'

export enum SearchType {
  type,
  recommend,
  history,
}

export type SearchItem = {
  keyword: string
  type: SearchType
  partId: string
  brandLogo?: string
  brandCode?: string
}

export default (value: Ref<string>) => {
  let blurTimer: ReturnType<typeof setTimeout> | null = null
  const searchFocused = ref(false)
  const searchHistory = ref<SearchItem[]>([])
  const showSearchRecommend = ref<boolean>(false)

  /**
   * 加载搜索历史
   */
  const loadSearchHistory = () => {
    const searchHistory = localStorage.getItem(SEARCH_KEY)
    try {
      const arr = JSON.parse(searchHistory || '[]')
      return arr.slice(-MAX_SEARCH_HISTORY)
    } catch (err) {
      return []
    }
  }

  /**
   * 保存搜索历史
   */
  const pushSearchHistory = (text: string) => {
    const arr = loadSearchHistory()
    if (!!text) {
      if (arr.includes(text)) {
        arr.splice(arr.indexOf(text), 1)
      }
      arr.push(text)
      localStorage.setItem(SEARCH_KEY, JSON.stringify(arr.slice(-MAX_SEARCH_HISTORY)))
    }
  }

  /**
   * 删除搜索历史
   */
  const deleteSearchHistory = (text: string) => {
    const arr = loadSearchHistory()
    if (arr.includes(text)) {
      arr.splice(arr.indexOf(text), 1)
      localStorage.setItem(SEARCH_KEY, JSON.stringify(arr.slice(-MAX_SEARCH_HISTORY)))
    }
    calSearchHistory()
  }

  const calSearchHistory = async () => {
    let history_arr = []
    if (has(value.value)) {
      let searchType = ''
      if (currentType.value.type == 'keyword') {
        searchType = 'BY_KEYWORD'
      } else if (currentType.value.type == 'skuCode') {
        searchType = 'BY_TYPE'
      }
      try {
        const res = await getSearchSuggestion({
          brand: '',
          categoryCode: '',
          keyword: value.value,
          optionFilters: [],
          pageNo: 1,
          pageSize: MAX_SHOW_SEARCH_RECOMMEND,
          websiteCode: '',
          searchType,
        })
        console.log(`output->res`, res)

        const list = res.data || []
        for (const item of list) {
          if (currentType.value.type == 'keyword') {
            item.type = SearchType.recommend
          } else if (currentType.value.type == 'skuCode') {
            item.type = SearchType.type
          }
        }

        searchHistory.value = list
      } catch (err) {
        console.log(`err`, err)
      }
    } else {
      history_arr = loadSearchHistory()
        .filter((ele: string) => ele.includes(value.value))
        .reverse()
        .slice(0, MAX_SHOW_SEARCH_RECOMMEND)
        .map((item) => {
          return {
            type: SearchType.history,
            keyword: item,
          }
        })
      searchHistory.value = history_arr
    }
  }

  const onSearch = (searchValue: string, isOuter = false) => {
    showSearchRecommend.value = false
    pushSearchHistory(searchValue)
    if (!!searchValue) {
      const url = {
        path: '/search',
        query: { s: searchValue, searchType: 'mechanicalParts', _t: new Date().getTime() },
      }
      if (isOuter) {
        window.open(router.resolve(url).href)
      } else {
        // 使用页面跳转而不是SPA路由跳转，确保搜索框能正确显示搜索词
        const fullUrl = router.resolve(url).href
        window.location.href = fullUrl
      }
    }
  }

  const onChange = debounce(() => {
    if (!showSearchRecommend.value) {
      showSearchRecommend.value = true
    }
    calSearchHistory()
  }, 500)

  const blur = () => {
    searchFocused.value = false
    showSearchRecommend.value = false
    document.removeEventListener('click', blur)
  }
  const onFocus = () => {
    searchFocused.value = true
    showSearchRecommend.value = true
    calSearchHistory()
    document.addEventListener('click', blur)
  }

  const onLeave = () => {
    if (blurTimer) {
      clearTimeout(blurTimer)
    }
    blurTimer = setTimeout(() => {
      searchFocused.value = false
      showSearchRecommend.value = false
    }, 500)
  }

  const onEnter = () => {
    if (blurTimer) {
      clearTimeout(blurTimer)
    }
  }

  const router = useRouter()

  const onSelectionItemClick = (item: SearchItem, isOuter = false) => {
    value.value = item.keyword
    pushSearchHistory(item.keyword)
    if (has(item.partId)) {
      const isMall = item.partId.startsWith('mall-') && has(item.brandCode)
      let url
      if (currentType.value.type == 'keyword') {
        if (isMall) {
          url = {
            path: `/brand/${item.brandCode}/${item.partId}`,
          }
        } else {
          url = {
            path: `/parts/${item.partId}`,
          }
        }
      } else {
        if (isMall) {
          url = {
            path: `/brand/${item.brandCode}/${item.partId}`,
            query: {
              typeCode: item.keyword,
            },
          }
        } else {
          url = {
            path: `/parts/${item.partId}`,
            query: {
              typeCode: item.keyword,
            },
          }
        }
      }
      if (isOuter) {
        const href = router.resolve(url).href
        window.open(href)
      } else {
        router.push(url)
      }
    } else {
      onSearch(item.keyword, isOuter)
    }
  }

  const onDeleteHistoryClick = (item: SearchItem) => {
    deleteSearchHistory(item.keyword)
  }

  const clearHistory = () => {
    Modal.confirm({
      title: '提示',
      content: '搜索记录将被删除，是否继续？',
      onOk() {
        localStorage.removeItem(SEARCH_KEY)
        calSearchHistory()
      },
    })
  }

  const searchStore = useSearch()

  const currentType = computed(() => searchStore.currentType)

  const placeholder = computed(() => {
    switch (currentType.value.type) {
      case 'keyword':
        return '输入类目名称/产品名称搜索'
      case 'skuCode':
        return '输入产品型号搜索'
    }
  })

  return {
    onEnter,
    onLeave,
    onFocus,
    onSearch,
    onChange,
    showSearchRecommend,
    searchHistory,
    onSelectionItemClick,
    onDeleteHistoryClick,
    currentType,
    changeType: searchStore.changeType,
    clearHistory,
    placeholder,
  }
}
