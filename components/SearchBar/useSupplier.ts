/**
 * 找供应商逻辑
 */
export default (kw: Ref<string>) => {
  const placehoder = ref('输入供应商名称进行搜索')
  const onSupplilerSearch = () => {
    // todo 推荐商家
    // if (!kw.value) {
    //   kw.value = placehoder.value
    // }
    navigateTo({
      path: '/supplier',
      query: {
        s: kw.value,
        _t: new Date().getTime(),
        searchType: 'supplier'
      }
    })
  }
  
  const clickAddress = () => {
    navigateTo({
      path: '/supplier/address',
      query: {
        _t: new Date().getTime(),
        searchType: 'supplier'
      }
    })
  }
  return {
    onSupplilerSearch,
    clickAddress,
    supplierPlaceholder: placehoder
  }
}
