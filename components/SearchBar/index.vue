<template>
  <div w-full>
    <div text-left m-b-12>
      <a-space :size="[0, 8]" wrap>
        <a-checkable-tag
          v-for="tag in tagList"
          :key="tag.key"
          color-white
          :checked="tag.key === currentTag"
          @change="(checked) => onTagChange(tag.key, checked)"
        >
          {{ tag.name }}
        </a-checkable-tag>
      </a-space>
    </div>
    <div relative w-full v-if="currentTag == 'mechanicalParts'">
      <div class="flex relative">
        <a-popover
          placement="bottomLeft"
          v-model:open="showTree"
          trigger="click"
          :get-popup-container="(el) => el.parentNode as any"
        >
          <a-button type="primary" :size="size" class="rounded-r-none!">
            <template #icon>
              <UnorderedListOutlined />
            </template>
            全部类目
          </a-button>
          <template #content>
            <CategoryTree :tree="categoryTreeList" @to-detail="showTree = false" />
          </template>
        </a-popover>
        <a-input-search
          class="input-search"
          v-model:value="value"
          :placeholder="computedShowPlaceholder"
          enter-button
          @search="onSearch"
          @change="onChange"
          @click.stop="onFocus"
          allowClear
          :size="size"
        >
          <template #suffix>
            <div flex items-center text="primary" cursor-pointer @click="changeType">
              <div mr-8 text-14>{{ currentType.name }}</div>
              <OrderedListOutlined />
            </div>
          </template>
        </a-input-search>
      </div>
      <div v-show="showSearchRecommend" class="selection-list-container shadow-md">
        <div
          class="selection-item h-32 flex items-center justify-between"
          v-for="(item, index) in searchHistory"
          :key="index"
          @click="onSelectionItemClick(item)"
        >
          <span text>{{ item.keyword }}</span>
          <a
            v-show="item.type == Type.history"
            font-size-14
            href="javascript:void(0)"
            @click.stop="onDeleteHistoryClick(item)"
          >
            删除
          </a>
          <img v-if="item.brandLogo" h-full :src="item.brandLogo" alt="" />
        </div>
        <div h-32 flex-center v-if="!has(value) && searchHistory.length" @click.stop>
          <a-button type="link" @click.stop="clearHistory">清空搜索记录</a-button>
        </div>
      </div>
    </div>

    <div flex v-else-if="currentTag == 'docs'">
      <a-button type="primary" :size="size" class="rounded-r-none!" @click="clickDoc">
        <template #icon>
          <UnorderedListOutlined />
        </template>
        全部文档
      </a-button>
      <a-input-search
        class="input-search"
        v-model:value="value"
        placeholder="请输入关键词或回车查找文档，如“量具量仪产品质量分等”"
        enter-button
        @search="onDocSearch"
        allowClear
        :size="size"
      ></a-input-search>
    </div>

    <div flex v-else-if="currentTag == 'supplier'">
      <a-button type="primary" :size="size" class="rounded-r-none!" @click="clickAddress">
        <template #icon>
          <UnorderedListOutlined />
        </template>
        通讯录
      </a-button>
      <a-input-search
        class="input-search"
        v-model:value="value"
        :placeholder="supplierPlaceholder"
        enter-button
        @search="onSupplilerSearch"
        allowClear
        :size="size"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { treeStore } from '~/store/tree'
import { ButtonProps } from 'ant-design-vue'
import usePart, { SearchType as Type } from './usePart'
import useDoc from './useDoc'
import useSupplier from './useSupplier'

const route = useRoute()

const value = ref<string>((route.query.s as string) || '')
const treeStoreObj = treeStore()

const showTree = ref(false)
watch(showTree, (v) => {
  if (v) {
    const data = treeStoreObj.tree.data
    if (Array.isArray(data) && !!data[0]) {
      categoryTreeList.value = data[0].children || []
    }
  }
})

export interface Props {
  size?: ButtonProps['size']
}

const props = withDefaults(defineProps<Props>(), {
  size: 'middle',
})
const tagList = reactive([
  {
    key: 'mechanicalParts',
    name: '找型号',
    defaultPlaceholder: '导向轴',
  },
  // {
  //   key: 'electricalPars',
  //   name: '气动件',
  //   defaultPlaceholder: '针型气缸',
  // },
  // {
  //   key: 'docs',
  //   name: '找文档',
  //   defaultPlaceholder: '量具量仪产品质量分等',
  // },
  {
    key: 'supplier',
    name: '找供应商',
    defaultPlaceholder: '',
  },
] as const)

type SearchType = (typeof tagList)[number]['key']

const searchFocused = ref<boolean>(false)

const currentTag = ref(route.query.searchType as SearchType)

const isRecommend = ref<boolean>(false)

const categoryTreeList = ref([])

const onTagChange = (tag: SearchType, checked: boolean) => {
  if (!checked) return
  // TODO是否清空当前kw
  value.value = ''
  currentTag.value = tag
  // switch (tag) {
  //   case 'docs':
  //     return navigateTo({
  //       name: 'document',
  //       query: {
  //         s: value.value,
  //         _t: new Date().getTime()
  //       }
  //     })
  //   case 'mechanicalParts':
  //     return navigateTo({
  //       path: '/', query: {
  //         s: value.value,
  //         _t: new Date().getTime()
  //       }
  //     })
  //   case 'supplier':
  //     return navigateTo({
  //       path: '/supplier', query: {
  //         s: value.value,
  //         _t: new Date().getTime()
  //       }
  //     })
  // }
}

onMounted(() => {
  setTimeout(() => {
    currentTag.value = (route.query.searchType as SearchType) || 'mechanicalParts'
    value.value = (route.query?.s as string) ?? ''
  })
})

const computedCurrentTagItem = computed(() => {
  return tagList.find((ele) => ele.key === currentTag.value)
})

/**
 * 成为搜索字符串的placeholder
 */
const computedPurePlaceholder = computed(() => {
  return computedCurrentTagItem.value?.defaultPlaceholder
})

/**
 * 显示在界面上的placeholder
 */
const computedShowPlaceholder = computed(() => {
  switch (currentType.value.type) {
    case 'keyword':
      return '输入类目名称/产品名称搜索'
    case 'skuCode':
      return '输入产品型号搜索'
  }
})

const offset = computed(() => (props.size == 'large' ? '40px' : '32px'))
const {
  onEnter,
  onLeave,
  onChange,
  onFocus,
  onSearch,
  onDeleteHistoryClick,
  onSelectionItemClick,
  showSearchRecommend,
  searchHistory,
  currentType,
  changeType,
  clearHistory,
} = usePart(value)

const { onDocSearch, clickDoc } = useDoc(value)

const { onSupplilerSearch, clickAddress, supplierPlaceholder } = useSupplier(value)
</script>

<style lang="less" scoped>
.selection-list-container {
  border-bottom-left-radius: 2px;
  border-bottom-right-radius: 2px;
  text-align: left;
  background-color: #fff;
  position: absolute;
  z-index: 999;
  display: block;
  width: calc(100% - 1px);
  top: v-bind('offset');
  left: 0;

  .selection-item {
    padding: 8px 8px;
    cursor: pointer;

    &:hover {
      background-color: #e6f4ff;
    }
  }
}

:deep(.ant-popover) {
  // width: 80vw;
  min-width: 900px;
}

@media (max-width: 900px) {
  :deep(.ant-popover) {
    width: 90vw;
    min-width: auto;
    overflow: auto;

    .ant-popover-content {
      width: 100%;

      .ant-popover-inner {
        padding: 0;
      }
    }
  }
}

:deep(.ant-input-affix-wrapper) {
  border-start-start-radius: 0 !important;
  border-end-start-radius: 0 !important;
  border-color: var(--primary-color) !important;
}
:deep(.ant-btn) {
  box-shadow: none !important;
}
</style>
