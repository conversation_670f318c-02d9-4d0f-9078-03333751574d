/**
 * 搜索文档
 */
export default (kw: Ref<string>) => {
  const router = useRouter()
  const onDocSearch = () => {
    router.push({
      path: '/document',
      query: {
        _t: new Date().getTime(),
        s: kw.value,
        searchType: 'docs'
      }
    })
  }
  
  const clickDoc = () => {
    kw.value = ''
    router.push({
      path: '/document',
      query: {
        searchType: 'docs',
        _t: new Date().getTime()
      }
    })
  }
  return {
    onDocSearch,
    clickDoc
  }
}