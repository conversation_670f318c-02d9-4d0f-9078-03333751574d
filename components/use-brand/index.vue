<template>
  <a-select
    v-model:value="modelValue"
    show-search
    :filter-option="false"
    :not-found-content="fetching ? undefined : null"
    :options="data"
    @search="fetchData"
    :field-names="{ label: label, value: value, key: value }"
    label-in-value
    v-bind="$attrs"
    :mode="props.mode == 1 ? 'multiple' : ''"
  >
    <template v-if="fetching" #notFoundContent>
      <a-spin size="small" />
    </template>
  </a-select>
</template>

<script setup>
import { debounce } from 'lodash-es'

const props = defineProps({
  remoteMethod: {
    type: Function,
    required: true,
  },
  label: {
    type: String,
    default: 'label',
  },
  value: {
    type: String,
    default: 'value',
  },
  mode: {
    // 0 单选, 1 多选
    type: [Number, String],
    default: 0,
  },
})

const emit = defineEmits(['change'])
const modelValue = computed({
  get() {
    if (props.mode == 1) {
      return handleMultipleGet()
    } else {
      return handleSingleGet()
    }
  },
  set(value) {
    console.log('%c Line:47 🥝 value', 'color:#2eafb0', value)
    if (props.mode == 1) {
      handleMultipleSet(value)
    } else {
      handleSingleSet(value)
    }
  },
})

const handleSingleSet = (value) => {
  if (value) {
    selectedValue.value = {
      [props.value]: value.value,
      [props.label]: value.label,
    }
  } else {
    selectedValue.value = undefined
  }
}

const handleSingleGet = () => {
  if (!selectedValue.value) return undefined
  const label = selectedValue.value[props.label]
  const value = selectedValue.value[props.value]
  return { label, value }
}

const handleMultipleSet = (value) => {
  if (value && value.length > 0) {
    selectedValue.value = value.map((item) => ({
      [props.value]: item.value,
      [props.label]: item.label,
    }))
  } else {
    selectedValue.value = []
  }
}

const handleMultipleGet = () => {
  if (!selectedValue.value || selectedValue.value.length === 0) return []
  return selectedValue.value.map((item) => ({
    label: item[props.label],
    value: item[props.value],
  }))
}

const data = ref([])
const fetching = ref(false)
const selectedValue = defineModel()

const fetchData = debounce(async (searchText) => {
  data.value = []
  fetching.value = true
  try {
    const result = await props.remoteMethod(searchText)
    data.value = result || []
  } catch (error) {
    console.error('Error fetching data:', error)
  } finally {
    fetching.value = false
  }
}, 300)

onMounted(() => {
  fetchData('')
})
</script>

<style scoped>
/* Add any specific styles here if needed */
</style>
