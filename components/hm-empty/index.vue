<template>
  <a-empty
    :image-style="imageStyle"
  >    
    <template #image>        
      <slot name="image">
        <img class="image" :src="image" />
      </slot>
    </template>
    <template #description>        
      <slot name="description">
        <span class="description">{{ description }}</span>
      </slot>
    </template>
    <slot></slot>
  </a-empty>
</template>

<script setup>
import empty from '@/assets/images/empty.png'

defineProps({
  image: {
    type: String,
    default: empty
  },
  description: {
    type: String,
    default: '暂无数据'
  },
  imageStyle: {
    type: Object,
    default: () => ({})
  }
})
</script>

<style lang="less" scoped>
.image{
  margin-top: 50px;
  width: 64px;
  height: 40px !important;
}

.description{
  color: #dcdcdc;
}
</style>
