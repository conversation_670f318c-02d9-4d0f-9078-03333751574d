<template>
  <div bottom-0 mt-10 z-99 ref="target">
    <a-button-group
      
    >
      <a-button type="primary" @click="handleShow(1)">
        <DoubleLeftOutlined
          :class="{
            'rotate-90': !active,
            'rotate-270': active,
          }"
        />
        相关推荐
      </a-button>
      <!-- <a-button :type="activeTab == 'same' ? 'primary' : 'default'" @click="handleShow('same')">
        相似推荐
      </a-button>
      <a-button :type="activeTab == 'replace' ? 'primary' : 'default'" @click="handleShow('replace')">
        平替推荐
      </a-button> -->
    </a-button-group>
    <div w-full flex v-if="active">
      <!-- <div style="width: 100px">
        <a-button :type="activeTab == 'replace' ? 'primary' : 'default'" style="width: 100px" @click="handleShow('replace')"> 平替商品 </a-button>
        <a-button :type="activeTab == 'supplier' ? 'primary' : 'default'" style="width: 100px" @click="handleShow('supplier')"> 供应商 </a-button>
        <a-button :type="activeTab == 'same' ? 'primary' : 'default'" style="width: 100px" @click="handleShow('same')"> 相似产品 </a-button>
      </div> -->
      <div class="recommend shadow-[0_2px_12px_rgba(0,0,0,0.12)] bg-#fff"  overflow-hidden relative z-1>
        <div b-b-solid b-b-gray-1 b-b-1>
          <a-radio-group p-8 size="small" v-model:value="activeTab" @change="handleRadioChange">
            <a-radio-button value="replace">平替商品</a-radio-button>
            <a-radio-button value="supplier">供应商</a-radio-button>
            <a-radio-button value="same">相似产品</a-radio-button>
          </a-radio-group>
        </div>
        <div v-if="activeList.length" flex>
          <div
            w-20
            text-white
            flex-center
            text-20px
            :class="{
              'bg-primary bg-blue-100 cursor-pointer': active && !isReachStart,
            }"
            @click="changeSwiper(Action.prev)"
          >
            <LeftOutlined v-if="active" />
          </div>
          <div flex-1 overflow-hidden p-16>
            <Swiper :slides-per-view="6" :space-between="10" flex-1 @swiper="onSwiper" :modules="[Autoplay]">
              <SwiperSlide v-for="item in activeList" :key="item.id" @click="goDetail(item)">
                <div v-if="activeTab != 'supplier'" border="1px solid #eee" rounded-4 p-10 h-181px cursor-pointer hover="border-primary">
                  <img :src="item.website?.websiteImgUrl" h16px wauto block alt="" />
                  <img :src="item.imageUrl" w-60px h-60px block mx-auto my10px alt="" />
                  <div text-12px text-gray>{{ item.website?.websiteName }}</div>
                  <div text-14px line-clamp-2>{{ item.productName }}</div>
                </div>
                <div v-else border="1px solid #eee" rounded-4 p-10 h-181px cursor-pointer hover="border-primary">
                  <div text-16px line-clamp-2>{{ item.shopName }}</div>
                  <img :src="item.shopLogo" w-60px h-60px block mx-auto my10px alt="" />
                  <div text-12px text-gray line-clamp-2>简介: {{ item.intro }}</div>
                </div>
              </SwiperSlide>
            </Swiper>
          </div>
          <div
            w-20
            text-white
            flex-center
            text-20px
            :class="{
              'bg-primary bg-blue-100 cursor-pointer': active && !isReachEnd,
            }"
            @click="changeSwiper(Action.next)"
          >
            <RightOutlined v-if="active" />
          </div>
        </div>
        <div v-else class="recommend shadow-[0_2px_12px_rgba(0,0,0,0.12)] bg-#fff" p-16 style="text-align: center">
          <img h-181px src="~/assets/images/daodao_nodata.png" />
          <span text-18px color-primary font-bold>什么{{activeTab === 'supplier' ? '供应商' : '商品'}}也没有......</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { getRecommendFn, getRecommendFnV2, getRecommendFnV3, type Category } from '~/api/search';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Autoplay } from 'swiper/modules';
import type { Swiper as ISwiper } from 'swiper';
import 'swiper/css';

const activeTab = ref('replace');
const route = useRoute();
const target = ref();
onClickOutside(target, () => {
  active.value = false;
});
const id = route.params.id as string;
const recommendList = ref<Category[]>([]);
const replaceList = ref<Category[]>([]);
const supplierList = ref<Category[]>([]);
onMounted(() => {
  getRecommendFn(id).then((res) => {
    if (res.code == 'ok') {
      recommendList.value = res.data;
    } else {
      message.error(res.message);
    }
  });
  getRecommendFnV2(id).then((res) => {
    if (res.code == 'ok') {
      replaceList.value = res.data.map((d) => {
        let images = <any>[];
        try {
          images = d.imageUrl ? JSON.parse(d.imageUrl) : [];
        } catch (err) {
          images = [d.imageUrl];
        }
        // 图片取第一张
        d.imageUrl = images[0] || '';
        return d;
      });
    } else {
      message.error(res.message);
    }
  });
  getRecommendFnV3(id).then((res) => {
    if (res.code == 'ok') {
      supplierList.value = res.data;
    } else {
      message.error(res.message);
    }
  });
});
const swiper = ref<ISwiper>();
const onSwiper = (instance) => {
  swiper.value = instance;
};
const active = ref(false);
const change = () => {
  active.value = !active.value;
};
const isReachEnd = ref(false);
const isReachStart = ref(false);
enum Action {
  prev,
  next,
}
const changeSwiper = (action: Action) => {
  if (!active.value) return;
  switch (action) {
    case Action.prev:
      return swiper.value?.slidePrev();
    case Action.next:
      return swiper.value?.slideNext();
  }
};

const router = useRouter();
const goDetail = (item) => {
  if (activeTab.value == 'supplier') {
    window.open(item.shopAddress);
    return;
  }
  router.push({
    path: `/parts/${item.id}`,
  });
};

const activeList = ref<Category[]>([]);
const handleShow = (type) => {
  if (type == 1) {
    active.value = !active.value;
  } else {
    active.value = true;
    activeTab.value = type;
  }
  activeList.value = activeTab.value == 'same' ? recommendList.value : activeTab.value == 'replace' ? replaceList.value : supplierList.value;
};

const handleRadioChange = e => {
  const type = e.target.value
  if (type == 1) {
    active.value = !active.value;
  } else {
    active.value = true;
    activeTab.value = type;
  }
  activeList.value = activeTab.value == 'same' ? recommendList.value : activeTab.value == 'replace' ? replaceList.value : supplierList.value;
}

watchEffect(() => {
  if (swiper.value) {
    isReachStart.value = swiper.value.isBeginning;
    isReachEnd.value = swiper.value.isEnd;
  }
});
</script>

<style lang="less" scoped>
.recommend {
  flex: 1;

  :deep(.ant-collapse-content-box) {
    padding: 0 !important;
  }
  :deep(.ant-collapse-header) {
    padding: 0 !important;
  }
}
</style>
