<template>
  <a-drawer title="配置表格列" v-model:open="open">
    <a-spin :spinning="!!loading[key]" v-for="[key, item] in Object.entries(state)" :key="key">
      <h4>{{ item.name }}</h4>
      <a-checkbox-group :value="item.displays" @change="(value) => handleChange(value, item)" class="mb-20px">
        <a-row :gutter="[16, 4]">
          <a-col :span="12" v-for="col in item.columns" :key="col.dataIndex">
            <a-checkbox :value="col.dataIndex" :disabled="col.isSystem">{{ col.title }}</a-checkbox>
          </a-col>
        </a-row>
      </a-checkbox-group>

      <a-divider v-if="state.length"></a-divider>
    </a-spin>
  </a-drawer>

  <a-button @click="showColumnConfig">
    <SettingOutlined />
    列配置
  </a-button>
</template>

<script setup>
const open = ref(false)

const state = ref([])
const emits = defineEmits(['openConfig', 'changeConfig'])
let key = ''
let descriptions = ''
const loading = ref({})

const show = (config) => {
  key = config.key
  descriptions = config.descriptions
  state.value = config.map
  open.value = true
}

const handleChange = async (value, item) => {
  const oldConfig = Object.keys(state.value).reduce((res, item) => {
    res[key] = item.displays
    return res
  }, {})
  const newConfig = {
    ...oldConfig,
    [item.key]: value,
  }
  loading.value[item.key] = true
  const [err] = await try_http('/mall/p/config/user-form-view', {
    method: 'post',
    body: {
      key,
      config: JSON.stringify(newConfig),
      descriptions,
    },
  })
  loading.value[item.key] = false
  if (!err) {
    state.value[item.key].displays = value
    emits('changeConfig')
  }
}

const cb = (config) => {
  show(config)
}

const showColumnConfig = () => {
  emits('openConfig', cb)
}

defineExpose({
  show,
})
</script>
