<template>
  <a-input-group compact>
    <a-input v-model:value="val" placeholder="验证码" style="width: calc(100% - 120px)" v-bind="$attrs"></a-input>
    <a-button w-120 type="primary" :disabled="disabled" :loading="loading" @click="send">
      {{ text }}
    </a-button>
  </a-input-group>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    sendApi: () => Promise<Boolean>
    waitTime?: number
    defaultText?: string
    storageKey?: string
  }>(),
  {
    waitTime: 60,
    defaultText: '获取验证码',
    storageKey: 'msg_code_timer',
  }
)
const val = defineModel<string>({ default: '' })

// 从localStorage恢复计时状态
const restoreTimer = () => {
  const saved = localStorage.getItem(props.storageKey)
  if (saved) {
    const { time: savedTime, expire } = JSON.parse(saved)
    if (Date.now() < expire) {
      time.value = Math.ceil((expire - Date.now()) / 1000)
      disabled.value = true
      timeCalc()
    } else {
      localStorage.removeItem(props.storageKey)
    }
  }
}

onMounted(restoreTimer)

const loading = ref(false)

const disabled = ref(false)

const time = ref(props.waitTime)

const text = computed(() => {
  return disabled.value ? `重新获取(${time.value})` : '获取验证码'
})

const timeCalc = () => {
  setTimeout(() => {
    if (time.value > 1) {
      time.value--
      // 保存剩余时间和过期时间戳
      localStorage.setItem(
        props.storageKey,
        JSON.stringify({
          time: time.value,
          expire: Date.now() + time.value * 1000,
        })
      )
      timeCalc()
    } else {
      disabled.value = false
      localStorage.removeItem(props.storageKey)
    }
  }, 1000)
}

const send = async () => {
  loading.value = true
  const res = await props.sendApi()
  loading.value = false
  if (res) {
    message.success('验证码发送成功')
    disabled.value = true
    // 初始保存计时状态
    localStorage.setItem(
      props.storageKey,
      JSON.stringify({
        time: props.waitTime,
        expire: Date.now() + props.waitTime * 1000,
      })
    )
    timeCalc()
  }
}
</script>
