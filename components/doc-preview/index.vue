<template>
  <a-modal 
    v-model:open="show"
    title="文件预览"
    :footer="null" 
    :width="1000"
    destroyOnClose
  >
    <PDF
      class="pdf"
      :src="url"
    />
  </a-modal>
</template>

<script setup>
import PDF from 'pdf-vue3'

const show = ref(false)
const url = ref('')
const init = (path) => {
  show.value = true
  url.value = convertImage(path)
}

defineExpose({
  init
})
</script>

<style lang="less" scoped>
:deep(.pdf) {
  canvas {
    box-shadow: none !important;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
  }
}
</style>
