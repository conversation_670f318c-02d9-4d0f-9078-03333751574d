<template>
  <a-spin :spinning="loading">
    <div class="flex items-center h-40" v-if="isCadenas && model.seriesCodes.length">
      <div mr-10px shrink-0>产品系列</div>
      <a-dropdown trigger="click">
        <div text-primary cursor-pointer truncate :title="model.seriesCode">{{ model.seriesCode }}</div>
        <template #overlay>
          <a-menu>
            <a-menu-item v-for="item in model.seriesCodes" :key="item" @click="changePrj(item)">
              {{ item }}
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
    <div mt-10 p-8 bg-secondary rounded-4 v-if="model.selections.length > 1 && !model.isSelect">
      <a-select
        :options="model.selections"
        :field-names="{ label: 'partNumber', value: 'partNumber' }"
        label-in-value
        size="small"
        @change="handleChange"
        w-full
      >
        <template #placeholder>
          <div class="flex text-12 text-black">
            <div>共</div>
            <div class="text-primary mx5">{{ model.selections.length }}</div>
            <div>个候选项</div>
          </div>
        </template>
      </a-select>
    </div>
    <div mt-10 p-8 bg-secondary rounded-4 text-14px flex v-else>
      <div flex-1>当前选择型号：{{ model.skuCode }}</div>
      <CloseCircleOutlined
        v-if="model.isFinished"
        cursor-pointer
        text-20
        mr15
        text-primary
        @click="reset"
        title="清除"
      />
      <CopyOutlined text-20 cursor-pointer text-primary @click="copyModel(model.skuCode)" title="复制" />
    </div>
    <div divide="#e1e1e1 y" border="1 #e1e1e1" mt-10 rounded-4 overflow-hidden>
      <div
        grid
        grid-cols="[150px_1fr_60px]"
        text-14
        h-40
        divide="x #e1e1e1"
        v-for="item in model.list"
        :key="item.specCode"
      >
        <div bg-gray text-white flex flex-col justify-center px-8>
          <div font-bold ___>{{ item.specCode }}</div>
          <div
            text="#eee 12"
            truncate
            v-if="item.specCode !== item.specCodeDisplayName"
            :title="item.specCodeDisplayName"
          >
            {{ item.specCodeDisplayName }}
          </div>
        </div>

        <div flex items-center px-8 overflow-hidden>
          <a-input-number
            :min="+item.min"
            :max="+item.max"
            :step="+item.step"
            allow-clear
            :disabled="!!item.disabled"
            class="w-full!"
            :placeholder="rangePlaceholder(item)"
            v-if="item.type == 'Ranged'"
            v-model:value="model.params[item.specCode]"
            @blur="getSelectParams(item.specCode, model.params[item.specCode])"
          ></a-input-number>

          <a-select
            class="w-full!"
            placeholder="请选择"
            v-model:value="model.params[item.specCode]"
            :disabled="!!item.disabled"
            allow-clear
            v-if="item.type == 'Select'"
            :options="item.options"
            :field-names="{
              label: 'name',
              value: 'value',
            }"
            @change="getSelectParams(item.specCode, model.params[item.specCode])"
          ></a-select>

          <div v-if="item.type == 'Fixed'">{{ item.specValueDisplayName }}</div>

          <a-input
            v-if="item.type == 'OneValueSelect'"
            disabled
            :value="item.specValueDisplayName"
            class="w-full!"
          ></a-input>

          <a-input
            v-if="item.type == 'Input'"
            v-model:value="model.params[item.specCode]"
            class="w-full!"
            placeholder="请输入"
            @blur="getSelectParams(item.specCode, model.params[item.specCode])"
          ></a-input>

          <div
            text-primary
            cursor-pointer
            truncate
            type="link"
            v-if="item.type == 'ExeDocLink' || item.type == 'ExeDocPdf'"
            @click="goLink(item.executeResourceUrl)"
          >
            {{ item.specValueDisplayName }}
          </div>
        </div>

        <div flex-center>
          <div v-if="item.unit">
            {{ item.unit }}
          </div>
        </div>
      </div>
    </div>

    <a-space mt-20 v-if="$slots.default">
      <slot></slot>
    </a-space>
  </a-spin>
</template>

<script setup lang="ts">
import { getSelects } from '~/api/select'
import Model from './mode'

const emits = defineEmits<{}>()

const props = defineProps<{
  model: Model
}>()

const { model } = toRefs(props)
const isCadenas = computed(() => model.value.pageDetail?.from == 'Cadenas')

const loading = ref(false)

const copy = useCopy()
const copyModel = (text: string) => {
  copy(text)
  message.success('复制成功')
}

const getSelectParams = async (key?: string, val?: string, seriesCode?: string) => {
  let params: Obj = {
    partId: model.value.partId,
  }
  if (isCadenas.value) {
    let temp: Obj = {}
    if (key) {
      temp = { [key]: val || '' }
    } else {
      temp = {}
    }
    params.params = temp
    if (seriesCode) {
      params.seriesCode = seriesCode
    }
    params.extra = model.value.mident
  } else {
    params.params = model.value.params
  }
  loading.value = true
  try {
    const res = await getSelects(params)
    useRes(res, () => {
      model.value.setSelections(res.data)
    })
  } catch (e: any) {
    if (e?._data?.message) {
      message.error(e._data.message)
    }
  }
  loading.value = false
}

const changePrj = (seriesCode: string) => {
  model.value.seriesCode = seriesCode
  model.value.mident = ''
  getSelectParams('', '', seriesCode)
}

const rangePlaceholder = (row) => {
  const { min, max, step, disabled } = row
  if (!disabled) {
    return `[${min}-${max}/${step}]`
  } else {
    return `无可用范围`
  }
}

const handleChange = (val) => {
  model.value.setSelect(val.option)
}

const goLink = (url) => {
  window.open(url)
}

const route = useRoute()
const loadParams = async () => {
  loading.value = true
  const res = await request.post('/selection/part-parameters', {
    partId: model.value.partId,
    skuCode: model.value.skuCode,
  })
  loading.value = false
  return res
}

const reset = () => {
  Modal.confirm({
    title: '提示',
    content: '确定要重置所有已选的参数吗？',
    onOk() {
      model.value.isFinished = false
      model.value.doc = ''
      model.value.params = {}
      getSelectParams()
    },
  })
}

const init = async () => {
  if (typeCode.value) {
    model.value.skuCode = typeCode.value
    const res = await loadParams()
    useRes(
      res,
      () => {
        if (res.data.isFinished) {
          model.value.setSelections([res.data])
        } else {
          getSelectParams()
        }
      },
      () => {
        message.destroy()
        message.error(res.message)
        getSelectParams()
      },
    )
  } else {
    getSelectParams()
  }
}

onMounted(async () => {
  init()
})

const typeCode = computed(() => route.query.typeCode as string)
watch(typeCode, async () => {
  init()
})

defineExpose({})
</script>
