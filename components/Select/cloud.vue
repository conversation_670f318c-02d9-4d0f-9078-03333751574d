<template>
  <a-spin :spinning="loading > 0">
    <div mt-10 z10 bg-white v-if="!isGen">
      <div p-8 bg-secondary rounded-4 v-if="!isGen">
        <a-select
          v-if="!currentModel"
          :options="computedSelects"
          :field-names="{ label: 'partNumber', value: 'partNumber' }"
          size="small"
          @change="changeSelect"
          w-full
        >
          <template #placeholder>
            <div class="flex text-12 text-black">
              <div>共</div>
              <div class="text-primary mx5">{{ computedSelects.length }}</div>
              <div>个候选项</div>
            </div>
          </template>
        </a-select>
        <span text-14 v-else>
          当前选择型号：
          <span v-html="strUtil.format"></span>
          <CloseCircleOutlined cursor-pointer text-20 ml5 text-primary @click="reset" title="清除" />
          <RollbackOutlined
            v-if="strUtil.steps > 0"
            text-20
            cursor-pointer
            ml5
            text-primary
            @click="resetModel"
            title="重置"
          />
          <CopyOutlined text-20 cursor-pointer text-primary ml5 @click="copyModel(strUtil.raw)" title="复制" />
        </span>
      </div>
      <div v-if="currentModel && strUtil.steps > 0" flex h-10 mt-10>
        <div
          v-for="item in strUtil.steps"
          :key="item"
          flex-1
          bg-gray-200
          :class="{ 'bg-primary': strUtil.currentStep >= item }"
        ></div>
      </div>
    </div>

    <div v-if="isGen && currentModel" mt-10 p-8 bg-secondary rounded-4 text-14px flex>
      <div flex-1>当前选择型号：{{ currentModel }}</div>
      <CopyOutlined text-20 cursor-pointer text-primary @click="copyModel(currentModel)" title="复制" />
    </div>

    <div divide="#e1e1e1 y" border="1 #e1e1e1" mt-10 rounded-4 overflow-hidden>
      <div
        grid
        grid-cols="[150px_1fr_60px]"
        text-14
        h-40
        divide="x #e1e1e1"
        v-for="item in detail!.selectionParameters || []"
        :key="item.spec_code"
      >
        <part-form-item :item="item">
          <a-select
            v-if="item.spec_style == 'select'"
            placeholder="请选择"
            :options="item.spec_options"
            :field-names="{
              label: 'spec_option_name',
              value: 'spec_option_value',
            }"
            v-model:value="formData[item.spec_code]"
            @change="(val) => handleSelectChange(val, item.spec_code)"
            allow-clear
            :disabled="disableSet.has(item.spec_code)"
            w-full
            :status="(strUtil.rules[item.spec_code] && !formData[item.spec_code] && 'error') || ''"
          ></a-select>
          <a-input-number
            v-if="item.spec_style == 'input'"
            v-bind="rangeAttrs(item)"
            v-model:value="formData[item.spec_code]"
            style="width: 100%"
            @change="(val) => handleSelectChange(val, item.spec_code)"
            :disabled="disableSet.has(item.spec_code)"
            allow-clear
            :status="(strUtil.rules[item.spec_code] && !formData[item.spec_code] && 'error') || ''"
          ></a-input-number>
        </part-form-item>
      </div>
    </div>

    <a-space mt-20>
      <slot />
      <a-button type="primary" @click="reset">重置</a-button>
    </a-space>
  </a-spin>
</template>

<script lang="tsx" setup>
import { SelectParameter, TypeSelection, TypeSpecOption } from '~/api/search/type'
import StrUtil from '~/pages/parts/strUtil'
import { cloneDeep } from 'lodash-es'
import partFormItem from '~/pages/parts/component/partFormItem.vue'
import Model from './mode'
import { getSelects } from '~/api/select'

const props = defineProps<{
  model: Model
}>()

const { model } = toRefs(props)

const route = useRoute()
const loading = ref(0)

const detail = computed(() => model.value.pageDetail)
const pageDetail = ref(cloneDeep(detail.value))
const currentModel = ref('')
const filterMap = ref<Map<string, TypeSpecOption>>(new Map())

// 筛选列表
const filterSelects = ref<TypeSelection[]>([])

/**
 * 初始化选项列表为空,
 * 则表示需要手动生成
 */
const isGen = computed(() => !selects.value.length)
const isFilter = ref(false)

// 表单
const formData = ref<Obj>({})
const genForm = (list: SelectParameter[]) => {
  list.forEach((item) => {
    formData.value[item.spec_code] = undefined
  })
}

const computedSelects = computed(() => (isFilter.value ? filterSelects.value : selects.value))
const strUtil = computed(
  () => new StrUtil(currentModel.value, pageDetail.value?.selectionParameters || [], formData.value),
)
const formMap = computed(() =>
  (pageDetail.value?.selectionParameters || []).reduce(
    (map, item) => {
      map.set(item.spec_code, item)
      return map
    },
    new Map() as Map<string, SelectParameter>,
  ),
)

// 选项列表
const selects = ref<TypeSelection[]>([])

const fetchSelects = async (data) => {
  loading.value++
  try {
    const res = await getSelects(data)
    if (res.code == 'ok') {
      selects.value = res.data
      filterSelects.value = res.data
    } else {
      message.error(res.message)
    }
  } catch (e: any) {
    if (e?._data?.message) {
      message.error(e._data.message)
    }
  }
  loading.value--
}

/**
 * 型号确定disable合集
 * 1.普通型号
 * 2.型号带【】
 */
const disableSet = computed(() => {
  const set = new Set()
  if (isGen.value) return set
  if (strUtil.value.steps > 0 && strUtil.value.steps == strUtil.value.currentStep) {
    Object.entries(formData.value).forEach(([k, v]) => {
      if (v == null || v == undefined) {
        set.add(k)
      }
    })
  } else if (currentModel.value && strUtil.value.steps == 0) {
    Object.entries(formData.value).forEach(([k, v]) => {
      if (v == null || v == undefined) {
        set.add(k)
      }
    })
  }
  return set
})

const rangeAttrs = (item: SelectParameter) => {
  const [range] = item.value_range
  const min = Number(range.min_value)
  const max = Number(range.max_value)
  const step = Number(range.step_value)
  const placeholder = `[${min}-${max}/${step}]`
  return {
    min,
    max,
    step,
    placeholder,
  }
}

const handleSelectChange = useDebounceFn((value, code) => {
  isFilter.value = true
  if (strUtil.value.watchKes.has(code)) {
    return
  }
  // filterModel.value = ''
  // currentModel.value = ''
  if (!Object.values(formData.value).some((item) => !!item)) {
    isFilter.value = false
    pageDetail.value = cloneDeep(detail.value)
    if (isGen.value) currentModel.value = ''
    return
  }
  loading.value++
  getSelects({
    partId: model.value.partId,
    params: formData.value,
  }).then((res) => {
    loading.value--
    if (res.code == 'ok') {
      filterSelects.value = res.data || []
      if ((value || isGen.value) && filterSelects.value.length == 1) {
        const [option] = res.data
        currentModel.value = option.partNumber
        model.value.skuId = option.skuTemplateId
        setFormData(option)
      } else {
        currentModel.value = ''
        model.value.skuId = ''
      }
    } else {
      message.error(res.message)
    }
  })
}, 500)

const setFormData = (data: TypeSelection) => {
  filterMap.value = new Map()
  formData.value.typeCode = data.typeCode
  data.specValues.forEach((item) => {
    const formItem = formMap.value.get(item.specCode)
    if (formItem) {
      const formType = formItem.spec_style
      if (formType == 'input') {
        const match = item.specValue.match(/(\d+)\s*～\s*(\d+)/)
        if (match) {
          formItem.value_range[0].min_value = match[1]
          formItem.value_range[0].max_value = match[2]
          return
        }
      } else if (formType == 'select') {
        if (item.specValue.includes(',')) {
          const set = new Set(item.specValue.split(','))
          formItem.spec_options = formItem.spec_options.filter((item) => set.has(item.spec_option_value))
          return
        }
      }
      formData.value[item.specCode] = item.specValue
    }
  })
}

const changeSelect = (val, option) => {
  currentModel.value = val
  model.value.skuId = option.skuTemplateId
  // 重置 form
  genForm(pageDetail.value?.selectionParameters || [])
  !isGen.value && setFormData(option)
}

const reset = () => {
  Modal.confirm({
    title: '提示',
    content: '确定要重置所有已选的参数吗？',
    onOk() {
      pageDetail.value = cloneDeep(detail.value)
      isFilter.value = false
      currentModel.value = ''
      filterMap.value = new Map()
      // form.value?.resetFields()
      genForm(pageDetail.value?.selectionParameters || [])
    },
  })
}

const resetModel = () => {
  strUtil.value.matches.forEach((v, _k) => {
    formData.value[v.spec_code] = undefined
  })
}

const copy = useCopy()
const copyModel = (text: string) => {
  copy(text)
  message.success('复制成功')
}

const allowCompare = computed(() => {
  if (strUtil.value.steps > 0) {
    if (strUtil.value.currentStep == strUtil.value.steps) {
      return true
    }
    return false
  }
  if (selects.value.length == 0) {
    if (Object.values(formData.value).every((item) => !!item) && currentModel.value) {
      return true
    }
    return false
  }
  if (currentModel.value) {
    return true
  }
})

watchEffect(() => {
  if (allowCompare.value) {
    model.value.isFinished = true
    model.value.skuCode = !isGen.value ? strUtil.value.raw : currentModel.value
  } else {
    model.value.isFinished = false
    model.value.skuCode = ''
  }
})

onMounted(async () => {
  await fetchSelects({
    partId: model.value.partId,
    // pararms: formData.value,
  })
  const typeId = route.query.typeCode as string
  if (typeId) {
    const option = selects.value.find((item) => item.partNumber == typeId)
    if (option) {
      changeSelect(typeId, option)
      // history.pushState(null, '', route.path)
    }
  }
})
</script>
