<template>
  <a-spin :spinning="loading > 0">
    <div v-if="model.pageDetail">
      <cloud v-if="model.pageDetail.from == 'YiDaoCloud'" :model="model">
        <slot />
      </cloud>
      <other v-else :model="model">
        <slot />
      </other>
    </div>
  </a-spin>
</template>

<script lang="ts" setup>
import Model from './mode'
import cloud from './cloud.vue'
import other from './other.vue'
import { getPartDetail } from '~/api/search'
import { treeStore } from '~/store/tree'

const props = defineProps<{
  model: Model
}>()
const { model } = toRefs(props)

// 将类型放到最前面
const typeToBefore = (list) => {
  const idx = list.findIndex((item) => item.spec_code == 'typeCode')
  if (idx > -1) {
    const item = list[idx]
    list.splice(idx, 1)
    list.splice(0, 0, item)
  }
}

const loading = ref(0)

const fetchPartDetail = async () => {
  // 查询详情
  loading.value++
  try {
    const res = await getPartDetail({ id: model.value.partId })
    useRes(res, () => {
      treeStore().setActive(res.data.category?.id || '')
      typeToBefore(res.data.selectionParameters || [])
      res.data.selectionParameters.forEach((item) => {
        if (!item.spec_code) {
          item.spec_code = item.spec_name
        }
      })
      model.value.pageDetail = res.data
    })
  } catch (error: any) {
    if (error?._data?.message) {
      message.error(error._data.message)
    }
  }
  loading.value--
}
onMounted(() => {
  fetchPartDetail()
})

defineExpose({
  load: fetchPartDetail,
})
</script>
