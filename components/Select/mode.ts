import fileDownload from 'js-file-download'
import { fetchToken } from '~/api/cadenas'
import { Part, TypeSelection, TypeSpecOption, SelectType } from '~/api/search/type'
import { companyStore } from '~/store/company'
import { userStore } from '~/store/user'

class Model {
  skuCode = ''
  skuId = ''
  isFinished = false
  partId = ''
  params: Obj = {}
  pageDetail: Part | undefined
  selections: TypeSelection[] = []
  selection = {} as TypeSelection
  selectItems: TypeSpecOption[] = []
  mident: string | undefined
  modelUrl: string | undefined
  seriesCode = ''
  seriesCodes: string[] = []
  load = false
  doc = ''
  prjPath = ''
  image = ''
  isSelect = false
  constructor(partId: string) {
    this.partId = partId
  }

  get type() {
    return this.pageDetail?.renderModel
  }

  get skuImage() {
    if (this.isFinished) {
      return this.image ?? this.pageDetail?.imageUrl
    } else {
      return this.pageDetail?.imageUrl
    }
  }

  get noSkuImage() {
    return this.isFinished && !this.image
  }

  setSelect(selection: TypeSelection) {
    const {
      isFinished,
      skuTemplateId,
      partNumber,
      extra,
      viewUrl,
      seriesCode,
      seriesCodes,
      doc,
      modelFilePath,
      image,
      specValues,
    } = selection
    this.isFinished = isFinished
    this.skuId = skuTemplateId
    this.skuCode = partNumber
    this.mident = extra
    this.modelUrl = viewUrl
    this.seriesCode = seriesCode || ''
    this.seriesCodes = seriesCodes || []
    this.doc = doc
    this.prjPath = modelFilePath
    this.image = image
    this.selectItems = specValues

    this.isSelect = true
    specValues.forEach((item) => {
      this.params[item.specCode] = item.specValue
    })
  }

  setSelections(selections: TypeSelection[]) {
    this.isSelect = false
    this.selections = selections
    if (selections.length == 1) {
      const [selection] = selections
      const {
        isFinished,
        skuTemplateId,
        partNumber,
        extra,
        viewUrl,
        seriesCode,
        seriesCodes,
        doc,
        modelFilePath,
        image,
      } = selection
      this.isFinished = isFinished
      this.skuId = skuTemplateId
      this.skuCode = partNumber
      this.mident = extra
      this.modelUrl = viewUrl
      this.seriesCode = seriesCode || ''
      this.seriesCodes = seriesCodes || []
      this.doc = doc
      this.prjPath = modelFilePath
      this.image = image
    }
    if (selections.length) {
      this.skuCode = selections.length != 1 ? '' : this.skuCode
      if (this.type == SelectType.NEW) {
        const [selection] = selections
        this.selectItems = selection.specValues
        for (const item of selection.specValues) {
          this.params[item.specCode] = item.specValue
        }
      } else if (this.type == SelectType.NORMAL) {
        if (this.pageDetail?.selectionParameters) {
          // const map: Map<string, string> = new Map()
          const [selection] = selections
          if (selection) {
            if (selections.length == 1) {
              selection.specValues.forEach((item) => {
                this.params[item.specCode] = item.specValue
              })
            }
            this.selectItems = selection.specValues
          }
          // this.selectItems = this.pageDetail.selectionParameters.map((item) => {
          //   const obj = {
          //     disabled: false,
          //     executeResourceUrl: '',
          //     isHidden: false,
          //     max: 0,
          //     min: 0,
          //     options: [],
          //     order: 0,
          //     specCodeDisplayName: item.spec_code,
          //     specValueDisplayName: item.spec_name,
          //     step: 0,
          //     type: '',
          //     unit: '',
          //     specCode: item.spec_code,
          //     specValue: '',
          //     specValueDisp: '',
          //   }
          //   if (item.spec_style == 'select') {
          //     obj.type = 'Select'
          //     // @ts-ignore
          //     obj.options = item.spec_options.map((item) => {
          //       return {
          //         name: item.spec_option_name,
          //         value: item.spec_option_value,
          //         visible: true,
          //       }
          //     })
          //     if (selections.length == 1) {
          //       this.params[item.spec_code] = map.get(item.spec_code)
          //     }
          //   } else if (item.spec_style == 'input') {
          //     // 暂无
          //   }
          //   return obj
          // })
        }
      }
    } else {
      this.isFinished = false
      this.skuId = ''
      this.skuCode = ''
      this.mident = ''
      this.modelUrl = ''
      this.seriesCode = ''
      this.seriesCodes = []
      this.doc = ''
      this.prjPath = ''
      this.image = ''
    }
  }

  sort(arr) {
    arr.sort((a, b) => {
      const priority = (type) => ['Input', 'Select', 'Ranged'].includes(type)
      const isA = priority(a.type)
      const isB = priority(b.type)
      if (isA && isB) {
        return 0
      } else if (isA) {
        return -1
      } else if (isB) {
        return 1
      } else {
        return 0
      }
    })
    return arr
  }

  get list() {
    return this.sort(this.selectItems.filter((item) => !item.isHidden))
  }

  get hasModel() {
    return this.pageDetail?.has3DModel
  }

  // get prjPath() {
  //   const path = this.pageDetail?.prjPath
  //   if (!path) return ''
  //   if (path.includes('{')) {
  //     return JSON.parse(path)[this.seriesCode]
  //   }
  //   return path
  // }

  async download(modelFormats: string[]) {
    let filename = ''
    const res = await useNuxtApp().$customApi<Blob>('/api/selection/model-file-download', {
      method: 'post',
      body: {
        partId: this.partId,
        extra: this.mident,
        modelFormats,
      },
      // @ts-ignore
      headers: {
        authorization: useCookie(BBC_TOKEN).value,
      },
      responseType: 'arrayBuffer',
      onResponse(ctx) {
        console.log('%c Line:190 🥛 ctx', 'color:#5B6910', ctx)
        const _file = ctx.response.headers.get('Content-Disposition')
        if (_file) {
          filename = decodeURI(_file.replace('attachment;filename=', ''))
        }
      },
    })
    if (filename) {
      fileDownload(res, filename)
    } else {
      message.error('下载失败')
    }
    // if (this.pageDetail?.modelFile) {
    //   return this.downloadSview(loading)
    // } else if (this.pageDetail?.modelParameterFileId && this.isFinished && this.modelUrl) {
    //   loading.value = true
    //   useDownload(this.modelUrl)
    //   loading.value = false
    //   return
    // }
    // switch (this.pageDetail?.from) {
    //   case 'Cadenas':
    //     return this.downloadCa(loading)
    //   default:
    //     return this.downloadBe(loading)
    // }
  }

  async downloadCa(loading: Ref<boolean>) {
    loading.value = true
    const res = await fetchToken()
    loading.value = false

    const params = [
      ['info', this.prjPath],
      ['hidePortlets', 'generation'],
      ['hidePortlets', 'searchHeader'],
      ['hidePortlets', 'navigation'],
      ['hidePortlets', 'preview'],
      ['showPortlets', 'generation'],
      ['rlogintoken', encodeURIComponent(res.data.rlogintoken)],
      ['partFixed', 'false'],
    ]
    if (this.mident) {
      params.push(['varset', this.mident])
    }
    const iframeSrc =
      'https://yidaocloud-embedded.partcommunity.com/3d-cad-models?' +
      params.map((item) => `${item[0]}=${item[1]}`).join('&')
    return iframeSrc
  }

  async downloadSview(loading: Ref<boolean>) {
    loading.value = true
    const [file] = JSON.parse(this.pageDetail!.modelFile!)
    useDownload(file.target)
    loading.value = false
  }

  async isFavorite() {
    const skuCode = this.skuCode
    const user = userStore()
    const company = companyStore()
    let isFav = false
    const res = await http<any>('/mall/p/my-favorites/getPersonalFavoritesByFolderIdAndUserId', {
      method: 'get',
      params: {
        userId: user.user.userId,
        merchantId: company.company.shopCompanyId || 0,
        folderId: 0,
        model: skuCode,
        current: 1,
        pageSize: 10,
      },
    })
    await useMall(res, () => {
      if (res.data.records.length) {
        isFav = true
      }
    })
    return isFav
  }

  async getFavInfo() {
    const skuCode = this.skuCode
    const user = userStore()
    const company = companyStore()
    let favInfo = null
    const res = await http<any>('/mall/p/my-favorites/getPersonalFavoritesByFolderIdAndUserId', {
      method: 'get',
      params: {
        userId: user.user.userId,
        merchantId: company.company.shopCompanyId || 0,
        folderId: 0,
        model: skuCode,
        current: 1,
        pageSize: 10,
      },
    })
    await useMall(res, () => {
      if (res.data.records.length) {
        favInfo = res.data.records[0]
      }
    })
    return favInfo
  }

  async downloadBe(loading: Ref<boolean>) {
    let filename = ''
    loading.value = true
    const res = await useNuxtApp().$customApi<Blob>('/api/selection/model-file-download', {
      method: 'post',
      body: {
        partId: this.partId,
        extra: this.mident,
      },
      responseType: 'arrayBuffer',
      onResponse(ctx) {
        const _file = ctx.response.headers.get('Content-Disposition')
        if (_file) {
          filename = decodeURI(_file.replace('attachment;filename=', ''))
        }
      },
    })
    loading.value = false

    fileDownload(res, filename)
  }

  goSource() {
    if (this.pageDetail) {
      let url = this.pageDetail.pageUrl
      if (!url) return
      const urlSearch = new URL(url)
      if (this.isFinished && this.skuCode) {
        urlSearch.searchParams.set('productModel', this.skuCode)
      }
      window.open(urlSearch.toString())
    }
  }
}

export default Model
