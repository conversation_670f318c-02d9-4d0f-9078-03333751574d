<template>  
  <div flex flex-row flex-wrap>
    <div v-for="(item, index) in imgs" :key="item" flex class="img-box">
      <img
        :src="item"
        w-full
        h-full
      />
      <div class="mask">
        <EyeOutlined text-20 mr-10 color-white @click="onView(index)" />
        <DeleteOutlined text-20 color-white @click="onDel(index)"/>
      </div>
    </div>
    <a-upload
      v-if="imgs.length < limit"
      v-model:file-list="fileList"
      :show-upload-list="false"
      action=""
      list-type="picture-card"
      :before-upload="beforeUpload"
      :customRequest="uploadImage"
      :multiple="false"
      style="width: auto"
    >    
      <plus-outlined></plus-outlined>
    </a-upload>

    <a-modal 
      v-model:open="show" 
      :footer="null" 
      :width="800"
    >
      <img
        :src="showSrc"
        w-full
        h-full
        pt-30
      />
    </a-modal>
  </div>
</template>

<script setup>
import Http from '~/utils/request';
import { uploadFile } from '~/api/common'

const emits = defineEmits(['change'])

const fileList = ref([])

const props = defineProps({
  limit: Number,
  value: String,
  beforeUpload: Function
})

const imgs = computed(() => {
  return props.value ? props.value.split(',') : []
})

const onDel = (index) => {
  const data = [...imgs.value]
  data.splice(index, 1)
  emits('change', data.join())
}

const show = ref(false)
const showSrc = ref('')
const onView = (index) => {
  showSrc.value = imgs.value[index]
  show.value = true
}

const uploadImage = async (data) => {
  // Http.upload('/file/upload?resourceType=Avatar', data.file).then(res => {
  //   console.log(res)
  // })
  const res = await uploadFile(data.file, 'Avatar')
  if (res.code === 'ok') {
    const data = [...imgs.value, res.data].join()
    emits('change', data)
  }
}
</script>

<style lang="less" scoped>
.img-box{
  position: relative;
  padding: 5px;
  width: 100px;
  height: 100px;
  cursor: pointer;

  &:hover{
    .mask{
      display: flex;
    }
  }

  .mask{
    position: absolute;
    background: rgba(0, 0, 0, 0.5);
    width: 100%;
    height: 100%;
    display: none;
    justify-content: center;
    align-items: center;
  }
}
</style>
