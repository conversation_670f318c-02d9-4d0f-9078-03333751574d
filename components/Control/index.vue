<template>
  <div h-40 bottom-20 inline-flex items-center gap-10 px-20 class="bg-#000000/70" text-24 text-white rounded-50 z-999>
    <a-popover color="rgba(0,0,0,.7)" :overlay-inner-style="{ borderRadius: '8px' }">
      <template #title>
        <div text-white>
          <div>文档名: {{ node.name }}</div>
        </div>
      </template>
      <InfoCircleOutlined cursor-pointer class="hover:text-primary" />
    </a-popover>
    <template v-if="type == 'pdf'">
      <VerticalRightOutlined cursor-pointer class="hover:text-primary" @click="emits('toStart')" />
      <ArrowLeftOutlined cursor-pointer class="hover:text-primary" @click="emits('pageChange', page - 1)" />
      <div flex-center text-16>
        第
        <a-input h-30 w-50 mx-8 :value.number="page" readonly></a-input>
        页
      </div>
      <ArrowRightOutlined cursor-pointer class="hover:text-primary" @click="emits('pageChange', page + 1)" />
      <VerticalLeftOutlined cursor-pointer class="hover:text-primary" @click="emits('toEnd')" />
    </template>
    <DownloadOutlined cursor-pointer class="hover:text-primary" @click="emits('download')" />
  </div>
</template>

<script lang="ts" setup>
// import { type DocTreeItem } from '~/api/document/type'
type Node = {
  name: string
}

const page = defineModel<number>('page', { default: 0 })

const emits = defineEmits<{
  pageChange: [page: number]
  toStart: []
  toEnd: []
  download: []
}>()

defineProps<{
  node: Node
  type: string
}>()
</script>

