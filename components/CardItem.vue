<template>
  <div card--reactive hover:translate-y-3 @click="onItemClick">
    <div flex p="y-8 x-12">
      <img h-24 :src="websiteImgUrl" />
      <div class="flex-1"></div>
      <a-popover title="该产品提供3D模型查看与下载" v-if="has3DModel">
        <CodeSandboxOutlined text="20 gray" ml-5 />
      </a-popover>
      <!-- <a-checkbox :checked="comparing">对比</a-checkbox> -->
    </div>
    <div>
      <div text-center>
        <img h-125 w-125 object-scale-down :src="imageUrl || unknownImg" />
      </div>
      <div p="y-4 x-12">
        <div sub-text mb-4>{{ websiteName }}</div>
        <div text truncate mb-8 :title="productName" :innerHTML="highlightedText"></div>
      </div>
    </div>
    <div
      h-40
      line-height-40
      @click.stop="onPreviewDocClick"
      :class="{
        'btn-disabled': !specPdfUrl,
        'btn-primary': !!specPdfUrl,
      }"
    >
      <FileTextOutlined class="mr-4" />
      预览图档
    </div>
  </div>
</template>

<script lang="ts" setup>
import { usePartStore } from '~/store/part'
import unknownImg from '~/assets/images/unknown.png'
import ms from 'magic-string'
import { userStore } from '~/store/user'

const store = userStore()

export interface Property {
  id: string | number
  key: string
  value: string
}

const emits = defineEmits(['click'])

export interface Props {
  id: string | number
  websiteImgUrl: string
  websiteName: string
  imageUrl: string
  productName: string
  pageUrl: string
  specPdfUrl?: string
  highlights?: Array<string> | string
  filterConditions?: object
  partCode?: string
  has3DModel?: boolean
}
const { addValueToPart } = usePartStore()

const props = defineProps<Props>()

const highlightedText = computed(() => {
  if (!props.highlights) return props.productName
  if (Array.isArray(props.highlights)) {
    const [text] = props.highlights
    let match
    const reg = /<em>(.*?)<\/em>/g
    const s = new ms(text)
    while ((match = reg.exec(text))) {
      s.update(match.index, match.index + match[0].length, `<span style="color: red">${match[1]}</span>`)
    }
    return s.toString()
  } else if (typeof props.highlights === 'string') {
    const reg = new RegExp(`(${props.highlights})`, 'ig')
    const s = new ms(props.productName)
    let match
    while ((match = reg.exec(props.productName))) {
      s.update(match.index, match.index + match[0].length, `<span style="color: red">${match[1]}</span>`)
    }
    return s.toString()
  }
  return props.productName
})

const onItemClick = () => {
  console.log(`output->props`, props)
  // router.push({
  //   path: '/parts/' + props.id,
  //   query: {
  //     typeId: props.partCode,
  //   },
  // })

  emits('click')
  // 临时跳转到源网页
  // window.open(props.pageUrl, '_blank')
  addValueToPart(props)
}

const onPreviewDocClick = () => {
  if (!!props.specPdfUrl) {
    if (!store.user.userId) {
      loginPop.show()
      return
    }

    previewDoc(props.specPdfUrl)
  } else {
    return
  }
}
</script>
