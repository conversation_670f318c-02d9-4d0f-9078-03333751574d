<template>
  <div v-html="shopProtocolHtml" class="w-full px-20 mx-auto leading-25 text-14"></div>
  <div class="mt-30 border-t border-#eee pt-20">
    <div class="mb-20">
      <a-checkbox v-model:checked="formData.checked">
        我已阅读并同意《研选工场装备制造业AI供应链服务平台供应商入驻协议》
      </a-checkbox>
    </div>
  </div>
</template>

<script setup>
import shopProtocolHtml from '~/components/settleIn/shopProtocol.js'

const props = defineProps({
  formData: Object,
})

// 协议步骤不需要特别的验证规则
const validate = () => {
  return new Promise((resolve, reject) => {
    if (!props.formData.checked) {
      reject('请阅读并同意入驻协议')
    } else {
      resolve()
    }
  })
}

// 暴露验证方法给父组件
defineExpose({
  validate,
})
</script>
