<template>
  <a-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    :label-col="{ span: 8 }"
    :wrapper-col="{ span: 16 }"
    autocomplete="off"
  >
    <a-row :gutter="20" pt-50>
      <a-col :span="12">
        <a-form-item name="merchantName" label="企业名称">
          <Bussiness :disabled="!editable" v-model:value="formData.merchantName" @change="changeBussiness" />
        </a-form-item>
        <a-form-item name="creditCode" label="统一社会信用代码">
          <a-input :disabled="!editable" v-model:value="formData.creditCode" :maxlength="20" />
        </a-form-item>
        <a-form-item name="residence" label="住所">
          <a-input :disabled="!editable" v-model:value.trim="formData.residence" :maxlength="50" />
        </a-form-item>
        <a-form-item name="representative" label="法定代表人">
          <a-input :disabled="!editable" v-model:value.trim="formData.representative" :maxlength="20" />
        </a-form-item>
        <a-form-item name="capital" label="注册资本(万元)">
          <a-input-number
            :disabled="!editable"
            v-model:value.trim="formData.capital"
            type="number"
            prefix="¥"
            :min="0"
            :max="99999999"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item name="foundTime" label="成立日期">
          <a-date-picker
            :disabled="!editable"
            v-model:value="formData.foundTime"
            valueFormat="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item name="startTime" label="营业期限">
          <a-date-picker
            :disabled="!editable"
            v-model:value="formData.startTime"
            valueFormat="YYYY-MM-DD HH:mm:ss"
            style="width: 45%"
          />
          <span text-center style="display: inline-block; width: 10%">~</span>
          <a-form-item-rest>
            <a-date-picker
              :disabled="!editable"
              v-model:value="formData.endTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              placeholder="无固定期限"
              style="width: 45%"
            />
          </a-form-item-rest>
        </a-form-item>
        <a-form-item name="businessScope" label="经营范围">
          <a-textarea :disabled="!editable" v-model:value.trim="formData.businessScope" :maxlength="500" showCount />
        </a-form-item>
      </a-col>
      <a-col :span="10">
        <a-form-item text-center name="businessLicense" label="营业执照电子版">
          <a-row>
            <a-col :span="12">
              <img-upload
                name="businessLicense"
                :value="useObs(formData.businessLicense)"
                :beforeUpload="beforeUpload"
                :disabled="!editable"
                @change="(v) => (formData.businessLicense = v)"
              />
            </a-col>
            <a-col :span="12">
              <img src="~/assets/images/example/Business-license.png" w-80 h-100 />
            </a-col>
          </a-row>
          <div sub-text>限2MB以内的jpg、jpeg、png文件</div>
        </a-form-item>
        <a-form-item text-center name="identityCardFront" label="法人身份证(正面)">
          <a-row>
            <a-col :span="12">
              <img-upload
                name="identityCardFront"
                :value="useObs(formData.identityCardFront)"
                :beforeUpload="beforeUpload"
                @change="(v) => (formData.identityCardFront = v)"
                :disabled="!editable"
              />
            </a-col>
            <a-col :span="12">
              <img src="~/assets/images/example/idcard1.png" alt="identityCardFront" w-140 h-100 />
            </a-col>
          </a-row>
          <div sub-text>限2MB以内的jpg、jpeg、png文件</div>
        </a-form-item>
        <a-form-item text-center name="identityCardLater" label="法人身份证(反面)">
          <a-row>
            <a-col :span="12">
              <img-upload
                name="identityCardLater"
                :value="useObs(formData.identityCardLater)"
                :beforeUpload="beforeUpload"
                @change="(v) => (formData.identityCardLater = v)"
                :disabled="!editable"
              />
            </a-col>
            <a-col :span="12">
              <img src="~/assets/images/example/idcard2.png" alt="identityCardFront" w-140 h-100 />
            </a-col>
          </a-row>
          <div sub-text>限2MB以内的jpg、jpeg、png文件</div>
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
</template>

<script setup>
import dayjs from 'dayjs'
import { getShopCompanyByCreditCode } from '@/api/mall-platform'
const props = defineProps({
  form: Object,
  formData: Object,
  editable: {
    type: Boolean,
    default: true,
  },
})

const route = useRoute()
const companyId = route.query.companyId

const formRef = ref(null)
const vaildCreditCode = async (rule, value) => {
  if (!value) return Promise.resolve()
  const reg = /^(([0-9A-Za-z]{15})|([0-9A-Za-z]{18})|([0-9A-Za-z]{20}))$/
  if (!reg.test(value)) {
    return Promise.reject('请输入正确的统一社会信用代码')
  }
  if (!companyId) {
    const res = await getShopCompanyByCreditCode({
      creditCode: value,
      merchantId: 0,
    })
    // await http_mall(res, () => {})
    if (res.data == 'true') {
      return Promise.reject('该企业已入驻,请联系企业管理员开通服务')
      // 91320594MA26AEATX5
    }
    return Promise.resolve()
  }
  return Promise.resolve()
}

// 定义验证规则
const rules = reactive({
  merchantName: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
  creditCode: [
    { required: true, message: '请输入统一社会信用代码', trigger: 'blur' },
    { validator: vaildCreditCode, trigger: 'blur' },
  ],
  residence: [{ required: false, message: '请输入住所', trigger: 'blur' }],
  representative: [{ required: true, message: '请输入法定代表人', trigger: 'blur' }],
  capital: [{ required: false, message: '请输入注册资本', trigger: 'blur' }],
  // foundTime: [{ required: true, message: '请选择成立日期', trigger: 'change' }],
  startTime: [{ required: false, message: '请选择营业期限开始日期', trigger: 'change' }],
  businessScope: [{ required: false, message: '请输入经营范围', trigger: 'blur' }],
  businessLicense: [{ required: true, message: '请上传营业执照电子版', trigger: 'change' }],
  identityCardFront: [{ required: false, message: '请上传法人身份证正面', trigger: 'change' }],
  identityCardLater: [{ required: false, message: '请上传法人身份证反面', trigger: 'change' }],
})

// 验证方法
const validate = () => {
  return formRef.value.validate()
}

// 文件上传前验证
const beforeUpload = (file) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
  if (!isJpgOrPng) {
    message.error('只能上传JPG/PNG格式的图片!')
    return false
  }
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    message.error('图片大小不能超过2MB!')
    return false
  }
  return isJpgOrPng && isLt2M
}

const changeBussiness = (_, option) => {
  props.formData.representative = option.legalPersonName
  props.formData.creditCode = option.creditCode
  props.formData.foundTime = dayjs(option.estiblishTime).format('YYYY-MM-DD HH:mm:ss')
  nextTick(() => {
    formRef.value.validate(['merchantName', 'creditCode', 'representative', 'foundTime'])
  })
}

// 暴露验证方法给父组件
defineExpose({
  validate,
})
</script>
