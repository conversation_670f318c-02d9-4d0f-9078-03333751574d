<template>
  <a-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    :label-col="{ span: 8 }"
    :wrapper-col="{ span: 16 }"
    autocomplete="off"
  >
    <a-row :gutter="20" pt-50>
      <a-col :span="12">
        <div class="flex justify-between items-center mb-20">
          <span class="text-16 font-bold">财务信息</span>
          <!-- <a-button type="link" @click="onSkip" v-if="!hideSkip">跳过此步骤</a-button> -->
        </div>

        <a-form-item name="depositBank" label="开户银行">
          <a-input v-model:value.trim="formData.depositBank" placeholder="请输入开户银行" />
        </a-form-item>

        <a-form-item name="bankAccount" label="银行账号">
          <a-input v-model:value.trim="formData.bankAccount" placeholder="请输入15-21位银行账号" />
        </a-form-item>

        <a-form-item name="accountName" label="账户名称">
          <a-input v-model:value.trim="formData.accountName" placeholder="请输入账户名称" />
        </a-form-item>

        <a-form-item name="certifyDoc" label="证明文件">
          <common-upload-component
            v-model="formData.certifyDoc"
            :max-count="1"
            :before-upload="beforeUploadImageOrPdf"
            :accept="'.jpg,.jpeg,.png,.pdf'"
            tip="请上传JPG/PNG/PDF格式的银行开户证明或银行账户信息页截图"
          />
        </a-form-item>

        <a-form-item name="billingType" label="开票类型">
          <a-radio-group v-model:value="formData.billingType">
            <a-radio :value="1">13%增值税专用发票</a-radio>
            <a-radio :value="2">9%增值税专用发票</a-radio>
            <a-radio :value="3">3%增值税专用发票</a-radio>
            <a-radio :value="4">普通发票</a-radio>
            <a-radio :value="5">其他</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item v-if="formData.billingType === 5" name="billingTypeNotes" label="开票类型说明">
          <a-input v-model:value.trim="formData.billingTypeNotes" placeholder="请说明开票类型" />
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { UploadOutlined, PaperClipOutlined, CloseOutlined } from '@ant-design/icons-vue'
import { uploadFile } from '~/api/common'

const props = defineProps({
  formData: Object,
  hideSkip: Boolean,
})

onMounted(() => {
  props.formData.isSkipBank = false
})

const emit = defineEmits(['skip'])

const formRef = ref(null)

// 银行选项
const bankOptions = ref([])

// 定义验证规则 - 财务信息是可选的，但如果填写了部分信息，则需要验证完整性
const rules = reactive({
  depositBank: [
    {
      required: true,
      message: '请输入开户银行',
      trigger: 'blur',
    },
  ],
  bankAccount: [
    {
      required: true,
      message: '请输入银行账号',
      trigger: 'blur',
    },
    {
      validator: (rule, value, callback) => {
        if (value && (value.length < 15 || value.length > 21)) {
          callback(new Error('请输入15-21位银行账号'))
        }
        callback()
      },
      trigger: 'blur',
    },
  ],
  accountName: [
    {
      required: true,
      message: '请输入账户名称',
      trigger: 'blur',
    },
  ],
  certifyDoc: [
    {
      required: true,
      message: '请上传证明文件',
      trigger: 'change',
    },
  ],
  billingType: [
    {
      required: true,
      message: '请选择开票类型',
      trigger: 'blur',
    },
  ],
  billingTypeNotes: [
    {
      required: () => props.formData.billingType === 5,
      message: '请说明开票类型',
      trigger: 'blur',
    },
  ],
})

// 验证方法
const validate = () => {
  return formRef.value.validate()
}

// 跳过此步骤
const onSkip = () => {
  props.formData.isSkipBank = true
  emit('skip')
}

// 文件上传前验证
const beforeUploadImageOrPdf = (file) => {
  const isImageOrPdf = /\.(jpg|jpeg|png|pdf)$/i.test(file.name)
  if (!isImageOrPdf) {
    message.error('只能上传JPG/PNG/PDF格式的文件!')
    return false
  }
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!')
    return false
  }
  return isImageOrPdf && isLt10M
}

// 文件上传处理
const handleCustomUpload = async (options, fieldName) => {
  const { file, onSuccess, onError } = options
  try {
    const res = await uploadFile(file, 'Avatar')
    if (res.code === 'ok') {
      props.formData[fieldName] = res.data
      onSuccess(res)
    } else {
      onError()
      message.error('上传失败')
    }
  } catch (error) {
    onError()
    message.error('上传失败')
  }
}

// 暴露验证方法给父组件
defineExpose({
  validate,
})
</script>

<style scoped>
.uploaded-files {
  margin-top: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  margin-bottom: 4px;
  background: #f5f5f5;
  border-radius: 4px;
}

.file-name {
  margin: 0 8px;
  flex: 1;
}

.delete-icon {
  cursor: pointer;
  color: #999;
}

.delete-icon:hover {
  color: #ff4d4f;
}
</style>
