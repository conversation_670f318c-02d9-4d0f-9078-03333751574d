<template>
  <a-select
    v-model:value="value"
    label-in-value
    placeholder="请输入品牌名称"
    style="width: 100%"
    :filter-option="false"
    :not-found-content="fetching ? undefined : null"
    :options="computedOptions"
    @search="fetchBrand"
    v-bind="$attrs"
    :fieldNames="{ label: 'name', value: 'brandId' }"
  >
    <template v-if="fetching" #notFoundContent>
      <a-spin size="small" />
    </template>
  </a-select>
</template>
<script setup>
import { debounce } from 'lodash-es'

const props = defineProps({
  disableSet: {
    type: Set,
    default: () => new Set(),
  },
})

let lastFetchId = 0

const attrs = useAttrs()

const options = ref([])
const fetching = ref(false)
const modelValue = defineModel()

const value = computed({
  get: () => {
    if (attrs.mode == 'multiple') {
      return modelValue.value?.map((item) => ({ value: item })) || []
    } else {
      return modelValue.value ? { value: modelValue.value } : undefined
    }
  },
  set: (val) => {
    if (attrs.mode == 'multiple') {
      modelValue.value = val?.map((item) => item.value) || []
    } else {
      modelValue.value = val?.value
    }
  },
})

const fetchBrand = debounce(async (value) => {
  lastFetchId += 1
  const fetchId = lastFetchId
  options.value = []
  fetching.value = true
  const [err, res] = await try_http('/mall/brand/list', {
    params: {
      status: 1,
      name: value,
    },
  })
  if (!err) {
    if (fetchId !== lastFetchId) {
      // for fetch callback order
      return
    }
    options.value = res.data
    fetching.value = false
  }
}, 300)

const computedOptions = computed(() => {
  return options.value.map((item) => {
    return {
      ...item,
      disabled: props.disableSet.has(item.brandId),
    }
  })
})

onMounted(async () => {
  await fetchBrand('')
})
</script>
