<template>
  <a-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    :label-col="{ span: 8 }"
    :wrapper-col="{ span: 16 }"
    autocomplete="off"
    :disabled="disabled"
  >
    <a-row :gutter="20" pt-50>
      <a-col :span="12">
        <a-form-item name="supplierType" label="供应商类型">
          <a-radio-group v-model:value="formData.supplierType">
            <a-radio v-for="item in supplierTypeList" :key="item.value" :value="item.value">
              {{ item.name }}
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 贸易商特有字段 -->
        <div v-if="formData.supplierType === 1">
          <a-form-item name="agencyAreaScope" label="代理区域范围">
            <a-checkbox-group v-model:value="formData.agencyAreaScope" style="width: 100%">
              <a-checkbox :value="1">全国</a-checkbox>
              <a-checkbox :value="2">华东</a-checkbox>
              <a-checkbox :value="3">华南</a-checkbox>
              <a-checkbox :value="4">华北</a-checkbox>
              <a-checkbox :value="5">其他</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
          <a-form-item name="agencyBrands" label="代理品牌">
            <brand-selector
              mode="multiple"
              v-model="formData.agencyBrands"
              @change="handleAgencyBrandsChange"
            ></brand-selector>
            <!-- <a-select
              v-model:value="formData.agencyBrands"
              mode="multiple"
              placeholder="请选择代理品牌"
              style="width: 100%"
              :options="brandList"
              :fieldNames="{ label: 'name', value: 'brandId' }"
              optionFilterProp="name"
              @change="handleAgencyBrandsChange"
              :getPopupContainer="el => el.parentElement"
            /> -->
          </a-form-item>

          <!-- 假一罚十协议 -->
          <a-form-item
            name="penaltyAgreement"
            label="假一罚十协议"
            :rules="[{ required: hasEmptyAuthorization, message: '由于存在未提供授权书的品牌，请上传假一罚十协议' }]"
          >
            <div class="equipment-inventory-upload">
              <a-button type="link" @click="downloadPenaltyTemplate">
                <download-outlined />
                下载模板
              </a-button>
              <upload-component
                v-model="formData.penaltyAgreement"
                accept=".xlsx"
                :multiple="false"
                fileType="excel"
                buttonText="上传协议"
              />
            </div>
          </a-form-item>

          <!-- 贸易商品牌对应的产品分类与授权书 -->
          <div v-if="formData.agencyBrands && formData.agencyBrands.length > 0">
            <a-divider orientation="left">品牌产品分类与授权</a-divider>
            <div v-for="brand in formData.selectBrands" :key="brand.brandId" class="brand-authorization brand-item">
              <div class="brand-header">
                <span class="brand-name">{{ brand.name }}</span>
              </div>

              <!-- 为每个品牌添加产品分类选择 -->
              <a-form-item
                :name="['brandProductCategories', brand.brandId]"
                label="产品分类"
                :rules="[{ required: true, message: `请为${brand.name}选择产品分类` }]"
              >
                <product-select
                  :brand-id="brand.brandId"
                  v-model="formData.brandProductCategories[brand.brandId]"
                ></product-select>
              </a-form-item>

              <!-- 品牌授权书 -->
              <a-form-item :name="['brandAuthorizations', brand.brandId]" label="代理授权书">
                <upload-component
                  v-model="formData.brandAuthorizations[brand.brandId]"
                  accept=".pdf,.jpg,.jpeg,.png"
                  :multiple="true"
                  fileType="pdf"
                  buttonText="点击上传"
                  tip="请上传PDF/JPG/JPEG/PNG格式的代理授权书，可以传多份"
                />
              </a-form-item>
            </div>
          </div>
        </div>

        <!-- 加工商特有字段 -->
        <div v-if="formData.supplierType === 2">
          <a-form-item name="mainProductCategories" label="主营产品分类">
            <product-select v-model="formData.mainProductCategories"></product-select>
            <!-- <a-tree-select -->
            <!--   v-model:value="formData.mainProductCategories" -->
            <!--   :treeData="productCategoryOptions" -->
            <!--   treeCheckable -->
            <!--   showCheckedStrategy="SHOW_PARENT" -->
            <!--   placeholder="请选择主营产品分类" -->
            <!--   style="width: 100%" -->
            <!-- /> -->
          </a-form-item>

          <a-form-item name="coreProcessingCapability" label="核心加工能力">
            <a-checkbox-group v-model:value="formData.coreProcessingCapability" style="width: 100%">
              <a-checkbox :value="1">方件</a-checkbox>
              <a-checkbox :value="2">圆件</a-checkbox>
              <a-checkbox :value="3">大板</a-checkbox>
              <a-checkbox :value="4">钣金焊接加工</a-checkbox>
              <a-checkbox :value="5">精密机加工（精度≤±0.01mm）</a-checkbox>
              <a-checkbox :value="6">表面处理（喷涂/电镀）</a-checkbox>
              <a-checkbox :value="7">注塑成型</a-checkbox>
              <a-checkbox :value="8">铝挤型材</a-checkbox>
              <a-checkbox :value="9">铸造</a-checkbox>
              <a-checkbox :value="10">其他</a-checkbox>
            </a-checkbox-group>
          </a-form-item>

          <a-form-item name="maxOrderAmount" label="最大承接订单额(万元/月)">
            <a-input-number
              v-model:value="formData.maxOrderAmount"
              type="number"
              prefix="¥"
              :min="0"
              :max="99999999"
              style="width: 100%"
            />
          </a-form-item>

          <!-- 加工商、品牌商特有字段：设备清单表 -->
          <a-form-item name="equipmentList" label="设备清单表">
            <div class="equipment-inventory-upload">
              <a-button type="link" @click="downloadEquipmentTemplate">
                <download-outlined />
                下载模板
              </a-button>
              <upload-component
                v-model="formData.equipmentList"
                accept=".xlsx,.xls"
                maxCount="1"
                fileType="excel"
                buttonText="上传清单"
              />
            </div>
          </a-form-item>

          <a-form-item name="inspectionList" label="质检清单表">
            <div class="equipment-inventory-upload">
              <a-button type="link" @click="downloadInspectionTemplate">
                <download-outlined />
                下载模板
              </a-button>
              <upload-component
                v-model="formData.inspectionList"
                accept=".xlsx,.xls"
                maxCount="1"
                fileType="excel"
                buttonText="上传清单"
              />
            </div>
          </a-form-item>
        </div>

        <!-- 品牌商特有字段 -->
        <div v-if="formData.supplierType === 3">
          <a-form-item v-if="!formData.ownBrandList.length" name="ownBrands" label="自有品牌">
            <div class="brand-selection-wrapper">
              <!-- 品牌搜索与选择区域 -->
              <div class="brand-search-area">
                <div class="section-title">从品牌字典中搜索并选择品牌</div>
                <div class="brand-selection-container">
                  <!-- <a-select
                    v-model:value="selectedBrandFromDict"
                    style="width: 100%"
                    placeholder="请输入品牌名称搜索"
                    :options="brandList"
                    showSearch
                    allowClear
                    :fieldNames="{ label: 'name', value: 'brandId' }"
                    optionFilterProp="name"
                    @change="handleBrandSelect"
                  /> -->
                  <brand-selector
                    v-model="selectedBrandFromDict"
                    show-search
                    @change="handleBrandSelect"
                    :disable-set="disableBrand"
                  ></brand-selector>
                  <a-button type="primary" @click="addBrandFromDict" :disabled="!selectedBrandFromDict" class="ml-2">
                    添加选中品牌
                  </a-button>
                </div>
                <div class="search-tip">
                  <a-alert type="info" show-icon>
                    <template #message>请先在品牌字典中搜索您的品牌，大部分常见品牌已收录</template>
                  </a-alert>
                </div>
              </div>

              <!-- 自定义品牌添加区域 -->
              <div class="brand-custom-area mt-4">
                <div class="section-title">如果在品牌字典中找不到，可添加自定义品牌</div>
                <div class="custom-brand-container">
                  <a-input v-model:value="customBrandName" style="width: 100%" placeholder="输入自定义品牌名称" />
                  <a-button
                    type="default"
                    @click="addCustomBrand"
                    :disabled="!customBrandName"
                    class="ml-2"
                    :loading="addCustomLoading"
                  >
                    添加自定义品牌
                  </a-button>
                </div>
                <div class="custom-tip">
                  <a-alert type="warning" show-icon>
                    <template #message>自定义品牌需要额外上传品牌Logo</template>
                  </a-alert>
                </div>
              </div>
            </div>
          </a-form-item>

          <!-- 已添加品牌列表 -->
          <div v-if="formData.ownBrandList.length">
            <a-divider orientation="left">已添加品牌</a-divider>

            <div v-for="(brand, index) in formData.ownBrandList" :key="brand.brandId" class="brand-item">
              <div class="brand-header">
                <span class="brand-name">{{ brand.name }}</span>
                <span class="brand-source">{{ brand.type === 'dict' ? '(品牌字典)' : '(自定义品牌)' }}</span>
                <a-button type="link" danger size="small" @click="removeBrand(brand.brandId)">移除</a-button>
              </div>

              <!-- 为每个品牌添加产品分类选择 -->
              <a-form-item
                :name="['ownBrandList', index, 'categoryIds']"
                label="产品分类"
                :rules="[{ required: true, message: `请为${brand.name}选择产品分类` }]"
              >
                <product-select
                  :brand-id="brand.type === 'dict' ? brand.brandId : null"
                  v-model="formData.ownBrandList[index].categoryIds"
                ></product-select>
              </a-form-item>

              <!-- 商标注册证书上传 -->
              <a-form-item
                :name="['ownBrandList', index, 'trademarkRegistrationCertificate']"
                label="商标注册证书"
                :rules="[{ required: true, message: `请上传${brand.name}的商标注册证书` }]"
              >
                <upload-component
                  v-model="formData.ownBrandList[index].trademarkRegistrationCertificate"
                  accept=".pdf,.jpg,.jpeg,.png"
                  :multiple="false"
                  fileType="all"
                  buttonText="点击上传"
                  tip="请上传PDF/JPG/JPEG/PNG格式的商标注册证书"
                />
              </a-form-item>

              <!-- 仅自定义品牌需要上传logo -->
              <a-form-item
                v-if="brand.type === 'custom'"
                :name="['ownBrandList', index, 'logo']"
                label="品牌Logo"
                :rules="[{ required: true, message: `请上传${brand.name}的品牌Logo` }]"
              >
                <upload-component
                  v-model="formData.ownBrandList[index].logo"
                  accept=".jpg,.jpeg,.png,.svg"
                  :multiple="false"
                  max-count="1"
                  fileType="image"
                  buttonText="点击上传"
                  tip="请上传JPG/JPEG/PNG/SVG格式的品牌Logo"
                />
              </a-form-item>

              <a-form-item label="平替品牌">
                <brand-replace
                  :disable-set="disableSet"
                  v-model="formData.ownBrandList[index].replaceBrands"
                ></brand-replace>
              </a-form-item>
            </div>
          </div>

          <!-- 品牌商特有字段：专利证书列表 -->
          <a-form-item name="patentCertificate" label="专利证书">
            <upload-component
              v-model="formData.patentCertificate"
              accept=".pdf,.jpg,.jpeg,.png"
              fileType="all"
              buttonText="点击上传"
              tip="请上传PDF/JPG/JPEG/PNG格式的专利证书，可多选"
            />
          </a-form-item>

          <a-form-item name="equipmentList" label="设备清单表">
            <div class="equipment-inventory-upload">
              <a-button type="link" @click="downloadEquipmentTemplate">
                <download-outlined />
                下载模板
              </a-button>
              <upload-component
                v-model="formData.equipmentList"
                accept=".xlsx,.xls"
                :multiple="false"
                fileType="excel"
                buttonText="上传清单"
              />
            </div>
          </a-form-item>

          <a-form-item name="inspectionList" label="质检清单表">
            <div class="equipment-inventory-upload">
              <a-button type="link" @click="downloadInspectionTemplate">
                <download-outlined />
                下载模板
              </a-button>
              <upload-component
                v-model="formData.inspectionList"
                accept=".xlsx,.xls"
                :multiple="false"
                fileType="excel"
                buttonText="上传清单"
              />
            </div>
          </a-form-item>
        </div>
      </a-col>

      <a-col :span="10">
        <!-- 将以下四个字段移到右侧 -->
        <a-form-item name="companyAddress" label="企业地址">
          <a-input v-model:value="formData.companyAddress" placeholder="请输入企业地址" />
        </a-form-item>

        <a-form-item name="plantPropertyRight" label="厂房产权">
          <a-radio-group v-model:value="formData.plantPropertyRight">
            <a-radio :value="1">自有</a-radio>
            <a-radio :value="2">租赁</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item name="plantArea" label="厂房面积(平方米)">
          <a-radio-group v-model:value="formData.plantArea">
            <a-radio :value="1">小于500</a-radio>
            <a-radio :value="2">500-2000</a-radio>
            <a-radio :value="3">2000以上</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item name="staffAmount" label="在职人员数">
          <a-radio-group v-model:value="formData.staffAmount">
            <a-radio :value="1">20人以下</a-radio>
            <a-radio :value="2">20-50人</a-radio>
            <a-radio :value="3">50人以上</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item name="annualTurnover" label="年营业额">
          <a-input-number
            v-model:value="formData.annualTurnover"
            placeholder="请输入年营业额"
            style="width: 100%"
            :min="0"
            :precision="2"
            addonAfter="万元"
          />
        </a-form-item>

        <a-form-item name="qualityAuthentication" label="质量认证">
          <upload-component
            v-model="formData.qualityAuthentication"
            accept=".pdf"
            :multiple="false"
            fileType="pdf"
            buttonText="点击上传"
            max-count="1"
            tip="请上传PDF格式的质量认证文件"
          />
        </a-form-item>

        <a-form-item name="customerCase" label="客户合作案例">
          <upload-component
            v-model="formData.customerCase"
            accept=".jpg,.jpeg,.png,.pdf"
            :multiple="true"
            fileType="all"
            buttonText="点击上传"
            tip="请上传JPG/PNG/PDF格式的客户合作案例，可多选"
          />
        </a-form-item>

        <a-form-item name="plantVideo" label="工厂视频">
          <upload-component
            v-model="formData.plantVideo"
            accept=".mp4,.mov,.avi"
            :multiple="false"
            fileType="video"
            :maxSize="100"
            buttonText="点击上传"
            tip="请上传MP4/MOV/AVI格式的工厂视频，大小不超过100MB"
          />
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
</template>

<script setup>
import { message } from 'ant-design-vue'
import UploadComponent from '~/components/common/UploadComponent.vue'
import ProductSelect from '~/components/settle/product-select.vue'
import BrandSelector from './brand-selector.vue'

const props = defineProps({
  formData: Object,
  disabled: Boolean,
})

const formRef = ref(null)

// 自定义品牌名称
const customBrandName = ref('')

// 供应商类型列表
const supplierTypeList = [
  { name: '贸易商', value: 1 },
  { name: '加工商', value: 2 },
  { name: '品牌商', value: 3 },
]

// 验证供应商类型
const validateSupplierType = (rule, value) => {
  if (!value) {
    return Promise.reject('请选择供应商类型')
  }

  // 检查值是否在有效范围内
  const validValues = supplierTypeList.map((item) => item.value)
  if (!validValues.includes(value)) {
    return Promise.reject('请选择有效的供应商类型')
  }

  return Promise.resolve()
}

// 动态计算表单验证规则
const rules = computed(() => {
  // 基础规则，适用于所有供应商类型
  const baseRules = {
    supplierType: [{ required: true, validator: validateSupplierType, trigger: 'change' }],
    maxOrderAmount: [{ required: true, message: '请输入最大订单金额', trigger: 'blur' }],
    enterpriseAddress: [{ required: true, message: '请输入企业地址', trigger: 'blur' }],
    factoryPropertyRights: [{ required: false, message: '请选择厂房产权', trigger: 'change' }],
    factoryArea: [{ required: false, message: '请输入厂房面积', trigger: 'blur' }],
    qualityCertification: [{ required: false, message: '请上传质量认证', trigger: 'change' }],
    customerCooperationCases: [{ required: false, message: '请上传客户合作案例', trigger: 'change' }],
  }

  // 根据供应商类型添加特定规则
  if (props.formData.supplierType === 1) {
    // 贸易商
    return {
      ...baseRules,

      agencyBrands: [{ required: true, message: '请输入代理品牌', trigger: 'blur' }],
      agencyAuthorizationLetter: [{ required: true, message: '请上传代理授权书', trigger: 'change' }],
      agencyRegionScope: [{ required: true, message: '请选择代理区域范围', trigger: 'change' }],
    }
  } else if (props.formData.supplierType === 2) {
    // 加工商
    return {
      ...baseRules,
      mainProductCategories: [{ required: false, message: '请选择主营产品类别', trigger: 'change' }],
      coreProcessingCapabilities: [{ required: true, message: '请选择核心加工能力', trigger: 'change' }],
      plantVideo: [{ required: false, message: '请上传工厂视频', trigger: 'change' }],
    }
  } else if (props.formData.supplierType === 3) {
    // 品牌商
    return {
      ...baseRules,
      ownBrandName: [{ required: true, message: '请输入自有品牌名称', trigger: 'blur' }],
      trademarkRegistrationCertificate: [{ required: true, message: '请上传商标注册证书', trigger: 'change' }],
      patentCertificate: [{ required: false, message: '请上传专利证书', trigger: 'change' }],
    }
  }

  // 默认返回基础规则
  return baseRules
})

watchEffect(() => {
  if (props.formData.supplierType === 1) {
    props.formData.brandCategorySignings = props.formData.selectBrands.map((brand) => {
      return {
        brandId: brand.brandId,
        categories: props.formData.brandProductCategories[brand.brandId],
        qualifications: props.formData.brandAuthorizations[brand.brandId],
      }
    })
  } else if (props.formData.supplierType === 2) {
    props.formData.brandCategorySignings = [
      {
        categories: props.formData.mainProductCategories,
      },
    ]
  } else if (props.formData.supplierType === 3) {
    props.formData.brandCategorySignings = props.formData.ownBrandList.map((brand) => {
      if (brand.type === 'dict') {
        return {
          brandId: brand.brandId,
          brandName: brand.name,
          categories: brand.categoryIds,
          qualifications: brand.trademarkRegistrationCertificate,
          brandLogo: brand.logo,
          replaceBrands: brand.replaceBrands,
        }
      } else if (brand.type === 'custom') {
        return {
          brandName: brand.name,
          categories: brand.categoryIds,
          qualifications: brand.trademarkRegistrationCertificate,
          brandLogo: brand.logo,
          replaceBrands: brand.replaceBrands,
        }
      }
    })
  }
})
// 监听贸易商选择的代理品牌变化
const handleAgencyBrandsChange = (_selectedBrands, selectedOptions) => {
  props.formData.selectBrands = selectedOptions
  // 确保brandAuthorizations存在
  if (!props.formData.brandAuthorizations) {
    props.formData.brandAuthorizations = {}
  }

  // 确保brandProductCategories存在
  if (!props.formData.brandProductCategories) {
    props.formData.brandProductCategories = {}
  }

  // 移除不再选中的品牌授权书和产品分类
  const brandAuthorizations = {}
  const brandProductCategories = {}
  selectedOptions.forEach((brand) => {
    brandAuthorizations[brand.brandId] = props.formData.brandAuthorizations[brand.brandId] || ''
    brandProductCategories[brand.brandId] = props.formData.brandProductCategories[brand.brandId] || ''
  })
  props.formData.brandAuthorizations = brandAuthorizations
  props.formData.brandProductCategories = brandProductCategories
}

// 初始化贸易商品牌授权和产品分类数据结构
const initFormData = () => {
  if (!props.formData.brandAuthorizations) {
    props.formData.brandAuthorizations = {}
  }

  // 初始化品牌产品分类数据结构
  if (!props.formData.brandProductCategories) {
    props.formData.brandProductCategories = {}
  }
}

// 已有的初始化品牌相关数据函数
const initBrandData = () => {
  if (!props.formData.ownBrands) {
    props.formData.ownBrands = []
  }

  if (!props.formData.brandCertificates) {
    props.formData.brandCertificates = {}
  }

  // 初始化品牌产品分类数据结构
  if (!props.formData.brandProductCategories) {
    props.formData.brandProductCategories = {}
  }
}

// 提示用户未搜索到品牌时可以添加自定义品牌
const ownBrand = ref(null)
const handleBrandSelect = (_, option) => {
  ownBrand.value = option
}

// 已选择的字典品牌
const selectedBrandFromDict = ref(null)
// 添加来自字典的品牌
const addBrandFromDict = () => {
  if (!ownBrand.value) return

  if (disableBrand.value.has(ownBrand.value.brandId)) {
    message.error('您所添加的品牌在平替品牌中已存在')
    return
  }

  if (!props.formData.ownBrandList.some((item) => item.brandId === ownBrand.value.brandId)) {
    props.formData.ownBrandList.push({
      brandId: ownBrand.value.brandId,
      name: ownBrand.value.name,
      type: 'dict',
      categoryIds: '',
      logo: ownBrand.value.imgUrl,
    })

    nextTick(() => {
      // dictBrandRef.value.clear()
      selectedBrandFromDict.value = null
      ownBrand.value = null
    })
  } else {
    message.warning(`品牌 "${ownBrand.value.name}" 已添加，不能重复添加`)
  }
}

// 添加自定义品牌前先校验品牌字典
const addCustomLoading = ref(false)
const addCustomBrand = async () => {
  const name = customBrandName.value.trim()
  addCustomLoading.value = true
  const isExist = await checkBrandByName(name)
  addCustomLoading.value = false
  if (isExist) return message.warning(`品牌 "${name}" 已存在，请直接从品牌字典选择`)

  // 检查是否已添加过同名品牌
  const isDuplicate = props.formData.ownBrandList.some((brand) => brand.name.toLowerCase() === name.toLowerCase())

  if (isDuplicate) {
    message.warning(`品牌 "${name}" 已添加，不能重复添加`)
    return
  }

  // 生成一个唯一ID
  const customId = genUUID()

  // 添加自定义品牌
  props.formData.ownBrandList.push({
    brandId: customId,
    name,
    type: 'custom',
    categoryIds: '',
  })

  // 清空输入
  customBrandName.value = ''
}

const disableSet = computed(() => {
  const set = new Set()
  props.formData.ownBrandList?.forEach((item) => {
    set.add(item.brandId)
  })
  return set
})

const disableBrand = computed(() => {
  const set = new Set()
  if (props.formData.supplierType == 3) {
    props.formData.ownBrandList?.forEach((item) => {
      if (item.replaceBrands) {
        item.replaceBrands.forEach((replace) => {
          set.add(replace.brandId)
        })
      }
    })
  }
  return set
})

// 移除品牌
const removeBrand = (brandId) => {
  const brandIndex = props.formData.ownBrandList.findIndex((brand) => brand.brandId === brandId)
  if (brandIndex > -1) {
    props.formData.ownBrandList.splice(brandIndex, 1)
  }
}

// 在组件挂载时初始化数据
onMounted(() => {
  initFormData()
  initBrandData()
})

// 计算属性：检查是否有空的授权书
const hasEmptyAuthorization = computed(() => {
  if (props.formData.supplierType !== 1 || !props.formData.agencyBrands || props.formData.agencyBrands.length === 0) {
    return false
  }

  return props.formData.agencyBrands.some(
    (brand) =>
      !props.formData.brandAuthorizations?.[brand] ||
      (Array.isArray(props.formData.brandAuthorizations[brand]) &&
        props.formData.brandAuthorizations[brand].length === 0),
  )
})

// 自定义验证方法，确保每个品牌都有对应的授权书和产品分类
const validate = () => {
  // 如果是品牌商且没有添加任何品牌，直接返回验证失败
  if (props.formData.supplierType === 3 && (!props.formData.ownBrandList || props.formData.ownBrandList.length === 0)) {
    message.error('请至少添加一个自有品牌')
    return Promise.reject('请至少添加一个自有品牌')
  }

  return formRef.value.validate()
}

// 暴露验证方法给父组件
defineExpose({
  validate,
})

// 下载设备清单模板
const downloadEquipmentTemplate = () => {
  downloadTemplate('/settle/制造设备清单.xlsx', '设备清单模板.xlsx')
}

const downloadInspectionTemplate = () => {
  downloadTemplate('/settle/质检设备清单.xlsx', '质检设备清单模板.xlsx')
}

const downloadTemplate = (templateUrl, fileName) => {
  const link = document.createElement('a')
  link.href = templateUrl
  link.setAttribute('download', fileName)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const checkBrandByName = async (name) => {
  const [err, res] = await try_http('/mall/brand/list', {
    params: {
      status: 1,
      name,
      exactMatch: true,
    },
  })
  if (!err) {
    if (res.data.length > 0) {
      return true
    }
    return false
  }
  message.error('品牌查询失败, 请稍后再试')
  return true
}

onMounted(() => {
  window._form = props.formData
})
// 下载假一罚十协议模板
const downloadPenaltyTemplate = () => {
  downloadTemplate('/settle/假一罚十协议.xlsx', '假一罚十协议模板.xlsx')
}
</script>

<style scoped>
.uploaded-files {
  margin-top: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  margin-bottom: 4px;
  background: #f5f5f5;
  border-radius: 4px;
}

.file-name {
  margin: 0 8px;
  flex: 1;
}

.delete-icon {
  cursor: pointer;
  color: #999;
}

.delete-icon:hover {
  color: #ff4d4f;
}

.brand-authorization {
  margin-bottom: 12px;
  padding: 8px 12px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.ant-divider-inner-text {
  font-size: 14px;
  font-weight: 500;
}

.brand-selection-container,
.custom-brand-container {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.brand-item {
  margin-bottom: 24px;
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border-left: 3px solid #f94c30;
}

.brand-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.brand-name {
  font-size: 16px;
  font-weight: 500;
  margin-right: 8px;
}

.brand-source {
  color: #666;
  font-size: 14px;
  flex: 1;
}

.ml-2 {
  margin-left: 8px;
}

.mt-2 {
  margin-top: 8px;
}

.brand-selection-wrapper {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.brand-search-area,
.brand-custom-area {
  padding: 16px;
}

.brand-search-area {
  background-color: #fafafa;
  border-bottom: 1px dashed #d9d9d9;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #333;
}

.search-tip,
.custom-tip {
  margin-top: 8px;
}

.brand-selection-container,
.custom-brand-container {
  display: flex;
  align-items: center;
}

.mt-4 {
  margin-top: 16px;
}

:deep(.ant-radio-wrapper) {
  margin-right: 24px;
}

.equipment-inventory-upload {
  overflow: hidden;
  display: flex;
  gap: 8px;
}
</style>
