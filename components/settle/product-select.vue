<template>
  <a-tree-select
    :value="selects"
    :tree-data="categoryOptions"
    :placeholder="placeholder"
    :multiple="true"
    :loading="loading"
    :tree-checkable="true"
    :show-search="true"
    :fieldNames="{
      children: 'children',
      label: 'categoryName',
      key: 'categoryId',
      value: 'categoryId',
    }"
    @search="onSearch"
    label-in-value
    style="width: 100%"
    @change="onChange"
    :filter-tree-node="false"
    :getPopupContainer="(el) => el.parentElement"
  />
</template>

<script setup>
import { Form } from 'ant-design-vue'
const model = defineModel({
  type: Array,
  default: () => [],
})

const { onFieldChange } = Form.useInjectFormItemContext()

const props = defineProps({
  placeholder: {
    type: String,
    default: '请选择类目',
  },
  brandId: {
    type: Number,
    default: null,
  },
})

const emit = defineEmits(['change'])

const categoryOptions = ref([])
const loading = ref(false)
// const selects = computed(() => {
//   if (!model.value) {
//     return []
//   }
//   return model.value.map((item) => item.categoryId)
// })
const selects = ref([])
const selectMap = computed(() => {
  return selects.value.reduce((acc, item) => {
    acc[item.value] = item
    return acc
  }, {})
})

// watch(model, (val) => {
//   if (val) {
//     selects.value = val.map((item) => {
//       const _item = selectMap.value[item]
//       if (_item) {
//         return _item
//       } else {
//         return {
//           value: item.categoryId,
//         }
//       }
//     })
//   } else {
//     selects.value = []
//   }
// })

watch(selects, (val) => {
  model.value = val.map((item) => {
    return {
      categoryId: item.value,
      categoryName: item.label,
    }
  })
  onFieldChange()
})

// const selects = computed({
//   get() {
//     if (model.value) {
//       return model.value.split(',').map(Number)
//     } else {
//       return []
//     }
//   },
//   set(val) {
//     model.value = val.join(',')
//   },
// })

// 加载全部类目数据
const loadAllCategories = async (query = '') => {
  loading.value = true
  const params = {
    keyword: query,
  }
  if (props.brandId) {
    params.brandId = props.brandId
  }
  const [err, res] = await try_http('/mall/category/search', {
    params,
  })

  if (!err) {
    categoryOptions.value = res.data
  } else {
    console.error('加载类目失败:', err)
  }
  loading.value = false
}

// 组件挂载时加载全部数据
onMounted(async () => {
  await loadAllCategories()
  if (model.value) {
    selects.value = model.value.map((item) => ({ value: item.categoryId, label: item.categoryName }))
  }
})

const onChange = (value, label, extra) => {
  // const len = value.length
  // const result = []
  // for (let i = 0; i < len; i++) {
  //   const categoryId = value[i]
  //   const categoryName = label[i]
  //   const item = {
  //     categoryId,
  //     categoryName,
  //   }
  //   result.push(item)
  // }
  // model.value = result
  selects.value = value.map((item) => {
    const _item = selectMap.value[item.value]
    if (_item) {
      return {
        value: _item.value,
        label: _item.label,
      }
    } else {
      return item
    }
  })
}

const onSearch = (value) => {
  loadAllCategories(value)
}
</script>
