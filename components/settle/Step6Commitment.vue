<template>
  <div class="w-90% px-20 mx-auto leading-25 text-14">
    <h2 class="text-center font-bold text-18 mb-30">诚信承诺书</h2>

    <div class="mb-20">
      <p>本企业承诺：</p>
      <ol class="pl-20 mb-20">
        <li class="mb-10">本企业提交的所有资料及信息均真实、准确、有效，如有虚假，愿意承担相应法律责任。</li>
        <li class="mb-10">本企业将严格遵守平台各项规则及国家相关法律法规，不从事任何违法违规行为。</li>
        <li class="mb-10">本企业保证所提供的产品及服务质量符合国家相关标准，不侵犯任何第三方的知识产权。</li>
        <li class="mb-10">本企业承诺按照合同约定履行供货义务，不拖欠货款，不恶意违约。</li>
        <li class="mb-10">本企业将诚信经营，遵守商业道德，维护平台良好生态。</li>
        <li class="mb-10">
          如违反上述承诺，本企业愿意接受平台的处罚措施，包括但不限于降低信用等级、暂停或取消入驻资格等。
        </li>
      </ol>

      <p class="mb-10">特此承诺。</p>
      <p class="text-right mb-20">承诺企业：{{ formData.merchantName }}</p>
      <p class="text-right">日期：{{ currentDate }}</p>
    </div>

    <div class="mt-30 text-center">
      <a-checkbox v-model:checked="formData.integrityCommitment">我已阅读并同意遵守以上承诺内容</a-checkbox>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  formData: Object,
})

// 当前日期
const currentDate = computed(() => {
  const date = new Date()
  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
})

// 验证方法
const validate = () => {
  return new Promise((resolve, reject) => {
    if (!props.formData.integrityCommitment) {
      reject('请阅读并同意诚信承诺书')
    } else {
      resolve()
    }
  })
}

// 暴露验证方法给父组件
defineExpose({
  validate,
})
</script>
