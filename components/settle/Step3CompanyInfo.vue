<template>
  <a-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    :label-col="{ span: 8 }"
    :wrapper-col="{ span: 16 }"
    autocomplete="off"
  >
    <a-row :gutter="20" pt-50>
      <a-col :span="12">
        <!-- <a-form-item name="merchantName" label="企业名称">
          <a-input :disabled="!editable" v-model:value.trim="formData.merchantName" :maxlength="30" />
        </a-form-item> -->
        <a-form-item name="merchantShortName" label="企业简称">
          <a-input :disabled="!editable" v-model:value.trim="formData.merchantShortName" :maxlength="10" />
        </a-form-item>
        <a-form-item name="mainProduct" label="主营">
          <a-textarea :disabled="!editable" v-model:value.trim="formData.mainProduct" :maxlength="500" showCount />
        </a-form-item>
        <a-form-item name="merchantPhone" label="商家电话">
          <a-input :disabled="!editable" v-model:value.trim="formData.merchantPhone" :maxlength="13" />
        </a-form-item>
        <a-form-item name="merchantMail" label="邮箱">
          <a-input :disabled="!editable" v-model:value.trim="formData.merchantMail" />
        </a-form-item>
        <a-form-item name="merchantWebsite" label="企业官网">
          <a-input :disabled="!editable" v-model:value.trim="formData.merchantWebsite" />
        </a-form-item>
        <a-form-item name="merchantSlogan" label="企业宣传语">
          <a-textarea :disabled="!editable" v-model:value.trim="formData.merchantSlogan" :maxlength="200" showCount />
        </a-form-item>
      </a-col>
      <a-col :span="10">
        <a-form-item text-center name="merchantLogo" label="企业LOGO">
          <img-upload
            name="merchantLogo"
            :value="useObs(formData.merchantLogo)"
            :beforeUpload="beforeUpload"
            :disabled="!editable"
            @change="(v) => (formData.merchantLogo = v)"
          />
          <div sub-text>限2MB以内的jpg、jpeg、png文件</div>
        </a-form-item>
        <a-form-item text-center name="promotionalImg" label="企业宣传图">
          <img-upload
            :disabled="!editable"
            name="promotionalImg"
            :value="useObs(formData.promotionalImg)"
            :beforeUpload="beforeUpload"
            @change="(v) => (formData.promotionalImg = v)"
          />
          <div sub-text>限2MB以内的jpg、jpeg、png文件</div>
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
</template>

<script setup>
import { ref, watch, reactive } from 'vue'
import { message } from 'ant-design-vue'

const props = defineProps({
  form: Object,
  formData: Object,
  editable: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['update:modelValue'])
const formRef = ref()

// 定义验证规则
const rules = reactive({
  merchantShortName: [{ required: true, message: '请输入企业简称', trigger: 'blur' }],
  // mainProduct: [{ required: true, message: '请输入主营产品', trigger: 'blur' }],
  // merchantPhone: [{ required: true, message: '请输入商家电话', trigger: 'blur' }],
  // merchantMail: [{ required: true, message: '请输入邮箱', trigger: 'blur' }],
  // merchantLogo: [{ required: true, message: '请上传企业LOGO', trigger: 'change' }],
  // promotionalImg: [{ required: true, message: '请上传企业宣传图', trigger: 'change' }],
})

// 验证方法
const validate = () => {
  return formRef.value.validate()
}

// 文件上传前验证
const beforeUpload = (file) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
  if (!isJpgOrPng) {
    message.error('只能上传JPG/PNG格式的图片!')
    return false
  }
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    message.error('图片大小不能超过2MB!')
    return false
  }
  return isJpgOrPng && isLt2M
}

// 暴露验证方法给父组件
defineExpose({
  validate,
})
</script>
