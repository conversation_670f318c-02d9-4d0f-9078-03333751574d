<template>
  <div style="border: 1px solid #ccc; margin-top: 10px">
    <Toolbar :editor="editorRef" :defaultConfig="toolbarConfig" :mode="mode" style="border-bottom: 1px solid #ccc" />
    <Editor :defaultConfig="editorConfig" :mode="mode" v-model="valueHtml" style="height: 400px; overflow-y: auto" @onCreated="handleCreated" @onDestroyed="handleDestroyed" />
  </div>
</template>

<script>
import '@wangeditor/editor/dist/css/style.css';
import { onBeforeUnmount, ref, shallowRef, onMounted } from 'vue';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import { uploadFile } from '~/api/common';

export default {
  components: { Editor, Toolbar },
  props: {
    value: ''
  },
  emits: ['update:value'],
  setup(props, context) {
    // 编辑器实例，必须用 shallowRef，重要！
    const editorRef = shallowRef();

    // 内容 HTML
    const valueHtml = computed({
      get() {
        return props.value
      },
      set(val) {
        context.emit('update:value', val)
      }
    });


    const toolbarConfig = {
      toolbarKeys: [
        {
          key: 'group-image',
          title: '图片',
          iconSvg: '<svg viewBox="0 0 1024 1024"><path d="M959.877 128l0.123 0.123v767.775l-0.123 0.122H64.102l-0.122-0.122V128.123l0.122-0.123h895.775zM960 64H64C28.795 64 0 92.795 0 128v768c0 35.205 28.795 64 64 64h896c35.205 0 64-28.795 64-64V128c0-35.205-28.795-64-64-64zM832 288.01c0 53.023-42.988 96.01-96.01 96.01s-96.01-42.987-96.01-96.01S682.967 192 735.99 192 832 234.988 832 288.01zM896 832H128V704l224.01-384 256 320h64l224.01-192z"></path></svg>',
          menuKeys: ['insertImage', 'uploadImage'],
        },
        {
          key: 'group-video',
          title: '视频',
          iconSvg: '<svg viewBox="0 0 1024 1024"><path d="M981.184 160.096C837.568 139.456 678.848 128 512 128S186.432 139.456 42.816 160.096C15.296 267.808 0 386.848 0 512s15.264 244.16 42.816 351.904C186.464 884.544 345.*********** 896s325.568-11.456 469.184-32.096C1008.704 756.192 1024 637.152 1024 512s-15.264-244.16-42.816-351.904zM384 704V320l320 192-320 192z"></path></svg>',
          menuKeys: ['insertVideo', 'uploadVideo'],
        },
      ],
    };
    const editorConfig = {
      placeholder: '请输入内容...',
      MENU_CONF: {
        uploadImage: {
          async customUpload(file, insertFn) {
            uploadFile(file,'FeedbackFile').then((res) => {
              if (res.code === 'ok') {
                insertFn(res.data)
              }
            });
            // insertFn(url, alt, href);
          },
        },
        uploadVideo: {
          async customUpload(file, insertFn) {
            if (['video/mp4', 'video/ogg', 'video/webm'].indexOf(file.type) === -1) {
              message.warning('请上传MP4,OGG,Webm格式的视频')
              return
            }
            uploadFile(file,'FeedbackFile').then((res) => {
              if (res.code === 'ok') {
                insertFn(res.data)
              }
            });
            // insertFn(url, alt, href);
          },
        },
      },
    };

    // 组件销毁时，也及时销毁编辑器，重要！
    onBeforeUnmount(() => {
      const editor = editorRef.value;
      if (editor == null) return;

      editor.destroy();
    });

    // 编辑器回调函数
    const handleCreated = (editor) => {
      editorRef.value = editor; // 记录 editor 实例，重要！
    };
    const handleDestroyed = (editor) => {
      console.log('destroyed', editor);
    };


    const disable = () => {
      const editor = editorRef.value;
      if (editor == null) return;
      editor.disable();
    };

    return {
      editorRef,
      mode: 'default',
      valueHtml,
      toolbarConfig,
      editorConfig,
      handleCreated,
      handleDestroyed,
      disable,
    };
  },
};
</script>
