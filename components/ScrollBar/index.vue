<template>
  <div ref="divRef">
    <div ref="contentRef" v-bind="$attrs">
      <slot />
    </div>
  </div>
</template>

<script setup>
import { useOverlayScrollbars } from 'overlayscrollbars-vue'
import 'overlayscrollbars/overlayscrollbars.css'
const divRef = ref()
const [init, instance] = useOverlayScrollbars({
  defer: true,
  options: {
    overflow: {
      x: 'hidden',
    },
  },
})

const contentRef = ref()

onMounted(() => {
  init({
    target: divRef.value,
    elements: {
      viewport: contentRef.value,
    },
  })
})
onBeforeUnmount(() => {
  instance()?.destroy()
})
defineExpose({})
</script>

<style lang="less" scoped>
:deep(.os-theme-dark) {
  --os-handle-bg: theme('colors.primary');
  --os-handle-bg-hover: theme('colors.primary');
  --os-handle-bg-active: theme('colors.primary');
}
</style>
