<template>
  <div text-14>
    <div v-if="desc.length">
      <a-tabs :active-key="current" @change="changeTab" v-if="desc.length > 1">
        <a-tab-pane v-for="(item, i) in desc" :key="item.orderDeliveryId" :tab="`物流${i + 1}`"></a-tab-pane>
      </a-tabs>

      <div grid grid-cols-2 gap-30>
        <div>
          <div grid grid-cols-2>
            <div>配送方式：{{ getDeliveryType(dto.deliveryType) }}</div>
            <div>发货时间：{{ dto.updateTime }}</div>
          </div>
          <div grid grid-cols-2 my-10>
            <div>物流公司：{{ dto.deliveryDto?.companyName }}</div>
            <div>物流单号：{{ dto.dvyFlowId }}</div>
          </div>

          <a-table :columns="columns" :data-source="dto.orderItems" :pagination="false"></a-table>
        </div>

        <div v-if="has(dto.deliveryDto)">
          <div>物流状态: {{ getDeliveryState(dto.deliveryDto.state) }}</div>
          <a-timeline mt-10 h-300 overflow-y-auto py-20>
            <a-timeline-item v-for="(item, i) in deliveryList" :key="i" :color="i==0 ? 'blue' : 'gray'">
              <p>{{ item.acceptTime }}</p>
              <p>{{ item.acceptStation }}</p>
            </a-timeline-item>
          </a-timeline>
        </div>
      </div>
    </div>

    <Empty v-else />
  </div>
</template>

<script setup>
const props = defineProps(['orderNo', 'platformOrderNumber'])

const { orderNo, platformOrderNumber } = toRefs(props)

const current = ref()
const desc = ref([])

const detail = ref({})
const dto = ref({})

// 是否采购单
const isPlat = computed(() => has(platformOrderNumber.value))

const fetchDesc = async () => {
  const res = await plat_http.get(`/platform/orderDelivery/orderInfo/${orderNo.value}`)
  useMall(res, () => {
    const ret = JSON.parse(res.data)
    detail.value = ret
    desc.value = ret.deliveryExpresses

    if (desc.value.length) {
      current.value = desc.value[0].orderDeliveryId
      dto.value = desc.value[0]
    }
  })
}

const fetchPurchase = async() => {
  const res = await http(`/mall/p/orderDelivery/${platformOrderNumber.value}`)
  useMall(res, () => {
    
    desc.value = res.data

    if (desc.value.length) {
      current.value = desc.value[0].orderDeliveryId
      dto.value = desc.value[0]
    }
  })
}
isPlat.value ? fetchPurchase() : fetchDesc()

// 格式化快递类型
const getDeliveryType = (type) => {
  const types = ['线上发货', '快递配送', '用户自提', '无需快递', '同城配送']
  return types[type]
}

// 获取物流状态
const getDeliveryState = (state) => {
  const map = {
    0: '没有记录',
    1: '已揽收',
    2: '运输途中',
    201: '到达目的城市',
    3: '已签收',
    4: '问题件',
  }
  return map[state]
}

const fetchDto = async(key) => {
  const res = await plat_http.get(`/platform/orderDelivery/deliveryOrder/${key}`)
  useMall(res, () => {
    const ret = JSON.parse(res.data)
    dto.value = ret
    current.value = key
  })
}

const changeTab = key => {
  fetchDto(key)
}

const columns = ref([
  {
    title: '商品',
    dataIndex: 'prodName',
  },
  {
    title: '型号',
    dataIndex: 'partyCode'
  },
  {
    title: '数量',
    dataIndex: 'prodCount',
  },
])

// 快递信息列表
const deliveryList = computed(() => {
  const list = []
  if (dto.value) {
    list.unshift({
      acceptStation: '买家提交了订单，等待系统确认',
      acceptTime: detail.value.createTime
    })
    list.unshift({
      acceptStation: '买家已支付，等待发货',
      acceptTime: detail.value.payTime
    })
    list.unshift({
      acceptStation: '商家已发货，等待快递拣货',
      acceptTime: detail.value.dvyTime
    })
    const traces = dto.value.deliveryDto?.traces
    if (traces?.length) {
      const len = traces.length 
      for (let i = 0; i < len; i++) {
        list.unshift(traces[i])
      }
    } else {
      list.unshift({
        acceptStation: '暂无物流信息，请您稍后再试'
      })
    }
  }
  return list
})
</script>
