<template>
  <div class="sub-text flex max-w-1440 mx-auto pb-4" :class="showLink ? 'justify-between' : 'justify-center'">
    <div sub-text text-center>
      Copyright © {{ currentYear }} 研选工场（苏州）网络有限公司 |
      <a href="https://beian.miit.gov.cn" sub-text decoration-none target="_blank"
      >苏ICP备2024149956号</a
      >
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";

defineProps<{
  showLink?: boolean
}>()
const currentDate = new Date();
const currentYear = ref(currentDate.getFullYear().toString());
</script>
