<template>
  <div :style="style" ref="parent">
    <div :style="fixedStyle" ref="target">
      <slot />
    </div>
  </div>
</template>

<script lang="ts" setup>
const target = ref<HTMLElement>()
const init = ref(false)
const style = ref<Obj>({})
const fixedStyle = ref<Obj>({})
const parent = ref<HTMLElement>()
const root = ref<HTMLElement>()
const edge = ref(0)
const props = withDefaults(
  defineProps<{
    offset: number
  }>(),
  {
    offset: 0,
  },
)

onMounted(() => {
  init.value = true
  root.value = parent.value!.parentElement!
})

const update = () => {
  if (init.value) {
    if (parent.value && root.value && target.value) {
      const _p = parent.value.getBoundingClientRect()
      const _t = target.value.getBoundingClientRect()
      const _r = root.value.getBoundingClientRect()
      let _style: Obj = {}
      if (_p.top <= props.offset) {
        _style = {
          position: 'fixed',
          top: props.offset + 'px',
          width: style.value.width,
        }
      } else {
        Object.assign(style.value, {
          width: _t.width + 'px',
          height: _t.height + 'px',
        })
      }
      if (_r.bottom - _t.height <= 0) {
        edge.value = _r.bottom
        _style = {
          position: 'absolute',
          bottom: '0',
          width: '100%',
        }
      }
      fixedStyle.value = _style
    }
  }
}

useEventListener('scroll', update, { capture: true, passive: true })
useResizeObserver(target, update)

defineExpose({
  update
})
</script>

