<template>
  <a-upload-dragger
    v-model:file-list="fileList"
    list-type="picture-card"
    :show-upload-list="false"
    inline-block
    :class="{ 'w-full': type === 'image', 'w-160px': type === 'avatar' }"
    h-160px
    line-height-120px
    p-0
    action=""
    :before-upload="beforeUpload"
    :customRequest="(file) => uploadImage(file)"
  >
    <img v-if="value" :src="value" w-full h-full max-h-128px object-contain />
    <div v-else>
      <plus-outlined></plus-outlined>
    </div>
  </a-upload-dragger>
</template>

<script setup>
import { uploadFile } from '~/api/common'
import { Form } from 'ant-design-vue'

const { onFieldChange } = Form.useInjectFormItemContext()

const emits = defineEmits(['change'])
const props = defineProps({
  value: String,
  beforeUpload: Function,
  type: {
    type: String,
    default: 'image',
    validator: (value) => ['avatar', 'image'].includes(value),
  },
})

const fileList = ref([])

const uploadImage = (data) => {
  uploadFile(data.file, 'Avatar')
    .then((res) => {
      if (res.code === 'ok') {
        emits('change', res.data)
        onFieldChange()
      }
    })
    .catch((err) => {})
}
</script>
