<template>
  <div w-full text-white bg="#353541" rounded-2px py-8px min-h-100px flex flex-col>
    <slot name="top" />
    <div class="flex-1">
      <a-textarea
        v-model:value="value"
        v-bind="$attrs"
        ref="inputRef"
        class="bg-transparent! placeholder-#7f7f7f text-#fff resize-none!"
        placeholder="随便问点什么..."
        :bordered="false"
        :auto-size="true"
        :disabled="sending"
        @press-enter="pressEnter"
      ></a-textarea>
    </div>

    <div class="flex px-12px items-center text-24px">
      <!-- <a-tooltip placement="top">
        <template #title>
          <span>最近对话</span>
        </template>
        <div class="i-a-comment-outlined link" cursor-pointer></div>
      </a-tooltip> -->
      <div class="flex-1"></div>

      <a-tooltip :title="sending ? '停止' : '发送'">
        <a-button
          type="primary"
          shape="circle"
          :icon="h(sending ? PauseOutlined : SendOutlined)"
          @click.stop="emits('send')"
        />
      </a-tooltip>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { h } from 'vue'
import { PauseOutlined, SendOutlined } from '@ant-design/icons-vue'
const inputRef = ref()
const value = defineModel<string>()
onMounted(() => {
  inputRef.value?.focus()
})

defineProps<{
  sending?: boolean
}>()

const emits = defineEmits(['send'])

const pressEnter = (e: KeyboardEvent) => {
  e.preventDefault()
  emits('send')
}
</script>
