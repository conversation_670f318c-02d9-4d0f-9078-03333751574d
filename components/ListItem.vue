<template>
  <div class="list-item-component" card--reactive flex="~ items-center justify-between" h-90 pl-16 @click="onItemClick">
    <img h-80 w-80 object-scale-down :src="imageUrl" />
    <div text w-200 max-sm:flex-1 line-height-20 :title="productName" :innerHTML="highlightedText"></div>

    <div w-100 max-sm:hidden>
      <img mb-6 max-h-36 :src="websiteImgUrl" />
      <div sub-text>{{ websiteName }}</div>
    </div>
    <div w-300 max-sm:w-auto text-center text="20 gray">
      <a-popover v-if="has3DModel" title="该产品提供3D模型查看与下载">
        <CodeSandboxOutlined ml-5 />
      </a-popover>
    </div>

    <div
      v-if="!favMode"
      :class="specPdfUrl ? 'btn-primary' : 'btn-disabled'"
      w-130
      h-90
      font-size-16
      line-height-90
      @click.stop="onPreviewDocClick"
    >
      <FileTextOutlined mr-4 />
      预览图档
    </div>
    <div v-else btn-primary w-130 h-90 font-size-16 line-height-90 @click.stop="onUnfavClick">
      <FileTextOutlined mr-4 />
      取消收藏
    </div>
  </div>
</template>

<script lang="ts" setup>
import { createVNode, defineEmits } from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { disFavourite } from '~/api/user'
import ms from 'magic-string'
import { userStore } from '~/store/user'
const store = userStore()

export interface Property {
  id: string | number
  key: string
  value: string
}

export interface Props {
  id: string | number
  websiteImgUrl: string
  websiteName: string
  imageUrl: string
  productName: string
  pageUrl: string
  specPdfUrl?: string
  highlights?: Array<string> | string
  filterConditions?: object
  favMode?: boolean
  has3DModel?: boolean
}
const props = withDefaults(defineProps<Props>(), {
  favMode: false,
})

const emits = defineEmits(['disfavour', 'click'])

const highlightedText = computed(() => {
  if (!props.highlights) return props.productName
  if (Array.isArray(props.highlights)) {
    const [text] = props.highlights
    let match
    const reg = /<em>(.*?)<\/em>/g
    const s = new ms(text)
    while ((match = reg.exec(text))) {
      s.update(match.index, match.index + match[0].length, `<span style="color: red">${match[1]}</span>`)
    }
    return s.toString()
  } else if (typeof props.highlights === 'string') {
    const reg = new RegExp(`(${props.highlights})`, 'ig')
    const s = new ms(props.productName)
    let match
    while ((match = reg.exec(props.productName))) {
      s.update(match.index, match.index + match[0].length, `<span style="color: red">${match[1]}</span>`)
    }
    return s.toString()
  }
  return props.productName
})

const onItemClick = () => {
  emits('click')
}

const onPreviewDocClick = () => {
  if (!!props.specPdfUrl) {
    if (!store.user.userId) {
      loginPop.show()
      return
    }

    previewDoc(props.specPdfUrl)
  } else {
    return
  }
}

const onUnfavClick = () => {
  Modal.confirm({
    title: '提示',
    icon: createVNode(ExclamationCircleOutlined),
    content: '确定要取消收藏吗？',
    okText: '确定',
    cancelText: '取消',
    onOk() {
      console.log(props.id)
      disFavourite({ ids: props.id }).then((res) => {
        if (res.code === 'ok') {
          emits('disfavour', props.id)
        }
      })
    },
    onCancel() {
      console.log('Cancel')
    },
  })
}
</script>

<style lang="less" scoped>
.list-item-component {
  &:hover {
    transform: translateX(-3px);
  }
}
</style>
