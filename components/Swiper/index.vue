<template>
  <div text-20 select-none h-full w-full>
    <Swiper
      class="swiper-image"
      w-full
      h-full
      @init="init"
      navigation
      loop
      :autoplay="{ delay: 5000 }"
      :modules="[Pagination, Navigation, Autoplay]"
      :pagination="{ type: 'bullets', clickable: true }"
    >
      <swiper-slide v-for="(item, i) in list" :key="i" @click="() => emits('clickItem', item)">
        <div
          class="bg-red w-full h-full"
          :style="{
            background: `url(${useObs(item[prop])}) no-repeat center`,
            backgroundSize: 'cover',
          }"
        ></div>
      </swiper-slide>
      <!-- <template #container-start v-if="list.length"> -->
      <!--   <LeftOutlined -->
      <!--     absolute -->
      <!--     z10 -->
      <!--     top="1/2" -->
      <!--     mt--10 -->
      <!--     cursor-pointer -->
      <!--     hover:text-primary -->
      <!--     @click="() => swiper?.slidePrev()" -->
      <!--     v-if="!swiper?.isBeginning" -->
      <!--   /> -->
      <!-- </template> -->
      <!-- <template #container-end v-if="list.length"> -->
      <!--   <RightOutlined -->
      <!--     absolute -->
      <!--     z10 -->
      <!--     top="1/2" -->
      <!--     right-0 -->
      <!--     mt--10 -->
      <!--     cursor-pointer -->
      <!--     hover:text-primary -->
      <!--     @click="() => swiper?.slideNext()" -->
      <!--     v-if="!swiper?.isEnd" -->
      <!--   /> -->
      <!-- </template> -->
    </Swiper>
  </div>
</template>

<script lang="ts" setup>
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Swiper as TSwiper } from 'swiper'
import { Pagination, Navigation, Autoplay } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/pagination'
import 'swiper/css/navigation'

const emits = defineEmits(['clickItem'])

defineProps<{
  list: obj[]
  prop: string
}>()
const swiper = ref<TSwiper>()
const init = (instance: TSwiper) => {
  swiper.value = instance
}
</script>

<style lang="less" scoped>
:deep(.swiper-image) {
  // --swiper-pagination-color: theme('colors.primary');
  --swiper-theme-color: theme('colors.primary');
}
</style>
