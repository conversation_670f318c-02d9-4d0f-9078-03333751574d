<template>
  <a-alert type="error">
    <template #message>
      <div class="flex">
        <a-tooltip>
          <template #title>
            <div>1. 本表中的价格若未做特殊说明，均为含税价格。</div>
            <!-- <div>2. 在询价单转为订单后，将根据订单总价加收运费，具体规则如下：</div> -->
            <!-- <div v-for="(item, index) in ruleMsg" :key="index">{{ item }}</div> -->
          </template>
          <span class="text-#666">
            <InfoCircleFilled />
            <!-- 价格与运费说明 -->
            价格说明
          </span>
        </a-tooltip>

        <div class="flex-1"></div>

        <div>
          已选择：
          <a-tag color="red">{{ ids.length }}</a-tag>
          行物料
        </div>

        <div class="ml-16px">
          总金额：
          <a-tag color="red">{{ getPrice(total) }}</a-tag>
        </div>
        <!-- <div class="ml-16px"> -->
        <!--   预估运费： -->
        <!--   <a-tag color="red">{{ getPrice(freightFee) }}</a-tag> -->
        <!-- </div> -->
      </div>
    </template>
  </a-alert>
</template>

<script setup>
const props = defineProps({
  ids: {
    type: Array,
    default: () => [],
  },
  api: {
    type: String,
  },
})

const total = ref(0)
const freightFee = ref(0)

const fetchPriceAmount = async () => {
  const url = props.api || '/mall/p/sell-order/total-amount'
  const [err, res] = await try_http(url, {
    method: 'put',
    body: props.ids,
  })

  if (!err) {
    total.value = res.data ?? 0
  }
}

const fetchFreightFee = async () => {
  const [err, res] = await try_http('/mall/p/freight-config/getFreightByAmount', {
    method: 'post',
    body: {
      amount: total.value,
    },
  })

  if (!err) {
    freightFee.value = res.data ?? 0
  }
}

watchEffect(() => {
  if (props.ids.length) {
    fetchPriceAmount()
  } else {
    total.value = 0
  }
})

watchEffect(() => {
  if (total.value == 0) {
    freightFee.value = 0
  } else {
    fetchFreightFee()
  }
})

const { getRule, ruleMsg } = useFreightFeeRule()

onMounted(() => {
  getRule()
})
</script>
