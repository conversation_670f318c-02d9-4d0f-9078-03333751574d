<template>
  <a-form w-480 ref="formRef" :rules="rules" :model="formState" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" autocomplete="off">
    <a-form-item name="title" label="标题">
      <a-input v-model:value="formState.title" :maxlength="32" />
    </a-form-item>

    <a-form-item name="page" label="页面与模块">
      <a-select v-model:value="formState.page" labelInValue :options="pageList" allowClear></a-select>
    </a-form-item>

    <a-form-item name="feedbackType" label="反馈类型">
      <a-select v-model:value="formState.feedbackType" :options="feedType" allowClear></a-select>
    </a-form-item>

    <a-form-item name="content" label="反馈内容">
      <!-- <a-textarea v-model:value="formState.content" :rows="8" :maxlength="500" /> -->
      <rich-editor v-model:value="formState.content" />
    </a-form-item>

    <a-form-item :wrapper-col="{ offset: 6, span: 4 }">
      <div flex justify-between gap-8>
        <a-button w-full type="primary" html-type="submit" :loading="loading" @click="onConfirm">提交</a-button>
        <a-button w-full @click="onReset">重置</a-button>
      </div>
    </a-form-item>
  </a-form>
</template>

<script lang="ts" setup>
import {} from 'vue';
import type { SelectProps } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { commitFeedback } from '~/api/user';
interface FeedbackFormState {
  title: string;
  page: string | undefined;
  feedbackType: number | undefined;
  content: string;
}
const route = useRoute();
const router = useRouter();
const formRef: any = ref(null);
const formState = reactive<FeedbackFormState>({
  title: '',
  page: undefined,
  feedbackType: undefined,
  content: '',
});
const rules: Record<string, Rule[]> = {
  title: [{ required: true, message: '请输入标题', trigger: 'blur', whitespace: true }],
  feedbackType: [{ required: true, message: '请选择反馈类型', trigger: 'change' }],
  page: [{  required: true, message: '请选择页面与模块' }]
};
const emits = defineEmits(['close']);

const pageList = ref<SelectProps['options']>([
  {
    label: '首页',
    value: 'index',
    path: '/',
  },
  {
    label: '零部件搜索',
    value: 'search',
    path: '/search',
  },
  {
    label: '零部件类目',
    value: 'category',
    path: '/category',
  },
  {
    label: '零部件详情',
    value: 'detail',
    path: '/parts',
  },
  {
    label: '对比',
    value: 'compare',
    path: '/compare',
  },
  {
    label: '找文档',
    value: 'docs',
    path: '/',
  },
  {
    label: '个人中心',
    value: 'my',
    path: '/my',
  },
  {
    label: '其他',
    value: 'other',
  },
]);

const feedType = ref<SelectProps['options']>([
  {
    label: '程序错误',
    value: 'Bug',
  },
  {
    label: '内容错误',
    value: 'ContentErr',
  },
  {
    label: '体验改进',
    value: 'Experience',
  },
]);

const loading = ref<boolean>(false);

const onConfirm = () => {
  formRef.value.validate().then(() => {
    const { title, page, feedbackType, content } = formState;
    const data = {
      title,
      page: JSON.stringify(page),
      feedbackType,
      content,
    };
    loading.value = true;
    console.log(data);
    commitFeedback(data)
      .then((res) => {
        if (res.code === 'ok') {
          message.success('已提交，感谢您的反馈');
          onReset();
          emits('close');
        }
      })
      .finally(() => {
        loading.value = false;
      });
  });
};

const onReset = () => {
  formRef.value.resetFields()
  formRef.value.clearValidate();
  
  const currentRoutePath = route.path;
  const page = pageList.value.find((ele) => currentRoutePath === ele.path);
  if (!!page) {
    formState.page = page;
  }
};

onMounted(() => {
  onReset();
});

defineExpose({
  clear: () => {
    nextTick(() => {
      formRef.value.clearValidate()
    })
  }
})
</script>

<style scoped></style>
