<template>
  <div class="upload-component">
    <a-upload
      v-model:fileList="fileList"
      :beforeUpload="handleBeforeUpload"
      :customRequest="handleCustomUpload"
      :accept="accept"
      :multiple="multiple"
      :maxCount="maxCount"
    >
      <a-button v-if="fileList.length < maxCount">
        <upload-outlined />
        {{ buttonText }}
      </a-button>
    </a-upload>

    <div v-if="tip" class="upload-tip">{{ tip }}</div>
  </div>
</template>

<script setup>
import { message, Form } from 'ant-design-vue'
import { UploadOutlined } from '@ant-design/icons-vue'

const value = defineModel({ default: '' })
const { onFieldChange } = Form.useInjectFormItemContext()

const fileList = ref([])

watch(fileList, (newVal) => {
  value.value = newVal
    .filter((item) => item.status === 'done')
    .map((item) => item.response)
    .join(',')
  nextTick(() => {
    onFieldChange()
  })
})

watch(value, () => {
  if (!value.value) return
  fileList.value = value.value.split(',').map((item) => {
    return {
      uid: item,
      name: getFileName(item),
      status: 'done',
      response: item,
    }
  })
})

onMounted(() => {
  if (!value.value) return
  fileList.value = value.value.split(',').map((item) => {
    return {
      uid: item,
      name: getFileName(item),
      status: 'done',
      response: item,
    }
  })
})

const props = defineProps({
  accept: {
    type: String,
    default: '*',
  },
  maxCount: {
    type: Number,
    default: 10,
  },
  maxSize: {
    type: Number,
    default: 10, // 默认10MB
  },
  buttonText: {
    type: String,
    default: '点击上传',
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  tip: {
    type: String,
    default: '',
  },
})

// 上传前验证
const handleBeforeUpload = (file) => {
  // 文件大小验证
  const isLtMaxSize = file.size / 1024 / 1024 < props.maxSize
  if (!isLtMaxSize) {
    message.error(`文件大小不能超过${props.maxSize}MB!`)
    return false
  }
}

// 自定义上传
const handleCustomUpload = async (options) => {
  const { file, onSuccess, onError, onProgress } = options

  try {
    // 显示上传进度
    onProgress({ percent: 50 })

    const formData = new FormData()
    formData.append('file', file)
    const res = await http('/api/file/upload', {
      method: 'post',
      body: formData,
      params: {
        resourceType: 'Avatar',
      },
    })
    onSuccess(res.data)
  } catch (error) {
    onError()
    message.error('上传失败')
  }
}

const getFileName = (url) => {
  const name = decodeURIComponent(url).split('/').pop()
  if (name) {
    return name.replace(/_\d+((?:\.[a-zA-Z0-9]+)?)$/, '$1')
  }
  return ''
}
</script>

<style scoped>
.upload-component {
  flex: 1;
  overflow: hidden;
}

.upload-tip {
  margin-top: 8px;
  color: #999;
  font-size: 12px;
}
</style>
