<template>
  <a-form w="80%" :wrapper-col="{ span: 24 }">
    <a-config-provider component-size="large">
      <!-- <a-form-item v-bind="validateInfos.name"> -->
      <!--   <a-input v-model:value="form.name" placeholder="用户名"></a-input> -->
      <!-- </a-form-item> -->
      <a-form-item v-bind="validateInfos.phone" v-if="type == Type.phone">
        <a-input v-model:value="form.phone" placeholder="手机号"></a-input>
      </a-form-item>

      <a-form-item v-bind="validateInfos.email" v-if="type == Type.email">
        <a-input v-model:value="form.email" placeholder="邮箱"></a-input>
      </a-form-item>

      <a-form-item v-bind="validateInfos.verifyCode">
        <msg-input v-model="form.verifyCode" :send-api="sendMsg" placeholder="验证码"></msg-input>
      </a-form-item>

      <a-form-item v-if="type == Type.email" v-bind="validateInfos.password">
        <a-input-password
          v-model:value="form.password"
          placeholder="密码"
          autocomplete="new-password"
        ></a-input-password>
      </a-form-item>

      <a-form-item v-if="type == Type.email" v-bind="validateInfos.rePassword">
        <a-input-password
          v-model:value="form.rePassword"
          placeholder="确认密码"
          autocomplete="new-password"
        ></a-input-password>
      </a-form-item>
    </a-config-provider>

    <a-form-item class="mb-12!" v-bind="validateInfos.identity">
      <a-select v-model:value="form.identity" size="large" placeholder="身份" allowClear>
        <a-select-option v-for="item in identities" :key="item" :value="item">
          {{ item }}
        </a-select-option>
      </a-select>
    </a-form-item>

    <a-form-item v-bind="validateInfos.checked">
      <a-checkbox size="small" v-model:checked="form.checked">
        <span>
          本人已阅读并同意以下条款：
        </span>
      </a-checkbox>
      <div pl-16>
          <NuxtLink :to="{ path: '/agreement/termsOfService' }" target="_blank">《用户协议》</NuxtLink>
          <NuxtLink :to="{ path: '/agreement/disclaimer' }" target="_blank">《免责声明》</NuxtLink>
          <NuxtLink :to="{ path: '/agreement/privacyPolicy' }" target="_blank">《隐私政策》</NuxtLink>
          <NuxtLink :to="{ path: '/agreement/scoreConfig' }" target="_blank">《积分规则》</NuxtLink>
      </div>
      
    </a-form-item>

    <a-button w-full type="primary" size="large" mb-12 @click="register" :loading="regLoading">注册</a-button>
    
    <Verify
      ref="verify"
      :captcha-type="'blockPuzzle'"
      :img-size="{ width: '400px', height: '200px' }"
      @success="cb"
    />
  </a-form>
</template>

<script setup lang="ts">
import { Form } from 'ant-design-vue'
import { Type } from './type'
import Verify from '@/components/verifition/Verify.vue'

const { useForm } = Form
const props = defineProps<{
  inviteCode?: string
  type: Type
}>()

const identities = ['工程师', '采购', '供应商', '其他']

const form = ref({
  identity: undefined,
  // name: '',
  email: '',
  password: '',
  rePassword: '',
  verifyCode: '',
  phone: '',
  checked: false,
})

const rules = computed(() => {
  const _rules: Obj = {
    identity: [{ required: true, message: '请选择身份' }],
    // name: [
    //   {
    //     required: true,
    //     message: '请输入用户名',
    //   },
    //   {
    //     pattern: USER_NAME_REG.reg,
    //     message: USER_NAME_REG.msg,
    //   },
    // ],
    password: [
      { required: true, message: '请输入密码' },
      {
        pattern: PASSWORD_REG.reg,
        message: PASSWORD_REG.msg,
      },
    ],
    verifyCode: [{ required: true, message: '请输入验证码' }],
    rePassword: [
      { required: true, message: '请再输入一次密码' },
      {
        validator(_, value) {
          if (value != form.value.password) {
            return Promise.reject(new Error('密码输入不一致'))
          } else {
            return Promise.resolve()
          }
        },
      },
    ],
    checked: [
      {
        validator: (_rule, value) => {
          if (!value) {
            return Promise.reject(new Error('请点击同意用户协议'))
          } else {
            return Promise.resolve()
          }
        },
      },
    ],
  }

  if (props.type == Type.phone) {
    _rules.phone = [
      {
        required: true,
        message: '请输入手机号',
      },
      {
        pattern: MOBILE_REG.reg,
        message: MOBILE_REG.msg,
      },
    ]
    _rules.password = []
    _rules.rePassword = []
  } else {
    _rules.email = [
      {
        required: true,
        message: '请输入邮箱',
      },
      {
        pattern: EMAIL_REG.reg,
        message: EMAIL_REG.msg,
      },
    ]
  }
  return _rules
})

const { validateInfos, validate } = useForm(form, rules)

const sendMsg = async () => {
  try {
    const validField = props.type == Type.phone ? 'phone' : 'email'
    await validate(validField)
    const { phone, email } = form.value
    let data: Obj = {
      eventType: 'REGISTER',
    }
    if (props.type == Type.phone) {
      data.mobile = phone
    } else {
      data.userMail = email
    }
    const res = await http('/mall/user/sendVerifyCode', {
      method: 'put',
      body: data,
    })
    return await useMall(res)
  } catch (error) {
    return false
  }
}

const regLoading = ref(false)

const verify = ref(null)
const route = useRoute()
const register = () => {
  validate().then(async () => {
    verify.value.show()
  })
}

const cb = async (verifyResult) => {
  console.log(verifyResult)
  const { phone, email, verifyCode, password, identity } = form.value
  const data: Obj = {
    password: aesEncode(password),
    verifyCode,
    identity,
    inviteCipher: route.query.invite,
  }

  if (props.type == Type.phone) {
    data.mobile = phone
  } else {
    data.userMail = email
  }

  regLoading.value = true
  const res = await http('/mall/user/register', {
    method: 'post',
    body: data,
  })
  regLoading.value = false

  useMall(res, () => {
    message.success('注册成功，请登录')
    // 清空邀请码 invite_register_code
    sessionStorage.removeItem('invite_register_code')
    navigateTo({
      path: '/login',
      query: {
        ...route.query,
        type: 'LOGIN',
      },
    })
  })
}
</script>

<style lang="less" scoped>
:deep(.ant-select-selection-placeholder) {
  color: #d9d9d9 !important;
  font-weight: 200;
}
</style>

