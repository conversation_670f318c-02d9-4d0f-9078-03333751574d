<template>
  <mall-and-phone
    v-if="registerType == Type.phone || registerType == Type.email"
    :type="registerType"
    :key="registerType"
  ></mall-and-phone>
  <Wx v-else />

  <div text-center text-14>
    已有账号？
    <a @click="goLogin" cursor-pointer text-primary>立即登录</a>
  </div>

  <a-divider>注册方式</a-divider>
  <div text-center text-14>
    <a-space>
      <span cursor-pointer hover="text-primary" click="registerType = Type.phone" @click="registerType = Type.phone">
        <MobileOutlined color="blue" />
        手机
      </span>

      <span cursor-pointer hover="text-primary" @click="registerType = Type.email">
        <MailOutlined color="blue" />
        邮箱
      </span>
      <span cursor-pointer hover="text-primary" @click="registerType = Type.wx" v-if="!isWx">
        <WechatOutlined color="green" />
        微信
      </span>
    </a-space>
  </div>
</template>

<script setup lang="ts">
import MallAndPhone from './MallAndPhone.vue'
import { Type } from './type'
import Wx from './Wx.vue'

const registerType = ref(Type.phone)

const isWx = useWx()

const route = useRoute()
const router = useRouter()

const goLogin = () => {
  const query = route.query

  router.push({
    path: '/login',
    query: {
      ...query,
      type: 'LOGIN',
    },
  })
}
</script>
