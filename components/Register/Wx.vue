<template>
  <div id="wx-register"></div>
</template>

<script setup>
const route = useRoute()
const initWxCode = () => {
  const inviteCipher = route.query.invite || ''
  new WxLogin({
    self_redirect: true,
    id: 'wx-register',
    appid: 'wx14817ec4ef183e7b',
    scope: 'snsapi_login',
    redirect_uri: encodeURIComponent(
      `https://www.yidao.cloud/mall/wx/callback?inviteCipher=${inviteCipher}`,
    ),
    state: Math.ceil(Math.random() * 1000),
    style: '',
    href: 'data:text/css;base64,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',
  })
}
onMounted(() => {
  initWxCode()
})
</script>
