<template>
  <div class="flex-1 mx-30 relative">
    <a-input-search
      v-model:value="searchValue"
      size="large"
      :placeholder="placeholder"
      @search="onSearch"
      @click.stop="onFocus"
      @change="onChange"
    >
      <template #enterButton>
        <div>
          <SearchOutlined mr-8 />
          <span>搜索</span>
        </div>
      </template>
      <template #suffix>
        <div flex items-center text="primary" cursor-pointer @click="changeType">
          <div mr-8 text-14>{{ currentType.name }}</div>
          <OrderedListOutlined />
        </div>
      </template>
    </a-input-search>
    <div v-show="showSearchRecommend" class="selection-list-container shadow-md bg-white">
      <div
        class="selection-item h-32 flex items-center justify-between"
        v-for="(item, index) in searchHistory"
        :key="index"
        @click="onSelectionItemClick(item, isOuter)"
      >
        <span text>{{ item.keyword }}</span>
        <a
          v-show="item.type == Type.history"
          font-size-14
          href="javascript:void(0)"
          class="text-primary"
          @click.stop="onDeleteHistoryClick(item)"
        >
          删除
        </a>
        <img v-if="item.brandLogo" h-full :src="item.brandLogo" alt="" />
      </div>
      <div h-32 flex-center v-if="!has(searchValue) && searchHistory.length" @click.stop>
        <a-button type="link" @click.stop="clearHistory">清空搜索记录</a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { SearchOutlined, OrderedListOutlined } from '@ant-design/icons-vue'
import usePart, { SearchType as Type } from '~/components/SearchBar/usePart'

defineProps({
  isOuter: {
    type: Boolean,
    default: false,
  },
})

const searchValue = ref('')
const {
  placeholder,
  onSearch,
  onFocus,
  onChange,
  currentType,
  changeType,
  showSearchRecommend,
  searchHistory,
  clearHistory,
  onSelectionItemClick,
  onDeleteHistoryClick,
} = usePart(searchValue)
</script>

<style lang="less" scoped>
.selection-list-container {
  border-bottom-left-radius: 2px;
  border-bottom-right-radius: 2px;
  text-align: left;
  background-color: #fff;
  position: absolute;
  z-index: 999;
  display: block;
  width: calc(100% - 1px);
  top: 100%;
  left: 0;

  .selection-item {
    padding: 8px 8px;
    cursor: pointer;

    &:hover {
      background-color: #e6f4ff;
    }
  }
}
</style>
