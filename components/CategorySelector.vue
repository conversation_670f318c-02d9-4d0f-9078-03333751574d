<template>
  <a-tree-select
    v-model:value="categoryValue"
    style="width: 100%"
    :tree-data="categoryOptions"
    placeholder="请选择产品分类"
    :fieldNames="{
      children: 'children',
      label: 'title',
      key: 'value',
      value: 'value',
    }"
    tree-node-filter-prop="title"
    :tree-default-expand-all="treeDefaultExpandAll"
    :show-search="showSearch"
    :filter-option="false"
    :getPopupContainer="(el) => el.parentElement"
    :allow-clear="allowClear"
    :disabled="disabled"
    @change="handleChange"
    @search="handleSearch"
  />
</template>

<style scoped>
/* Prevent wrapping and add ellipsis for long text */
:deep(.ant-select-tree-node-content-wrapper) {
  white-space: nowrap;
  /* overflow: hidden; */
  /* text-overflow: ellipsis; */
}
</style>

<script setup>
import { Form } from 'ant-design-vue'
const { onFieldChange } = Form.useInjectFormItemContext()
const props = defineProps({
  value: {
    type: [String, Number],
    default: undefined,
  },
  allowClear: {
    type: Boolean,
    default: true,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  width: {
    type: [String, Number],
    default: '100%',
  },
  treeDefaultExpandAll: {
    type: Boolean,
    default: true,
  },
  showSearch: {
    type: Boolean,
    default: true,
  },
  api: {
    type: Function,
    default: null,
  },
})

const emit = defineEmits(['update:value', 'change'])

// 分类树数据
const categoryOptions = ref([])
// 分类选择值
const categoryValue = ref(props.value)

// 监听props.value变化
watch(
  () => props.value,
  (newVal) => {
    categoryValue.value = newVal
  },
)

// 监听categoryValue变化
watch(
  () => categoryValue.value,
  (newVal) => {
    emit('update:value', newVal)
    onFieldChange()
  },
)

// 处理搜索
const handleSearch = (keyword) => {
  fetchCategories(keyword)
}

// 选择变化事件
const handleChange = (value) => {
  emit('change', value)
}

// 获取产品分类
const fetchCategories = async (keyword = '') => {
  // 如果提供了自定义api方法，则使用自定义方法
  if (props.api) {
    const res = await props.api(keyword)
    if (res && res.data) {
      const dfs = (list) => {
        list.forEach((item) => {
          item.selectable = item.type == 'LEAF'
          item.title = item.categoryName
          item.value = item.categoryId
          item.key = item.categoryId
          if (item.children?.length) {
            dfs(item.children)
          }
        })
      }
      dfs(res.data)
      categoryOptions.value = res.data
    }
    return
  }

  // 使用默认api方法
  const [err, res] = await try_http('/mall/category/search', {
    params: {
      keyword,
    },
  })
  if (!err) {
    const dfs = (list) => {
      list.forEach((item) => {
        item.selectable = item.type == 'LEAF'
        item.title = item.categoryName
        item.value = item.categoryId
        item.key = item.categoryId
        if (item.children?.length) {
          dfs(item.children)
        }
      })
    }
    dfs(res.data)
    categoryOptions.value = res.data
  }
}

// 暴露方法
defineExpose({
  fetchCategories,
})

// 初始化
onMounted(() => {
  fetchCategories()
})
</script>
