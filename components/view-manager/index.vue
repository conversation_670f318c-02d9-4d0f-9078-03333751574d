<template>
  <a-button-group v-model:value="active">
    <a-button
      v-for="(item, i) in list"
      :key="item.type"
      :type="active == item.type ? 'primary' : 'default'"
      @click="active = item.type"
      class="flex items-center"
    >
      <i :class="getIcon(item.icon, i)"></i>
      <span class="ml-8px">
        {{ item.name }}
      </span>
    </a-button>
  </a-button-group>
</template>

<script setup>
const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
})

const defaultIcons = ['i-a-ordered-list-outlined', 'i-a-appstore-outlined']
const getIcon = (ic, idx) => {
  return ic || defaultIcons[idx]
}

const active = defineModel()
</script>
