<template>
  <Nav>
      <nuxt-link to="/">首页</nuxt-link>
      <div v-for="(item, i) in store.navList" :key="item.id">
        <nuxt-link v-if="item.type == 'TRUNK'" :to="{ name: 'categoryTree', query: { cid: item.id } }" @click="emits('clickNav', item)">{{ item.categoryName }}</nuxt-link>
      <nuxt-link v-else-if="item.type == 'LEAF'" :to="{ name: 'category', query: { id: item.categoryCode, _t: new Date().getTime() }}">{{ item.categoryName }}</nuxt-link>
      </div>
      <slot />
    </Nav>
</template>

<script lang="ts" setup>
import { treeStore } from '~/store/tree';

const store = treeStore()

const emits = defineEmits(['clickNav'])
</script>
