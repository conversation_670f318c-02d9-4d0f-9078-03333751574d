<template>
  <div>
    {{ timetxt }}
  </div>
</template>

<script setup>
  const props = defineProps({
    allTime: {
      type: Number,
      default: 0
    },
    separator: {
      type: String,
      default: ':'
    }
  })

  const time = ref(null)
  const timetxt = ref('')
  const timeFun = (diffTime) => {
    if (diffTime > 0) {
      time.value = setInterval(() => {
        let diffH = Math.floor(diffTime / 3600) > 9 ? Math.floor(diffTime / 3600) : `0${Math.floor(diffTime / 3600)}`;
        let diffM = Math.floor(diffTime / 60 % 60) > 9 ? Math.floor(diffTime / 60 % 60) : `0${Math.floor(diffTime / 60 % 60)}`;
        let diffS = Math.floor(diffTime % 60) > 9 ? Math.floor(diffTime % 60) : `0${Math.floor(diffTime % 60)}`;
        const [h = ':', m = ':', s = ''] = props.separator.split(',')
        timetxt.value = `${diffH} ${h} ${diffM} ${m} ${diffS} ${s}`;
        diffTime--;
        if (diffTime < 0) {
          clearInterval(time);
          return;
        }
      }, 1000);
    } else {
      timetxt.value = '已过期'
    }
  }

  onMounted(() => {
    timeFun(props.allTime)
  })
</script>