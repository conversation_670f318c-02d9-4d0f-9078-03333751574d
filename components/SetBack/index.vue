<template>
  <a-modal title="设置备选" v-model:open="show" width="80%" @ok="confirm">
    <a-form>
      <a-form-item label="设置备选">
        <a-table
          :data-source="bak.list"
          bordered
          :columns="columns"
          size="small"
          :scroll="{ x: 'max-content' }"
          :pagination="false"
        >
          <template #bodyCell="{ column, record }">
            <div w-200 truncate v-if="column.dataIndex == 'productName'" :title="record.prodName || record.productName">
              <a-tag color="#87d068" v-if="bak.master == record.skuId">主选</a-tag>
              {{ record.prodName || record.productName }}
            </div>
            <div v-if="column.dataIndex == 'action'">
              <a-button type="link" @click="goDetail(record)" :disabled="![0, 1].includes(record.source)">
                查看详情
              </a-button>
              <template v-if="record.skuId != bak.master">
                <a-button type="link" @click="bak.setMaster(record.skuId)">设为主选</a-button>
                <a-button type="link" v-if="!bak.hasRemove[record.skuId]" @click="bak.remove(record.skuId)">
                  删除
                </a-button>
                <a-button type="link" v-else @click="bak.recovery(record.skuId)">恢复</a-button>
              </template>
            </div>
          </template>
        </a-table>
      </a-form-item>

      <a-form-item label="推荐平替" v-if="!hideAdd">
        <a-table
          :data-source="replaceList"
          size="small"
          :columns="columns"
          bordered
          :scroll="{ x: 'max-content' }"
          :pagination="false"
        >
          <template #bodyCell="{ column, record }">
            <div v-if="column.dataIndex == 'productName'" truncate w-200 :title="record.productName">
              <a-tooltip title="请先完成选型">
                <span
                  class="i-mdi-alert-circle text-red text-20 mr-4 cursor-pointer inline-block relative top-5"
                  v-if="!record.skuId"
                ></span>
              </a-tooltip>
              {{ record.productName }}
            </div>

            <div v-if="column.dataIndex == 'action'">
              <a-button type="link" @click="goDetail(record)" :disabled="record.source != 0">查看详情</a-button>
              <a-button
                type="link"
                v-if="record.skuId"
                @click="bak.add(record as Sku)"
                :disabled="bak.skuSet[record.skuId]"
              >
                添加到备选
              </a-button>
              <a-button type="link" v-else @click="showSelect(record)">选型</a-button>
            </div>
          </template>
        </a-table>
      </a-form-item>

      <a-form-item label="添加备选" v-if="!hideAdd">
        <a-input-search
          class="max-w-400!"
          v-model:value="skukw"
          placeholder="搜索产品型号"
          enter-button
          :loading="loadingSearch"
          @search="searchSku"
        ></a-input-search>
        <div mt-10>
          <a-table
            bordered
            :columns="columns"
            :data-source="searchList"
            size="small"
            :scroll="{ x: 'max-content' }"
            :pagination="false"
          >
            <template #bodyCell="{ column, record }">
              <div v-if="column.dataIndex == 'action'">
                <a-button type="link" @click="goDetail(record)" :disabled="record.source != 0">查看详情</a-button>
                <a-button type="link" @click="bak.add(record as Sku)" :disabled="bak.skuSet[record.skuId]">
                  添加到备选
                </a-button>
              </div>
            </template>
          </a-table>
        </div>
      </a-form-item>
    </a-form>

    <set-back-replace ref="repRef"></set-back-replace>
  </a-modal>
</template>

<script setup lang="tsx">
import { PriceWithCount, Sku } from '~/api/ans/type'
import { Bak } from './bak'
import { getRecommendFnV2 } from '~/api/search'

defineProps<{
  hideAdd?: boolean
}>()

const show = ref(false)
const bak = ref(new Bak([], ''))
const searchList = ref<Sku[]>([])

const { formatTrade, formatPrice } = useFormatter()
const columns = [
  {
    title: '产品名称',
    dataIndex: 'productName',
    width: 200,
  },
  {
    title: '型号',
    dataIndex: 'skuCode',
    width: 200,
    customRender({ text }) {
      return (
        <div class="w-200 truncate" title={text}>
          {text}
        </div>
      )
    },
  },
  {
    title: '品牌',
    dataIndex: 'brandName',
  },
  {
    title: '交期',
    dataIndex: 'tradeTerm',
    customRender({ text }) {
      return formatTrade(text)
    },
  },
  {
    title: '单价',
    dataIndex: 'price',
    customRender({ text }) {
      return formatPrice(text)
    },
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
] as any

// 搜索sku
const skukw = ref('')
const loadingSearch = ref(false)
const searchSku = async (keyword: string) => {
  if (!has(keyword)) return
  loadingSearch.value = true
  const res = await request.post<Sku[]>('/selection/search-by-sku', {
    onlyMall: true,
    keyword,
    pageSize: 5,
  })
  loadingSearch.value = false
  useRes(res, () => {
    searchList.value = res.data
    res.data.forEach(async (item) => {
      const res = await http<PriceWithCount>('/mall/p/inquiry-list/total-discount-price', {
        method: 'post',
        body: {
          prodId: item.prodId,
          skuId: item.skuId,
          skuCode: item.skuCode,
          number,
        },
      })
      useMall(
        res,
        () => {
          item.price = res.data.skuPrice
          item.tradeTerm = res.data.tradeTerm
        },
        () => {},
      )
    })
  })
}
// 搜索平替
const replaceList = ref<Obj[]>([])
const fetchReplace = async (id) => {
  const res = await getRecommendFnV2(`mall-${id}`, 5)
  useRes(res, () => {
    replaceList.value = res.data.map((item) => {
      return {
        id: item.id,
        prodId: item.id.replace(/.*-(.*)/, '$1'),
        brandName: item.website.websiteName,
        productName: item.productName,
      }
    })
  })
}

let cb: (show: Ref<boolean>, data) => void
let number = 0
const open = (list, master, _number, mallId, callback: (show: Ref<boolean>, data) => void) => {
  cb = callback
  number = _number
  bak.value = new Bak(list, master)
  skukw.value = ''
  searchList.value = []
  replaceList.value = []
  fetchReplace(mallId)
  show.value = true
}

const repRef = ref()
const showSelect = (record) => {
  repRef.value?.open(record, async (open, skuCode, skuId) => {
    open.value = false
    record.skuCode = skuCode
    record.skuId = skuId
    const res = await http<PriceWithCount>('/mall/p/inquiry-list/total-discount-price', {
      method: 'post',
      params: {
        prodId: record.prodId,
        number,
        skuId,
        skuCode,
      },
    })
    useMall(
      res,
      () => {
        record.price = res.data.skuPrice
        record.tradeTerm = res.data.tradeTerm
      },
      () => {},
    )
    open.value = false
  })
}

const router = useRouter()
const goDetail = (row) => {
  let prodId = row.prodId
  if (row.source == 0) {
    prodId = 'mall-' + prodId
  } else if (row.source == 1) {
    prodId = row.brandCode + '-' + prodId
  }
  const path = router.resolve({
    path: `/parts/${prodId}`,
    query: {
      typeCode: row.skuCode,
    },
  })

  const href = path.href

  window.open(href)
}

const confirm = async () => {
  cb(show, bak.value.data)
}

defineExpose({
  open,
})
</script>
