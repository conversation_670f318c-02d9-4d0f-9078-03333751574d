<template>
  <a-modal
    v-model:open="show"
    title="备选选型"
    :ok-button-props="{
      disabled: !select.isFinished,
    }"
    @ok="handleOk"
    :confirm-loading="loading"
  >
    <div h-500 overflow-y-auto>
      <Select :key="show" :model="select" />
    </div>
  </a-modal>
</template>

<script setup>
import Model from '../Select/mode'

const emits = defineEmits(['skuCode'])

const show = ref(false)
const select = ref(new Model())

let cb
const open = (record, _cb) => {
  select.value = new Model(record.id)
  cb = _cb
  show.value = true
}
const loading = ref(false)

const handleOk = async () => {
  loading.value = true
  await cb(show, select.value.skuCode, select.value.skuId)
  loading.value = false
}

defineExpose({
  open,
})
</script>
