import { Sku } from '~/api/ans/type'
type Data = Pick<Sku, 'prodId' | 'skuId' | 'skuCode' | 'source'> & {
  isMaster: boolean
}

export class Bak {
  list: Sku[] = []

  master: number | undefined = undefined

  hasRemove: Record<any, boolean> = {}

  constructor(_list, master) {
    this.list = _list
    this.master = master
  }

  get skuSet() {
    return this.list.reduce(
      (res, item) => {
        res[item.skuId] = true
        return res
      },
      {} as Record<any, boolean>,
    )
  }

  add(item: Sku) {
    if (this.skuSet[item.skuId]) {
      return
    }
    this.list.push(item)
  }

  remove(skuId: number) {
    this.hasRemove[skuId] = true
  }

  recovery(skuId: number) {
    this.hasRemove[skuId] = false
  }

  setMaster(skuId: number) {
    this.master = skuId
  }

  get data() {
    return this.list.reduce((res, item) => {
      const { prodId, skuId, skuCode, source } = item
      if (!this.hasRemove[skuId]) {
        res.push({
          prodId,
          skuId,
          skuCode,
          source,
          isMaster: this.master == skuId,
        })
      }
      return res
    }, [] as Data[])
  }
}
