<template>
  <a-sub-menu class="sub-menu" v-if="menuItem.type === 'TRUNK'" :key="menuItem.id">
    <template #title>
      <div flex flex-row justify-start items-center h-16>
        <FolderOutlined />
        <p class="menu-item-title">{{ menuItem.categoryName }}</p>
      </div>
    </template>
    <template v-for="item in menuItem.children">
      <CategoryMenuItem v-if="item.type === 'TRUNK'" :key="item.id" :menuItem="item" />
      <a-menu-item v-else class="menu-item" :key="item.categoryCode">
        <template #icon>
          <img w-16 :src="item.categoryImgUrl || unknownImg" />
        </template>
        <p class="menu-item-text">{{ item.categoryName }}</p>
      </a-menu-item>
    </template>
  </a-sub-menu>
  <a-menu-item class="menu-item" :key="menuItem.categoryCode" v-else>
    <template #icon>
      <img w-16 :src="menuItem.categoryImgUrl || unknownImg" />
    </template>
    <p class="menu-item-text">{{ menuItem.categoryName }}</p>
  </a-menu-item>
</template>

<script>
import unknownImg from '~/assets/images/unknown.png';
export default {
  name: 'CategoryMenuItem',
  props: {
    menuItem: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      unknownImg,
    };
  },
};
</script>

<style lang="less" scoped>
.sub-menu {
  display: flex;
  flex-direction: row;
  justify-self: start;
}

.menu-item {
  max-width: 120px;

  .menu-item-text {
    display: block;
    margin: 0;
    padding: 0;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 14px;
    font-weight: 500;
  }
}

.menu-item-title {
  margin: 0 0 0 10px;
  padding: 0;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  font-weight: 500;
}
</style>
