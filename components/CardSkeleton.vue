<template>
  <div class="grid grid-cols-[repeat(auto-fill,minmax(240px,1fr))] gap-20">
    <div v-for="item in count" :key="item" text-center>
      <a-skeleton-button active block />
      <br />
      <br />
      <a-skeleton-image w-full />
      <br />
      <br />
      <a-skeleton-button active block />
    </div>
  </div>
</template>

<script lang="ts" setup>
withDefaults(
  defineProps<{
    count?: number
  }>(),
  {
    count: 20,
  },
)
</script>

