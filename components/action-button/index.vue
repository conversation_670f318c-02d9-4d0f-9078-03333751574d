<template>
  <div
    class="action-button relative"
    :class="{
      'action-button-disabled': disabled,
    }"
    @click="click"
  >
    <slot />
  </div>
</template>

<script setup>
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
})

const emits = defineEmits(['click'])
const click = () => {
  if (!props.disabled) {
    emits('click')
  }
}
</script>

<style lang="less" scoped>
.action-button {
  @apply h-32px inline-flex justify-center items-center cursor-pointer text-primary;
  & + .action-button {
    margin-left: 8px;
  }
  &-disabled {
    cursor: not-allowed;
    color: rgba(0, 0, 0, 0.25);
  }
}
</style>
