const renderChildren = (children) => children.map((item, index) => {
  if (Array.isArray(item.children)) {
    return renderChildren(item.children)
  }
  return <a-breadcrumb-item key={index}>
    <div class="inline-block">{item}</div>
  </a-breadcrumb-item>
})
const Nav = defineComponent({
  setup() {
    const router = useRouter()
    return () => {
      const slots = useSlots()
      return <div class="flex items-center">
        <a-button type="link" onClick={() => router.back()} class="pl-0 text-12 relative top--1 sm:text-14 sm:top-0">&lt;返回</a-button>
        <a-breadcrumb separator=">" class="text-12 lg:text-14">
          { slots.default && renderChildren(slots.default())}
        </a-breadcrumb>
      </div>
    }
  }
})

export default Nav