<template>
  <div class="h-full bg-#fff text-#333 text-14" @click.prevent ref="parentRef">
    <div flex h-full>
      <div w-250 h-full overflow-y-auto>
        <a-tree
          :tree-data="treeData as any"
          v-if="treeData.length"
          default-expand-all
          :showIcon="false"
          v-model:selected-keys="selectedKeys"
          @select="(_, e) => clickNode(e.node.dataRef)"
          :field-names="{ key: 'id' }"
        >
          <template #title="node">
            <img v-if="node.logoUrl" :src="node.logoUrl" w-20 h-20 leading-20 mr-5 />
            <span :title="node.categoryName">
              {{ node.categoryName }}
            </span>
          </template>
        </a-tree>
      </div>

      <div
        class="w-[calc(100%-250px)] absolute left-250 h-full bg-#fff"
        v-if="showRight"
        overflow-y-auto
        :style="{ borderLeft: '1px solid #eee', borderRight: '1px solid #eee' }"
      >
        <h3 py-8 pl-8 style="border-bottom: 1px solid #eee">
          <div flex items-center :class="{ 'cursor-pointer': treeMap[active.parentId] }" @click="goPrev">
            <LeftOutlined mr-16 v-if="treeMap[active.parentId]" />
            {{ active.categoryName }}
          </div>
        </h3>
        <div v-if="loading" class="flex-center w-full h-full">
          <a-spin></a-spin>
        </div>
        <template v-else-if="data.length">
          <div class="px-8 grid grid-cols-[repeat(auto-fill,minmax(200px,1fr))]">
            <div class="truncate relative" v-for="item in data" :key="item.id" @click="clickItem(item)">
              <div
                v-if="item.type == 'TRUNK'"
                flex
                items-center
                h-80
                leading-25
                rounded-2
                cursor-pointer
                class="hover:bg-primary px-10 hover:text-#fff w-full"
                truncate
                :title="item.categoryName"
              >
                <img :src="item.categoryImgUrl || folder" w-50 h-50 mr-10 alt="" />
                <div flex flex-col flex-1 overflow-hidden>
                  <div truncate>{{ item.categoryName }}</div>
                  <div>类目数: {{ item.categorySize }}</div>
                </div>
                <folder-filled
                  absolute
                  left-10
                  bottom-10
                  text-20
                  text-yellow
                  v-if="item.categoryImgUrl"
                ></folder-filled>
              </div>

              <div
                v-if="item.type == 'LEAF'"
                inline-block
                h-80
                leading-80
                rounded-2
                cursor-pointer
                class="hover:bg-primary px-10 hover:text-#fff w-full"
                truncate
                :title="item.categoryName"
              >
                <img :src="item.categoryImgUrl || unknownImg" w-50 h-50 mr-10 alt="" />
                <span>
                  {{ item.categoryName }}
                </span>
              </div>
            </div>
          </div>
        </template>

        <Empty mt-50 v-else></Empty>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { Category } from '~/types/category'
import unknownImg from '~/assets/images/unknown.png'
import folder from '~/assets/images/folder.svg'
import { getCategoryChildren, getCategoryTree } from '~/api/category'

const showRight = defineModel<boolean>('show')
const parentRef = ref()
onClickOutside(parentRef, () => {
  emits('outClick')
  // showRight.value = false
})
const emits = defineEmits<{
  toDetail: []
  outClick: []
}>()

const treeData = ref<Category[]>([])

const treeMap = ref<
  Record<
    string,
    {
      node: Category
      list: Category[]
    }
  >
>({})

const loading = ref(false)

const active = ref({} as Category)
const selectedKeys = ref<string[]>([])

const router = useRouter()
const goCategory = (id) => {
  emits('toDetail')
  router.push({
    path: '/category',
    query: { id, _t: new Date().getTime() },
  })
}
const clickNode = async (node) => {
  showRight.value = true
  changeNode(node)
}

const changeNode = async (node) => {
  const { id } = node
  if (!treeMap.value[id]) {
    await loadNode(node)
  }
  active.value = node
}
const openCategory = (node: Category) => {
  if (!treeMap.value[node.id]) {
    treeMap.value[node.id] = {
      node,
      list: node.children || [],
    }
  }
  // selectedKeys.value = [node.id as string];
  active.value = node
}

const loadNode = async (node: Category) => {
  loading.value = true
  const res = await useCacheFetch(`tree-node-${node.id}`, () => getCategoryChildren(node.id))
  loading.value = false
  useRes(res, () => {
    treeMap.value[node.id] = {
      node,
      list: res.data,
    }
  })
}

const clickItem = (item: Category) => {
  if (item.type == 'TRUNK') {
    openCategory(item)
  } else if (item.type === 'LEAF') {
    goCategory(item.categoryCode)
  }
}

const goPrev = () => {
  const node = treeMap.value[active.value.parentId]
  openCategory(node.node)
}

const data = computed(() => {
  let list: Category[] = []
  const node = treeMap.value[active.value.id]
  if (node) {
    list = node.list
  }

  return list
})

onMounted(async () => {
  const res = await useCacheFetch('tree-all', () => getCategoryTree({ maxGrade: 3 }))
  useRes(res, () => {
    treeData.value = res.data.reduce((list, item) => {
      if (item.children?.length) {
        list = list.concat(item.children)
      }
      return list
    }, [] as Category[])
    if (treeData.value.length) {
      changeNode(treeData.value[0])
    }
  })
})
</script>

<style lang="less" scoped>
:deep(.ant-tree-treenode) {
  width: 100%;
  .ant-tree-node-content-wrapper-normal {
    padding-right: 12px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
.cateh {
  height: calc(100vh - 70px);
}
</style>
