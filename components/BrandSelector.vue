<template>
  <a-select
    v-model:value="brandValue"
    placeholder="请选择品牌"
    style="width: 100%"
    :allowClear="allowClear"
    :options="brandOptions"
    :fieldNames="{ label: 'name', value: 'brandId' }"
    :disabled="disabled"
    @change="handleChange"
    :show-search="true"
    :filter-option="false"
    @search="handleSearch"
    :loading="loading"
  />
</template>

<script setup>
const props = defineProps({
  value: {
    type: [String, Number],
    default: undefined,
  },
  allowClear: {
    type: Boolean,
    default: true,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  width: {
    type: [String, Number],
    default: '100%',
  },
  api: {
    type: Function,
    default: null,
  },
})

const emit = defineEmits(['update:value', 'change'])

// 品牌列表
const brandOptions = ref([])
// 品牌选择值
const brandValue = ref(props.value)
// 加载状态
const loading = ref(false)

// 过滤选项方法
const filterOption = (input, option) => {
  return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 监听props.value变化
watch(
  () => props.value,
  (newVal) => {
    brandValue.value = newVal
  },
)

// 监听brandValue变化
watch(
  () => brandValue.value,
  (newVal) => {
    emit('update:value', newVal)
  },
)

// 选择变化事件
const handleChange = (value) => {
  emit('change', value)
}

// 获取品牌列表
const fetchBrands = async (name = '') => {
  // 如果提供了自定义api方法，则使用自定义方法
  if (props.api) {
    const res = await props.api(name)
    if (res && res.data) {
      brandOptions.value = res.data || []
    }
    return
  }

  // 使用默认api方法
  const [err, res] = await try_http('/mall/brand/list', {
    params: {
      status: 1,
      name,
    },
  })
  if (!err) {
    brandOptions.value = res.data || []
  }
}

// 处理搜索
const handleSearch = useDebounceFn((value) => {
  if (!value) {
    fetchBrands() // 当搜索值为空时，重置为全部数据
    return
  }

  loading.value = true
  fetchBrands(value).finally(() => {
    loading.value = false
  })
}, 300)

// 暴露方法
defineExpose({
  fetchBrands,
})

// 初始化
onMounted(() => {
  fetchBrands()
})
</script>
