<template>
  <div flex items-center>
    <div mr-30>
      <div
        :class="{ dark: theme === 'dark' }"
        inline-block
        text-center
        color-white
        cursor-pointer
        mr-20
        hover:color-primary
        max-md-hidden
        @click="navigateTo({ path: '/my/score' })"
      >
        <PayCircleOutlined text="20" mb-4 />
        <div font-size-12>我的积分</div>
      </div>

      <a-badge :count="messages.unReadNum" :overflow-count="99" class="max-md-hidden" :offset="[-10]">
        <div
          :class="{ dark: theme === 'dark' }"
          inline-block
          text-center
          color-white
          cursor-pointer
          hover:color-primary
          @click="navigateTo({ path: '/message/platform' })"
        >
          <MailOutlined text="20" mb-4 />
          <div font-size-12>消息中心</div>
        </div>
      </a-badge>
    </div>
    <a-popover placement="bottom" @visibleChange="onClickChange">
      <template #content>
        <div w-125 text-center>
          <div class="text-20 font-bold" style="overflow: hidden" :title="user.user.nickName">
            {{ user.user.nickName }}
          </div>
          <div
            :style="{ background: memberLevel.color }"
            class="py-2 px-4 mt-10 inline-block rounded-8 text-12 text-white cursor-pointer"
            @click="navigateTo({ path: '/my/member' })"
          >
            {{ memberLevel.label }}
          </div>
          <div text-14 mt-5 text-gray cursor-pointer @click="navigateTo({ path: '/my/score' })">
            <PayCircleOutlined />
            <span ml-4>积分:</span>
            {{ score }}
          </div>

          <div
            flex
            text="gray 14"
            mt-15
            text-left
            cursor-pointer
            hover="text-primary"
            @click="navigateTo({ path: '/my/info' })"
          >
            <div flex-1>
              <UserOutlined />
              个人中心
            </div>
            <RightOutlined />
          </div>

          <div flex text="gray 14" mt-5 text-left cursor-pointer max-md-hidden hover="text-primary" @click="goIn">
            <div flex-1>
              <HomeOutlined />
              <span class="space" :title="company.merchantShortName || company.merchantName">
                {{ company.shopCompanyId ? company.merchantShortName || company.merchantName : '个人空间' }}
              </span>
            </div>
            <RightOutlined />
          </div>

          <div flex text="gray 14" mt-5 text-left cursor-pointer hover="text-primary" @click="userLogout">
            <div flex-1>
              <LogoutOutlined />
              退出登录
            </div>
            <RightOutlined />
          </div>
        </div>
      </template>
      <div class="ava-box">
        <a-avatar class="bg-primary cursor-pointer" :src="user.user.pic" :size="48" :draggable="false">
          {{ user.user.nickName }}
        </a-avatar>
        <img v-if="company.shopCompanyId" class="qy" src="~/assets/images/svg/qy.png" />
      </div>
    </a-popover>
  </div>
</template>

<script setup lang="ts">
import { getTotalScore } from '~/api/user'
import { MemberLevel, userStore } from '~/store/user'
import { companyStore } from '~/store/company'
import { messageStore } from '~/store/message'
import { authMenuStore } from '~/store/authMenu'

const props = defineProps({
  theme: {
    type: String,
    default: 'light',
  },
})

const user = userStore()
const memberLevel = computed(() => {
  switch (user.user.level) {
    case MemberLevel.NORMAL:
      return { label: '普通会员', color: '#71c4ef' }
    case MemberLevel.AUTHENTICATION:
      return { label: '认证会员', color: '#67abff' }
    case MemberLevel.GENERALIZATION:
      return { label: '推广会员', color: '#1c344f' }
    case MemberLevel.PROFESSIONAL:
      return { label: '专业会员', color: 'var(--primary-color)' }
  }
})

const companyStoreObj = companyStore()
const { company } = storeToRefs(companyStoreObj)
const useAuthMenu = authMenuStore()
const { authMenu } = storeToRefs(useAuthMenu)
const goIn = () => {
  const companyId = company.value.shopCompanyId
  const { menuItemKeys } = authMenu.value
  if (companyId) {
    navigateTo({
      path: `/workSpace/company-space/${menuItemKeys[0] || 'no-menu'}`,
      query: { companyId },
    })
  } else {
    navigateTo({
      path: `/workSpace/my-space/selection-library`,
    })
  }
}

const starts = ['/my', '/workSpace', '/message', '/ans-price']

const userLogout = async () => {
  http('/mall/p/logOut', {
    method: 'post',
  }).then((res) => {
    useMall(res, () => {
      user.clearUser()
      companyStoreObj.setCompany({})
      const pathname = window.location.pathname
      if (starts.some((item) => pathname.startsWith(item))) {
        message.success('已退出登录，即将跳转到登录页...')
        setTimeout(() => {
          window.location.href = '/login'
        }, 1000)
      } else {
        message.success('已退出登录')
      }
    })
  })
}

const score = ref(0)
onMounted(() => {
  getTotalScore().then((res) => {
    useRes(res, () => {
      score.value = res.data.score
    })
  })
})

const onClickChange = (v) => {
  if (!v) return
  getTotalScore().then((res) => {
    useRes(res, () => {
      score.value = res.data.score
    })
  })
}

const messageStoreObj = messageStore()
const { messages, refreshMessage } = messageStoreObj
refreshMessage()
</script>

<style scoped>
.ava-box {
  position: relative;
  padding: 3px;
  background: inherit;
}

.dark {
  color: #333;
}

.qy {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 20px;
  height: 20px;
  z-index: 2;
  background: inherit;
}

.space {
  display: inline-block;
  padding: 0 5px;
  width: 90px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
}
</style>
