<template>
  <div text-20 select-none>
    <Swiper
      class="swiper-image"
      w-full
      h-full
      @init="init"
      :modules="[Pagination]"
      :pagination="{ type: 'bullets', clickable: true }"
    >
      <swiper-slide v-for="item in list" :key="item">
        <img :src="item" w-full h-full object-scale-down alt="" />
      </swiper-slide>
      <template #container-start>
        <LeftOutlined
          absolute
          z10
          top="1/2"
          mt--10
          cursor-pointer
          hover:text-primary
          @click="() => swiper?.slidePrev()"
          v-if="!swiper?.isBeginning"
        />
      </template>
      <template #container-end>
        <RightOutlined
          absolute
          z10
          top="1/2"
          right-0
          mt--10
          cursor-pointer
          hover:text-primary
          @click="() => swiper?.slideNext()"
          v-if="!swiper?.isEnd"
        />
      </template>
    </Swiper>
  </div>
</template>

<script lang="ts" setup>
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Swiper as TSwiper } from 'swiper'
import { Pagination } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/pagination'

const props = defineProps<{
  links?: string
}>()
const list = computed(() => useImage(props.links, true))
const swiper = ref<TSwiper>()
const init = (instance: TSwiper) => {
  swiper.value = instance
}
</script>

<style lang="less" scoped>
:deep(.swiper-image) {
  --swiper-pagination-color: theme('colors.primary');
}
</style>

