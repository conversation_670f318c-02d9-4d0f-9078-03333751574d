<template>
  <div card--reactive hover:translate-y-3 overflow-hidden @click="onItemClick" v-if="type == 'card'">
    <div bg-primary absolute text="10 #fff" rotate--45 translate-z-0 backface-hidden will-change-transform top-20 h-14
      leading-14 left--20 w-100 text-center shadow-md v-if="info.fullMatched">
      完全匹配
    </div>
    <div flex p="y-8 x-12">
      <img h-24 :src="info.website.websiteImgUrl" />
      <div class="flex-1"></div>
      <a-popover v-if="info.specPdfUrl" title="该产品提供文档查看与下载">
        <FileOutlined text="20 gray" @click.stop="onPreviewDocClick" />
      </a-popover>
      <a-popover title="该产品提供3D模型查看与下载" v-if="info.has3DModel">
        <CodeSandboxOutlined text="20 gray" ml-5 />
      </a-popover>
      <!-- <a-checkbox :checked="comparing">对比</a-checkbox> -->
    </div>
    <div>
      <div text-center>
        <img h-160 w-125 object-scale-down :src="useImage(info.imageUrl || unknownImg)" />
      </div>
      <div p="y-15 x-12">
        <div text text-16 font-bold truncate mb-8 :title="info.productName"
          :innerHTML="getHighlightText(info.productName, 'keyword')"></div>
        <div v-if="searchType == 'skuCode'" text font-bold truncate mb-8 :title="info.productName"
          :innerHTML="getHighlightText(info.partCode || '', 'skuCode')"></div>
        <div v-if="!isLogin" text="primary 14" mt-16 font-bold cursor-pointer @click.stop="login">
          登录后查看价格与交期 &gt;
        </div>
        <div v-else>
          <!-- <div text="14 #858585" font-bold v-if="!info.canInquiry">该产品暂不支持询价与购买</div> -->
          <div text="14 #ff0000" font-bold v-if="isUpdate">价格信息更新中</div>
          <div v-else flex>
            <div flex-1 text="18 #ff0000" font-bold>
              <span>
                {{ getPrice(info.discountedPrice) }}
              </span>
              <span text-10 v-if="searchType == 'keyword'">起</span>
              <span text-14 ml-5 v-if="info.packingUnit">/{{ info.packingUnit }}</span>
            </div>
            <div v-if="!isUpdate && isLogin" bg-primary text-white text-12 px-4 py-2 rounded-2>{{ getTerm() }}</div>
            <!-- <div line-through text="12 #858585">{{ getPrice(info.price) }}</div> -->
          </div>
        </div>
        <div mt-10 mb-4 flex items-center>
          <div sub-text flex-1>{{ info.website.websiteName }}</div>
          <a-tooltip title="询问客服">
            <div class="i-custom-customer text-20px cursor-pointer hover-text-primary" @click.stop="askAgent"></div>
          </a-tooltip>
          <!-- <div v-if="!isUpdate && isLogin" bg-primary text-white text-12 px-4 py-2 rounded-2>{{ getTerm() }}</div> -->
        </div>
      </div>
    </div>
  </div>

  <div v-else-if="type == 'list'" class="list-item-component" card--reactive flex="~ items-center justify-between" h-90
    pl-16 overflow-hidden @click="onItemClick">
    <div bg-primary absolute text="10 #fff" rotate--45 translate-z-0 backface-hidden will-change-transform top-20 h-14
      leading-14 left--20 w-100 text-center shadow-md v-if="info.fullMatched">
      完全匹配
    </div>
    <img h-80 w-80 object-scale-down :src="useImage(info.imageUrl || unknownImg)" />
    <div text w-200 max-sm:flex-1 line-height-20 :title="info.productName"
      :innerHTML="getHighlightText(info.productName, 'keyword')"></div>

    <div v-if="searchType == 'skuCode'" text w-200 max-sm:flex-1 line-height-20 :title="info.productName"
      :innerHTML="getHighlightText(info.partCode || '', 'skuCode')"></div>

    <div w-100 max-sm:hidden>
      <img mb-6 max-h-36 :src="info.website.websiteImgUrl" />
      <div sub-text>{{ info.website.websiteName }}</div>
    </div>
    <div w-300 max-sm:w-auto text-center text="20 gray">
      <a-popover v-if="info.specPdfUrl" title="该产品提供文档查看与下载">
        <FileOutlined text="20 gray" @click.stop="onPreviewDocClick" />
      </a-popover>

      <a-popover v-if="info.has3DModel" title="该产品提供3D模型查看与下载">
        <CodeSandboxOutlined ml-5 />
      </a-popover>
    </div>

    <div w-100>
      <div v-if="!isUpdate && isLogin" bg-primary text-white text-12 px-4 inline-block py-2 rounded-2>
        {{ getTerm() }}
      </div>
    </div>

    <div w-200 v-if="!isLogin" text="primary 14" mt-16 font-bold cursor-pointer @click.stop="login">
      登录后查看价格与交期 &gt;
    </div>
    <div w-200 v-else>
      <div text="14 #858585" font-bold v-if="!info.canInquiry">该产品暂不支持询价与购买</div>
      <div text="14 #ff0000" font-bold v-else-if="isUpdate">价格信息更新中</div>
      <div v-else class="text-center">
        <!-- <div line-through text="12 #858585">{{ getPrice(info.price) }}</div> -->
        <div text="14 #ff0000" font-bold mt-16>
          {{ getPrice(info.discountedPrice) }}
          <span text-10 v-if="searchType == 'keyword'">起</span>
          <span text-14 v-if="info.packingUnit">/{{ info.packingUnit }}</span>
        </div>
        <a-tooltip title="询问客服">
          <div class="i-custom-customer text-20px cursor-pointer hover-text-primary mt-10px" @click.stop="askAgent">
          </div>
        </a-tooltip>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { usePartStore } from '~/store/part'
import unknownImg from '~/assets/images/unknown.png'
import ms from 'magic-string'
import { userStore } from '~/store/user'
import { SearchItem } from './type'

const store = userStore()
const isLogin = computed(() => !!store.user.userId)

const emits = defineEmits(['click', 'afterLogin', 'askAgent'])

const { addValueToPart } = usePartStore()
type SearchType = 'skuCode' | 'keyword'

const props = defineProps<{
  info: SearchItem
  type: 'card' | 'list'
  searchType: SearchType
  shopId?: string
}>()

const getHighlightText = (originText: string, searchType: SearchType) => {
  if (props.searchType != searchType) return originText
  const { highLights, fullMatched } = props.info
  if (!highLights) return originText
  if (fullMatched) return `<span style="color: red">${originText}</span>`
  if (Array.isArray(highLights)) {
    const [text] = highLights
    let match
    const reg = /<em>(.*?)<\/em>/g
    const s = new ms(text)
    while ((match = reg.exec(text))) {
      s.update(match.index, match.index + match[0].length, `<span style="color: red">${match[1]}</span>`)
    }
    return s.toString()
  } else if (typeof highLights === 'string') {
    const reg = new RegExp(`(${highLights})`, 'ig')
    const s = new ms(originText)
    let match
    while ((match = reg.exec(originText))) {
      s.update(match.index, match.index + match[0].length, `<span style="color: red">${match[1]}</span>`)
    }
    return s.toString()
  }
  return originText
}

const onItemClick = () => {
  console.log(`output->props`, props)
  // router.push({
  //   path: '/parts/' + props.id,
  //   query: {
  //     typeId: props.partCode,
  //   },
  // })
  emits('click')
  // 临时跳转到源网页
  // window.open(props.pageUrl, '_blank')
  addValueToPart(props)
}

const onPreviewDocClick = () => {
  const { specPdfUrl } = props.info
  if (!!specPdfUrl) {
    if (!store.user.userId) {
      loginPop.show()
      return
    }

    previewDoc(specPdfUrl)
  } else {
    return
  }
}

const login = () => {
  loginPop.show(() => {
    emits('afterLogin')
  })
}

const isUpdate = computed(() => {
  const { price, tradeTerm } = props.info
  return !has(price) || !has(tradeTerm)
})

const getTerm = () => {
  const { tradeTerm } = props.info
  if (props.searchType == 'skuCode') {
    return getTradeTerm(tradeTerm)
  } else {
    return getTradeTerm(tradeTerm, '最快')
  }
}

const askAgent = () => {
  const { id, partCode } = props.info
  const isSku = props.searchType == 'skuCode'
  let _id = id
  if (isSku) {
    _id = id + partCode
  }
  chatPop.show(props.shopId ?? '0', {
    ...props.info,
    _id,
    isSku,
    skuCode: partCode,
  })
}
</script>
