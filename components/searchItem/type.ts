import { Website } from '~/api/search'

export type SearchItem = {
  canInquiry: boolean
  discountedPrice?: number
  from: string
  fullMatched: boolean
  has3DModel: boolean
  highLights: string[]
  id: string
  imageUrl: string
  modelFile: string
  modelParameterFileId?: string
  modelParameterType: number
  packingUnit?: string
  pageUrl: string
  partCode?: string
  price?: number
  prjPath?: string
  productBrand: string
  productName: string
  source: number
  specPdfUrl: string
  tradeTerm?: number
  typeCode?: string
  website: Website
}
