<template>
  <a-modal v-model:open="open" title="选择格式" width="600px" :footer="null">
    <div flex mb-10>
      <a-button
        type="primary"
        :loading="loadingSets.has('select')"
        :disabled="!rowkeys.length"
        @click="download(rowkeys, 'select')"
      >
        批量下载
      </a-button>
      <div flex-1></div>
      <div max-w-300>
        <a-input-search enter-button v-model:value="filterKey"></a-input-search>
      </div>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      bordered
      row-key="value"
      size="small"
      :row-selection="{
        selectedRowKeys: rowkeys,
        onChange: changeSelect,
        hideSelectAll: true,
      }"
    >
      <template #bodyCell="{ column, record }">
        <a-button
          :loading="loadingSets.has(record.value)"
          type="link"
          v-if="column.dataIndex == 'action'"
          @click="download([record.value], record.value)"
        >
          下载
        </a-button>
      </template>
    </a-table>
  </a-modal>
</template>

<script setup lang="ts">
import Model from '../Select/mode'

const open = ref(false)

const props = defineProps<{
  select: Model
}>()

type DownItem = { key: string; value: string }

const filterKey = ref('')

const { select } = toRefs(props)
const columns = ref([
  {
    title: '格式',
    dataIndex: 'key',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '100px',
  },
])
const list = computed(() => {
  const { from } = select.value.pageDetail || {}
  let list: DownItem[] = []
  if (from == 'Cadenas') {
    list = caList
  } else if (from == 'BE') {
    list = beList.map((item) => ({ key: item, value: item }))
  } else if (from == 'XinDi') {
    list = xindiList.map((item) => ({ key: item, value: item }))
  }
  if (filterKey.value) {
    list = list.filter((item) => item.key.toLowerCase().includes(filterKey.value.toLowerCase()))
  }
  return list
})

const caList = [
  { key: 'HP ME 10 >=V9 (2d)', value: 'ME10' },
  { key: 'AutoCAD >=V14 (3d)', value: 'ACAD3D' },
  { key: 'Trimble Sketchup 2017 (3d)', value: 'SKETCHUP2017' },
  { key: 'STEP AP214 (3d)', value: 'STEP-2.14' },
  { key: 'Inventor 2023 (3d)', value: 'AIS2023' },
  { key: 'FBX (Filmbox) (3d)', value: 'FBX' },
  { key: 'Autodesk Revit Instance+Family 2023 (3d)', value: 'REVIT_INSTFAM_2023' },
  { key: 'CoCreate Modeling >=2007 (3d)', value: 'm3d_sd_cc2007' },
  { key: 'Inventor 2024 (3d)', value: 'AIS2024' },
  { key: 'NX 1899 (3d)', value: 'UG3DNX1899' },
  { key: 'Metafile 2D V1 (2d)', value: 'meta' },
  { key: 'JT (3d)', value: 'EAI' },
  { key: 'BeckerCAD (3d)', value: 'BECKERCAD' },
  { key: 'Animated GIF 640x480 (2d)', value: 'ANIMGIF-640x480' },
  { key: 'DXF AUTOCAD VERSION >= 2013 (2d)', value: 'DXF2D-2013' },
  { key: 'DXF AUTOCAD VERSION 2007 - 2009 (2d)', value: 'DXF2D-2007' },
  { key: 'Autodesk Revit Instance+Family 2024 (3d)', value: 'REVIT_INSTFAM_2024' },
  { key: 'Autodesk Revit Instance+Family 2021 (3d)', value: 'REVIT_INSTFAM_2021' },
  { key: 'AutomationML (3d)', value: 'AML' },
  { key: 'DWG AUTOCAD VERSION 2007 - 2009 (3d)', value: 'DWG3D-2007' },
  { key: 'Autodesk Revit 2022 (3d)', value: 'REVIT_2022' },
  { key: 'FBX with additional data (3d)', value: 'FBX_EXTRA' },
  { key: 'Creo Parametric 11 (3d)', value: 'CREO11' },
  { key: 'DWG AUTOCAD VERSION 2010 - 2012 (2d)', value: 'DWG2D-2010' },
  { key: 'DWG AUTOCAD VERSION 2004 - 2006 (3d)', value: 'DWG3D-2004' },
  { key: 'NX 1872 (3d)', value: 'UG3DNX1872' },
  { key: 'AutoCAD Electrical (2d)', value: 'AUTOCADELECTRICALMDBFILE' },
  { key: 'Inventor 2022 (3d)', value: 'AIS2022' },
  { key: 'NX 2206 (3d)', value: 'UG3DNX2206' },
  { key: 'STEP AP203 (3d)', value: 'STEP-2.03' },
  { key: 'OBJ (WaveFront) (3d)', value: 'OBJFILE' },
  { key: 'SolidWorks (Macro) >=2001+ (3d)', value: 'SW_MAC' },
  { key: 'VRML =V1.0 (3d)', value: 'VRML' },
  { key: 'Inventor 2021 (3d)', value: 'AIS2021' },
  { key: 'Creo Elements/Direct Modeling >=17.0 (3d)', value: 'm3d_sd_creo17' },
  { key: 'SolidWorks >=2015 (3d)', value: 'SOLIDWORKS' },
  { key: 'Metafile 3D (PS3) V2 (3d)', value: 'meta3DV2' },
  { key: 'Autodesk Revit 2023 (3d)', value: 'REVIT_2023' },
  { key: 'Solid Edge 2021 (3d)', value: 'SE_3D_2021' },
  { key: 'DXF AUTOCAD VERSION 2007 - 2009 (3d)', value: 'DXF3D-2007' },
  { key: 'DXF AUTOCAD VERSION >= 2013 (3d)', value: 'DXF3D-2013' },
  { key: 'PRO-Desktop (3d)', value: 'PRODMAC' },
  { key: 'Bill of material JSON (2d)', value: 'BOMEXPORTJSON' },
  { key: 'DWF ASCII 5.5 (2d)', value: 'DWF2D-A5.5' },
  { key: 'HPGL V2 (2d)', value: 'HPGL2' },
  { key: 'DXF AUTOCAD VERSION 2004 - 2006 (3d)', value: 'DXF3D-2004' },
  { key: 'JPEG (2D View) (2d)', value: 'JPG2D' },
  { key: 'DWG AUTOCAD VERSION 2004 - 2006 (2d)', value: 'DWG2D-2004' },
  { key: 'DWG AUTOCAD VERSION 2010 - 2012 (3d)', value: 'DWG3D-2010' },
  { key: 'Bill of material XML (2d)', value: 'BOMEXPORTXML' },
  { key: 'Bricscad (via DWG) (3d)', value: 'BRICSCAD' },
  { key: 'DWG AUTOCAD VERSION 2007 - 2009 (2d)', value: 'DWG2D-2007' },
  { key: 'JPEG (3D View) (2d)', value: 'JPEGFILE' },
  { key: 'NX 1953 (3d)', value: 'UG3DNX1953' },
  { key: 'SVG (2d)', value: 'SVG' },
  { key: 'Parasolid Binary V15 (3d)', value: 'XB-90' },
  { key: 'DWF Compressed 5.5 (2d)', value: 'DWF2D-C5.5' },
  { key: 'Creo Parametric 6 (3d)', value: 'CREO6' },
  { key: 'NX 1847 (3d)', value: 'UG3DNX1847' },
  { key: 'Microstation (DGN) >=V8 (2d)', value: 'DGN2D' },
  { key: 'STL (3d)', value: 'STL' },
  { key: 'DXF AUTOCAD VERSION 2010 - 2012 (3d)', value: 'DXF3D-2010' },
  { key: 'IGES (3d)', value: 'IGES' },
  { key: 'COLLADA (3d)', value: 'COLLADA' },
  { key: 'Creo Parametric 7 (3d)', value: 'CREO7' },
  { key: 'Microstation (3D) (3d)', value: 'DWG3D_MICROSTATION' },
  { key: 'MI >=V8 (2d)', value: 'MI' },
  { key: 'Solid Edge 2022 (3d)', value: 'SE_3D_2022' },
  { key: 'DWF DWF V6, ASCII (2d)', value: 'DWF2D-A6.0' },
  { key: 'IFC4 mesh attributes (3d)', value: 'IFC4MAC' },
  { key: 'DXF AUTOCAD VERSION 2004 - 2006 (2d)', value: 'DXF2D-2004' },
  { key: 'Solid Edge 2019 (3d)', value: 'SE_3D_2019' },
  { key: 'Creo Parametric 5 (3d)', value: 'CREO5' },
  { key: 'MegaCAD SAT-V2.0 (3d)', value: 'MegaCAD' },
  { key: 'PARTjava (3d)', value: 'PARTjava' },
  { key: 'PDF Datasheet (2d)', value: 'PDFDATASHEET' },
  { key: 'Animated GIF (2d)', value: 'ANIMGIFFILE-640x480' },
  { key: 'AVEVA E3D (3d)', value: 'M3DPDMS' },
  { key: 'Creo Parametric 8 (3d)', value: 'CREO8' },
  { key: 'IFC2x3 mesh attributes (3d)', value: 'IFCMAC' },
  { key: 'MAX 3D Studio (3d)', value: '3DSTUDIOMAX' },
  { key: 'DWG AUTOCAD VERSION >= 2013 (3d)', value: 'DWG3D-2013' },
  { key: 'Capital XML (2d)', value: 'CAPITALXML' },
  { key: 'One Space Modeling >=2007 (3d)', value: 'm3d_sd' },
  { key: 'BMP (3D View) (2d)', value: 'BMPFILE' },
  { key: 'Creo Parametric 9 (3d)', value: 'CREO9' },
  { key: 'PDF 3D 7.01 (3d)', value: 'PDF' },
  { key: 'DWF Binary 5.5 (2d)', value: 'DWF2D-B5.5' },
  { key: 'Caddy++ SAT-V4.2 (3d)', value: 'Ziegler' },
  { key: 'BMP (2D View) (2d)', value: 'BMP2D' },
  { key: 'Catia (Macro)>=V5 (3d)', value: 'CATV5MAC' },
  { key: 'Parasolid Text V15 (3d)', value: 'XT-90' },
  { key: 'Inventor 2020 (3d)', value: 'AIS2020' },
  { key: 'DXF AUTOCAD VERSION 2010 - 2012 (2d)', value: 'DXF2D-2010' },
  { key: 'Inventor 2019 (3d)', value: 'AIS2019' },
  { key: 'Mechanical Desktop >=V5 (3d)', value: 'MD3DMACRO' },
  { key: 'NX >=12 (3d)', value: 'UG3DNX12' },
  { key: 'Catia >=V5 (3d)', value: 'CATIAV5' },
  { key: 'Autodesk Revit 2021 (3d)', value: 'REVIT_2021' },
  { key: 'Autodesk Revit 2024 (3d)', value: 'REVIT_2024' },
  { key: 'Creo Parametric 10 (3d)', value: 'CREO10' },
  { key: 'TIFF (2D View) (2d)', value: 'TIFF2D' },
  { key: 'NX 1926 (3d)', value: 'UG3DNX1926' },
  { key: 'Solid Edge 2023 (3d)', value: 'SE_3D_2023' },
  { key: 'NX 1980 (3d)', value: 'UG3DNX1980' },
  { key: 'Metafile 2D (PS2) V2 (2d)', value: 'META2DV2' },
  { key: 'Solid Edge 2020 (3d)', value: 'SE_3D_2020' },
  { key: 'DWG AUTOCAD VERSION >= 2013 (2d)', value: 'DWG2D-2013' },
  { key: 'Autodesk Revit Instance+Family 2022 (3d)', value: 'REVIT_INSTFAM_2022' },
  { key: 'DWF V6, UNCOMPRESSED BINARY (2d)', value: 'DWF2D-B6.0' },
  { key: 'SAT 7.0 (3d)', value: 'SAT-700' },
]

const xindiList = ['step', 'asm', 'stp', 'wges', 'wgs', 'obj', 'x_t', 'x_b']
const beList = ['stp']

const showDownload = () => {
  if (list.value.length <= 1) {
    download(list.value.map((item) => item.value))
  } else {
    open.value = true
  }
}

const loadingSets = ref(new Set())

const download = async (formats: string[], key?: string) => {
  key && loadingSets.value.add(key)
  await select.value.download(formats)
  key && loadingSets.value.delete(key)
}

const rowkeys = ref<string[]>([])
const changeSelect = (keys) => {
  if (keys.length > 3) {
    message.warn('每次最多只能下载三种格式的3D模型文件')
    return
  }
  rowkeys.value = keys
}

defineExpose({
  showDownload,
})
</script>
