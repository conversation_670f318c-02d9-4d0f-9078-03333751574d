<template>
  <div id="water" style=" pointer-events: none;">
		<slot></slot>
	</div>
</template>

<script setup>
const setWatermark = (str) => {
	const can = document.createElement('canvas');
	can.width = 250;
	can.height = 180;
	const cans = can.getContext('2d');
	cans.rotate((-20 * Math.PI) / 180);
	cans.font = '14px Vedana';
	cans.fillStyle = 'rgba(231, 84, 84, 0.50)';
	cans.textAlign = 'center';
	cans.textBaseline = 'Middle';
	cans.fillText(str, can.width / 10, can.height / 2);
  
	const div = document.createElement('div');
	div.style.pointerEvents = 'none';
	// div.style.top = '35px';
	// div.style.left = '0px';
	// div.style.position = 'fixed';
	// div.style.zIndex = '10000000';
	div.style.width = '100%';
	div.style.height = '100%';
	div.style.background = `url(${can.toDataURL('image/png')}) left top repeat`;

	document.getElementById('water').appendChild(div);
}

onMounted(() => {
  setWatermark('账号受限')
})
</script>