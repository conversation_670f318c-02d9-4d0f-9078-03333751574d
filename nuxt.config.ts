// https://nuxt.com/docs/api/configuration/nuxt-config
import { NuxtPage } from 'nuxt/schema'
import { loadEnv } from 'vite'
import compression from 'vite-plugin-compression'
import sitemap from './sitemap'
interface VITE_ENV_CONFIG {
  VITE_API_HOST: string
  VITE_PACK_ENV: string
  VITE_PACK_URL: string
  VITE_API_PREFIX: string
  VITE_MERGER_URL: string
  VITE_MALL_URL: string
  VITE_MALL_MANAGE_URL: string
  VITE_SSO: string
  VITE_API_BASE: string
  VITE_HOME: string
}

const envScript = (process.env as any).npm_lifecycle_script.split(' ')
const envName = envScript[envScript.length - 1] // 通过启动命令区分环境
const envData = loadEnv(envName, 'env') as unknown as VITE_ENV_CONFIG
if (process.env.NODE_ENV == 'production') {
  console.log('%c Line:20 🥛 envData', 'color:#14909C', envData)
}

export default defineNuxtConfig({
  devServer: {
    host: '0.0.0.0',
    port: 8000,
  },
  vue: {
    compilerOptions: {
      isCustomElement: (tag) => tag.startsWith('wx-'),
    },
  },
  typescript: {
    tsConfig: {
      compilerOptions: {
        verbatimModuleSyntax: false,
      },
    },
  },
  app: {
    baseURL: '/',
    head: {
      title: '零部件库 - 研选工场',
      charset: 'utf-8',
      htmlAttrs: {
        lang: 'zh-CN',
      },
      meta: [
        {
          name: 'referrer',
          content: 'no-referrer',
        },
        {
          name: 'viewport',
          content: 'width=1920',
        },
        {
          name: 'baidu-site-verification',
          content: 'codeva-SlpS3QXuFT',
        },
      ],
      // link: [{ rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }],
    },
  },
  build: {
    transpile: ['vue-countup-v3'],
  },
  devtools: { enabled: true },
  css: ['ant-design-vue/dist/reset.css', '@/assets/css/main.less', 'vxe-table/lib/style.css'],
  modules: [
    [
      '@unocss/nuxt',
      {
        hmrTopLevelAwait: false,
      },
    ],
    '@ant-design-vue/nuxt',
    '@pinia/nuxt',
    '@vueuse/nuxt',
    'nuxt-icons',
    '@nuxtjs/sitemap',
  ],
  // @ts-ignore
  sitemap,
  antd: {
    extractStyle: true,
  },
  imports: {
    autoImport: true,
  },
  runtimeConfig: {
    public: {
      baseUrl: envData,
    },
  },
  vite: {
    envDir: '~/env',
    resolve: {
      alias: {
        'ant-design-vue/dist': 'ant-design-vue/dist',
        'ant-design-vue/es': 'ant-design-vue/es',
        'ant-design-vue/lib': 'ant-design-vue/es',
        'ant-design-vue': 'ant-design-vue/es',
      },
    },
    vue: {
      script: {
        defineModel: true,
      },
    },
    build: {
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true,
        },
      },
      // rollupOptions: {
      //   output: {
      //     manualChunks(id) {
      //       if (id.includes('pdf')) return 'pdf'
      //       if (id.includes('ant')) return 'ant'
      //       if (id.includes('node_modules')) return 'vendor'
      //     }
      //   }
      // }
    },
    plugins: [
      compression({
        algorithm: 'gzip', // 可以使用 'brotliCompress' 启用 Brotli 压缩
        ext: '.gz',
        threshold: 10240, // 只压缩大于 10kB 的文件
        deleteOriginFile: false, // 是否删除未压缩的原文件
      }),
    ],
  },
  nitro: {
    devProxy: {
      '/mall-manage': {
        target: envData.VITE_API_BASE + '/mall-manage', // 这里是接口地址
        changeOrigin: true,
      },
      '/mall-platform': {
        target: envData.VITE_API_BASE + '/mall-platform', // 这里是接口地址
        changeOrigin: true,
      },
      '/api': {
        target: envData.VITE_API_BASE + '/api', // 这里是接口地址
        changeOrigin: true,
      },
      '/mall': {
        target: envData.VITE_API_BASE + '/mall',
        changeOrigin: true,
      },
      '/sso': {
        target: envData.VITE_SSO,
        changeOrigin: true,
      },
    },
    compressPublicAssets: true,
  },
  routeRules: {
    '/my': { redirect: '/my/info' },
  },
  hooks: {
    // 路径包含component的文件不生成路由
    'pages:extend'(pages) {
      function removePagesMatching(pattern: RegExp, pages: NuxtPage[] = []) {
        const pagesToRemove: NuxtPage[] = []
        for (const page of pages) {
          if (page.file && pattern.test(page.file)) {
            pagesToRemove.push(page)
          } else {
            removePagesMatching(pattern, page.children)
          }
        }
        for (const page of pagesToRemove) {
          pages.splice(pages.indexOf(page), 1)
        }
      }
      removePagesMatching(/.*components.*\.(vue|ts|tsx)$/, pages)
    },
  },
})
