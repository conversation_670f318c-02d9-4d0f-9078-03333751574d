.SView-message {
    /* display: none; */
    position: absolute;
    top: 16px;
    left: 50%;
    transform: translateX(-50%);
    /* margin: 0 auto; */
    padding: 8px;
    transition: all 0.3s;
    width: max-content;
    /*background-color: #fff;*/
}

.SView-message-content {
    width: fit-content;
    padding: 10px 40px;
    border: 1px solid #aeb5c0;
    border-radius: 6px;
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.3);
    color: #212121;
    background-color: #fff;
}

.SView-message-withBtn .SView-message-content {
    width: auto;
    position: relative;
    padding: 10px 60px 10px 40px;
}



.SView-message-closeBtn {
    display: inline-block;
    position: absolute;
    right: 8px;
    top: 2px;
    width: 28px;
    height: 28px;
}

    .SView-message-closeBtn:hover {
        background-color: #fff;
        opacity: .7;
    }
/*权限提示框*/
.SView-permission-message {
    width: 264px;
    height: 70px;
    background: #FFFFFF;
    border-radius: 4px;
    box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.2);
    margin: 5px 0;
    display: flex;
}

    .SView-permission-message > img {
        width: 30px;
        height: 30px;
        margin: 20px 10px;
    }

.SView-permissionMessage-content {
    width: 130px;
    line-height: 25px;
    font-size: 14px;
    color: #333333;
    line-height: 20px;
    margin-top: 15px;
}

.SView-permission-message .SView-button {
    color: #00c853;
    margin-top: 18px;
}
