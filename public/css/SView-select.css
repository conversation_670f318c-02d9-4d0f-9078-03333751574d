.SView-select {
    /* position: relative; */
    display: inline-block;
    /* padding: 3px; */
    /* line-height: 36px; */
    height: 32px;
    cursor: pointer;
    border-color: #aeb5c0;
    height: 24px;
    min-width: 50px;
}

    .SView-select:focus {
        cursor: pointer;
        outline: none;
        border-color: rgba(44, 135, 255, 0.7);
        box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(44, 135, 255, 0.7);
    }

@media screen and (min-width: 305px) and (max-width: 900px) {
    .SView-select {
        padding: 1px;
        line-height: 28px;
        font-size: 12px;
    }
}

@media screen and (max-width: 305px) {
    .SView-select {
        padding: 1px;
        line-height: 20px;
        font-size: 10px;
    }
}
/*底部菜单的选择框*/
.SView-bottomMenu .SView-select {
    margin-top: 10px;
}

.SView-choices-select {
    width: 100%;
    height: 36px;
    position: relative;
}

.SView-choices-input {
    width: 100%;
    height: 100%;
    border-radius: 4px;
    border: 1px solid #DFDFDF;
    box-sizing: border-box;
    line-height: 34px;
    color: #333333;
    font-size: 14px;
    padding-left: 5px;
    cursor: pointer;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

    .SView-choices-input::before {
        content: url(../images/icon/selectIcon.png);
        float: right;
        width: 20px;
        height: 20px;
        line-height: 40px;
        background-size: 100% 100%;
        margin-right: 5px;
    }

.SView-choices-option {
    width: 100%;
    height: max-content;
    position: absolute;
    left: 0px;
    top: 36px;
    box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.2);
    border-radius: 4px;
    background: #FFFFFF;
    display: none;
}

    .SView-choices-option li {
        width: 100%;
        height: 30px;
        line-height: 30px;
        font-size: 14px;
        color: #000000;
        list-style: none;
        overflow: hidden;
    }

        .SView-choices-option li:hover {
            background-color: #E5EAF4;
        }

        .SView-choices-option li label {
            line-height: 30px;
            font-size: 14px;
            color: #333333;
            list-style: none;
        }

        .SView-choices-option li img {
            width: 15px;
            height: 15px;
            margin-top: 5px;
        }

.SView-choices-option-leftText li label {
    margin-left: 5px;
    margin-left: 5px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: block;
}

.SView-choices-option-leftText li img {
    margin-right: 5px;
    float: right;
    margin-left: 5px;
}

.SView-choices-option-leftImg li label {
    margin-right: 5px;
    float: right;
}

.SView-choices-option-leftImg li img {
    margin-left: 5px;
}

.SView-choices-option-active {
    background: #E5EAF4;
}
/*批注选择框*/
.annotation_select {
    width: 80px;
    height: 36px;
    border-radius: 4px;
    border: 1px solid #DFDFDF;
}
