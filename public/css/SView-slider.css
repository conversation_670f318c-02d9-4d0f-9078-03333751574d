.SView-slider {
    display: inline-block;
    position: relative;
    height: 42px;
    line-height: 36px;
    box-sizing: border-box;
    padding: 3px;
    text-align: center;
    border-radius: 4px;
}

    .SView-slider input[type=range] {
        width: 100px;
        -webkit-appearance: none;
        cursor: pointer;
        background-color: #e0e0e0;
        border-radius: 4px;
        background: -webkit-linear-gradient(#2c87ff, #2c87ff) no-repeat white;
        background-size: 50% 100%;
        outline: none;
        /*border: 1px solid #aeb5c0;*/
        /*box-sizing: border-box;*/
    }

        .SView-slider input[type=range]::-webkit-slider-runnable-track {
            -webkit-appearance: none;
            height: 8px;
            /*border: 1px solid #aeb5c0;*/
            box-sizing: border-box;
            border-radius: 4px;
            /* transition: 0.2s; */
        }

            .SView-slider input[type=range]::-webkit-slider-runnable-track:hover {
                border-color: rgba(44, 135, 255, 0.7);
                border-radius: 6px;
                box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(44, 135, 255, 0.7);
            }

        .SView-slider input[type=range]::-webkit-slider-thumb {
            -webkit-appearance: none;
            position: relative;
            height: 16px;
            width: 16px;
            border-radius: 50%;
            border: 1px solid #aeb5c0;
            margin-top: -4px;
            background-color: #fff;
            cursor: grab;
            /*box-sizing: border-box;*/
            /* transition: 0.2s; */
        }

            .SView-slider input[type=range]::-webkit-slider-thumb:active {
                cursor: grabbing;
                border: 4px solid #2c87ff;
            }

@media screen and (min-width: 305px) and (max-width: 900px) {
    .SView-slider {
        height: 32px;
        line-height: 26px;
    }

        .SView-slider input[type=range] {
            width: 80px;
        }
}

@media screen and (max-width: 305px) {
    .SView-slider {
        height: 26px;
        line-height: 20px;
    }

        .SView-slider input[type=range] {
            width: 60px;
        }
}
