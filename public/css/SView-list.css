* {
    margin: 0;
    padding: 0
}

.SView-animationList-hidden {
    width: 40px !important;
    height: 40px !important;
    position: absolute;
    left: 0px !important;
    top: 50% !important;
    margin-top: -12px;
    border: none !important;
}

    .SView-animationList-hidden .SView-animationListContent {
        display: none !important;
    }

.SView-animationList-icon {
    width: 24px !important;
    height: 24px !important;
    position: absolute;
    left: 0px;
    top: 50%;
    margin-top: -12px;
}

.SView-animationList-iconOpen {
    background: url(../images/icon/left-close.png) no-repeat;
    background-size: 24px 24px;
}

.SView-animationList-iconClose {
    background: url(../images/icon/left-open.png) no-repeat;
    background-size: 24px 24px;
}

#SView-animationList {
    width: 100px;
    height: 100%;
    position: absolute;
    left: 0px;
    top: 0px;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 2;
}

@media (min-width:850px ) {
    #SView-animationList {
        width: 250px;
        height: 100%;
        position: absolute;
        left: 0px;
        top: 0px;
        background-color: rgba(255, 255, 255, 0.8);
        z-index: 2;
    }
}



.SView-animationList img {
    border: 0;
    vertical-align: middle
}

.SView-animationList-content {
    width: max-content;
    height: max-content;
    text-align: left;
    border: 1px solid #ccc;
    box-sizing: border-box;
    overflow-y: auto;
}

.SView-animationListContent {
    margin: 40px 0;
}

.SView-animationList-item {
    padding-left: 5px;
    /*height: 40px;*/
    line-height: 30px;
}

.SView-animationList .SView-animationList-subItem {
    padding-left: 20px;
    display: none
}

.SView-animationList .SView-animationList-show {
    display: block
}

.SView-animationList-item {
    position: relative
}

    .SView-animationList-item .SView-animationList-dropdownList {
        position: relative
    }

    .SView-animationList-item .node-name {
        display: inline-block;
        vertical-align: middle;
        cursor: pointer;
        font-size: 16px;
        color: #000000;
        margin-left: 5px;
    }

    .SView-animationList-item .SView-animationList-number {
        margin-left: 6px;
        color: #999;
        font-size: 12px;
        display: inline-block;
        vertical-align: middle;
    }

.SView-animationList-item {
    position: relative;
    width: max-content;
    padding-right: 5px;
}

    .SView-animationList-item .SView-animationList-close {
        margin: auto 0;
        width: 16px;
        height: 16px;
        background: url("../images/icon-close.svg") no-repeat;
        background-size: 100%;
        display: inline-block;
        cursor: pointer
    }

.animationList-open {
    width: 16px;
    height: 16px;
    display: inline-block;
    vertical-align: middle;
    background-repeat: no-repeat;
    background-image: url(../images/icon/listOpen.png);
    background-size: 100% 100%;
}

.animationList-close {
    width: 16px;
    height: 16px;
    display: inline-block;
    vertical-align: middle;
    background-repeat: no-repeat;
    background-image: url(../images/icon/listClose.png);
    background-size: 100% 100%;
}

.animationList-node-active {
    background-color: #bae7ff;
    padding-bottom: 3px;
}
/*标题-labelItem*/
sectionShapeList {
    width: 96%;
    margin: 10px auto;
}

#sectionShapeList .SView-labelItem {
    cursor: pointer;
}

.SView-labelItem-List li:hover {
    background-color: #DFDFDF;
}

#sectionShapeList li label {
    color: #333333;
    cursor: pointer;
    font-size: 14px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 100%;
    display: block;
}

.SView-labelItem-List {
    width: 100%;
    height: auto;
    background-color: #FFF;
    margin-top: 5px;
}

    .SView-labelItem-List li {
        box-sizing: border-box;
        min-height: 40px;
    }

        .SView-labelItem-List li > div {
            line-height: 20px;
            padding: 10px 14px;
        }


.SView-list-item-active {
    background-color: #DFDFDF;
}

.SView-labelItem > label {
    width: 80px;
    text-align: right;
}

#shapeset-dialog .SView-labelItem > label {
    text-align: left !important;
    margin-left: 0px !important;
}

#shapeset-dialog .SView-labelItem {
    padding-left: 16px !important;
}

#section-shapeset-dialog .SView-labelItem > label {
    text-align: left !important;
    margin-left: 0px !important;
    align-self: center;
    line-height: 21px;
}

#section-shapeset-dialog .SView-labelItem {
    padding-left: 14px !important;
}

#shapeset-dialog .SView-labelItem input {
    width: 100% !important;
    margin: 0px !important;
    height: 36px !important;
}

.SView-labelItem > span {
    width: calc(100% - 106px)
}

.SViewlabelItem > label {
    color: rgba(0,0,0,.85);
    font-size: 14px;
    line-height: 32px;
    float: left;
    width: 60%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
}

.SViewlabelItem > span {
    /*width: 60%;*/
    height: 32px;
    display: inline-block;
    line-height: 32px;
    color: #000000;
    font-size: 14px;
    text-align: right;
    float: right;
    padding: 0 10px;
    display: flex;
    justify-content: space-around;
}

    .SViewlabelItem > span div {
        user-select: none;
        display: inline-block;
        position: relative;
        cursor: pointer;
        width: 25px;
        height: 25px;
        box-sizing: border-box;
        padding: 0px !important;
        text-align: center;
        margin: 3px 10px !important;
        border-radius: 4px;
    }

.SView-labelItem-List li > h2 {
    font-size: 18px;
    font-weight: 600;
    color: #333333;
    margin: 15px 0;
}

.SView-labelItem-List li > h3 {
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #333333;
    margin: 10px 0;
}
/*图片-imgItem*/
.SView-imgItem-List {
    width: 100%;
    height: auto;
    background-color: #FFF;
}

    .SView-imgItem-List li {
        box-sizing: border-box;
        height: 60px;
        background-color: #bae7ff;
        border: 1px solid #e5e5e5;
        width: 100%;
        margin: 5px 0;
    }

.SView-imgItem {
    width: 100%;
    height: 100%;
}

    .SView-imgItem > img {
        width: 50px;
        height: 50px;
        border-radius: 5px;
        margin: 5px;
        float: left;
    }

    .SView-imgItem > div {
        float: left;
        width: calc(100% - 60px);
    }

        .SView-imgItem > div > p {
            color: #999;
            font-size: 14px;
            height: 30px;
            line-height: 30px;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
            word-break: keep-all;
            white-space: nowrap;
        }
/*聊天List*/
.SView-chat-list {
    width: 100%;
    height: auto;
    background-color: #FFF;
    max-height: 500px;
    overflow: auto;
}

    .SView-chat-list li {
        margin: 10px;
        min-height: 50px;
        position: relative;
        padding-top: 25px;
    }

.SView-rightChatItem .SView-chat-bubble {
    min-width: 50px;
    min-height: 22px;
    background-color: rgb(208,227,255);
    color: #000000;
    font-size: 14px;
    padding: 10px;
    line-height: 22px;
    border-radius: 5px;
    margin-right: 40px;
}

.SView-rightChatItem > span {
    height: 20px;
    line-height: 20px;
    font-size: 12px;
    position: absolute;
    right: 40px;
    top: 0px;
    color: #C0C4CC;
}

.SView-leftChatItem .SView-time {
    margin-left: 10px;
}

.SView-rightChatItem img {
    width: 30px;
    height: 30px;
    border-radius: 15px;
    position: absolute;
    right: 0px;
    top: 0px;
}

.SView-leftChatItem .SView-chat-bubble {
    min-width: 50px;
    min-height: 22px;
    background-color: rgb(234,236,237);
    color: #000000;
    font-size: 14px;
    padding: 10px;
    line-height: 22px;
    border-radius: 5px;
    margin-left: 40px;
}

.SView-leftChatItem > span {
    height: 20px;
    line-height: 20px;
    font-size: 12px;
    position: absolute;
    left: 40px;
    top: 0px;
    color: #C0C4CC;
    display: block;
}

.SView-leftChatItem img {
    width: 30px;
    height: 30px;
    border-radius: 15px;
    position: absolute;
    left: 0px;
    top: 0px;
}
/*设置列表*/
.SView-setting-List {
    height: auto;
    background-color: #FFF;
    padding-top: 10px;
}

.SView-setting-List-Border {
    height: auto;
    background-color: #FFF;
    padding-top: 10px;
    border-radius: 6px;
    border: 1px solid #e5e5e5;
    margin: 10px;
    padding: 10px 0;
}

.SView-setting-List {
    display: flex;
    flex-wrap: wrap;
}

    .SView-setting-List li {
        padding: 8px 24px 5px 24px;
        flex-basis: 100%;
    }

#systemDebug .SView-show {
    display: flex !important;
}

.SView-setting-List .SView-listItem-content {
    flex-basis: auto !important;
    padding-right: 0px;
}

.SView-setting-List li > div {
    padding-left: 10px;
}

.SView-setting-List li .SView-labelItem > label {
    font-size: 14px;
    font-weight: 400;
    color: #606060;
}

.SView-setting-List li > h2 {
    font-size: 18px;
    font-weight: 600;
    color: #333333;
}

.SView-setting-List li > h3 {
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    font-size: 14px;
    color: #333333;
}

.SView-setting-List .SView-labelItem > span {
    display: block;
}

.SView-setting-List .SView-select {
    width: 100%;
    max-width: 493px;
    height: 36px;
    border-radius: 4px;
    border: 1px solid #DFDFDF;
    margin-top: 10px;
    padding-left: 10px;
}

.SView-setting-List .SView-checkbox {
    width: max-content;
    min-width: 170px;
}

    .SView-setting-List .SView-checkbox label {
        margin: 0px !important;
    }

@media screen and (max-width: 550px) {
    .SView-setting-List .SView-checkbox {
        width: 170px !important;
        min-width: 170px !important;
    }
}

.SView-setting-List-mid .SView-checkbox {
    min-width: 130px;
}

.SView-setting-List-Min .SView-checkbox {
    min-width: 80px;
}

.SView-setting-List-max .SView-checkbox {
    min-width: 305px;
}

.SView-setting-List .SView-color-item > span {
    display: inline-block;
    padding-right: 10px;
    display: flex;
}

    .SView-setting-List .SView-color-item > span > span {
        padding-right: 10px;
    }
/*item*/
.SView-labelItem.SView-flex-row {
    margin-top: 10px;
    height: 36px;
}

    .SView-labelItem.SView-flex-row > label {
        margin: 0 10px;
        line-height: 36px;
    }
/*版本说明*/
.SView-VersionItem .SView-version-title {
    color: #333333;
    font-size: 14px;
    line-height: 20px;
    font-weight: bold;
    float: left;
}

.SView-VersionItem .SView-version-time {
    color: #999999;
    font-size: 14px;
    line-height: 40px;
    float: right;
}

.SView-VersionItem .SView-version-content {
    color: #666666;
    font-size: 14px;
    line-height: 25px;
    padding: 10px;
    clear: both;
}

.SView-permissionMessageList {
    width: 300px;
    height: 240px;
    position: absolute;
    left: 20px;
    bottom: 20px;
    overflow: hidden;
}
/*关于*/
.SView-about {
    text-align: center;
}

    .SView-about img {
        height: 107px;
        width: 107px;
        background-size: 107px 107px;
        margin: 0px auto;
    }

    .SView-about h3 {
        font-size: 22px;
        line-height: 35px;
        font-weight: 300;
    }

    .SView-about p {
        margin-top: 20px;
        font-size: 20px;
        line-height: 35px;
        color: #505050;
    }

.footer {
    text-align: center;
    margin-top: 30px;
    color: #929292;
    font-size: 11px;
    margin-bottom: 15px;
}
/*许可*/
.SView-license-bodyheader {
    width: 90%;
    text-align: center;
    margin: 25px auto;
    padding: 10px;
    background: #4A545D; /*285ADC*/
    /*边框*/
    border: solid 1px rgba(102, 146, 191, 0.68);
    /*边角弧度*/
    border-radius: 10px;
    /*阴影*/
    box-shadow: 7px 15px 30px #285a63;
    -moz-box-shadow: 2px 2px 5px #333333;
    -webkit-box-shadow: 2px 2px 5px #333333;
    box-shadow: 7px 15px 30px #285a63;
    /*延迟过度*/
    -moz-box-sizing: border-box; /* Firefox */
    -webkit-box-sizing: border-box;
    -o-box-sizing: border-box; /* Opera */
    transition: all 0.3s linear; /*0.3s过渡时间*/
    -moz-transition: all 0.3s linear; /* Firefox 4 */
    -webkit-transition: all 0.3s linear; /* Safari 和 Chrome */ /*边框*/
}


.SView-license-headername {
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    text-align: left;
    margin-bottom: 5px;
}

.SView-license-expiretime {
    color: #fff;
    text-align: left;
    font-size: 12px;
    margin-bottom: 15px;
}

.SView-license-licenseid {
    color: #fff;
    text-align: left;
}

.SView-license-usermessages {
    text-align: center;
    color: #fff;
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 5px;
}

.SView-license-useremail {
    text-align: center;
    color: #fff;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 25px;
}

.SView-license-licenseCreateDate, .SView-license-licenseCreator {
    width: auto;
    height: 24px;
    text-indent: 30%;
    font-size: 13px;
    color: #929292;
}
