.SView-treeDialog {
    min-width: 240px;
    width: max-content;
    height: 600px;
    position: absolute;
    float: left;
    left: 10px;
    top: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    background: #E5EAF0;
    max-width: 90%;
}
/* 标题部分 */
.SView-treeDialog-title {
    width: 100%;
    height: 48px;
    box-sizing: border-box;
    padding: 8px;
}

    .SView-treeDialog-title .SView-button-iconBtn {
        width: 36px;
        height: 36px;
        margin: 0;
    }

    .SView-treeDialog-title .SView-treeDialog-closeBtn {
        position: absolute;
        top: 5px;
        right: 8px;
    }

        .SView-treeDialog-title .SView-treeDialog-closeBtn .SView-button-iconBtn {
            width: 28px;
            height: 28px;
        }

            .SView-treeDialog-title .SView-treeDialog-closeBtn .SView-button-iconBtn:hover {
                opacity: 0.7;
                background-color: transparent;
            }
/* 搜索部分 */
.SView-treeDialog-search {
    width: 100%;
    text-align: left;
}

.SView-treeDialog-content {
    overflow: auto;
    width: 100%;
    height: calc(100% - 88px);
}

    .SView-treeDialog-content .SView-button-iconLR {
        width: calc(100% - 17px);
        margin-left: 8px;
    }

        .SView-treeDialog-content .SView-button-iconLR:hover {
            background-color: #2c87ff !important;
        }

    .SView-treeDialog-content #SView-assemblyTree {
        height: 100%;
        overflow: auto;
        resize: horizontal;
        min-width: 242px;
        position: relative;
    }

#SView-viewList {
    height: 100%;
    overflow: hidden;
    width: 242px;
    position: relative;
}

.SView-treeDialog-search .SView-complexInput {
    width: calc(100% - 58px);
}
/* 视图列表 */
.SView-view-list {
    width: 200px;
}

.SView-pull-left-div {
    max-width: 100%;
    margin: auto;
    height: calc(100% - 50px);
    overflow: auto;
    overflow-x: hidden;
}

.SView-pull-left-viewList {
    width: calc(100% - 17px);
    margin: auto;
}

.SView-pull-left-item {
    width: calc(100% - 6px);
    height: auto;
    margin: 10px auto;
    border-radius: 5px;
    padding: 6px;
    cursor: pointer;
    background-color: #FFFFFF;
}

    .SView-pull-left-item:hover {
        background-color: #e6f7ff;
    }

.SView-pull-left-item-active {
    background-color: #e6f7ff;
}

.SView-view-item-header {
    width: 100%;
    min-height: 25px;
    justify-content: space-between;
    display: flex;
}

.SView-view-name {
    font-size: 14px;
    color: #000000;
    line-height: 25px;
    margin-right: 5px;
    width: calc(100% - 44px);
}

    .SView-view-name > span {
        margin-right: 5px;
        word-break: break-all;
    }

.SView-view-item-header img {
    width: 17px;
    height: 17px;
    vertical-align: middle;
    margin-right: 5px;
}

.SView-edit-view-name {
    display: flex;
}

.SView-view-item-center {
    width: 100%;
    height: 100px;
    text-align: center;
    position: relative;
}

.SView-view_img {
    border-width: 1px;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: 5px;
    transform: translate(-50%,-50%);
    max-height: 100%;
    max-width: 100%;
}

.SView-view-item-bottom {
    display: flex;
}

.SView-view_time {
    margin-top: 5px;
    text-align: right;
    width: 75%;
}

.SView-view_creator {
    width: 25%;
    margin-top: 5px;
}

.SView-view_creator, .SView-view_time {
    font-size: 13px;
    color: #C0C4CC;
}

.SView-tree-content {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    min-height: 50px;
    height: 100%;
}

.SView-tree-scroll {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    z-index: -1;
}

.SView-tree-rightClickMenu {
    position: absolute !important;
}

#searchInput {
    background: none !important;
}
