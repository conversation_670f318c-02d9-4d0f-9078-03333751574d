.SView-switch {
    /* margin: -1px; */
    display: inline-block;
    height: 32px;
    position: relative;
    width: 70px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.SView-switch-checkbox {
    display: none;
}

.SView-switch-label {
    display: block;
    overflow: hidden;
    cursor: pointer;
    border: 1px solid #aeb5c0;
    border-radius: 20px;
    height: 30px;
}

.SView-switch-inner {
    display: block;
    width: 200% !important;
    margin-left: -100%;
    transition: margin 0.3s ease-in 0s;
}

    .SView-switch-inner::before,
    .SView-switch-inner::after {
        display: block;
        float: right;
        width: 50%;
        height: 30px;
        padding: 0;
        line-height: 30px;
        font-size: 14px;
        color: #fff;
        font-weight: bold;
        box-sizing: border-box;
    }

    .SView-switch-inner::after {
        text-align: left !important;
        content: attr(data-on);
        padding-left: 10px;
        background-color: #2c87ff;
        color: #fff;
    }

    .SView-switch-inner::before {
        content: attr(data-off);
        padding-right: 10px;
        background-color: #e0e0e0;
        color: #717171;
        text-align: right;
    }

.SView-switch-button {
    position: absolute;
    display: block;
    width: 22px !important;
    height: 22px !important;
    margin: 4px;
    background-color: #fff;
    top: 0;
    bottom: 0;
    right: 36px;
    border: 1px solid #aeb5c0;
    border-radius: 50%;
    transition: all 0.3s ease-in 0s;
}

.SView-switch-checkbox:checked + .SView-switch-label .SView-switch-inner {
    margin-left: 0;
}

.SView-switch-checkbox:checked + .SView-switch-label .SView-switch-button {
    right: 0;
}

.SView-switch:hover {
    border-color: rgba(44, 135, 255, 0.7);
    border-radius: 20px;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(44, 135, 255, 0.7);
}

@media screen and (min-width: 305px) and (max-width: 900px) {
    .SView-switch {
        width: 60px;
    }

    .SView-switch-inner::before,
    .SView-switch-inner::after {
        height: 25px;
        line-height: 25px;
        font-size: 14px;
        color: #fff;
        font-weight: bold;
        box-sizing: border-box;
        padding: 0;
    }

    .SView-switch-inner::after {
        content: attr(data-on);
        padding-left: 5px;
        background-color: #2c87ff;
        color: #fff;
    }

    .SView-switch-inner::before {
        content: attr(data-off);
        padding-right: 5px;
        background-color: #e0e0e0;
        color: #717171;
        text-align: right;
    }

    .SView-switch-button {
        position: absolute;
        display: block;
        width: 18.5px;
        height: 18.5px;
        margin: 3px;
        background-color: #fff;
        top: 0;
        bottom: 0;
        right: 33px;
        border: 1px solid #aeb5c0;
        border-radius: 50%;
        transition: all 0.3s ease-in 0s;
    }
}

@media screen and (max-width: 305px) {
    .SView-switch {
        width: 50px;
    }

    .SView-switch-inner::before,
    .SView-switch-inner::after {
        height: 20px;
        line-height: 20px;
        font-size: 12px;
        color: #fff;
        font-weight: bold;
        box-sizing: border-box;
        padding: 0;
    }

    .SView-switch-inner::after {
        content: attr(data-on);
        padding-left: 5px;
        background-color: #2c87ff;
        color: #fff;
    }

    .SView-switch-inner::before {
        content: attr(data-off);
        padding-right: 5px;
        background-color: #e0e0e0;
        color: #717171;
        text-align: right;
    }

    .SView-switch-button {
        position: absolute;
        display: block;
        width: 15px;
        height: 15px;
        margin: 2px;
        background-color: #fff;
        top: 0;
        bottom: 0;
        right: 27px;
        border: 1px solid #aeb5c0;
        border-radius: 50%;
        transition: all 0.3s ease-in 0s;
    }
}
