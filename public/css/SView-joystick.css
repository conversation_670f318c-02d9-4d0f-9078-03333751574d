/*topAndDown*/
.TopAndDownFront {
    position: absolute;
    top: calc(50% - 15px);
    height: 31px;
    margin-left: 1px;
}

.TopAndDownBack {
    height: 160px;
}

    .TopAndDownBack img {
        width: 32px;
        height: 160px;
    }

.joystick-topanddown {
    left: calc(100% - 200px);
    position: absolute;
    bottom: 128px;
    text-align: left;
    vertical-align: bottom;
    margin: 0;
    opacity: 0.8;
}

@media ( max-width : 767px) {
    .SView-virtualRocker {
        position: absolute;
        left: 60px;
        bottom: 100px;
        text-align: left;
        vertical-align: bottom;
        margin: 0;
        padding: 5px 10px;
        -webkit-touch-callout: none; /* iOS Safari */
        -webkit-user-select: none; /* Safari */
    }

    .joystick-topanddown {
        left: calc(100% - 100px);
        position: absolute;
        bottom: 28px;
        text-align: left;
        vertical-align: bottom;
        margin: 0;
        padding: 5px 10px;
        opacity: 0.8;
    }
}

@media ( min-width : 768px) {

    .SView-virtualRocker {
        position: absolute;
        left: 100px;
        bottom: 200px;
        text-align: left;
        vertical-align: bottom;
        margin: 0;
        padding: 5px 10px;
        -webkit-touch-callout: none; /* iOS Safari */
        -webkit-user-select: none; /* Safari */
    }

    .joystick-topanddown {
        left: calc(100% - 200px);
        position: absolute;
        bottom: 128px;
        text-align: left;
        vertical-align: bottom;
        margin: 0;
        padding: 0px 10px;
        opacity: 0.8;
    }
}

/*左右摇杆*/
.VirtualRockerContent {
    position: absolute;
    opacity: 0.8;
    display: block;
    z-index: 999;
    transition: opacity 250ms ease 0s;
    top: 50%;
    left: 50%;
}

.VirtualRockerBack {
    position: absolute;
    display: block;
    width: 150px;
    height: 150px;
    margin-left: -75px;
    margin-top: -75px;
    opacity: 1;
    border-radius: 50%;
}

    .VirtualRockerBack img {
        width: 100%;
        height: 100%;
        position: absolute;
        vertical-align: middle;
    }

.VirtualRockerFront {
    width: 75px;
    height: 75px;
    position: absolute;
    display: block;
    margin-left: -22px;
    margin-top: -28.5px;
    opacity: 0.5;
    border-radius: 50%;
    user-select: none;
    transition: none 0s ease 0s;
    left: 0px;
    top: 0px;
}

    .VirtualRockerFront img {
        width: 60%;
        height: 60%;
        position: absolute;
        vertical-align: middle;
    }
