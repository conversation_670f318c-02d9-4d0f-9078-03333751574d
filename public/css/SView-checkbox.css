.SView-checkbox input[type=checkbox]::before {
    content: '';
    display: block;
    width: 14px;
    height: 14px;
    background: url('../images/duigou.png') no-repeat;
    background-color: #fff;
    background-size: 100%;
}

.SView-checkbox input[type=checkbox] {
    -webkit-appearance: none;
    cursor: pointer;
    width: 16px;
    height: 16px;
    margin: 0 5px 2.5px 5px;
    vertical-align: middle;
    border: 1px solid #aeb5c0;
    border-radius: 2px;
    background-color: #fff;
}

    .SView-checkbox input[type=checkbox]:hover {
        border-color: rgba(44, 135, 255, 0.7);
        box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 4px rgba(44, 135, 255, 0.7);
    }

    .SView-checkbox input[type=checkbox]:checked::before {
        background-color: #2c87ff;
    }

    .SView-checkbox input[type=checkbox]:checked:hover {
        opacity: .8;
    }

.SView-checkbox label {
    font-size: 14px;
    margin: 0 5px;
}

.SView-checkbox p {
    font-size: 12px;
    font-weight: 400;
    color: #999999;
    margin-left: 31px;
}

@media screen and (min-width: 305px) and (max-width: 900px) {
    .SView-checkbox input[type=checkbox] {
        width: 14px;
        height: 14px;
    }

        .SView-checkbox input[type=checkbox]::before {
            width: 12px;
            height: 12px;
        }
}

@media screen and (max-width: 305px) {
    .SView-checkbox input[type=checkbox] {
        width: 12px;
        height: 12px;
    }

        .SView-checkbox input[type=checkbox]::before {
            width: 10px;
            height: 10px;
        }
}
