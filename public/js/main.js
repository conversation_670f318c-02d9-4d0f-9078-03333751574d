"use strict"

window.onload = function () {

    window.language = M3D.Utility.CookieHelper.
        getCookie(M3D.Config.language) === "1" ? SView.UIManager.languageConstants.EN : SView.UIManager.languageConstants.CN;
    //初始化多语言
    initView();
    //初始化页面

    sview0 = new Main("sview_1");
    let uiManager = sview0.sviewFrame.getUIManager();
    uiManager.load(RightMenu);
    uiManager.load(RightClickMenu);
    uiManager.load(RightClickSelectMenu);
    uiManager.load(RightClickSelectMeasureMenu);
    uiManager.load(RightClickSelectAnnotateMenu);
	uiManager.load(RightClickSelectRingMenu);
    rightClickMenu(availableRightMenuClick);
    let configManager = sview0.sviewFrame.getConfigManager();
	
    let basePath = window.location.href.slice(0,window.location.href.lastIndexOf("/")+1);

	var cookie = document.cookie;
        
	var cookieArray = cookie.split(";");
	var path;
	var isArray;
	var isSvlx;
	for (var i = 0; i < cookieArray.length; i++) {
		let keyValue = cookieArray[i].split("=");
		keyValue[0] = keyValue[0].replace(/\s*/g, "");
		if (keyValue[0] =="path") {
			path = keyValue[1];
		}
		if (keyValue[0] == "isArray") {
			isArray = keyValue[1];
		}
		if(keyValue[0] == "isSvlx"){
			isSvlx= keyValue[1];
		}
	}


    configManager.setCommandConfigPath(basePath+"config/commandParameters.json");
    M3D.Config.Parameters.getInstance().setParameter(M3D.Config.licencePath, basePath+"lic");
    M3D.Config.Parameters.getInstance().setParameter(M3D.Config.aniToolFileUrl, basePath);
    configManager.loadCommandConfigs().then((ret) => {
        let commandsManager = sview0.sviewFrame.getCommandsManager();
        let openCommand = null;
        if (ret) {
            openCommand = commandsManager.getCommand(SView.Commands.CommandNames.OPENSAMPLEFILE);
        } else {
            //没有配置文件的情况
            let array = new Map();
            array.set("fullClassName", "SView.Commands.OpenCommand");
            openCommand = commandsManager.getCommand(SView.Commands.CommandNames.OPENSAMPLEFILE, array);
        }
		if (isArray == "false") {
			openCommand.path = path;
		}else{
			let paths = path.split(",");
			for(let i=0;i<paths.length;i++){
				openCommand.addPath(paths[i]);
			}
		}

        commandsManager.execute(SView.Commands.CommandNames.OPENSAMPLEFILE);
    });
    //点击其他位置隐藏
    document.documentElement.onclick = function (e) {
        sview0.sviewFrame.getUIManager().hideLabel("treeRightClickMenu");
        //右键菜单隐藏
        var rm1;
        var rm2;
        var rm3;
        var rm4;
		var rm5;
        rm1 = document.getElementById(RightClickSelectMenu.RightClickMenu.id);
        rm2 = document.getElementById(RightClickMenu.RightClickMenu.id);
        rm3 = document.getElementById(RightClickSelectAnnotateMenu.RightClickMenu.id);
        rm4 = document.getElementById(RightClickSelectMeasureMenu.RightClickMenu.id);
		rm5 = document.getElementById(RightClickSelectRingMenu.RightClickMenu.id);
        if (rm1 && rm1.style) {
            rm1.style.display = "none";
        }
        if (rm2 && rm2.style) {
            rm2.style.display = "none";
        }
        if (rm3 && rm3.style) {
            rm3.style.display = "none";
        }
        if (rm4 && rm4.style) {
            rm4.style.display = "none";
        }
		if (rm5 && rm5.style) {
            rm5.style.display = "none";
        }
    }

}
//右键菜单是否可用
let availableRightMenuClick = true;
/**
 * 修改右键菜单是否可用
 * @param {any} enable
 */
function changeRightMenuClickable(enable) {
    availableRightMenuClick = enable;
    rightClickMenu(availableRightMenuClick);
}

//初始化各个控件库
function initView() {
    initRightMenu();
    initRightClickMenu();
    window.sviewFrameDiv = document.getElementById("sview_1");
}