!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e=e||self).TRTC=t()}(this,function(){var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function t(e,t){return e(t={exports:{}},t.exports),t.exports}var n=function(e){return e&&e.Math==Math&&e},r=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof e&&e)||Function("return this")(),i=function(e){try{return!!e()}catch(t){return!0}},o=!i(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}),a={}.propertyIsEnumerable,s=Object.getOwnPropertyDescriptor,c={f:s&&!a.call({1:2},1)?function(e){var t=s(this,e);return!!t&&t.enumerable}:a},u=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},d={}.toString,l=function(e){return d.call(e).slice(8,-1)},p="".split,f=i(function(){return!Object("z").propertyIsEnumerable(0)})?function(e){return"String"==l(e)?p.call(e,""):Object(e)}:Object,h=function(e){if(null==e)throw TypeError("Can't call method on "+e);return e},m=function(e){return f(h(e))},v=function(e){return"object"==typeof e?null!==e:"function"==typeof e},g=function(e,t){if(!v(e))return e;var n,r;if(t&&"function"==typeof(n=e.toString)&&!v(r=n.call(e)))return r;if("function"==typeof(n=e.valueOf)&&!v(r=n.call(e)))return r;if(!t&&"function"==typeof(n=e.toString)&&!v(r=n.call(e)))return r;throw TypeError("Can't convert object to primitive value")},_={}.hasOwnProperty,y=function(e,t){return _.call(e,t)},S=r.document,b=v(S)&&v(S.createElement),k=function(e){return b?S.createElement(e):{}},w=!o&&!i(function(){return 7!=Object.defineProperty(k("div"),"a",{get:function(){return 7}}).a}),T=Object.getOwnPropertyDescriptor,R={f:o?T:function(e,t){if(e=m(e),t=g(t,!0),w)try{return T(e,t)}catch(n){}if(y(e,t))return u(!c.f.call(e,t),e[t])}},C=function(e){if(!v(e))throw TypeError(String(e)+" is not an object");return e},E=Object.defineProperty,I={f:o?E:function(e,t,n){if(C(e),t=g(t,!0),C(n),w)try{return E(e,t,n)}catch(r){}if("get"in n||"set"in n)throw TypeError("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},x=o?function(e,t,n){return I.f(e,t,u(1,n))}:function(e,t,n){return e[t]=n,e},P=function(e,t){try{x(r,e,t)}catch(n){r[e]=t}return t},A=r["__core-js_shared__"]||P("__core-js_shared__",{}),O=Function.toString;"function"!=typeof A.inspectSource&&(A.inspectSource=function(e){return O.call(e)});var D,N,L,M=A.inspectSource,j=r.WeakMap,U="function"==typeof j&&/native code/.test(M(j)),V=t(function(e){(e.exports=function(e,t){return A[e]||(A[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.6.1",mode:"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})}),F=0,G=Math.random(),W=function(e){return"Symbol("+String(void 0===e?"":e)+")_"+(++F+G).toString(36)},B=V("keys"),H=function(e){return B[e]||(B[e]=W(e))},z={},J=r.WeakMap;if(U){var q=new J,Q=q.get,K=q.has,$=q.set;D=function(e,t){return $.call(q,e,t),t},N=function(e){return Q.call(q,e)||{}},L=function(e){return K.call(q,e)}}else{var Y=H("state");z[Y]=!0,D=function(e,t){return x(e,Y,t),t},N=function(e){return y(e,Y)?e[Y]:{}},L=function(e){return y(e,Y)}}var X,Z,ee={set:D,get:N,has:L,enforce:function(e){return L(e)?N(e):D(e,{})},getterFor:function(e){return function(t){var n;if(!v(t)||(n=N(t)).type!==e)throw TypeError("Incompatible receiver, "+e+" required");return n}}},te=t(function(e){var t=ee.get,n=ee.enforce,i=String(String).split("String");(e.exports=function(e,t,o,a){var s=!!a&&!!a.unsafe,c=!!a&&!!a.enumerable,u=!!a&&!!a.noTargetGet;"function"==typeof o&&("string"!=typeof t||y(o,"name")||x(o,"name",t),n(o).source=i.join("string"==typeof t?t:"")),e!==r?(s?!u&&e[t]&&(c=!0):delete e[t],c?e[t]=o:x(e,t,o)):c?e[t]=o:P(t,o)})(Function.prototype,"toString",function(){return"function"==typeof this&&t(this).source||M(this)})}),ne=r,re=function(e){return"function"==typeof e?e:void 0},ie=function(e,t){return arguments.length<2?re(ne[e])||re(r[e]):ne[e]&&ne[e][t]||r[e]&&r[e][t]},oe=Math.ceil,ae=Math.floor,se=function(e){return isNaN(e=+e)?0:(e>0?ae:oe)(e)},ce=Math.min,ue=function(e){return e>0?ce(se(e),9007199254740991):0},de=Math.max,le=Math.min,pe=function(e,t){var n=se(e);return n<0?de(n+t,0):le(n,t)},fe=function(e){return function(t,n,r){var i,o=m(t),a=ue(o.length),s=pe(r,a);if(e&&n!=n){for(;a>s;)if((i=o[s++])!=i)return!0}else for(;a>s;s++)if((e||s in o)&&o[s]===n)return e||s||0;return!e&&-1}},he={includes:fe(!0),indexOf:fe(!1)},me=he.indexOf,ve=function(e,t){var n,r=m(e),i=0,o=[];for(n in r)!y(z,n)&&y(r,n)&&o.push(n);for(;t.length>i;)y(r,n=t[i++])&&(~me(o,n)||o.push(n));return o},ge=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],_e=ge.concat("length","prototype"),ye={f:Object.getOwnPropertyNames||function(e){return ve(e,_e)}},Se={f:Object.getOwnPropertySymbols},be=ie("Reflect","ownKeys")||function(e){var t=ye.f(C(e)),n=Se.f;return n?t.concat(n(e)):t},ke=function(e,t){for(var n=be(t),r=I.f,i=R.f,o=0;o<n.length;o++){var a=n[o];y(e,a)||r(e,a,i(t,a))}},we=/#|\.prototype\./,Te=function(e,t){var n=Ce[Re(e)];return n==Ie||n!=Ee&&("function"==typeof t?i(t):!!t)},Re=Te.normalize=function(e){return String(e).replace(we,".").toLowerCase()},Ce=Te.data={},Ee=Te.NATIVE="N",Ie=Te.POLYFILL="P",xe=Te,Pe=R.f,Ae=function(e,t){var n,i,o,a,s,c=e.target,u=e.global,d=e.stat;if(n=u?r:d?r[c]||P(c,{}):(r[c]||{}).prototype)for(i in t){if(a=t[i],o=e.noTargetGet?(s=Pe(n,i))&&s.value:n[i],!xe(u?i:c+(d?".":"#")+i,e.forced)&&void 0!==o){if(typeof a==typeof o)continue;ke(a,o)}(e.sham||o&&o.sham)&&x(a,"sham",!0),te(n,i,a,e)}},Oe=Array.isArray||function(e){return"Array"==l(e)},De=function(e){return Object(h(e))},Ne=function(e,t,n){var r=g(t);r in e?I.f(e,r,u(0,n)):e[r]=n},Le=!!Object.getOwnPropertySymbols&&!i(function(){return!String(Symbol())}),Me=Le&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,je=V("wks"),Ue=r.Symbol,Ve=Me?Ue:Ue&&Ue.withoutSetter||W,Fe=function(e){return y(je,e)||(Le&&y(Ue,e)?je[e]=Ue[e]:je[e]=Ve("Symbol."+e)),je[e]},Ge=Fe("species"),We=function(e,t){var n;return Oe(e)&&("function"!=typeof(n=e.constructor)||n!==Array&&!Oe(n.prototype)?v(n)&&null===(n=n[Ge])&&(n=void 0):n=void 0),new(void 0===n?Array:n)(0===t?0:t)},Be=ie("navigator","userAgent")||"",He=r.process,ze=He&&He.versions,Je=ze&&ze.v8;Je?Z=(X=Je.split("."))[0]+X[1]:Be&&(!(X=Be.match(/Edge\/(\d+)/))||X[1]>=74)&&(X=Be.match(/Chrome\/(\d+)/))&&(Z=X[1]);var qe=Z&&+Z,Qe=Fe("species"),Ke=function(e){return qe>=51||!i(function(){var t=[];return(t.constructor={})[Qe]=function(){return{foo:1}},1!==t[e](Boolean).foo})},$e=Fe("isConcatSpreadable"),Ye=qe>=51||!i(function(){var e=[];return e[$e]=!1,e.concat()[0]!==e}),Xe=Ke("concat"),Ze=function(e){if(!v(e))return!1;var t=e[$e];return void 0!==t?!!t:Oe(e)};Ae({target:"Array",proto:!0,forced:!Ye||!Xe},{concat:function(e){var t,n,r,i,o,a=De(this),s=We(a,0),c=0;for(t=-1,r=arguments.length;t<r;t++)if(o=-1===t?a:arguments[t],Ze(o)){if(c+(i=ue(o.length))>9007199254740991)throw TypeError("Maximum allowed index exceeded");for(n=0;n<i;n++,c++)n in o&&Ne(s,c,o[n])}else{if(c>=9007199254740991)throw TypeError("Maximum allowed index exceeded");Ne(s,c++,o)}return s.length=c,s}});var et=function(e){if("function"!=typeof e)throw TypeError(String(e)+" is not a function");return e},tt=function(e,t,n){if(et(e),void 0===t)return e;switch(n){case 0:return function(){return e.call(t)};case 1:return function(n){return e.call(t,n)};case 2:return function(n,r){return e.call(t,n,r)};case 3:return function(n,r,i){return e.call(t,n,r,i)}}return function(){return e.apply(t,arguments)}},nt=[].push,rt=function(e){var t=1==e,n=2==e,r=3==e,i=4==e,o=6==e,a=5==e||o;return function(s,c,u,d){for(var l,p,h=De(s),m=f(h),v=tt(c,u,3),g=ue(m.length),_=0,y=d||We,S=t?y(s,g):n?y(s,0):void 0;g>_;_++)if((a||_ in m)&&(p=v(l=m[_],_,h),e))if(t)S[_]=p;else if(p)switch(e){case 3:return!0;case 5:return l;case 6:return _;case 2:nt.call(S,l)}else if(i)return!1;return o?-1:r||i?i:S}},it={forEach:rt(0),map:rt(1),filter:rt(2),some:rt(3),every:rt(4),find:rt(5),findIndex:rt(6)},ot=it.filter,at=Ke("filter"),st=at&&!i(function(){[].filter.call({length:-1,0:1},function(e){throw e})});Ae({target:"Array",proto:!0,forced:!at||!st},{filter:function(e){return ot(this,e,arguments.length>1?arguments[1]:void 0)}});var ct=it.map,ut=Ke("map"),dt=ut&&!i(function(){[].map.call({length:-1,0:1},function(e){throw e})});function lt(e){return(lt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function pt(e,t,n,r,i,o,a){try{var s=e[o](a),c=s.value}catch(u){return void n(u)}s.done?t(c):Promise.resolve(c).then(r,i)}function ft(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var o=e.apply(t,n);function a(e){pt(o,r,i,a,s,"next",e)}function s(e){pt(o,r,i,a,s,"throw",e)}a(void 0)})}}function ht(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function mt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function vt(e,t,n){return t&&mt(e.prototype,t),n&&mt(e,n),e}function gt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _t(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){gt(e,t,n[t])})}return e}function yt(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&bt(e,t)}function St(e){return(St=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function bt(e,t){return(bt=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function kt(e,t,n){return(kt=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(e){return!1}}()?Reflect.construct:function(e,t,n){var r=[null];r.push.apply(r,t);var i=new(Function.bind.apply(e,r));return n&&bt(i,n.prototype),i}).apply(null,arguments)}function wt(e){var t="function"==typeof Map?new Map:void 0;return(wt=function(e){if(null===e||(n=e,-1===Function.toString.call(n).indexOf("[native code]")))return e;var n;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return kt(e,arguments,St(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),bt(r,e)})(e)}function Tt(e,t){return!t||"object"!=typeof t&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function Rt(e,t,n){return(Rt="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,n){var r=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=St(e)););return e}(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(n):i.value}})(e,t,n||e)}function Ct(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=[],r=!0,i=!1,o=void 0;try{for(var a,s=e[Symbol.iterator]();!(r=(a=s.next()).done)&&(n.push(a.value),!t||n.length!==t);r=!0);}catch(c){i=!0,o=c}finally{try{r||null==s.return||s.return()}finally{if(i)throw o}}return n}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}function Et(e){return function(e){if(Array.isArray(e)){for(var t=0,n=new Array(e.length);t<e.length;t++)n[t]=e[t];return n}}(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}Ae({target:"Array",proto:!0,forced:!ut||!dt},{map:function(e){return ct(this,e,arguments.length>1?arguments[1]:void 0)}});t(function(e){!function(t){var n,r=Object.prototype,i=r.hasOwnProperty,o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag",u=t.regeneratorRuntime;if(u)e.exports=u;else{(u=t.regeneratorRuntime=e.exports).wrap=y;var d="suspendedStart",l="suspendedYield",p="executing",f="completed",h={},m={};m[a]=function(){return this};var v=Object.getPrototypeOf,g=v&&v(v(P([])));g&&g!==r&&i.call(g,a)&&(m=g);var _=w.prototype=b.prototype=Object.create(m);k.prototype=_.constructor=w,w.constructor=k,w[c]=k.displayName="GeneratorFunction",u.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===k||"GeneratorFunction"===(t.displayName||t.name))},u.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,c in e||(e[c]="GeneratorFunction")),e.prototype=Object.create(_),e},u.awrap=function(e){return{__await:e}},T(R.prototype),R.prototype[s]=function(){return this},u.AsyncIterator=R,u.async=function(e,t,n,r){var i=new R(y(e,t,n,r));return u.isGeneratorFunction(t)?i:i.next().then(function(e){return e.done?e.value:i.next()})},T(_),_[c]="Generator",_[a]=function(){return this},_.toString=function(){return"[object Generator]"},u.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var r=t.pop();if(r in e)return n.value=r,n.done=!1,n}return n.done=!0,n}},u.values=P,x.prototype={constructor:x,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=n,this.done=!1,this.delegate=null,this.method="next",this.arg=n,this.tryEntries.forEach(I),!e)for(var t in this)"t"===t.charAt(0)&&i.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=n)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(r,i){return s.type="throw",s.arg=e,t.next=r,i&&(t.method="next",t.arg=n),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return r("end");if(a.tryLoc<=this.prev){var c=i.call(a,"catchLoc"),u=i.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc)return r(a.catchLoc,!0);if(this.prev<a.finallyLoc)return r(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return r(a.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return r(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&i.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,h):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),I(n),h}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;I(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:P(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=n),h}}}function y(e,t,n,r){var i=t&&t.prototype instanceof b?t:b,o=Object.create(i.prototype),a=new x(r||[]);return o._invoke=function(e,t,n){var r=d;return function(i,o){if(r===p)throw new Error("Generator is already running");if(r===f){if("throw"===i)throw o;return A()}for(n.method=i,n.arg=o;;){var a=n.delegate;if(a){var s=C(a,n);if(s){if(s===h)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(r===d)throw r=f,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r=p;var c=S(e,t,n);if("normal"===c.type){if(r=n.done?f:l,c.arg===h)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r=f,n.method="throw",n.arg=c.arg)}}}(e,n,a),o}function S(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(r){return{type:"throw",arg:r}}}function b(){}function k(){}function w(){}function T(e){["next","throw","return"].forEach(function(t){e[t]=function(e){return this._invoke(t,e)}})}function R(e){var t;this._invoke=function(n,r){function o(){return new Promise(function(t,o){!function t(n,r,o,a){var s=S(e[n],e,r);if("throw"!==s.type){var c=s.arg,u=c.value;return u&&"object"==typeof u&&i.call(u,"__await")?Promise.resolve(u.__await).then(function(e){t("next",e,o,a)},function(e){t("throw",e,o,a)}):Promise.resolve(u).then(function(e){c.value=e,o(c)},a)}a(s.arg)}(n,r,t,o)})}return t=t?t.then(o,o):o()}}function C(e,t){var r=e.iterator[t.method];if(r===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=n,C(e,t),"throw"===t.method))return h;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return h}var i=S(r,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,h;var o=i.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=n),t.delegate=null,h):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,h)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function I(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function x(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function P(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(i.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=n,t.done=!0,t};return o.next=o}}return{next:A}}function A(){return{value:n,done:!0}}}(function(){return this}()||Function("return this")())});let It=!0,xt=!0;function Pt(e,t,n){const r=e.match(t);return r&&r.length>=n&&parseInt(r[n],10)}function At(e,t,n){if(!e.RTCPeerConnection)return;const r=e.RTCPeerConnection.prototype,i=r.addEventListener;r.addEventListener=function(e,r){if(e!==t)return i.apply(this,arguments);const o=e=>{const t=n(e);t&&r(t)};return this._eventMap=this._eventMap||{},this._eventMap[r]=o,i.apply(this,[e,o])};const o=r.removeEventListener;r.removeEventListener=function(e,n){if(e!==t||!this._eventMap||!this._eventMap[n])return o.apply(this,arguments);const r=this._eventMap[n];return delete this._eventMap[n],o.apply(this,[e,r])},Object.defineProperty(r,"on"+t,{get(){return this["_on"+t]},set(e){this["_on"+t]&&(this.removeEventListener(t,this["_on"+t]),delete this["_on"+t]),e&&this.addEventListener(t,this["_on"+t]=e)},enumerable:!0,configurable:!0})}function Ot(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(It=e,e?"adapter.js logging disabled":"adapter.js logging enabled")}function Dt(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(xt=!e,"adapter.js deprecation warnings "+(e?"disabled":"enabled"))}function Nt(){if("object"==typeof window){if(It)return;"undefined"!=typeof console&&"function"==typeof console.log&&console.log.apply(console,arguments)}}function Lt(e,t){xt&&console.warn(e+" is deprecated, please use "+t+" instead.")}function Mt(e){const{navigator:t}=e,n={browser:null,version:null};if(void 0===e||!e.navigator)return n.browser="Not a browser.",n;if(t.mozGetUserMedia)n.browser="firefox",n.version=Pt(t.userAgent,/Firefox\/(\d+)\./,1);else if(t.webkitGetUserMedia||!1===e.isSecureContext&&e.webkitRTCPeerConnection&&!e.RTCIceGatherer)n.browser="chrome",n.version=Pt(t.userAgent,/Chrom(e|ium)\/(\d+)\./,2);else if(t.mediaDevices&&t.userAgent.match(/Edge\/(\d+).(\d+)$/))n.browser="edge",n.version=Pt(t.userAgent,/Edge\/(\d+).(\d+)$/,2);else{if(!e.RTCPeerConnection||!t.userAgent.match(/AppleWebKit\/(\d+)\./))return n.browser="Not a supported browser.",n;n.browser="safari",n.version=Pt(t.userAgent,/AppleWebKit\/(\d+)\./,1),n.supportsUnifiedPlan=e.RTCRtpTransceiver&&"currentDirection"in e.RTCRtpTransceiver.prototype}return n}function jt(e){return"[object Object]"===Object.prototype.toString.call(e)}function Ut(e){return jt(e)?Object.keys(e).reduce(function(t,n){const r=jt(e[n]),i=r?Ut(e[n]):e[n],o=r&&!Object.keys(i).length;return void 0===i||o?t:Object.assign(t,{[n]:i})},{}):e}function Vt(e,t,n){const r=n?"outbound-rtp":"inbound-rtp",i=new Map;if(null===t)return i;const o=[];return e.forEach(e=>{"track"===e.type&&e.trackIdentifier===t.id&&o.push(e)}),o.forEach(t=>{e.forEach(n=>{n.type===r&&n.trackId===t.id&&function e(t,n,r){n&&!r.has(n.id)&&(r.set(n.id,n),Object.keys(n).forEach(i=>{i.endsWith("Id")?e(t,t.get(n[i]),r):i.endsWith("Ids")&&n[i].forEach(n=>{e(t,t.get(n),r)})}))}(e,n,i)})}),i}const Ft=Nt;function Gt(e){const t=e&&e.navigator;if(!t.mediaDevices)return;const n=Mt(e),r=function(e){if("object"!=typeof e||e.mandatory||e.optional)return e;const t={};return Object.keys(e).forEach(n=>{if("require"===n||"advanced"===n||"mediaSource"===n)return;const r="object"==typeof e[n]?e[n]:{ideal:e[n]};void 0!==r.exact&&"number"==typeof r.exact&&(r.min=r.max=r.exact);const i=function(e,t){return e?e+t.charAt(0).toUpperCase()+t.slice(1):"deviceId"===t?"sourceId":t};if(void 0!==r.ideal){t.optional=t.optional||[];let e={};"number"==typeof r.ideal?(e[i("min",n)]=r.ideal,t.optional.push(e),(e={})[i("max",n)]=r.ideal,t.optional.push(e)):(e[i("",n)]=r.ideal,t.optional.push(e))}void 0!==r.exact&&"number"!=typeof r.exact?(t.mandatory=t.mandatory||{},t.mandatory[i("",n)]=r.exact):["min","max"].forEach(e=>{void 0!==r[e]&&(t.mandatory=t.mandatory||{},t.mandatory[i(e,n)]=r[e])})}),e.advanced&&(t.optional=(t.optional||[]).concat(e.advanced)),t},i=function(e,i){if(n.version>=61)return i(e);if((e=JSON.parse(JSON.stringify(e)))&&"object"==typeof e.audio){const t=function(e,t,n){t in e&&!(n in e)&&(e[n]=e[t],delete e[t])};t((e=JSON.parse(JSON.stringify(e))).audio,"autoGainControl","googAutoGainControl"),t(e.audio,"noiseSuppression","googNoiseSuppression"),e.audio=r(e.audio)}if(e&&"object"==typeof e.video){let o=e.video.facingMode;o=o&&("object"==typeof o?o:{ideal:o});const a=n.version<66;if(o&&("user"===o.exact||"environment"===o.exact||"user"===o.ideal||"environment"===o.ideal)&&(!t.mediaDevices.getSupportedConstraints||!t.mediaDevices.getSupportedConstraints().facingMode||a)){let n;if(delete e.video.facingMode,"environment"===o.exact||"environment"===o.ideal?n=["back","rear"]:"user"!==o.exact&&"user"!==o.ideal||(n=["front"]),n)return t.mediaDevices.enumerateDevices().then(t=>{let a=(t=t.filter(e=>"videoinput"===e.kind)).find(e=>n.some(t=>e.label.toLowerCase().includes(t)));return!a&&t.length&&n.includes("back")&&(a=t[t.length-1]),a&&(e.video.deviceId=o.exact?{exact:a.deviceId}:{ideal:a.deviceId}),e.video=r(e.video),Ft("chrome: "+JSON.stringify(e)),i(e)})}e.video=r(e.video)}return Ft("chrome: "+JSON.stringify(e)),i(e)},o=function(e){return n.version>=64?e:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[e.name]||e.name,message:e.message,constraint:e.constraint||e.constraintName,toString(){return this.name+(this.message&&": ")+this.message}}};if(t.getUserMedia=function(e,n,r){i(e,e=>{t.webkitGetUserMedia(e,n,e=>{r&&r(o(e))})})}.bind(t),t.mediaDevices.getUserMedia){const e=t.mediaDevices.getUserMedia.bind(t.mediaDevices);t.mediaDevices.getUserMedia=function(t){return i(t,t=>e(t).then(e=>{if(t.audio&&!e.getAudioTracks().length||t.video&&!e.getVideoTracks().length)throw e.getTracks().forEach(e=>{e.stop()}),new DOMException("","NotFoundError");return e},e=>Promise.reject(o(e))))}}}function Wt(e){e.MediaStream=e.MediaStream||e.webkitMediaStream}function Bt(e){if("object"!=typeof e||!e.RTCPeerConnection||"ontrack"in e.RTCPeerConnection.prototype)At(e,"track",e=>(e.transceiver||Object.defineProperty(e,"transceiver",{value:{receiver:e.receiver}}),e));else{Object.defineProperty(e.RTCPeerConnection.prototype,"ontrack",{get(){return this._ontrack},set(e){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=e)},enumerable:!0,configurable:!0});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){return this._ontrackpoly||(this._ontrackpoly=t=>{t.stream.addEventListener("addtrack",n=>{let r;r=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find(e=>e.track&&e.track.id===n.track.id):{track:n.track};const i=new Event("track");i.track=n.track,i.receiver=r,i.transceiver={receiver:r},i.streams=[t.stream],this.dispatchEvent(i)}),t.stream.getTracks().forEach(n=>{let r;r=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find(e=>e.track&&e.track.id===n.id):{track:n};const i=new Event("track");i.track=n,i.receiver=r,i.transceiver={receiver:r},i.streams=[t.stream],this.dispatchEvent(i)})},this.addEventListener("addstream",this._ontrackpoly)),t.apply(this,arguments)}}}function Ht(e){if("object"==typeof e&&e.RTCPeerConnection&&!("getSenders"in e.RTCPeerConnection.prototype)&&"createDTMFSender"in e.RTCPeerConnection.prototype){const t=function(e,t){return{track:t,get dtmf(){return void 0===this._dtmf&&("audio"===t.kind?this._dtmf=e.createDTMFSender(t):this._dtmf=null),this._dtmf},_pc:e}};if(!e.RTCPeerConnection.prototype.getSenders){e.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};const n=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,r){let i=n.apply(this,arguments);return i||(i=t(this,e),this._senders.push(i)),i};const r=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){r.apply(this,arguments);const t=this._senders.indexOf(e);-1!==t&&this._senders.splice(t,1)}}const n=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._senders=this._senders||[],n.apply(this,[e]),e.getTracks().forEach(e=>{this._senders.push(t(this,e))})};const r=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){this._senders=this._senders||[],r.apply(this,[e]),e.getTracks().forEach(e=>{const t=this._senders.find(t=>t.track===e);t&&this._senders.splice(this._senders.indexOf(t),1)})}}else if("object"==typeof e&&e.RTCPeerConnection&&"getSenders"in e.RTCPeerConnection.prototype&&"createDTMFSender"in e.RTCPeerConnection.prototype&&e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders;e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e},Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}}function zt(e){if(!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){const[e,n,r]=arguments;if(arguments.length>0&&"function"==typeof e)return t.apply(this,arguments);if(0===t.length&&(0===arguments.length||"function"!=typeof e))return t.apply(this,[]);const i=function(e){const t={};return e.result().forEach(e=>{const n={id:e.id,timestamp:e.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[e.type]||e.type};e.names().forEach(t=>{n[t]=e.stat(t)}),t[n.id]=n}),t},o=function(e){return new Map(Object.keys(e).map(t=>[t,e[t]]))};if(arguments.length>=2){const r=function(e){n(o(i(e)))};return t.apply(this,[r,e])}return new Promise((e,n)=>{t.apply(this,[function(t){e(o(i(t)))},n])}).then(n,r)}}function Jt(e){if(!("object"==typeof e&&e.RTCPeerConnection&&e.RTCRtpSender&&e.RTCRtpReceiver))return;if(!("getStats"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e});const n=e.RTCPeerConnection.prototype.addTrack;n&&(e.RTCPeerConnection.prototype.addTrack=function(){const e=n.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){const e=this;return this._pc.getStats().then(t=>Vt(t,e.track,!0))}}if(!("getStats"in e.RTCRtpReceiver.prototype)){const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){const e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e}),At(e,"track",e=>(e.receiver._pc=e.srcElement,e)),e.RTCRtpReceiver.prototype.getStats=function(){const e=this;return this._pc.getStats().then(t=>Vt(t,e.track,!1))}}if(!("getStats"in e.RTCRtpSender.prototype&&"getStats"in e.RTCRtpReceiver.prototype))return;const t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){if(arguments.length>0&&arguments[0]instanceof e.MediaStreamTrack){const e=arguments[0];let t,n,r;return this.getSenders().forEach(n=>{n.track===e&&(t?r=!0:t=n)}),this.getReceivers().forEach(t=>(t.track===e&&(n?r=!0:n=t),t.track===e)),r||t&&n?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):t?t.getStats():n?n.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return t.apply(this,arguments)}}function qt(e){e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map(e=>this._shimmedLocalStreams[e][0])};const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,n){if(!n)return t.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};const r=t.apply(this,arguments);return this._shimmedLocalStreams[n.id]?-1===this._shimmedLocalStreams[n.id].indexOf(r)&&this._shimmedLocalStreams[n.id].push(r):this._shimmedLocalStreams[n.id]=[n,r],r};const n=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._shimmedLocalStreams=this._shimmedLocalStreams||{},e.getTracks().forEach(e=>{if(this.getSenders().find(t=>t.track===e))throw new DOMException("Track already exists.","InvalidAccessError")});const t=this.getSenders();n.apply(this,arguments);const r=this.getSenders().filter(e=>-1===t.indexOf(e));this._shimmedLocalStreams[e.id]=[e].concat(r)};const r=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[e.id],r.apply(this,arguments)};const i=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},e&&Object.keys(this._shimmedLocalStreams).forEach(t=>{const n=this._shimmedLocalStreams[t].indexOf(e);-1!==n&&this._shimmedLocalStreams[t].splice(n,1),1===this._shimmedLocalStreams[t].length&&delete this._shimmedLocalStreams[t]}),i.apply(this,arguments)}}function Qt(e){if(!e.RTCPeerConnection)return;const t=Mt(e);if(e.RTCPeerConnection.prototype.addTrack&&t.version>=65)return qt(e);const n=e.RTCPeerConnection.prototype.getLocalStreams;e.RTCPeerConnection.prototype.getLocalStreams=function(){const e=n.apply(this);return this._reverseStreams=this._reverseStreams||{},e.map(e=>this._reverseStreams[e.id])};const r=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(t){if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},t.getTracks().forEach(e=>{if(this.getSenders().find(t=>t.track===e))throw new DOMException("Track already exists.","InvalidAccessError")}),!this._reverseStreams[t.id]){const n=new e.MediaStream(t.getTracks());this._streams[t.id]=n,this._reverseStreams[n.id]=t,t=n}r.apply(this,[t])};const i=e.RTCPeerConnection.prototype.removeStream;function o(e,t){let n=t.sdp;return Object.keys(e._reverseStreams||[]).forEach(t=>{const r=e._reverseStreams[t],i=e._streams[r.id];n=n.replace(new RegExp(i.id,"g"),r.id)}),new RTCSessionDescription({type:t.type,sdp:n})}function a(e,t){let n=t.sdp;return Object.keys(e._reverseStreams||[]).forEach(t=>{const r=e._reverseStreams[t],i=e._streams[r.id];n=n.replace(new RegExp(r.id,"g"),i.id)}),new RTCSessionDescription({type:t.type,sdp:n})}e.RTCPeerConnection.prototype.removeStream=function(e){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},i.apply(this,[this._streams[e.id]||e]),delete this._reverseStreams[this._streams[e.id]?this._streams[e.id].id:e.id],delete this._streams[e.id]},e.RTCPeerConnection.prototype.addTrack=function(t,n){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");const r=[].slice.call(arguments,1);if(1!==r.length||!r[0].getTracks().find(e=>e===t))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");const i=this.getSenders().find(e=>e.track===t);if(i)throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};const o=this._streams[n.id];if(o)o.addTrack(t),Promise.resolve().then(()=>{this.dispatchEvent(new Event("negotiationneeded"))});else{const r=new e.MediaStream([t]);this._streams[n.id]=r,this._reverseStreams[r.id]=n,this.addStream(r)}return this.getSenders().find(e=>e.track===t)},["createOffer","createAnswer"].forEach(function(t){const n=e.RTCPeerConnection.prototype[t],r={[t](){const e=arguments;return arguments.length&&"function"==typeof arguments[0]?n.apply(this,[t=>{const n=o(this,t);e[0].apply(null,[n])},t=>{e[1]&&e[1].apply(null,t)},arguments[2]]):n.apply(this,arguments).then(e=>o(this,e))}};e.RTCPeerConnection.prototype[t]=r[t]});const s=e.RTCPeerConnection.prototype.setLocalDescription;e.RTCPeerConnection.prototype.setLocalDescription=function(){return arguments.length&&arguments[0].type?(arguments[0]=a(this,arguments[0]),s.apply(this,arguments)):s.apply(this,arguments)};const c=Object.getOwnPropertyDescriptor(e.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(e.RTCPeerConnection.prototype,"localDescription",{get(){const e=c.get.apply(this);return""===e.type?e:o(this,e)}}),e.RTCPeerConnection.prototype.removeTrack=function(e){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!e._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(!(e._pc===this))throw new DOMException("Sender was not created by this connection.","InvalidAccessError");let t;this._streams=this._streams||{},Object.keys(this._streams).forEach(n=>{this._streams[n].getTracks().find(t=>e.track===t)&&(t=this._streams[n])}),t&&(1===t.getTracks().length?this.removeStream(this._reverseStreams[t.id]):t.removeTrack(e.track),this.dispatchEvent(new Event("negotiationneeded")))}}function Kt(e){const t=Mt(e);if(!e.RTCPeerConnection&&e.webkitRTCPeerConnection&&(e.RTCPeerConnection=e.webkitRTCPeerConnection),!e.RTCPeerConnection)return;t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(t){const n=e.RTCPeerConnection.prototype[t],r={[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),n.apply(this,arguments)}};e.RTCPeerConnection.prototype[t]=r[t]});const n=e.RTCPeerConnection.prototype.addIceCandidate;e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?t.version<78&&arguments[0]&&""===arguments[0].candidate?Promise.resolve():n.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())}}function $t(e){At(e,"negotiationneeded",e=>{if("stable"===e.target.signalingState)return e})}var Yt=Object.freeze({shimMediaStream:Wt,shimOnTrack:Bt,shimGetSendersWithDtmf:Ht,shimGetStats:zt,shimSenderReceiverGetStats:Jt,shimAddTrackRemoveTrackWithNative:qt,shimAddTrackRemoveTrack:Qt,shimPeerConnection:Kt,fixNegotiationNeeded:$t,shimGetUserMedia:Gt,shimGetDisplayMedia:function(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&("function"==typeof t?e.navigator.mediaDevices.getDisplayMedia=function(n){return t(n).then(t=>{const r=n.video&&n.video.width,i=n.video&&n.video.height,o=n.video&&n.video.frameRate;return n.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:t,maxFrameRate:o||3}},r&&(n.video.mandatory.maxWidth=r),i&&(n.video.mandatory.maxHeight=i),e.navigator.mediaDevices.getUserMedia(n)})}:console.error("shimGetDisplayMedia: getSourceId argument is not a function"))}});var Xt=t(function(e){var t={generateIdentifier:function(){return Math.random().toString(36).substr(2,10)}};t.localCName=t.generateIdentifier(),t.splitLines=function(e){return e.trim().split("\n").map(function(e){return e.trim()})},t.splitSections=function(e){return e.split("\nm=").map(function(e,t){return(t>0?"m="+e:e).trim()+"\r\n"})},t.getDescription=function(e){var n=t.splitSections(e);return n&&n[0]},t.getMediaSections=function(e){var n=t.splitSections(e);return n.shift(),n},t.matchPrefix=function(e,n){return t.splitLines(e).filter(function(e){return 0===e.indexOf(n)})},t.parseCandidate=function(e){for(var t,n={foundation:(t=0===e.indexOf("a=candidate:")?e.substring(12).split(" "):e.substring(10).split(" "))[0],component:parseInt(t[1],10),protocol:t[2].toLowerCase(),priority:parseInt(t[3],10),ip:t[4],address:t[4],port:parseInt(t[5],10),type:t[7]},r=8;r<t.length;r+=2)switch(t[r]){case"raddr":n.relatedAddress=t[r+1];break;case"rport":n.relatedPort=parseInt(t[r+1],10);break;case"tcptype":n.tcpType=t[r+1];break;case"ufrag":n.ufrag=t[r+1],n.usernameFragment=t[r+1];break;default:n[t[r]]=t[r+1]}return n},t.writeCandidate=function(e){var t=[];t.push(e.foundation),t.push(e.component),t.push(e.protocol.toUpperCase()),t.push(e.priority),t.push(e.address||e.ip),t.push(e.port);var n=e.type;return t.push("typ"),t.push(n),"host"!==n&&e.relatedAddress&&e.relatedPort&&(t.push("raddr"),t.push(e.relatedAddress),t.push("rport"),t.push(e.relatedPort)),e.tcpType&&"tcp"===e.protocol.toLowerCase()&&(t.push("tcptype"),t.push(e.tcpType)),(e.usernameFragment||e.ufrag)&&(t.push("ufrag"),t.push(e.usernameFragment||e.ufrag)),"candidate:"+t.join(" ")},t.parseIceOptions=function(e){return e.substr(14).split(" ")},t.parseRtpMap=function(e){var t=e.substr(9).split(" "),n={payloadType:parseInt(t.shift(),10)};return t=t[0].split("/"),n.name=t[0],n.clockRate=parseInt(t[1],10),n.channels=3===t.length?parseInt(t[2],10):1,n.numChannels=n.channels,n},t.writeRtpMap=function(e){var t=e.payloadType;void 0!==e.preferredPayloadType&&(t=e.preferredPayloadType);var n=e.channels||e.numChannels||1;return"a=rtpmap:"+t+" "+e.name+"/"+e.clockRate+(1!==n?"/"+n:"")+"\r\n"},t.parseExtmap=function(e){var t=e.substr(9).split(" ");return{id:parseInt(t[0],10),direction:t[0].indexOf("/")>0?t[0].split("/")[1]:"sendrecv",uri:t[1]}},t.writeExtmap=function(e){return"a=extmap:"+(e.id||e.preferredId)+(e.direction&&"sendrecv"!==e.direction?"/"+e.direction:"")+" "+e.uri+"\r\n"},t.parseFmtp=function(e){for(var t,n={},r=e.substr(e.indexOf(" ")+1).split(";"),i=0;i<r.length;i++)n[(t=r[i].trim().split("="))[0].trim()]=t[1];return n},t.writeFmtp=function(e){var t="",n=e.payloadType;if(void 0!==e.preferredPayloadType&&(n=e.preferredPayloadType),e.parameters&&Object.keys(e.parameters).length){var r=[];Object.keys(e.parameters).forEach(function(t){e.parameters[t]?r.push(t+"="+e.parameters[t]):r.push(t)}),t+="a=fmtp:"+n+" "+r.join(";")+"\r\n"}return t},t.parseRtcpFb=function(e){var t=e.substr(e.indexOf(" ")+1).split(" ");return{type:t.shift(),parameter:t.join(" ")}},t.writeRtcpFb=function(e){var t="",n=e.payloadType;return void 0!==e.preferredPayloadType&&(n=e.preferredPayloadType),e.rtcpFeedback&&e.rtcpFeedback.length&&e.rtcpFeedback.forEach(function(e){t+="a=rtcp-fb:"+n+" "+e.type+(e.parameter&&e.parameter.length?" "+e.parameter:"")+"\r\n"}),t},t.parseSsrcMedia=function(e){var t=e.indexOf(" "),n={ssrc:parseInt(e.substr(7,t-7),10)},r=e.indexOf(":",t);return r>-1?(n.attribute=e.substr(t+1,r-t-1),n.value=e.substr(r+1)):n.attribute=e.substr(t+1),n},t.parseSsrcGroup=function(e){var t=e.substr(13).split(" ");return{semantics:t.shift(),ssrcs:t.map(function(e){return parseInt(e,10)})}},t.getMid=function(e){var n=t.matchPrefix(e,"a=mid:")[0];if(n)return n.substr(6)},t.parseFingerprint=function(e){var t=e.substr(14).split(" ");return{algorithm:t[0].toLowerCase(),value:t[1]}},t.getDtlsParameters=function(e,n){return{role:"auto",fingerprints:t.matchPrefix(e+n,"a=fingerprint:").map(t.parseFingerprint)}},t.writeDtlsParameters=function(e,t){var n="a=setup:"+t+"\r\n";return e.fingerprints.forEach(function(e){n+="a=fingerprint:"+e.algorithm+" "+e.value+"\r\n"}),n},t.getIceParameters=function(e,n){var r=t.splitLines(e);return{usernameFragment:(r=r.concat(t.splitLines(n))).filter(function(e){return 0===e.indexOf("a=ice-ufrag:")})[0].substr(12),password:r.filter(function(e){return 0===e.indexOf("a=ice-pwd:")})[0].substr(10)}},t.writeIceParameters=function(e){return"a=ice-ufrag:"+e.usernameFragment+"\r\na=ice-pwd:"+e.password+"\r\n"},t.parseRtpParameters=function(e){for(var n={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},r=t.splitLines(e)[0].split(" "),i=3;i<r.length;i++){var o=r[i],a=t.matchPrefix(e,"a=rtpmap:"+o+" ")[0];if(a){var s=t.parseRtpMap(a),c=t.matchPrefix(e,"a=fmtp:"+o+" ");switch(s.parameters=c.length?t.parseFmtp(c[0]):{},s.rtcpFeedback=t.matchPrefix(e,"a=rtcp-fb:"+o+" ").map(t.parseRtcpFb),n.codecs.push(s),s.name.toUpperCase()){case"RED":case"ULPFEC":n.fecMechanisms.push(s.name.toUpperCase())}}}return t.matchPrefix(e,"a=extmap:").forEach(function(e){n.headerExtensions.push(t.parseExtmap(e))}),n},t.writeRtpDescription=function(e,n){var r="";r+="m="+e+" ",r+=n.codecs.length>0?"9":"0",r+=" UDP/TLS/RTP/SAVPF ",r+=n.codecs.map(function(e){return void 0!==e.preferredPayloadType?e.preferredPayloadType:e.payloadType}).join(" ")+"\r\n",r+="c=IN IP4 0.0.0.0\r\n",r+="a=rtcp:9 IN IP4 0.0.0.0\r\n",n.codecs.forEach(function(e){r+=t.writeRtpMap(e),r+=t.writeFmtp(e),r+=t.writeRtcpFb(e)});var i=0;return n.codecs.forEach(function(e){e.maxptime>i&&(i=e.maxptime)}),i>0&&(r+="a=maxptime:"+i+"\r\n"),r+="a=rtcp-mux\r\n",n.headerExtensions&&n.headerExtensions.forEach(function(e){r+=t.writeExtmap(e)}),r},t.parseRtpEncodingParameters=function(e){var n,r=[],i=t.parseRtpParameters(e),o=-1!==i.fecMechanisms.indexOf("RED"),a=-1!==i.fecMechanisms.indexOf("ULPFEC"),s=t.matchPrefix(e,"a=ssrc:").map(function(e){return t.parseSsrcMedia(e)}).filter(function(e){return"cname"===e.attribute}),c=s.length>0&&s[0].ssrc,u=t.matchPrefix(e,"a=ssrc-group:FID").map(function(e){return e.substr(17).split(" ").map(function(e){return parseInt(e,10)})});u.length>0&&u[0].length>1&&u[0][0]===c&&(n=u[0][1]),i.codecs.forEach(function(e){if("RTX"===e.name.toUpperCase()&&e.parameters.apt){var t={ssrc:c,codecPayloadType:parseInt(e.parameters.apt,10)};c&&n&&(t.rtx={ssrc:n}),r.push(t),o&&((t=JSON.parse(JSON.stringify(t))).fec={ssrc:c,mechanism:a?"red+ulpfec":"red"},r.push(t))}}),0===r.length&&c&&r.push({ssrc:c});var d=t.matchPrefix(e,"b=");return d.length&&(d=0===d[0].indexOf("b=TIAS:")?parseInt(d[0].substr(7),10):0===d[0].indexOf("b=AS:")?1e3*parseInt(d[0].substr(5),10)*.95-16e3:void 0,r.forEach(function(e){e.maxBitrate=d})),r},t.parseRtcpParameters=function(e){var n={},r=t.matchPrefix(e,"a=ssrc:").map(function(e){return t.parseSsrcMedia(e)}).filter(function(e){return"cname"===e.attribute})[0];r&&(n.cname=r.value,n.ssrc=r.ssrc);var i=t.matchPrefix(e,"a=rtcp-rsize");n.reducedSize=i.length>0,n.compound=0===i.length;var o=t.matchPrefix(e,"a=rtcp-mux");return n.mux=o.length>0,n},t.parseMsid=function(e){var n,r=t.matchPrefix(e,"a=msid:");if(1===r.length)return{stream:(n=r[0].substr(7).split(" "))[0],track:n[1]};var i=t.matchPrefix(e,"a=ssrc:").map(function(e){return t.parseSsrcMedia(e)}).filter(function(e){return"msid"===e.attribute});return i.length>0?{stream:(n=i[0].value.split(" "))[0],track:n[1]}:void 0},t.generateSessionId=function(){return Math.random().toString().substr(2,21)},t.writeSessionBoilerplate=function(e,n,r){var i=void 0!==n?n:2;return"v=0\r\no="+(r||"thisisadapterortc")+" "+(e||t.generateSessionId())+" "+i+" IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n"},t.writeMediaSection=function(e,n,r,i){var o=t.writeRtpDescription(e.kind,n);if(o+=t.writeIceParameters(e.iceGatherer.getLocalParameters()),o+=t.writeDtlsParameters(e.dtlsTransport.getLocalParameters(),"offer"===r?"actpass":"active"),o+="a=mid:"+e.mid+"\r\n",e.direction?o+="a="+e.direction+"\r\n":e.rtpSender&&e.rtpReceiver?o+="a=sendrecv\r\n":e.rtpSender?o+="a=sendonly\r\n":e.rtpReceiver?o+="a=recvonly\r\n":o+="a=inactive\r\n",e.rtpSender){var a="msid:"+i.id+" "+e.rtpSender.track.id+"\r\n";o+="a="+a,o+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" "+a,e.sendEncodingParameters[0].rtx&&(o+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" "+a,o+="a=ssrc-group:FID "+e.sendEncodingParameters[0].ssrc+" "+e.sendEncodingParameters[0].rtx.ssrc+"\r\n")}return o+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" cname:"+t.localCName+"\r\n",e.rtpSender&&e.sendEncodingParameters[0].rtx&&(o+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" cname:"+t.localCName+"\r\n"),o},t.getDirection=function(e,n){for(var r=t.splitLines(e),i=0;i<r.length;i++)switch(r[i]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return r[i].substr(2)}return n?t.getDirection(n):"sendrecv"},t.getKind=function(e){return t.splitLines(e)[0].split(" ")[0].substr(2)},t.isRejected=function(e){return"0"===e.split(" ",2)[1]},t.parseMLine=function(e){var n=t.splitLines(e)[0].substr(2).split(" ");return{kind:n[0],port:parseInt(n[1],10),protocol:n[2],fmt:n.slice(3).join(" ")}},t.parseOLine=function(e){var n=t.matchPrefix(e,"o=")[0].substr(2).split(" ");return{username:n[0],sessionId:n[1],sessionVersion:parseInt(n[2],10),netType:n[3],addressType:n[4],address:n[5]}},t.isValidSDP=function(e){if("string"!=typeof e||0===e.length)return!1;for(var n=t.splitLines(e),r=0;r<n.length;r++)if(n[r].length<2||"="!==n[r].charAt(1))return!1;return!0},e.exports=t});function Zt(e,t,n,r,i){var o=Xt.writeRtpDescription(e.kind,t);if(o+=Xt.writeIceParameters(e.iceGatherer.getLocalParameters()),o+=Xt.writeDtlsParameters(e.dtlsTransport.getLocalParameters(),"offer"===n?"actpass":i||"active"),o+="a=mid:"+e.mid+"\r\n",e.rtpSender&&e.rtpReceiver?o+="a=sendrecv\r\n":e.rtpSender?o+="a=sendonly\r\n":e.rtpReceiver?o+="a=recvonly\r\n":o+="a=inactive\r\n",e.rtpSender){var a=e.rtpSender._initialTrackId||e.rtpSender.track.id;e.rtpSender._initialTrackId=a;var s="msid:"+(r?r.id:"-")+" "+a+"\r\n";o+="a="+s,o+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" "+s,e.sendEncodingParameters[0].rtx&&(o+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" "+s,o+="a=ssrc-group:FID "+e.sendEncodingParameters[0].ssrc+" "+e.sendEncodingParameters[0].rtx.ssrc+"\r\n")}return o+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" cname:"+Xt.localCName+"\r\n",e.rtpSender&&e.sendEncodingParameters[0].rtx&&(o+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" cname:"+Xt.localCName+"\r\n"),o}function en(e,t){var n={codecs:[],headerExtensions:[],fecMechanisms:[]},r=function(e,t){e=parseInt(e,10);for(var n=0;n<t.length;n++)if(t[n].payloadType===e||t[n].preferredPayloadType===e)return t[n]},i=function(e,t,n,i){var o=r(e.parameters.apt,n),a=r(t.parameters.apt,i);return o&&a&&o.name.toLowerCase()===a.name.toLowerCase()};return e.codecs.forEach(function(r){for(var o=0;o<t.codecs.length;o++){var a=t.codecs[o];if(r.name.toLowerCase()===a.name.toLowerCase()&&r.clockRate===a.clockRate){if("rtx"===r.name.toLowerCase()&&r.parameters&&a.parameters.apt&&!i(r,a,e.codecs,t.codecs))continue;(a=JSON.parse(JSON.stringify(a))).numChannels=Math.min(r.numChannels,a.numChannels),n.codecs.push(a),a.rtcpFeedback=a.rtcpFeedback.filter(function(e){for(var t=0;t<r.rtcpFeedback.length;t++)if(r.rtcpFeedback[t].type===e.type&&r.rtcpFeedback[t].parameter===e.parameter)return!0;return!1});break}}}),e.headerExtensions.forEach(function(e){for(var r=0;r<t.headerExtensions.length;r++){var i=t.headerExtensions[r];if(e.uri===i.uri){n.headerExtensions.push(i);break}}}),n}function tn(e,t,n){return-1!=={offer:{setLocalDescription:["stable","have-local-offer"],setRemoteDescription:["stable","have-remote-offer"]},answer:{setLocalDescription:["have-remote-offer","have-local-pranswer"],setRemoteDescription:["have-local-offer","have-remote-pranswer"]}}[t][e].indexOf(n)}function nn(e,t){var n=e.getRemoteCandidates().find(function(e){return t.foundation===e.foundation&&t.ip===e.ip&&t.port===e.port&&t.priority===e.priority&&t.protocol===e.protocol&&t.type===e.type});return n||e.addRemoteCandidate(t),!n}function rn(e,t){var n=new Error(t);return n.name=e,n.code={NotSupportedError:9,InvalidStateError:11,InvalidAccessError:15,TypeError:void 0,OperationError:void 0}[e],n}var on=function(e,t){function n(t,n){n.addTrack(t),n.dispatchEvent(new e.MediaStreamTrackEvent("addtrack",{track:t}))}function r(t,n,r,i){var o=new Event("track");o.track=n,o.receiver=r,o.transceiver={receiver:r},o.streams=i,e.setTimeout(function(){t._dispatchEvent("track",o)})}var i=function(n){var r=this,i=document.createDocumentFragment();if(["addEventListener","removeEventListener","dispatchEvent"].forEach(function(e){r[e]=i[e].bind(i)}),this.canTrickleIceCandidates=null,this.needNegotiation=!1,this.localStreams=[],this.remoteStreams=[],this._localDescription=null,this._remoteDescription=null,this.signalingState="stable",this.iceConnectionState="new",this.connectionState="new",this.iceGatheringState="new",n=JSON.parse(JSON.stringify(n||{})),this.usingBundle="max-bundle"===n.bundlePolicy,"negotiate"===n.rtcpMuxPolicy)throw rn("NotSupportedError","rtcpMuxPolicy 'negotiate' is not supported");switch(n.rtcpMuxPolicy||(n.rtcpMuxPolicy="require"),n.iceTransportPolicy){case"all":case"relay":break;default:n.iceTransportPolicy="all"}switch(n.bundlePolicy){case"balanced":case"max-compat":case"max-bundle":break;default:n.bundlePolicy="balanced"}if(n.iceServers=function(e,t){var n=!1;return(e=JSON.parse(JSON.stringify(e))).filter(function(e){if(e&&(e.urls||e.url)){var r=e.urls||e.url;e.url&&!e.urls&&console.warn("RTCIceServer.url is deprecated! Use urls instead.");var i="string"==typeof r;return i&&(r=[r]),r=r.filter(function(e){return 0===e.indexOf("turn:")&&-1!==e.indexOf("transport=udp")&&-1===e.indexOf("turn:[")&&!n?(n=!0,!0):0===e.indexOf("stun:")&&t>=14393&&-1===e.indexOf("?transport=udp")}),delete e.url,e.urls=i?r[0]:r,!!r.length}})}(n.iceServers||[],t),this._iceGatherers=[],n.iceCandidatePoolSize)for(var o=n.iceCandidatePoolSize;o>0;o--)this._iceGatherers.push(new e.RTCIceGatherer({iceServers:n.iceServers,gatherPolicy:n.iceTransportPolicy}));else n.iceCandidatePoolSize=0;this._config=n,this.transceivers=[],this._sdpSessionId=Xt.generateSessionId(),this._sdpSessionVersion=0,this._dtlsRole=void 0,this._isClosed=!1};Object.defineProperty(i.prototype,"localDescription",{configurable:!0,get:function(){return this._localDescription}}),Object.defineProperty(i.prototype,"remoteDescription",{configurable:!0,get:function(){return this._remoteDescription}}),i.prototype.onicecandidate=null,i.prototype.onaddstream=null,i.prototype.ontrack=null,i.prototype.onremovestream=null,i.prototype.onsignalingstatechange=null,i.prototype.oniceconnectionstatechange=null,i.prototype.onconnectionstatechange=null,i.prototype.onicegatheringstatechange=null,i.prototype.onnegotiationneeded=null,i.prototype.ondatachannel=null,i.prototype._dispatchEvent=function(e,t){this._isClosed||(this.dispatchEvent(t),"function"==typeof this["on"+e]&&this["on"+e](t))},i.prototype._emitGatheringStateChange=function(){var e=new Event("icegatheringstatechange");this._dispatchEvent("icegatheringstatechange",e)},i.prototype.getConfiguration=function(){return this._config},i.prototype.getLocalStreams=function(){return this.localStreams},i.prototype.getRemoteStreams=function(){return this.remoteStreams},i.prototype._createTransceiver=function(e,t){var n=this.transceivers.length>0,r={track:null,iceGatherer:null,iceTransport:null,dtlsTransport:null,localCapabilities:null,remoteCapabilities:null,rtpSender:null,rtpReceiver:null,kind:e,mid:null,sendEncodingParameters:null,recvEncodingParameters:null,stream:null,associatedRemoteMediaStreams:[],wantReceive:!0};if(this.usingBundle&&n)r.iceTransport=this.transceivers[0].iceTransport,r.dtlsTransport=this.transceivers[0].dtlsTransport;else{var i=this._createIceAndDtlsTransports();r.iceTransport=i.iceTransport,r.dtlsTransport=i.dtlsTransport}return t||this.transceivers.push(r),r},i.prototype.addTrack=function(t,n){if(this._isClosed)throw rn("InvalidStateError","Attempted to call addTrack on a closed peerconnection.");var r;if(this.transceivers.find(function(e){return e.track===t}))throw rn("InvalidAccessError","Track already exists.");for(var i=0;i<this.transceivers.length;i++)this.transceivers[i].track||this.transceivers[i].kind!==t.kind||(r=this.transceivers[i]);return r||(r=this._createTransceiver(t.kind)),this._maybeFireNegotiationNeeded(),-1===this.localStreams.indexOf(n)&&this.localStreams.push(n),r.track=t,r.stream=n,r.rtpSender=new e.RTCRtpSender(t,r.dtlsTransport),r.rtpSender},i.prototype.addStream=function(e){var n=this;if(t>=15025)e.getTracks().forEach(function(t){n.addTrack(t,e)});else{var r=e.clone();e.getTracks().forEach(function(e,t){var n=r.getTracks()[t];e.addEventListener("enabled",function(e){n.enabled=e.enabled})}),r.getTracks().forEach(function(e){n.addTrack(e,r)})}},i.prototype.removeTrack=function(t){if(this._isClosed)throw rn("InvalidStateError","Attempted to call removeTrack on a closed peerconnection.");if(!(t instanceof e.RTCRtpSender))throw new TypeError("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.");var n=this.transceivers.find(function(e){return e.rtpSender===t});if(!n)throw rn("InvalidAccessError","Sender was not created by this connection.");var r=n.stream;n.rtpSender.stop(),n.rtpSender=null,n.track=null,n.stream=null,-1===this.transceivers.map(function(e){return e.stream}).indexOf(r)&&this.localStreams.indexOf(r)>-1&&this.localStreams.splice(this.localStreams.indexOf(r),1),this._maybeFireNegotiationNeeded()},i.prototype.removeStream=function(e){var t=this;e.getTracks().forEach(function(e){var n=t.getSenders().find(function(t){return t.track===e});n&&t.removeTrack(n)})},i.prototype.getSenders=function(){return this.transceivers.filter(function(e){return!!e.rtpSender}).map(function(e){return e.rtpSender})},i.prototype.getReceivers=function(){return this.transceivers.filter(function(e){return!!e.rtpReceiver}).map(function(e){return e.rtpReceiver})},i.prototype._createIceGatherer=function(t,n){var r=this;if(n&&t>0)return this.transceivers[0].iceGatherer;if(this._iceGatherers.length)return this._iceGatherers.shift();var i=new e.RTCIceGatherer({iceServers:this._config.iceServers,gatherPolicy:this._config.iceTransportPolicy});return Object.defineProperty(i,"state",{value:"new",writable:!0}),this.transceivers[t].bufferedCandidateEvents=[],this.transceivers[t].bufferCandidates=function(e){var n=!e.candidate||0===Object.keys(e.candidate).length;i.state=n?"completed":"gathering",null!==r.transceivers[t].bufferedCandidateEvents&&r.transceivers[t].bufferedCandidateEvents.push(e)},i.addEventListener("localcandidate",this.transceivers[t].bufferCandidates),i},i.prototype._gather=function(t,n){var r=this,i=this.transceivers[n].iceGatherer;if(!i.onlocalcandidate){var o=this.transceivers[n].bufferedCandidateEvents;this.transceivers[n].bufferedCandidateEvents=null,i.removeEventListener("localcandidate",this.transceivers[n].bufferCandidates),i.onlocalcandidate=function(e){if(!(r.usingBundle&&n>0)){var o=new Event("icecandidate");o.candidate={sdpMid:t,sdpMLineIndex:n};var a=e.candidate,s=!a||0===Object.keys(a).length;if(s)"new"!==i.state&&"gathering"!==i.state||(i.state="completed");else{"new"===i.state&&(i.state="gathering"),a.component=1,a.ufrag=i.getLocalParameters().usernameFragment;var c=Xt.writeCandidate(a);o.candidate=Object.assign(o.candidate,Xt.parseCandidate(c)),o.candidate.candidate=c,o.candidate.toJSON=function(){return{candidate:o.candidate.candidate,sdpMid:o.candidate.sdpMid,sdpMLineIndex:o.candidate.sdpMLineIndex,usernameFragment:o.candidate.usernameFragment}}}var u=Xt.getMediaSections(r._localDescription.sdp);u[o.candidate.sdpMLineIndex]+=s?"a=end-of-candidates\r\n":"a="+o.candidate.candidate+"\r\n",r._localDescription.sdp=Xt.getDescription(r._localDescription.sdp)+u.join("");var d=r.transceivers.every(function(e){return e.iceGatherer&&"completed"===e.iceGatherer.state});"gathering"!==r.iceGatheringState&&(r.iceGatheringState="gathering",r._emitGatheringStateChange()),s||r._dispatchEvent("icecandidate",o),d&&(r._dispatchEvent("icecandidate",new Event("icecandidate")),r.iceGatheringState="complete",r._emitGatheringStateChange())}},e.setTimeout(function(){o.forEach(function(e){i.onlocalcandidate(e)})},0)}},i.prototype._createIceAndDtlsTransports=function(){var t=this,n=new e.RTCIceTransport(null);n.onicestatechange=function(){t._updateIceConnectionState(),t._updateConnectionState()};var r=new e.RTCDtlsTransport(n);return r.ondtlsstatechange=function(){t._updateConnectionState()},r.onerror=function(){Object.defineProperty(r,"state",{value:"failed",writable:!0}),t._updateConnectionState()},{iceTransport:n,dtlsTransport:r}},i.prototype._disposeIceAndDtlsTransports=function(e){var t=this.transceivers[e].iceGatherer;t&&(delete t.onlocalcandidate,delete this.transceivers[e].iceGatherer);var n=this.transceivers[e].iceTransport;n&&(delete n.onicestatechange,delete this.transceivers[e].iceTransport);var r=this.transceivers[e].dtlsTransport;r&&(delete r.ondtlsstatechange,delete r.onerror,delete this.transceivers[e].dtlsTransport)},i.prototype._transceive=function(e,n,r){var i=en(e.localCapabilities,e.remoteCapabilities);n&&e.rtpSender&&(i.encodings=e.sendEncodingParameters,i.rtcp={cname:Xt.localCName,compound:e.rtcpParameters.compound},e.recvEncodingParameters.length&&(i.rtcp.ssrc=e.recvEncodingParameters[0].ssrc),e.rtpSender.send(i)),r&&e.rtpReceiver&&i.codecs.length>0&&("video"===e.kind&&e.recvEncodingParameters&&t<15019&&e.recvEncodingParameters.forEach(function(e){delete e.rtx}),e.recvEncodingParameters.length?i.encodings=e.recvEncodingParameters:i.encodings=[{}],i.rtcp={compound:e.rtcpParameters.compound},e.rtcpParameters.cname&&(i.rtcp.cname=e.rtcpParameters.cname),e.sendEncodingParameters.length&&(i.rtcp.ssrc=e.sendEncodingParameters[0].ssrc),e.rtpReceiver.receive(i))},i.prototype.setLocalDescription=function(e){var t,n,r=this;if(-1===["offer","answer"].indexOf(e.type))return Promise.reject(rn("TypeError",'Unsupported type "'+e.type+'"'));if(!tn("setLocalDescription",e.type,r.signalingState)||r._isClosed)return Promise.reject(rn("InvalidStateError","Can not set local "+e.type+" in state "+r.signalingState));if("offer"===e.type)t=Xt.splitSections(e.sdp),n=t.shift(),t.forEach(function(e,t){var n=Xt.parseRtpParameters(e);r.transceivers[t].localCapabilities=n}),r.transceivers.forEach(function(e,t){r._gather(e.mid,t)});else if("answer"===e.type){t=Xt.splitSections(r._remoteDescription.sdp),n=t.shift();var i=Xt.matchPrefix(n,"a=ice-lite").length>0;t.forEach(function(e,t){var o=r.transceivers[t],a=o.iceGatherer,s=o.iceTransport,c=o.dtlsTransport,u=o.localCapabilities,d=o.remoteCapabilities;if(!(Xt.isRejected(e)&&0===Xt.matchPrefix(e,"a=bundle-only").length)&&!o.rejected){var l=Xt.getIceParameters(e,n),p=Xt.getDtlsParameters(e,n);i&&(p.role="server"),r.usingBundle&&0!==t||(r._gather(o.mid,t),"new"===s.state&&s.start(a,l,i?"controlling":"controlled"),"new"===c.state&&c.start(p));var f=en(u,d);r._transceive(o,f.codecs.length>0,!1)}})}return r._localDescription={type:e.type,sdp:e.sdp},"offer"===e.type?r._updateSignalingState("have-local-offer"):r._updateSignalingState("stable"),Promise.resolve()},i.prototype.setRemoteDescription=function(i){var o=this;if(-1===["offer","answer"].indexOf(i.type))return Promise.reject(rn("TypeError",'Unsupported type "'+i.type+'"'));if(!tn("setRemoteDescription",i.type,o.signalingState)||o._isClosed)return Promise.reject(rn("InvalidStateError","Can not set remote "+i.type+" in state "+o.signalingState));var a={};o.remoteStreams.forEach(function(e){a[e.id]=e});var s=[],c=Xt.splitSections(i.sdp),u=c.shift(),d=Xt.matchPrefix(u,"a=ice-lite").length>0,l=Xt.matchPrefix(u,"a=group:BUNDLE ").length>0;o.usingBundle=l;var p=Xt.matchPrefix(u,"a=ice-options:")[0];return o.canTrickleIceCandidates=!!p&&p.substr(14).split(" ").indexOf("trickle")>=0,c.forEach(function(r,c){var p=Xt.splitLines(r),f=Xt.getKind(r),h=Xt.isRejected(r)&&0===Xt.matchPrefix(r,"a=bundle-only").length,m=p[0].substr(2).split(" ")[2],v=Xt.getDirection(r,u),g=Xt.parseMsid(r),_=Xt.getMid(r)||Xt.generateIdentifier();if(h||"application"===f&&("DTLS/SCTP"===m||"UDP/DTLS/SCTP"===m))o.transceivers[c]={mid:_,kind:f,protocol:m,rejected:!0};else{var y,S,b,k,w,T,R,C,E;!h&&o.transceivers[c]&&o.transceivers[c].rejected&&(o.transceivers[c]=o._createTransceiver(f,!0));var I,x,P=Xt.parseRtpParameters(r);h||(I=Xt.getIceParameters(r,u),(x=Xt.getDtlsParameters(r,u)).role="client"),R=Xt.parseRtpEncodingParameters(r);var A=Xt.parseRtcpParameters(r),O=Xt.matchPrefix(r,"a=end-of-candidates",u).length>0,D=Xt.matchPrefix(r,"a=candidate:").map(function(e){return Xt.parseCandidate(e)}).filter(function(e){return 1===e.component});if(("offer"===i.type||"answer"===i.type)&&!h&&l&&c>0&&o.transceivers[c]&&(o._disposeIceAndDtlsTransports(c),o.transceivers[c].iceGatherer=o.transceivers[0].iceGatherer,o.transceivers[c].iceTransport=o.transceivers[0].iceTransport,o.transceivers[c].dtlsTransport=o.transceivers[0].dtlsTransport,o.transceivers[c].rtpSender&&o.transceivers[c].rtpSender.setTransport(o.transceivers[0].dtlsTransport),o.transceivers[c].rtpReceiver&&o.transceivers[c].rtpReceiver.setTransport(o.transceivers[0].dtlsTransport)),"offer"!==i.type||h){if("answer"===i.type&&!h){S=(y=o.transceivers[c]).iceGatherer,b=y.iceTransport,k=y.dtlsTransport,w=y.rtpReceiver,T=y.sendEncodingParameters,C=y.localCapabilities,o.transceivers[c].recvEncodingParameters=R,o.transceivers[c].remoteCapabilities=P,o.transceivers[c].rtcpParameters=A,D.length&&"new"===b.state&&(!d&&!O||l&&0!==c?D.forEach(function(e){nn(y.iceTransport,e)}):b.setRemoteCandidates(D)),l&&0!==c||("new"===b.state&&b.start(S,I,"controlling"),"new"===k.state&&k.start(x)),!en(y.localCapabilities,y.remoteCapabilities).codecs.filter(function(e){return"rtx"===e.name.toLowerCase()}).length&&y.sendEncodingParameters[0].rtx&&delete y.sendEncodingParameters[0].rtx,o._transceive(y,"sendrecv"===v||"recvonly"===v,"sendrecv"===v||"sendonly"===v),!w||"sendrecv"!==v&&"sendonly"!==v?delete y.rtpReceiver:(E=w.track,g?(a[g.stream]||(a[g.stream]=new e.MediaStream),n(E,a[g.stream]),s.push([E,w,a[g.stream]])):(a.default||(a.default=new e.MediaStream),n(E,a.default),s.push([E,w,a.default])))}}else{(y=o.transceivers[c]||o._createTransceiver(f)).mid=_,y.iceGatherer||(y.iceGatherer=o._createIceGatherer(c,l)),D.length&&"new"===y.iceTransport.state&&(!O||l&&0!==c?D.forEach(function(e){nn(y.iceTransport,e)}):y.iceTransport.setRemoteCandidates(D)),C=e.RTCRtpReceiver.getCapabilities(f),t<15019&&(C.codecs=C.codecs.filter(function(e){return"rtx"!==e.name})),T=y.sendEncodingParameters||[{ssrc:1001*(2*c+2)}];var N,L=!1;if("sendrecv"===v||"sendonly"===v){if(L=!y.rtpReceiver,w=y.rtpReceiver||new e.RTCRtpReceiver(y.dtlsTransport,f),L)E=w.track,g&&"-"===g.stream||(g?(a[g.stream]||(a[g.stream]=new e.MediaStream,Object.defineProperty(a[g.stream],"id",{get:function(){return g.stream}})),Object.defineProperty(E,"id",{get:function(){return g.track}}),N=a[g.stream]):(a.default||(a.default=new e.MediaStream),N=a.default)),N&&(n(E,N),y.associatedRemoteMediaStreams.push(N)),s.push([E,w,N])}else y.rtpReceiver&&y.rtpReceiver.track&&(y.associatedRemoteMediaStreams.forEach(function(t){var n=t.getTracks().find(function(e){return e.id===y.rtpReceiver.track.id});n&&function(t,n){n.removeTrack(t),n.dispatchEvent(new e.MediaStreamTrackEvent("removetrack",{track:t}))}(n,t)}),y.associatedRemoteMediaStreams=[]);y.localCapabilities=C,y.remoteCapabilities=P,y.rtpReceiver=w,y.rtcpParameters=A,y.sendEncodingParameters=T,y.recvEncodingParameters=R,o._transceive(o.transceivers[c],!1,L)}}}),void 0===o._dtlsRole&&(o._dtlsRole="offer"===i.type?"active":"passive"),o._remoteDescription={type:i.type,sdp:i.sdp},"offer"===i.type?o._updateSignalingState("have-remote-offer"):o._updateSignalingState("stable"),Object.keys(a).forEach(function(t){var n=a[t];if(n.getTracks().length){if(-1===o.remoteStreams.indexOf(n)){o.remoteStreams.push(n);var i=new Event("addstream");i.stream=n,e.setTimeout(function(){o._dispatchEvent("addstream",i)})}s.forEach(function(e){var t=e[0],i=e[1];n.id===e[2].id&&r(o,t,i,[n])})}}),s.forEach(function(e){e[2]||r(o,e[0],e[1],[])}),e.setTimeout(function(){o&&o.transceivers&&o.transceivers.forEach(function(e){e.iceTransport&&"new"===e.iceTransport.state&&e.iceTransport.getRemoteCandidates().length>0&&(console.warn("Timeout for addRemoteCandidate. Consider sending an end-of-candidates notification"),e.iceTransport.addRemoteCandidate({}))})},4e3),Promise.resolve()},i.prototype.close=function(){this.transceivers.forEach(function(e){e.iceTransport&&e.iceTransport.stop(),e.dtlsTransport&&e.dtlsTransport.stop(),e.rtpSender&&e.rtpSender.stop(),e.rtpReceiver&&e.rtpReceiver.stop()}),this._isClosed=!0,this._updateSignalingState("closed")},i.prototype._updateSignalingState=function(e){this.signalingState=e;var t=new Event("signalingstatechange");this._dispatchEvent("signalingstatechange",t)},i.prototype._maybeFireNegotiationNeeded=function(){var t=this;"stable"===this.signalingState&&!0!==this.needNegotiation&&(this.needNegotiation=!0,e.setTimeout(function(){if(t.needNegotiation){t.needNegotiation=!1;var e=new Event("negotiationneeded");t._dispatchEvent("negotiationneeded",e)}},0))},i.prototype._updateIceConnectionState=function(){var e,t={new:0,closed:0,checking:0,connected:0,completed:0,disconnected:0,failed:0};if(this.transceivers.forEach(function(e){e.iceTransport&&!e.rejected&&t[e.iceTransport.state]++}),e="new",t.failed>0?e="failed":t.checking>0?e="checking":t.disconnected>0?e="disconnected":t.new>0?e="new":t.connected>0?e="connected":t.completed>0&&(e="completed"),e!==this.iceConnectionState){this.iceConnectionState=e;var n=new Event("iceconnectionstatechange");this._dispatchEvent("iceconnectionstatechange",n)}},i.prototype._updateConnectionState=function(){var e,t={new:0,closed:0,connecting:0,connected:0,completed:0,disconnected:0,failed:0};if(this.transceivers.forEach(function(e){e.iceTransport&&e.dtlsTransport&&!e.rejected&&(t[e.iceTransport.state]++,t[e.dtlsTransport.state]++)}),t.connected+=t.completed,e="new",t.failed>0?e="failed":t.connecting>0?e="connecting":t.disconnected>0?e="disconnected":t.new>0?e="new":t.connected>0&&(e="connected"),e!==this.connectionState){this.connectionState=e;var n=new Event("connectionstatechange");this._dispatchEvent("connectionstatechange",n)}},i.prototype.createOffer=function(){var n=this;if(n._isClosed)return Promise.reject(rn("InvalidStateError","Can not call createOffer after close"));var r=n.transceivers.filter(function(e){return"audio"===e.kind}).length,i=n.transceivers.filter(function(e){return"video"===e.kind}).length,o=arguments[0];if(o){if(o.mandatory||o.optional)throw new TypeError("Legacy mandatory/optional constraints not supported.");void 0!==o.offerToReceiveAudio&&(r=!0===o.offerToReceiveAudio?1:!1===o.offerToReceiveAudio?0:o.offerToReceiveAudio),void 0!==o.offerToReceiveVideo&&(i=!0===o.offerToReceiveVideo?1:!1===o.offerToReceiveVideo?0:o.offerToReceiveVideo)}for(n.transceivers.forEach(function(e){"audio"===e.kind?--r<0&&(e.wantReceive=!1):"video"===e.kind&&--i<0&&(e.wantReceive=!1)});r>0||i>0;)r>0&&(n._createTransceiver("audio"),r--),i>0&&(n._createTransceiver("video"),i--);var a=Xt.writeSessionBoilerplate(n._sdpSessionId,n._sdpSessionVersion++);n.transceivers.forEach(function(r,i){var o=r.track,a=r.kind,s=r.mid||Xt.generateIdentifier();r.mid=s,r.iceGatherer||(r.iceGatherer=n._createIceGatherer(i,n.usingBundle));var c=e.RTCRtpSender.getCapabilities(a);t<15019&&(c.codecs=c.codecs.filter(function(e){return"rtx"!==e.name})),c.codecs.forEach(function(e){"H264"===e.name&&void 0===e.parameters["level-asymmetry-allowed"]&&(e.parameters["level-asymmetry-allowed"]="1"),r.remoteCapabilities&&r.remoteCapabilities.codecs&&r.remoteCapabilities.codecs.forEach(function(t){e.name.toLowerCase()===t.name.toLowerCase()&&e.clockRate===t.clockRate&&(e.preferredPayloadType=t.payloadType)})}),c.headerExtensions.forEach(function(e){(r.remoteCapabilities&&r.remoteCapabilities.headerExtensions||[]).forEach(function(t){e.uri===t.uri&&(e.id=t.id)})});var u=r.sendEncodingParameters||[{ssrc:1001*(2*i+1)}];o&&t>=15019&&"video"===a&&!u[0].rtx&&(u[0].rtx={ssrc:u[0].ssrc+1}),r.wantReceive&&(r.rtpReceiver=new e.RTCRtpReceiver(r.dtlsTransport,a)),r.localCapabilities=c,r.sendEncodingParameters=u}),"max-compat"!==n._config.bundlePolicy&&(a+="a=group:BUNDLE "+n.transceivers.map(function(e){return e.mid}).join(" ")+"\r\n"),a+="a=ice-options:trickle\r\n",n.transceivers.forEach(function(e,t){a+=Zt(e,e.localCapabilities,"offer",e.stream,n._dtlsRole),a+="a=rtcp-rsize\r\n",!e.iceGatherer||"new"===n.iceGatheringState||0!==t&&n.usingBundle||(e.iceGatherer.getLocalCandidates().forEach(function(e){e.component=1,a+="a="+Xt.writeCandidate(e)+"\r\n"}),"completed"===e.iceGatherer.state&&(a+="a=end-of-candidates\r\n"))});var s=new e.RTCSessionDescription({type:"offer",sdp:a});return Promise.resolve(s)},i.prototype.createAnswer=function(){var n=this;if(n._isClosed)return Promise.reject(rn("InvalidStateError","Can not call createAnswer after close"));if("have-remote-offer"!==n.signalingState&&"have-local-pranswer"!==n.signalingState)return Promise.reject(rn("InvalidStateError","Can not call createAnswer in signalingState "+n.signalingState));var r=Xt.writeSessionBoilerplate(n._sdpSessionId,n._sdpSessionVersion++);n.usingBundle&&(r+="a=group:BUNDLE "+n.transceivers.map(function(e){return e.mid}).join(" ")+"\r\n"),r+="a=ice-options:trickle\r\n";var i=Xt.getMediaSections(n._remoteDescription.sdp).length;n.transceivers.forEach(function(e,o){if(!(o+1>i)){if(e.rejected)return"application"===e.kind?"DTLS/SCTP"===e.protocol?r+="m=application 0 DTLS/SCTP 5000\r\n":r+="m=application 0 "+e.protocol+" webrtc-datachannel\r\n":"audio"===e.kind?r+="m=audio 0 UDP/TLS/RTP/SAVPF 0\r\na=rtpmap:0 PCMU/8000\r\n":"video"===e.kind&&(r+="m=video 0 UDP/TLS/RTP/SAVPF 120\r\na=rtpmap:120 VP8/90000\r\n"),void(r+="c=IN IP4 0.0.0.0\r\na=inactive\r\na=mid:"+e.mid+"\r\n");var a;if(e.stream)"audio"===e.kind?a=e.stream.getAudioTracks()[0]:"video"===e.kind&&(a=e.stream.getVideoTracks()[0]),a&&t>=15019&&"video"===e.kind&&!e.sendEncodingParameters[0].rtx&&(e.sendEncodingParameters[0].rtx={ssrc:e.sendEncodingParameters[0].ssrc+1});var s=en(e.localCapabilities,e.remoteCapabilities);!s.codecs.filter(function(e){return"rtx"===e.name.toLowerCase()}).length&&e.sendEncodingParameters[0].rtx&&delete e.sendEncodingParameters[0].rtx,r+=Zt(e,s,"answer",e.stream,n._dtlsRole),e.rtcpParameters&&e.rtcpParameters.reducedSize&&(r+="a=rtcp-rsize\r\n")}});var o=new e.RTCSessionDescription({type:"answer",sdp:r});return Promise.resolve(o)},i.prototype.addIceCandidate=function(e){var t,n=this;return e&&void 0===e.sdpMLineIndex&&!e.sdpMid?Promise.reject(new TypeError("sdpMLineIndex or sdpMid required")):new Promise(function(r,i){if(!n._remoteDescription)return i(rn("InvalidStateError","Can not add ICE candidate without a remote description"));if(e&&""!==e.candidate){var o=e.sdpMLineIndex;if(e.sdpMid)for(var a=0;a<n.transceivers.length;a++)if(n.transceivers[a].mid===e.sdpMid){o=a;break}var s=n.transceivers[o];if(!s)return i(rn("OperationError","Can not add ICE candidate"));if(s.rejected)return r();var c=Object.keys(e.candidate).length>0?Xt.parseCandidate(e.candidate):{};if("tcp"===c.protocol&&(0===c.port||9===c.port))return r();if(c.component&&1!==c.component)return r();if((0===o||o>0&&s.iceTransport!==n.transceivers[0].iceTransport)&&!nn(s.iceTransport,c))return i(rn("OperationError","Can not add ICE candidate"));var u=e.candidate.trim();0===u.indexOf("a=")&&(u=u.substr(2)),(t=Xt.getMediaSections(n._remoteDescription.sdp))[o]+="a="+(c.type?u:"end-of-candidates")+"\r\n",n._remoteDescription.sdp=Xt.getDescription(n._remoteDescription.sdp)+t.join("")}else for(var d=0;d<n.transceivers.length&&(n.transceivers[d].rejected||(n.transceivers[d].iceTransport.addRemoteCandidate({}),(t=Xt.getMediaSections(n._remoteDescription.sdp))[d]+="a=end-of-candidates\r\n",n._remoteDescription.sdp=Xt.getDescription(n._remoteDescription.sdp)+t.join(""),!n.usingBundle));d++);r()})},i.prototype.getStats=function(t){if(t&&t instanceof e.MediaStreamTrack){var n=null;if(this.transceivers.forEach(function(e){e.rtpSender&&e.rtpSender.track===t?n=e.rtpSender:e.rtpReceiver&&e.rtpReceiver.track===t&&(n=e.rtpReceiver)}),!n)throw rn("InvalidAccessError","Invalid selector.");return n.getStats()}var r=[];return this.transceivers.forEach(function(e){["rtpSender","rtpReceiver","iceGatherer","iceTransport","dtlsTransport"].forEach(function(t){e[t]&&r.push(e[t].getStats())})}),Promise.all(r).then(function(e){var t=new Map;return e.forEach(function(e){e.forEach(function(e){t.set(e.id,e)})}),t})};["RTCRtpSender","RTCRtpReceiver","RTCIceGatherer","RTCIceTransport","RTCDtlsTransport"].forEach(function(t){var n=e[t];if(n&&n.prototype&&n.prototype.getStats){var r=n.prototype.getStats;n.prototype.getStats=function(){return r.apply(this).then(function(e){var t=new Map;return Object.keys(e).forEach(function(n){var r;e[n].type={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[(r=e[n]).type]||r.type,t.set(n,e[n])}),t})}}});var o=["createOffer","createAnswer"];return o.forEach(function(e){var t=i.prototype[e];i.prototype[e]=function(){var e=arguments;return"function"==typeof e[0]||"function"==typeof e[1]?t.apply(this,[arguments[2]]).then(function(t){"function"==typeof e[0]&&e[0].apply(null,[t])},function(t){"function"==typeof e[1]&&e[1].apply(null,[t])}):t.apply(this,arguments)}}),(o=["setLocalDescription","setRemoteDescription","addIceCandidate"]).forEach(function(e){var t=i.prototype[e];i.prototype[e]=function(){var e=arguments;return"function"==typeof e[1]||"function"==typeof e[2]?t.apply(this,arguments).then(function(){"function"==typeof e[1]&&e[1].apply(null)},function(t){"function"==typeof e[2]&&e[2].apply(null,[t])}):t.apply(this,arguments)}}),["getStats"].forEach(function(e){var t=i.prototype[e];i.prototype[e]=function(){var e=arguments;return"function"==typeof e[1]?t.apply(this,arguments).then(function(){"function"==typeof e[1]&&e[1].apply(null)}):t.apply(this,arguments)}}),i};function an(e){const t=e&&e.navigator,n=t.mediaDevices.getUserMedia.bind(t.mediaDevices);t.mediaDevices.getUserMedia=function(e){return n(e).catch(e=>Promise.reject(function(e){return{name:{PermissionDeniedError:"NotAllowedError"}[e.name]||e.name,message:e.message,constraint:e.constraint,toString(){return this.name}}}(e)))}}function sn(e){"getDisplayMedia"in e.navigator&&e.navigator.mediaDevices&&(e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||(e.navigator.mediaDevices.getDisplayMedia=e.navigator.getDisplayMedia.bind(e.navigator)))}function cn(e){const t=Mt(e);if(e.RTCIceGatherer&&(e.RTCIceCandidate||(e.RTCIceCandidate=function(e){return e}),e.RTCSessionDescription||(e.RTCSessionDescription=function(e){return e}),t.version<15025)){const t=Object.getOwnPropertyDescriptor(e.MediaStreamTrack.prototype,"enabled");Object.defineProperty(e.MediaStreamTrack.prototype,"enabled",{set(e){t.set.call(this,e);const n=new Event("enabled");n.enabled=e,this.dispatchEvent(n)}})}!e.RTCRtpSender||"dtmf"in e.RTCRtpSender.prototype||Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=new e.RTCDtmfSender(this):"video"===this.track.kind&&(this._dtmf=null)),this._dtmf}}),e.RTCDtmfSender&&!e.RTCDTMFSender&&(e.RTCDTMFSender=e.RTCDtmfSender);const n=on(e,t.version);e.RTCPeerConnection=function(e){return e&&e.iceServers&&(e.iceServers=function(e,t){let n=!1;return(e=JSON.parse(JSON.stringify(e))).filter(e=>{if(e&&(e.urls||e.url)){var t=e.urls||e.url;e.url&&!e.urls&&Lt("RTCIceServer.url","RTCIceServer.urls");const r="string"==typeof t;return r&&(t=[t]),t=t.filter(e=>{if(0===e.indexOf("stun:"))return!1;const t=e.startsWith("turn")&&!e.startsWith("turn:[")&&e.includes("transport=udp");return t&&!n?(n=!0,!0):t&&!n}),delete e.url,e.urls=r?t[0]:t,!!t.length}})}(e.iceServers,t.version),Nt("ICE servers after filtering:",e.iceServers)),new n(e)},e.RTCPeerConnection.prototype=n.prototype}function un(e){!e.RTCRtpSender||"replaceTrack"in e.RTCRtpSender.prototype||(e.RTCRtpSender.prototype.replaceTrack=e.RTCRtpSender.prototype.setTrack)}var dn=Object.freeze({shimPeerConnection:cn,shimReplaceTrack:un,shimGetUserMedia:an,shimGetDisplayMedia:sn});function ln(e){const t=Mt(e),n=e&&e.navigator,r=e&&e.MediaStreamTrack;if(n.getUserMedia=function(e,t,r){Lt("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),n.mediaDevices.getUserMedia(e).then(t,r)},!(t.version>55&&"autoGainControl"in n.mediaDevices.getSupportedConstraints())){const e=function(e,t,n){t in e&&!(n in e)&&(e[n]=e[t],delete e[t])},t=n.mediaDevices.getUserMedia.bind(n.mediaDevices);if(n.mediaDevices.getUserMedia=function(n){return"object"==typeof n&&"object"==typeof n.audio&&(n=JSON.parse(JSON.stringify(n)),e(n.audio,"autoGainControl","mozAutoGainControl"),e(n.audio,"noiseSuppression","mozNoiseSuppression")),t(n)},r&&r.prototype.getSettings){const t=r.prototype.getSettings;r.prototype.getSettings=function(){const n=t.apply(this,arguments);return e(n,"mozAutoGainControl","autoGainControl"),e(n,"mozNoiseSuppression","noiseSuppression"),n}}if(r&&r.prototype.applyConstraints){const t=r.prototype.applyConstraints;r.prototype.applyConstraints=function(n){return"audio"===this.kind&&"object"==typeof n&&(n=JSON.parse(JSON.stringify(n)),e(n,"autoGainControl","mozAutoGainControl"),e(n,"noiseSuppression","mozNoiseSuppression")),t.apply(this,[n])}}}}function pn(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function fn(e){const t=Mt(e);if("object"!=typeof e||!e.RTCPeerConnection&&!e.mozRTCPeerConnection)return;!e.RTCPeerConnection&&e.mozRTCPeerConnection&&(e.RTCPeerConnection=e.mozRTCPeerConnection),t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(t){const n=e.RTCPeerConnection.prototype[t],r={[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),n.apply(this,arguments)}};e.RTCPeerConnection.prototype[t]=r[t]});const n=e.RTCPeerConnection.prototype.addIceCandidate;e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?t.version<68&&arguments[0]&&""===arguments[0].candidate?Promise.resolve():n.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())};const r={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},i=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){const[e,n,o]=arguments;return i.apply(this,[e||null]).then(e=>{if(t.version<53&&!n)try{e.forEach(e=>{e.type=r[e.type]||e.type})}catch(i){if("TypeError"!==i.name)throw i;e.forEach((t,n)=>{e.set(n,Object.assign({},t,{type:r[t.type]||t.type}))})}return e}).then(n,o)}}function hn(e){if("object"!=typeof e||!e.RTCPeerConnection||!e.RTCRtpSender)return;if(e.RTCRtpSender&&"getStats"in e.RTCRtpSender.prototype)return;const t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e});const n=e.RTCPeerConnection.prototype.addTrack;n&&(e.RTCPeerConnection.prototype.addTrack=function(){const e=n.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}function mn(e){if("object"!=typeof e||!e.RTCPeerConnection||!e.RTCRtpSender)return;if(e.RTCRtpSender&&"getStats"in e.RTCRtpReceiver.prototype)return;const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){const e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e}),At(e,"track",e=>(e.receiver._pc=e.srcElement,e)),e.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}function vn(e){!e.RTCPeerConnection||"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){Lt("removeStream","removeTrack"),this.getSenders().forEach(t=>{t.track&&e.getTracks().includes(t.track)&&this.removeTrack(t)})})}function gn(e){e.DataChannel&&!e.RTCDataChannel&&(e.RTCDataChannel=e.DataChannel)}var _n=Object.freeze({shimOnTrack:pn,shimPeerConnection:fn,shimSenderGetStats:hn,shimReceiverGetStats:mn,shimRemoveStream:vn,shimRTCDataChannel:gn,shimGetUserMedia:ln,shimGetDisplayMedia:function(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&(e.navigator.mediaDevices.getDisplayMedia=function(n){if(!n||!n.video){const e=new DOMException("getDisplayMedia without video constraints is undefined");return e.name="NotFoundError",e.code=8,Promise.reject(e)}return!0===n.video?n.video={mediaSource:t}:n.video.mediaSource=t,e.navigator.mediaDevices.getUserMedia(n)})}});function yn(e){if("object"==typeof e&&e.RTCPeerConnection){if("getLocalStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in e.RTCPeerConnection.prototype)){const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addStream=function(e){this._localStreams||(this._localStreams=[]),this._localStreams.includes(e)||this._localStreams.push(e),e.getAudioTracks().forEach(n=>t.call(this,n,e)),e.getVideoTracks().forEach(n=>t.call(this,n,e))},e.RTCPeerConnection.prototype.addTrack=function(e){const n=arguments[1];return n&&(this._localStreams?this._localStreams.includes(n)||this._localStreams.push(n):this._localStreams=[n]),t.apply(this,arguments)}}"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){this._localStreams||(this._localStreams=[]);const t=this._localStreams.indexOf(e);if(-1===t)return;this._localStreams.splice(t,1);const n=e.getTracks();this.getSenders().forEach(e=>{n.includes(e.track)&&this.removeTrack(e)})})}}function Sn(e){if("object"==typeof e&&e.RTCPeerConnection&&("getRemoteStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in e.RTCPeerConnection.prototype))){Object.defineProperty(e.RTCPeerConnection.prototype,"onaddstream",{get(){return this._onaddstream},set(e){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=e),this.addEventListener("track",this._onaddstreampoly=e=>{e.streams.forEach(e=>{if(this._remoteStreams||(this._remoteStreams=[]),this._remoteStreams.includes(e))return;this._remoteStreams.push(e);const t=new Event("addstream");t.stream=e,this.dispatchEvent(t)})})}});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){const e=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(t){t.streams.forEach(t=>{if(e._remoteStreams||(e._remoteStreams=[]),e._remoteStreams.indexOf(t)>=0)return;e._remoteStreams.push(t);const n=new Event("addstream");n.stream=t,e.dispatchEvent(n)})}),t.apply(e,arguments)}}}function bn(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype,n=t.createOffer,r=t.createAnswer,i=t.setLocalDescription,o=t.setRemoteDescription,a=t.addIceCandidate;t.createOffer=function(e,t){const r=arguments.length>=2?arguments[2]:arguments[0],i=n.apply(this,[r]);return t?(i.then(e,t),Promise.resolve()):i},t.createAnswer=function(e,t){const n=arguments.length>=2?arguments[2]:arguments[0],i=r.apply(this,[n]);return t?(i.then(e,t),Promise.resolve()):i};let s=function(e,t,n){const r=i.apply(this,[e]);return n?(r.then(t,n),Promise.resolve()):r};t.setLocalDescription=s,s=function(e,t,n){const r=o.apply(this,[e]);return n?(r.then(t,n),Promise.resolve()):r},t.setRemoteDescription=s,s=function(e,t,n){const r=a.apply(this,[e]);return n?(r.then(t,n),Promise.resolve()):r},t.addIceCandidate=s}function kn(e){const t=e&&e.navigator;if(t.mediaDevices&&t.mediaDevices.getUserMedia){const e=t.mediaDevices,n=e.getUserMedia.bind(e);t.mediaDevices.getUserMedia=e=>n(wn(e))}!t.getUserMedia&&t.mediaDevices&&t.mediaDevices.getUserMedia&&(t.getUserMedia=function(e,n,r){t.mediaDevices.getUserMedia(e).then(n,r)}.bind(t))}function wn(e){return e&&void 0!==e.video?Object.assign({},e,{video:Ut(e.video)}):e}function Tn(e){const t=e.RTCPeerConnection;e.RTCPeerConnection=function(e,n){if(e&&e.iceServers){const t=[];for(let n=0;n<e.iceServers.length;n++){let r=e.iceServers[n];!r.hasOwnProperty("urls")&&r.hasOwnProperty("url")?(Lt("RTCIceServer.url","RTCIceServer.urls"),(r=JSON.parse(JSON.stringify(r))).urls=r.url,delete r.url,t.push(r)):t.push(e.iceServers[n])}e.iceServers=t}return new t(e,n)},e.RTCPeerConnection.prototype=t.prototype,"generateCertificate"in e.RTCPeerConnection&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:()=>t.generateCertificate})}function Rn(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function Cn(e){const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(e){if(e){void 0!==e.offerToReceiveAudio&&(e.offerToReceiveAudio=!!e.offerToReceiveAudio);const t=this.getTransceivers().find(e=>"audio"===e.receiver.track.kind);!1===e.offerToReceiveAudio&&t?"sendrecv"===t.direction?t.setDirection?t.setDirection("sendonly"):t.direction="sendonly":"recvonly"===t.direction&&(t.setDirection?t.setDirection("inactive"):t.direction="inactive"):!0!==e.offerToReceiveAudio||t||this.addTransceiver("audio"),void 0!==e.offerToReceiveVideo&&(e.offerToReceiveVideo=!!e.offerToReceiveVideo);const n=this.getTransceivers().find(e=>"video"===e.receiver.track.kind);!1===e.offerToReceiveVideo&&n?"sendrecv"===n.direction?n.setDirection?n.setDirection("sendonly"):n.direction="sendonly":"recvonly"===n.direction&&(n.setDirection?n.setDirection("inactive"):n.direction="inactive"):!0!==e.offerToReceiveVideo||n||this.addTransceiver("video")}return t.apply(this,arguments)}}var En=Object.freeze({shimLocalStreamsAPI:yn,shimRemoteStreamsAPI:Sn,shimCallbacksAPI:bn,shimGetUserMedia:kn,shimConstraints:wn,shimRTCIceServerUrls:Tn,shimTrackEventTransceiver:Rn,shimCreateOfferLegacy:Cn});function In(e){if(!e.RTCIceCandidate||e.RTCIceCandidate&&"foundation"in e.RTCIceCandidate.prototype)return;const t=e.RTCIceCandidate;e.RTCIceCandidate=function(e){if("object"==typeof e&&e.candidate&&0===e.candidate.indexOf("a=")&&((e=JSON.parse(JSON.stringify(e))).candidate=e.candidate.substr(2)),e.candidate&&e.candidate.length){const n=new t(e),r=Xt.parseCandidate(e.candidate),i=Object.assign(n,r);return i.toJSON=function(){return{candidate:i.candidate,sdpMid:i.sdpMid,sdpMLineIndex:i.sdpMLineIndex,usernameFragment:i.usernameFragment}},i}return new t(e)},e.RTCIceCandidate.prototype=t.prototype,At(e,"icecandidate",t=>(t.candidate&&Object.defineProperty(t,"candidate",{value:new e.RTCIceCandidate(t.candidate),writable:"false"}),t))}function xn(e){if(!e.RTCPeerConnection)return;const t=Mt(e);"sctp"in e.RTCPeerConnection.prototype||Object.defineProperty(e.RTCPeerConnection.prototype,"sctp",{get(){return void 0===this._sctp?null:this._sctp}});const n=function(e){if(!e||!e.sdp)return!1;const t=Xt.splitSections(e.sdp);return t.shift(),t.some(e=>{const t=Xt.parseMLine(e);return t&&"application"===t.kind&&-1!==t.protocol.indexOf("SCTP")})},r=function(e){const t=e.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(null===t||t.length<2)return-1;const n=parseInt(t[1],10);return n!=n?-1:n},i=function(e){let n=65536;return"firefox"===t.browser&&(n=t.version<57?-1===e?16384:2147483637:t.version<60?57===t.version?65535:65536:2147483637),n},o=function(e,n){let r=65536;"firefox"===t.browser&&57===t.version&&(r=65535);const i=Xt.matchPrefix(e.sdp,"a=max-message-size:");return i.length>0?r=parseInt(i[0].substr(19),10):"firefox"===t.browser&&-1!==n&&(r=2147483637),r},a=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){if(this._sctp=null,"chrome"===t.browser&&t.version>=76){const{sdpSemantics:e}=this.getConfiguration();"plan-b"===e&&Object.defineProperty(this,"sctp",{get(){return void 0===this._sctp?null:this._sctp},enumerable:!0,configurable:!0})}if(n(arguments[0])){const e=r(arguments[0]),t=i(e),n=o(arguments[0],e);let a;a=0===t&&0===n?Number.POSITIVE_INFINITY:0===t||0===n?Math.max(t,n):Math.min(t,n);const s={};Object.defineProperty(s,"maxMessageSize",{get:()=>a}),this._sctp=s}return a.apply(this,arguments)}}function Pn(e){if(!(e.RTCPeerConnection&&"createDataChannel"in e.RTCPeerConnection.prototype))return;function t(e,t){const n=e.send;e.send=function(){const r=arguments[0],i=r.length||r.size||r.byteLength;if("open"===e.readyState&&t.sctp&&i>t.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+t.sctp.maxMessageSize+" bytes)");return n.apply(e,arguments)}}const n=e.RTCPeerConnection.prototype.createDataChannel;e.RTCPeerConnection.prototype.createDataChannel=function(){const e=n.apply(this,arguments);return t(e,this),e},At(e,"datachannel",e=>(t(e.channel,e.target),e))}function An(e){if(!e.RTCPeerConnection||"connectionState"in e.RTCPeerConnection.prototype)return;const t=e.RTCPeerConnection.prototype;Object.defineProperty(t,"connectionState",{get(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(t,"onconnectionstatechange",{get(){return this._onconnectionstatechange||null},set(e){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),e&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=e)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach(e=>{const n=t[e];t[e]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=e=>{const t=e.target;if(t._lastConnectionState!==t.connectionState){t._lastConnectionState=t.connectionState;const n=new Event("connectionstatechange",e);t.dispatchEvent(n)}return e},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),n.apply(this,arguments)}})}function On(e){if(!e.RTCPeerConnection)return;const t=Mt(e);if("chrome"===t.browser&&t.version>=71)return;const n=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(e){return e&&e.sdp&&-1!==e.sdp.indexOf("\na=extmap-allow-mixed")&&(e.sdp=e.sdp.split("\n").filter(e=>"a=extmap-allow-mixed"!==e.trim()).join("\n")),n.apply(this,arguments)}}var Dn=Object.freeze({shimRTCIceCandidate:In,shimMaxMessageSize:xn,shimSendThrowTypeError:Pn,shimConnectionState:An,removeAllowExtmapMixed:On});const Nn=function({window:e}={},t={shimChrome:!0,shimFirefox:!0,shimEdge:!0,shimSafari:!0}){const n=Nt,r=Mt(e),i={browserDetails:r,commonShim:Dn,extractVersion:Pt,disableLog:Ot,disableWarnings:Dt};switch(r.browser){case"chrome":if(!Yt||!Kt||!t.shimChrome)return n("Chrome shim is not included in this adapter release."),i;n("adapter.js shimming chrome."),i.browserShim=Yt,Gt(e),Wt(e),Kt(e),Bt(e),Qt(e),Ht(e),zt(e),Jt(e),$t(e),In(e),An(e),xn(e),Pn(e),On(e);break;case"firefox":if(!_n||!fn||!t.shimFirefox)return n("Firefox shim is not included in this adapter release."),i;n("adapter.js shimming firefox."),i.browserShim=_n,ln(e),fn(e),pn(e),vn(e),hn(e),mn(e),gn(e),In(e),An(e),xn(e),Pn(e);break;case"edge":if(!dn||!cn||!t.shimEdge)return n("MS edge shim is not included in this adapter release."),i;n("adapter.js shimming edge."),i.browserShim=dn,an(e),sn(e),cn(e),un(e),xn(e),Pn(e);break;case"safari":if(!En||!t.shimSafari)return n("Safari shim is not included in this adapter release."),i;n("adapter.js shimming safari."),i.browserShim=En,Tn(e),Cn(e),bn(e),yn(e),Sn(e),Rn(e),kn(e),In(e),xn(e),Pn(e),On(e);break;default:n("Unsupported browser!")}return i}({window:window});var Ln=function(e,t){var n=[][e];return!n||!i(function(){n.call(null,t||function(){throw 1},1)})},Mn=he.indexOf,jn=[].indexOf,Un=!!jn&&1/[1].indexOf(1,-0)<0,Vn=Ln("indexOf");Ae({target:"Array",proto:!0,forced:Un||Vn},{indexOf:function(e){return Un?jn.apply(this,arguments)||0:Mn(this,e,arguments.length>1?arguments[1]:void 0)}});var Fn=Fe("species"),Gn=[].slice,Wn=Math.max;Ae({target:"Array",proto:!0,forced:!Ke("slice")},{slice:function(e,t){var n,r,i,o=m(this),a=ue(o.length),s=pe(e,a),c=pe(void 0===t?a:t,a);if(Oe(o)&&("function"!=typeof(n=o.constructor)||n!==Array&&!Oe(n.prototype)?v(n)&&null===(n=n[Fn])&&(n=void 0):n=void 0,n===Array||void 0===n))return Gn.call(o,s,c);for(r=new(void 0===n?Array:n)(Wn(c-s,0)),i=0;s<c;s++,i++)s in o&&Ne(r,i,o[s]);return r.length=i,r}});var Bn=[].slice,Hn={},zn=function(e,t,n){if(!(t in Hn)){for(var r=[],i=0;i<t;i++)r[i]="a["+i+"]";Hn[t]=Function("C,a","return new C("+r.join(",")+")")}return Hn[t](e,n)},Jn=Function.bind||function(e){var t=et(this),n=Bn.call(arguments,1),r=function(){var i=n.concat(Bn.call(arguments));return this instanceof r?zn(t,i.length,i):t.apply(e,i)};return v(t.prototype)&&(r.prototype=t.prototype),r};Ae({target:"Function",proto:!0},{bind:Jn});var qn=I.f,Qn=Function.prototype,Kn=Qn.toString,$n=/^\s*function ([^ (]*)/;!o||"name"in Qn||qn(Qn,"name",{configurable:!0,get:function(){try{return Kn.call(this).match($n)[1]}catch(e){return""}}});var Yn=function(){var e=C(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t};function Xn(e,t){return RegExp(e,t)}var Zn={UNSUPPORTED_Y:i(function(){var e=Xn("a","y");return e.lastIndex=2,null!=e.exec("abcd")}),BROKEN_CARET:i(function(){var e=Xn("^r","gy");return e.lastIndex=2,null!=e.exec("str")})},er=RegExp.prototype.exec,tr=String.prototype.replace,nr=er,rr=function(){var e=/a/,t=/b*/g;return er.call(e,"a"),er.call(t,"a"),0!==e.lastIndex||0!==t.lastIndex}(),ir=Zn.UNSUPPORTED_Y||Zn.BROKEN_CARET,or=void 0!==/()??/.exec("")[1];(rr||or||ir)&&(nr=function(e){var t,n,r,i,o=this,a=ir&&o.sticky,s=Yn.call(o),c=o.source,u=0,d=e;return a&&(-1===(s=s.replace("y","")).indexOf("g")&&(s+="g"),d=String(e).slice(o.lastIndex),o.lastIndex>0&&(!o.multiline||o.multiline&&"\n"!==e[o.lastIndex-1])&&(c="(?: "+c+")",d=" "+d,u++),n=new RegExp("^(?:"+c+")",s)),or&&(n=new RegExp("^"+c+"$(?!\\s)",s)),rr&&(t=o.lastIndex),r=er.call(a?n:o,d),a?r?(r.input=r.input.slice(u),r[0]=r[0].slice(u),r.index=o.lastIndex,o.lastIndex+=r[0].length):o.lastIndex=0:rr&&r&&(o.lastIndex=o.global?r.index+r[0].length:t),or&&r&&r.length>1&&tr.call(r[0],n,function(){for(i=1;i<arguments.length-2;i++)void 0===arguments[i]&&(r[i]=void 0)}),r});var ar=nr;Ae({target:"RegExp",proto:!0,forced:/./.exec!==ar},{exec:ar});var sr=function(){var e=function(){},t="undefined",n=["trace","debug","info","warn","error"];function r(e,t){var n=e[t];if("function"==typeof n.bind)return n.bind(e);try{return Function.prototype.bind.call(n,e)}catch(r){return function(){return Function.prototype.apply.apply(n,[e,arguments])}}}function i(e,t){for(var r=0;r<n.length;r++){var i=n[r];this[i]=this.methodFactory(i,e,t)}this.log=this.debug}function o(e,n,r){return function(){("undefined"==typeof console?"undefined":lt(console))!==t&&(i.call(this,n,r),this[e].apply(this,arguments))}}function a(n,i,a){return function(n){return"debug"===n&&(n="log"),("undefined"==typeof console?"undefined":lt(console))!==t&&(void 0!==console[n]?r(console,n):void 0!==console.log?r(console,"log"):e)}(n)||o.apply(this,arguments)}function s(e,r,o){var s,c=this,u="loglevel";function d(){var e;if(("undefined"==typeof window?"undefined":lt(window))!==t){try{e=window.localStorage[u]}catch(i){}if(lt(e)===t)try{var n=window.document.cookie,r=n.indexOf(encodeURIComponent(u)+"=");-1!==r&&(e=/^([^;]+)/.exec(n.slice(r))[1])}catch(i){}return void 0===c.levels[e]&&(e=void 0),e}}e&&(u+=":"+e),c.name=e,c.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},c.methodFactory=o||a,c.getLevel=function(){return s},c.setLevel=function(r,o){if("string"==typeof r&&void 0!==c.levels[r.toUpperCase()]&&(r=c.levels[r.toUpperCase()]),!("number"==typeof r&&r>=0&&r<=c.levels.SILENT))throw"log.setLevel() called with invalid level: "+r;if(s=r,!1!==o&&function(e){var r=(n[e]||"silent").toUpperCase();if(("undefined"==typeof window?"undefined":lt(window))!==t){try{return void(window.localStorage[u]=r)}catch(i){}try{window.document.cookie=encodeURIComponent(u)+"="+r+";"}catch(i){}}}(r),i.call(c,r,e),("undefined"==typeof console?"undefined":lt(console))===t&&r<c.levels.SILENT)return"No console available for logging"},c.setDefaultLevel=function(e){d()||c.setLevel(e,!1)},c.enableAll=function(e){c.setLevel(c.levels.TRACE,e)},c.disableAll=function(e){c.setLevel(c.levels.SILENT,e)};var l=d();null==l&&(l=null==r?"WARN":r),c.setLevel(l,!1)}var c=new s,u={};c.getLogger=function(e){if("string"!=typeof e||""===e)throw new TypeError("You must supply a name when creating a logger.");var t=u[e];return t||(t=u[e]=new s(e,c.getLevel(),c.methodFactory)),t};var d=("undefined"==typeof window?"undefined":lt(window))!==t?window.log:void 0;return c.noConflict=function(){return("undefined"==typeof window?"undefined":lt(window))!==t&&window.log===c&&(window.log=d),c},c.getLoggers=function(){return u},c}();Ae({target:"Array",stat:!0},{isArray:Oe});var cr=[].join,ur=f!=Object,dr=Ln("join",",");Ae({target:"Array",proto:!0,forced:ur||dr},{join:function(e){return cr.call(m(this),void 0===e?",":e)}});var lr=it.some;Ae({target:"Array",proto:!0,forced:Ln("some")},{some:function(e){return lr(this,e,arguments.length>1?arguments[1]:void 0)}});var pr=Math.max,fr=Math.min;Ae({target:"Array",proto:!0,forced:!Ke("splice")},{splice:function(e,t){var n,r,i,o,a,s,c=De(this),u=ue(c.length),d=pe(e,u),l=arguments.length;if(0===l?n=r=0:1===l?(n=0,r=u-d):(n=l-2,r=fr(pr(se(t),0),u-d)),u+n-r>9007199254740991)throw TypeError("Maximum allowed length exceeded");for(i=We(c,r),o=0;o<r;o++)(a=d+o)in c&&Ne(i,o,c[a]);if(i.length=r,n<r){for(o=d;o<u-r;o++)s=o+n,(a=o+r)in c?c[s]=c[a]:delete c[s];for(o=u;o>u-r+n;o--)delete c[o-1]}else if(n>r)for(o=u-r;o>d;o--)s=o+n-1,(a=o+r-1)in c?c[s]=c[a]:delete c[s];for(o=0;o<n;o++)c[o+d]=arguments[o+2];return c.length=u-r+n,i}});var hr="".repeat||function(e){var t=String(h(this)),n="",r=se(e);if(r<0||Infinity==r)throw RangeError("Wrong number of repetitions");for(;r>0;(r>>>=1)&&(t+=t))1&r&&(n+=t);return n},mr=Math.ceil,vr=function(e){return function(t,n,r){var i,o,a=String(h(t)),s=a.length,c=void 0===r?" ":String(r),u=ue(n);return u<=s||""==c?a:(i=u-s,(o=hr.call(c,mr(i/c.length))).length>i&&(o=o.slice(0,i)),e?a+o:o+a)}},gr={start:vr(!1),end:vr(!0)}.start,_r=Math.abs,yr=Date.prototype,Sr=yr.getTime,br=yr.toISOString,kr=i(function(){return"0385-07-25T07:06:39.999Z"!=br.call(new Date(-5e13-1))})||!i(function(){br.call(new Date(NaN))})?function(){if(!isFinite(Sr.call(this)))throw RangeError("Invalid time value");var e=this.getUTCFullYear(),t=this.getUTCMilliseconds(),n=e<0?"-":e>9999?"+":"";return n+gr(_r(e),n?6:4,0)+"-"+gr(this.getUTCMonth()+1,2,0)+"-"+gr(this.getUTCDate(),2,0)+"T"+gr(this.getUTCHours(),2,0)+":"+gr(this.getUTCMinutes(),2,0)+":"+gr(this.getUTCSeconds(),2,0)+"."+gr(t,3,0)+"Z"}:br;Ae({target:"Date",proto:!0,forced:Date.prototype.toISOString!==kr},{toISOString:kr});var wr=Date.prototype,Tr=wr.toString,Rr=wr.getTime;new Date(NaN)+""!="Invalid Date"&&te(wr,"toString",function(){var e=Rr.call(this);return e==e?Tr.call(this):"Invalid Date"});var Cr=R.f,Er=i(function(){Cr(1)});Ae({target:"Object",stat:!0,forced:!o||Er,sham:!o},{getOwnPropertyDescriptor:function(e,t){return Cr(m(e),t)}});var Ir=!i(function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype}),xr=H("IE_PROTO"),Pr=Object.prototype,Ar=Ir?Object.getPrototypeOf:function(e){return e=De(e),y(e,xr)?e[xr]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?Pr:null},Or=i(function(){Ar(1)});Ae({target:"Object",stat:!0,forced:Or,sham:!Ir},{getPrototypeOf:function(e){return Ar(De(e))}});var Dr={};Dr[Fe("toStringTag")]="z";var Nr="[object z]"===String(Dr),Lr=Fe("toStringTag"),Mr="Arguments"==l(function(){return arguments}()),jr=Nr?l:function(e){var t,n,r;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(n){}}(t=Object(e),Lr))?n:Mr?l(t):"Object"==(r=l(t))&&"function"==typeof t.callee?"Arguments":r},Ur=Nr?{}.toString:function(){return"[object "+jr(this)+"]"};Nr||te(Object.prototype,"toString",Ur,{unsafe:!0});var Vr=RegExp.prototype,Fr=Vr.toString,Gr=i(function(){return"/a/b"!=Fr.call({source:"a",flags:"b"})}),Wr="toString"!=Fr.name;(Gr||Wr)&&te(RegExp.prototype,"toString",function(){var e=C(this),t=String(e.source),n=e.flags;return"/"+t+"/"+String(void 0===n&&e instanceof RegExp&&!("flags"in Vr)?Yn.call(e):n)},{unsafe:!0});var Br=Fe("species"),Hr=!i(function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")}),zr="$0"==="a".replace(/./,"$0"),Jr=!i(function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var n="ab".split(e);return 2!==n.length||"a"!==n[0]||"b"!==n[1]}),qr=function(e,t,n,r){var o=Fe(e),a=!i(function(){var t={};return t[o]=function(){return 7},7!=""[e](t)}),s=a&&!i(function(){var t=!1,n=/a/;return"split"===e&&((n={}).constructor={},n.constructor[Br]=function(){return n},n.flags="",n[o]=/./[o]),n.exec=function(){return t=!0,null},n[o](""),!t});if(!a||!s||"replace"===e&&(!Hr||!zr)||"split"===e&&!Jr){var c=/./[o],u=n(o,""[e],function(e,t,n,r,i){return t.exec===ar?a&&!i?{done:!0,value:c.call(t,n,r)}:{done:!0,value:e.call(n,t,r)}:{done:!1}},{REPLACE_KEEPS_$0:zr}),d=u[0],l=u[1];te(String.prototype,e,d),te(RegExp.prototype,o,2==t?function(e,t){return l.call(e,this,t)}:function(e){return l.call(e,this)})}r&&x(RegExp.prototype[o],"sham",!0)},Qr=function(e){return function(t,n){var r,i,o=String(h(t)),a=se(n),s=o.length;return a<0||a>=s?e?"":void 0:(r=o.charCodeAt(a))<55296||r>56319||a+1===s||(i=o.charCodeAt(a+1))<56320||i>57343?e?o.charAt(a):r:e?o.slice(a,a+2):i-56320+(r-55296<<10)+65536}},Kr={codeAt:Qr(!1),charAt:Qr(!0)},$r=Kr.charAt,Yr=function(e,t,n){return t+(n?$r(e,t).length:1)},Xr=function(e,t){var n=e.exec;if("function"==typeof n){var r=n.call(e,t);if("object"!=typeof r)throw TypeError("RegExp exec method returned something other than an Object or null");return r}if("RegExp"!==l(e))throw TypeError("RegExp#exec called on incompatible receiver");return ar.call(e,t)},Zr=Math.max,ei=Math.min,ti=Math.floor,ni=/\$([$&'`]|\d\d?|<[^>]*>)/g,ri=/\$([$&'`]|\d\d?)/g;qr("replace",2,function(e,t,n,r){return[function(n,r){var i=h(this),o=null==n?void 0:n[e];return void 0!==o?o.call(n,i,r):t.call(String(i),n,r)},function(e,o){if(r.REPLACE_KEEPS_$0||"string"==typeof o&&-1===o.indexOf("$0")){var a=n(t,e,this,o);if(a.done)return a.value}var s=C(e),c=String(this),u="function"==typeof o;u||(o=String(o));var d=s.global;if(d){var l=s.unicode;s.lastIndex=0}for(var p=[];;){var f=Xr(s,c);if(null===f)break;if(p.push(f),!d)break;""===String(f[0])&&(s.lastIndex=Yr(c,ue(s.lastIndex),l))}for(var h,m="",v=0,g=0;g<p.length;g++){f=p[g];for(var _=String(f[0]),y=Zr(ei(se(f.index),c.length),0),S=[],b=1;b<f.length;b++)S.push(void 0===(h=f[b])?h:String(h));var k=f.groups;if(u){var w=[_].concat(S,y,c);void 0!==k&&w.push(k);var T=String(o.apply(void 0,w))}else T=i(_,c,y,S,k,o);y>=v&&(m+=c.slice(v,y)+T,v=y+_.length)}return m+c.slice(v)}];function i(e,n,r,i,o,a){var s=r+e.length,c=i.length,u=ri;return void 0!==o&&(o=De(o),u=ni),t.call(a,u,function(t,a){var u;switch(a.charAt(0)){case"$":return"$";case"&":return e;case"`":return n.slice(0,r);case"'":return n.slice(s);case"<":u=o[a.slice(1,-1)];break;default:var d=+a;if(0===d)return t;if(d>c){var l=ti(d/10);return 0===l?t:l<=c?void 0===i[l-1]?a.charAt(1):i[l-1]+a.charAt(1):t}u=i[d-1]}return void 0===u?"":u})}});var ii=Fe("match"),oi=function(e){var t;return v(e)&&(void 0!==(t=e[ii])?!!t:"RegExp"==l(e))},ai=Fe("species"),si=function(e,t){var n,r=C(e).constructor;return void 0===r||null==(n=C(r)[ai])?t:et(n)},ci=[].push,ui=Math.min,di=!i(function(){return!RegExp(4294967295,"y")});qr("split",2,function(e,t,n){var r;return r="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(e,n){var r=String(h(this)),i=void 0===n?4294967295:n>>>0;if(0===i)return[];if(void 0===e)return[r];if(!oi(e))return t.call(r,e,i);for(var o,a,s,c=[],u=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),d=0,l=new RegExp(e.source,u+"g");(o=ar.call(l,r))&&!((a=l.lastIndex)>d&&(c.push(r.slice(d,o.index)),o.length>1&&o.index<r.length&&ci.apply(c,o.slice(1)),s=o[0].length,d=a,c.length>=i));)l.lastIndex===o.index&&l.lastIndex++;return d===r.length?!s&&l.test("")||c.push(""):c.push(r.slice(d)),c.length>i?c.slice(0,i):c}:"0".split(void 0,0).length?function(e,n){return void 0===e&&0===n?[]:t.call(this,e,n)}:t,[function(t,n){var i=h(this),o=null==t?void 0:t[e];return void 0!==o?o.call(t,i,n):r.call(String(i),t,n)},function(e,i){var o=n(r,e,this,i,r!==t);if(o.done)return o.value;var a=C(e),s=String(this),c=si(a,RegExp),u=a.unicode,d=(a.ignoreCase?"i":"")+(a.multiline?"m":"")+(a.unicode?"u":"")+(di?"y":"g"),l=new c(di?a:"^(?:"+a.source+")",d),p=void 0===i?4294967295:i>>>0;if(0===p)return[];if(0===s.length)return null===Xr(l,s)?[s]:[];for(var f=0,h=0,m=[];h<s.length;){l.lastIndex=di?h:0;var v,g=Xr(l,di?s:s.slice(h));if(null===g||(v=ui(ue(l.lastIndex+(di?0:h)),s.length))===f)h=Yr(s,h,u);else{if(m.push(s.slice(f,h)),m.length===p)return m;for(var _=1;_<=g.length-1;_++)if(m.push(g[_]),m.length===p)return m;h=f=v}}return m.push(s.slice(f)),m}]},!di);var li=[].slice,pi=/MSIE .\./.test(Be),fi=function(e){return function(t,n){var r=arguments.length>2,i=r?li.call(arguments,2):void 0;return e(r?function(){("function"==typeof t?t:Function(t)).apply(this,i)}:t,n)}};Ae({global:!0,bind:!0,forced:pi},{setTimeout:fi(r.setTimeout),setInterval:fi(r.setInterval)});var hi=function(e,t){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];return e.apply(t,n)}},mi=Object.prototype.toString;function vi(e){return"[object Array]"===mi.call(e)}function gi(e){return null!==e&&"object"==typeof e}function _i(e){return"[object Function]"===mi.call(e)}function yi(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),vi(e))for(var n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.call(null,e[i],i,e)}var Si={isArray:vi,isArrayBuffer:function(e){return"[object ArrayBuffer]"===mi.call(e)},isBuffer:function(e){return null!=e&&null!=e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:gi,isUndefined:function(e){return void 0===e},isDate:function(e){return"[object Date]"===mi.call(e)},isFile:function(e){return"[object File]"===mi.call(e)},isBlob:function(e){return"[object Blob]"===mi.call(e)},isFunction:_i,isStream:function(e){return gi(e)&&_i(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&("undefined"!=typeof window&&"undefined"!=typeof document)},forEach:yi,merge:function e(){var t={};function n(n,r){"object"==typeof t[r]&&"object"==typeof n?t[r]=e(t[r],n):t[r]=n}for(var r=0,i=arguments.length;r<i;r++)yi(arguments[r],n);return t},deepMerge:function e(){var t={};function n(n,r){"object"==typeof t[r]&&"object"==typeof n?t[r]=e(t[r],n):t[r]="object"==typeof n?e({},n):n}for(var r=0,i=arguments.length;r<i;r++)yi(arguments[r],n);return t},extend:function(e,t,n){return yi(t,function(t,r){e[r]=n&&"function"==typeof t?hi(t,n):t}),e},trim:function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")}};function bi(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}var ki=function(e,t,n){if(!t)return e;var r;if(n)r=n(t);else if(Si.isURLSearchParams(t))r=t.toString();else{var i=[];Si.forEach(t,function(e,t){null!=e&&(Si.isArray(e)?t+="[]":e=[e],Si.forEach(e,function(e){Si.isDate(e)?e=e.toISOString():Si.isObject(e)&&(e=JSON.stringify(e)),i.push(bi(t)+"="+bi(e))}))}),r=i.join("&")}if(r){var o=e.indexOf("#");-1!==o&&(e=e.slice(0,o)),e+=(-1===e.indexOf("?")?"?":"&")+r}return e};function wi(){this.handlers=[]}wi.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},wi.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},wi.prototype.forEach=function(e){Si.forEach(this.handlers,function(t){null!==t&&e(t)})};var Ti=wi,Ri=function(e,t,n){return Si.forEach(n,function(n){e=n(e,t)}),e},Ci=function(e){return!(!e||!e.__CANCEL__)},Ei="undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{};function Ii(){throw new Error("setTimeout has not been defined")}function xi(){throw new Error("clearTimeout has not been defined")}var Pi=Ii,Ai=xi;function Oi(e){if(Pi===setTimeout)return setTimeout(e,0);if((Pi===Ii||!Pi)&&setTimeout)return Pi=setTimeout,setTimeout(e,0);try{return Pi(e,0)}catch(t){try{return Pi.call(null,e,0)}catch(t){return Pi.call(this,e,0)}}}"function"==typeof Ei.setTimeout&&(Pi=setTimeout),"function"==typeof Ei.clearTimeout&&(Ai=clearTimeout);var Di,Ni=[],Li=!1,Mi=-1;function ji(){Li&&Di&&(Li=!1,Di.length?Ni=Di.concat(Ni):Mi=-1,Ni.length&&Ui())}function Ui(){if(!Li){var e=Oi(ji);Li=!0;for(var t=Ni.length;t;){for(Di=Ni,Ni=[];++Mi<t;)Di&&Di[Mi].run();Mi=-1,t=Ni.length}Di=null,Li=!1,function(e){if(Ai===clearTimeout)return clearTimeout(e);if((Ai===xi||!Ai)&&clearTimeout)return Ai=clearTimeout,clearTimeout(e);try{Ai(e)}catch(t){try{return Ai.call(null,e)}catch(t){return Ai.call(this,e)}}}(e)}}function Vi(e,t){this.fun=e,this.array=t}Vi.prototype.run=function(){this.fun.apply(null,this.array)};function Fi(){}var Gi=Fi,Wi=Fi,Bi=Fi,Hi=Fi,zi=Fi,Ji=Fi,qi=Fi;var Qi=Ei.performance||{},Ki=Qi.now||Qi.mozNow||Qi.msNow||Qi.oNow||Qi.webkitNow||function(){return(new Date).getTime()};var $i=new Date;var Yi={nextTick:function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];Ni.push(new Vi(e,t)),1!==Ni.length||Li||Oi(Ui)},title:"browser",browser:!0,env:{},argv:[],version:"",versions:{},on:Gi,addListener:Wi,once:Bi,off:Hi,removeListener:zi,removeAllListeners:Ji,emit:qi,binding:function(e){throw new Error("process.binding is not supported")},cwd:function(){return"/"},chdir:function(e){throw new Error("process.chdir is not supported")},umask:function(){return 0},hrtime:function(e){var t=.001*Ki.call(Qi),n=Math.floor(t),r=Math.floor(t%1*1e9);return e&&(n-=e[0],(r-=e[1])<0&&(n--,r+=1e9)),[n,r]},platform:"browser",release:{},config:{},uptime:function(){return(new Date-$i)/1e3}},Xi=function(e,t){Si.forEach(e,function(n,r){r!==t&&r.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[r])})},Zi=function(e,t,n,r,i){return function(e,t,n,r,i){return e.config=t,n&&(e.code=n),e.request=r,e.response=i,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},e}(new Error(e),t,n,r,i)},eo=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"],to=Si.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function r(e){var r=e;return t&&(n.setAttribute("href",r),r=n.href),n.setAttribute("href",r),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return e=r(window.location.href),function(t){var n=Si.isString(t)?r(t):t;return n.protocol===e.protocol&&n.host===e.host}}():function(){return!0},no=Si.isStandardBrowserEnv()?{write:function(e,t,n,r,i,o){var a=[];a.push(e+"="+encodeURIComponent(t)),Si.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),Si.isString(r)&&a.push("path="+r),Si.isString(i)&&a.push("domain="+i),!0===o&&a.push("secure"),document.cookie=a.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}},ro=function(e){return new Promise(function(t,n){var r=e.data,i=e.headers;Si.isFormData(r)&&delete i["Content-Type"];var o=new XMLHttpRequest;if(e.auth){var a=e.auth.username||"",s=e.auth.password||"";i.Authorization="Basic "+btoa(a+":"+s)}if(o.open(e.method.toUpperCase(),ki(e.url,e.params,e.paramsSerializer),!0),o.timeout=e.timeout,o.onreadystatechange=function(){if(o&&4===o.readyState&&(0!==o.status||o.responseURL&&0===o.responseURL.indexOf("file:"))){var r="getAllResponseHeaders"in o?function(e){var t,n,r,i={};return e?(Si.forEach(e.split("\n"),function(e){if(r=e.indexOf(":"),t=Si.trim(e.substr(0,r)).toLowerCase(),n=Si.trim(e.substr(r+1)),t){if(i[t]&&eo.indexOf(t)>=0)return;i[t]="set-cookie"===t?(i[t]?i[t]:[]).concat([n]):i[t]?i[t]+", "+n:n}}),i):i}(o.getAllResponseHeaders()):null,i={data:e.responseType&&"text"!==e.responseType?o.response:o.responseText,status:o.status,statusText:o.statusText,headers:r,config:e,request:o};!function(e,t,n){var r=n.config.validateStatus;!r||r(n.status)?e(n):t(Zi("Request failed with status code "+n.status,n.config,null,n.request,n))}(t,n,i),o=null}},o.onabort=function(){o&&(n(Zi("Request aborted",e,"ECONNABORTED",o)),o=null)},o.onerror=function(){n(Zi("Network Error",e,null,o)),o=null},o.ontimeout=function(){n(Zi("timeout of "+e.timeout+"ms exceeded",e,"ECONNABORTED",o)),o=null},Si.isStandardBrowserEnv()){var c=no,u=(e.withCredentials||to(e.url))&&e.xsrfCookieName?c.read(e.xsrfCookieName):void 0;u&&(i[e.xsrfHeaderName]=u)}if("setRequestHeader"in o&&Si.forEach(i,function(e,t){void 0===r&&"content-type"===t.toLowerCase()?delete i[t]:o.setRequestHeader(t,e)}),e.withCredentials&&(o.withCredentials=!0),e.responseType)try{o.responseType=e.responseType}catch(d){if("json"!==e.responseType)throw d}"function"==typeof e.onDownloadProgress&&o.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&o.upload&&o.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then(function(e){o&&(o.abort(),n(e),o=null)}),void 0===r&&(r=null),o.send(r)})},io={"Content-Type":"application/x-www-form-urlencoded"};function oo(e,t){!Si.isUndefined(e)&&Si.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var ao={adapter:function(){var e;return void 0!==Yi&&"[object process]"===Object.prototype.toString.call(Yi)?e=ro:"undefined"!=typeof XMLHttpRequest&&(e=ro),e}(),transformRequest:[function(e,t){return Xi(t,"Accept"),Xi(t,"Content-Type"),Si.isFormData(e)||Si.isArrayBuffer(e)||Si.isBuffer(e)||Si.isStream(e)||Si.isFile(e)||Si.isBlob(e)?e:Si.isArrayBufferView(e)?e.buffer:Si.isURLSearchParams(e)?(oo(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):Si.isObject(e)?(oo(t,"application/json;charset=utf-8"),JSON.stringify(e)):e}],transformResponse:[function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(t){}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};Si.forEach(["delete","get","head"],function(e){ao.headers[e]={}}),Si.forEach(["post","put","patch"],function(e){ao.headers[e]=Si.merge(io)});var so=ao;function co(e){e.cancelToken&&e.cancelToken.throwIfRequested()}var uo=function(e){var t,n;return co(e),e.baseURL&&!function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}(e.url)&&(e.url=(t=e.baseURL,(n=e.url)?t.replace(/\/+$/,"")+"/"+n.replace(/^\/+/,""):t)),e.headers=e.headers||{},e.data=Ri(e.data,e.headers,e.transformRequest),e.headers=Si.merge(e.headers.common||{},e.headers[e.method]||{},e.headers||{}),Si.forEach(["delete","get","head","post","put","patch","common"],function(t){delete e.headers[t]}),(e.adapter||so.adapter)(e).then(function(t){return co(e),t.data=Ri(t.data,t.headers,e.transformResponse),t},function(t){return Ci(t)||(co(e),t&&t.response&&(t.response.data=Ri(t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)})},lo=function(e,t){t=t||{};var n={};return Si.forEach(["url","method","params","data"],function(e){void 0!==t[e]&&(n[e]=t[e])}),Si.forEach(["headers","auth","proxy"],function(r){Si.isObject(t[r])?n[r]=Si.deepMerge(e[r],t[r]):void 0!==t[r]?n[r]=t[r]:Si.isObject(e[r])?n[r]=Si.deepMerge(e[r]):void 0!==e[r]&&(n[r]=e[r])}),Si.forEach(["baseURL","transformRequest","transformResponse","paramsSerializer","timeout","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","maxContentLength","validateStatus","maxRedirects","httpAgent","httpsAgent","cancelToken","socketPath"],function(r){void 0!==t[r]?n[r]=t[r]:void 0!==e[r]&&(n[r]=e[r])}),n};function po(e){this.defaults=e,this.interceptors={request:new Ti,response:new Ti}}po.prototype.request=function(e){"string"==typeof e?(e=arguments[1]||{}).url=arguments[0]:e=e||{},(e=lo(this.defaults,e)).method=e.method?e.method.toLowerCase():"get";var t=[uo,void 0],n=Promise.resolve(e);for(this.interceptors.request.forEach(function(e){t.unshift(e.fulfilled,e.rejected)}),this.interceptors.response.forEach(function(e){t.push(e.fulfilled,e.rejected)});t.length;)n=n.then(t.shift(),t.shift());return n},po.prototype.getUri=function(e){return e=lo(this.defaults,e),ki(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},Si.forEach(["delete","get","head","options"],function(e){po.prototype[e]=function(t,n){return this.request(Si.merge(n||{},{method:e,url:t}))}}),Si.forEach(["post","put","patch"],function(e){po.prototype[e]=function(t,n,r){return this.request(Si.merge(r||{},{method:e,url:t,data:n}))}});var fo=po;function ho(e){this.message=e}ho.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},ho.prototype.__CANCEL__=!0;var mo=ho;function vo(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise(function(e){t=e});var n=this;e(function(e){n.reason||(n.reason=new mo(e),t(n.reason))})}vo.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},vo.source=function(){var e;return{token:new vo(function(t){e=t}),cancel:e}};var go=vo;function _o(e){var t=new fo(e),n=hi(fo.prototype.request,t);return Si.extend(n,fo.prototype,t),Si.extend(n,t),n}var yo=_o(so);yo.Axios=fo,yo.create=function(e){return _o(lo(yo.defaults,e))},yo.Cancel=mo,yo.CancelToken=go,yo.isCancel=Ci,yo.all=function(e){return Promise.all(e)},yo.spread=function(e){return function(t){return e.apply(null,t)}};var So=yo,bo=yo;So.default=bo;var ko,wo=So,To=((new Date).getTime(),0),Ro=function(){return(new Date).getTime()+To},Co=function(){var e=new Date;return e.setTime(Ro()),e.toLocaleString()};function Eo(e){try{return JSON.stringify(e)}catch(n){if(!ko)try{var t={};t.a=t,JSON.stringify(t)}catch(r){ko=r.message}if(n.message===ko)return"[Circular]";throw n}}function Io(e){var t="",n=0;return e.length>1&&"string"==typeof e[0]&&(t=(t=e[0].replace(/(%?)(%([sdjo]))/g,function(t,r,i,o){if(!r){var a=e[n+=1],s="";switch(o){case"s":s+=a;break;case"d":s+=+a;break;case"j":s=Eo(a);break;case"o":var c=Eo(a);"{"!==c[0]&&"["!==c[0]&&(c="<".concat(c,">")),s=function(e){if(!Object.getOwnPropertyDescriptor||!Object.getPrototypeOf)return Object.prototype.toString.call(e).slice(8,-1);for(;e;){var t=Object.getOwnPropertyDescriptor(e,"constructor");if(void 0!==t&&"function"==typeof t.value&&""!==t.value.name)return t.value.name;e=Object.getPrototypeOf(e)}return""}(a)+c}return s}return t})).replace(/%{2,2}/g,"%"),n+=1),e.length>n&&(t&&(t+=" "),t+=e.slice(n).join(" ")),t}var xo=Object.prototype.hasOwnProperty;function Po(){try{throw new Error}catch(e){return e.stack}}function Ao(e){var t=this,n=[],r=[];this.length=function(){return n.length},this.sent=function(){return r.length},this.push=function(t){n.push(t),n.length>e&&n.shift()},this.send=function(){return r.length||(r=n,n=[]),r},this.confirm=function(){r=[],t.content=""},this.fail=function(){var i=1+n.length+r.length-e;i>0&&(r.splice(0,i),n=r.concat(n),t.confirm())}}var Oo,Do,No,Lo=!!Po();function Mo(e){return"[".concat(e.timestamp,"] <").concat(e.level.label.toUpperCase(),">").concat(e.logger?" (".concat(e.logger,")"):"",": ").concat(e.message).concat(e.stacktrace?"\n".concat(e.stacktrace):"")}var jo={url:"https://yun.tim.qq.com/v5/AVQualityReportSvc/C2S?sdkappid=1&cmdtype=jssdk_log",interval:1e3,level:"trace",capacity:0,stacktrace:{levels:["trace","warn","error"],depth:3,excess:0},timestamp:function(){return(new Date).toISOString()},format:Mo},Uo=-1,Vo=!1,Fo="",Go="",Wo="",Bo=function(e){Vo||(Fo="".concat(e.sdkAppId),Go="".concat(e.userId),Wo="".concat(e.version),Vo=!0)},Ho=function(e,t){if(!e||!e.getLogger)throw new TypeError("Argument is not a root loglevel object");if(Oo)throw new Error("You can assign a plugin only one time");Oo=e;var n=function e(){for(var t={},n=0;n<arguments.length;n+=1){var r=Object(arguments[n]);for(var i in r)xo.call(r,i)&&(t[i]="object"!==lt(r[i])||Array.isArray(r[i])?r[i]:e(t[i],r[i]))}return t}(jo,t);n.capacity=n.capacity||500;var r,i=n.interval;Uo=setInterval(function(){if(!Vo)return;if(!o.sent()){if(!o.length())return;var e=o.send();o.content=r?'{"logs":['.concat(e.join(","),"]}"):e.join("\n"),function(e){if(!Vo)return;var t=JSON.stringify({timestamp:Co(),sdkAppId:Fo,userId:Go,version:Wo,log:e});wo.post(n.url,t).then(function(e){o.confirm()}).catch(function(e){console.log(e),o.fail()})}(o.content)}},i);var o=new Ao(n.capacity);return Do=e.methodFactory,No=function(e,t,i){var a=Do(e,t,i),s=Lo&&n.stacktrace.levels.some(function(t){return t===e}),c=Oo.levels[e.toUpperCase()];return function(){for(var u=arguments.length,d=new Array(u),l=0;l<u;l++)d[l]=arguments[l];var p=Io(d),f=c>=t;if(f){var h=new Date;h.setTime(Ro());var m=h.toTimeString().replace(/.*(\d{2}:\d{2}:\d{2}).*/,"$1"),v="["+m+"] <"+e.toUpperCase()+"> "+p;a.apply(void 0,[v])}var g=Co(),_=s?Po():"";if(_){var y=_.split("\n");y.splice(0,n.stacktrace.excess+3);var S=n.stacktrace.depth;if(S&&y.length!==S+1){var b=y.splice(0,S);_=b.join("\n"),y.length&&(_+="\n    and ".concat(y.length," more"))}else _=y.join("\n")}var k=n.format({message:p,level:{label:e,value:c},logger:i||"",timestamp:g,stacktrace:_});void 0===r&&(r="string"!=typeof k);var w="";if(r)try{w+=JSON.stringify(k)}catch(T){return a.apply(void 0,d),void Oo.getLogger("logger").error(T)}else w+=k;o.push(w)}},e.methodFactory=No,e.setLevel(e.getLevel()),e},zo=function(){if(!Oo)throw new Error("You can't disable a not appled plugin");if(No!==Oo.methodFactory)throw new Error("You can't disable a plugin after appling another plugin");Oo.methodFactory=Do,Oo.setLevel(Oo.getLevel()),Oo=void 0,clearInterval(Uo)},Jo=!1;sr.setConfig=function(e){Bo(e)},sr.setLogLevel=function(e){sr.info("TRTC LogLevel was set to: "+e),sr.setLevel(e)},sr.enableUploadLog=function(){Jo||(sr.info("enable upload log"),Ho(sr),Jo=!0)},sr.disableUploadLog=function(){Jo&&(sr.warn("disable upload log! Without log we are difficult to help you triage the issue you might run into!"),zo(),Jo=!1)},sr.enableUploadLog(),sr.setLevel("INFO");var qo,Qo=Object.keys||function(e){return ve(e,ge)},Ko=o?Object.defineProperties:function(e,t){C(e);for(var n,r=Qo(t),i=r.length,o=0;i>o;)I.f(e,n=r[o++],t[n]);return e},$o=ie("document","documentElement"),Yo=H("IE_PROTO"),Xo=function(){},Zo=function(e){return"<script>"+e+"<\/script>"},ea=function(){try{qo=document.domain&&new ActiveXObject("htmlfile")}catch(r){}var e,t;ea=qo?function(e){e.write(Zo("")),e.close();var t=e.parentWindow.Object;return e=null,t}(qo):((t=k("iframe")).style.display="none",$o.appendChild(t),t.src=String("javascript:"),(e=t.contentWindow.document).open(),e.write(Zo("document.F=Object")),e.close(),e.F);for(var n=ge.length;n--;)delete ea.prototype[ge[n]];return ea()};z[Yo]=!0;var ta=Object.create||function(e,t){var n;return null!==e?(Xo.prototype=C(e),n=new Xo,Xo.prototype=null,n[Yo]=e):n=ea(),void 0===t?n:Ko(n,t)},na=ye.f,ra={}.toString,ia="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],oa={f:function(e){return ia&&"[object Window]"==ra.call(e)?function(e){try{return na(e)}catch(t){return ia.slice()}}(e):na(m(e))}},aa={f:Fe},sa=I.f,ca=function(e){var t=ne.Symbol||(ne.Symbol={});y(t,e)||sa(t,e,{value:aa.f(e)})},ua=I.f,da=Fe("toStringTag"),la=function(e,t,n){e&&!y(e=n?e:e.prototype,da)&&ua(e,da,{configurable:!0,value:t})},pa=it.forEach,fa=H("hidden"),ha=Fe("toPrimitive"),ma=ee.set,va=ee.getterFor("Symbol"),ga=Object.prototype,_a=r.Symbol,ya=ie("JSON","stringify"),Sa=R.f,ba=I.f,ka=oa.f,wa=c.f,Ta=V("symbols"),Ra=V("op-symbols"),Ca=V("string-to-symbol-registry"),Ea=V("symbol-to-string-registry"),Ia=V("wks"),xa=r.QObject,Pa=!xa||!xa.prototype||!xa.prototype.findChild,Aa=o&&i(function(){return 7!=ta(ba({},"a",{get:function(){return ba(this,"a",{value:7}).a}})).a})?function(e,t,n){var r=Sa(ga,t);r&&delete ga[t],ba(e,t,n),r&&e!==ga&&ba(ga,t,r)}:ba,Oa=function(e,t){var n=Ta[e]=ta(_a.prototype);return ma(n,{type:"Symbol",tag:e,description:t}),o||(n.description=t),n},Da=Me?function(e){return"symbol"==typeof e}:function(e){return Object(e)instanceof _a},Na=function(e,t,n){e===ga&&Na(Ra,t,n),C(e);var r=g(t,!0);return C(n),y(Ta,r)?(n.enumerable?(y(e,fa)&&e[fa][r]&&(e[fa][r]=!1),n=ta(n,{enumerable:u(0,!1)})):(y(e,fa)||ba(e,fa,u(1,{})),e[fa][r]=!0),Aa(e,r,n)):ba(e,r,n)},La=function(e,t){C(e);var n=m(t),r=Qo(n).concat(Va(n));return pa(r,function(t){o&&!Ma.call(n,t)||Na(e,t,n[t])}),e},Ma=function(e){var t=g(e,!0),n=wa.call(this,t);return!(this===ga&&y(Ta,t)&&!y(Ra,t))&&(!(n||!y(this,t)||!y(Ta,t)||y(this,fa)&&this[fa][t])||n)},ja=function(e,t){var n=m(e),r=g(t,!0);if(n!==ga||!y(Ta,r)||y(Ra,r)){var i=Sa(n,r);return!i||!y(Ta,r)||y(n,fa)&&n[fa][r]||(i.enumerable=!0),i}},Ua=function(e){var t=ka(m(e)),n=[];return pa(t,function(e){y(Ta,e)||y(z,e)||n.push(e)}),n},Va=function(e){var t=e===ga,n=ka(t?Ra:m(e)),r=[];return pa(n,function(e){!y(Ta,e)||t&&!y(ga,e)||r.push(Ta[e])}),r};if(Le||(te((_a=function(){if(this instanceof _a)throw TypeError("Symbol is not a constructor");var e=arguments.length&&void 0!==arguments[0]?String(arguments[0]):void 0,t=W(e),n=function(e){this===ga&&n.call(Ra,e),y(this,fa)&&y(this[fa],t)&&(this[fa][t]=!1),Aa(this,t,u(1,e))};return o&&Pa&&Aa(ga,t,{configurable:!0,set:n}),Oa(t,e)}).prototype,"toString",function(){return va(this).tag}),te(_a,"withoutSetter",function(e){return Oa(W(e),e)}),c.f=Ma,I.f=Na,R.f=ja,ye.f=oa.f=Ua,Se.f=Va,aa.f=function(e){return Oa(Fe(e),e)},o&&(ba(_a.prototype,"description",{configurable:!0,get:function(){return va(this).description}}),te(ga,"propertyIsEnumerable",Ma,{unsafe:!0}))),Ae({global:!0,wrap:!0,forced:!Le,sham:!Le},{Symbol:_a}),pa(Qo(Ia),function(e){ca(e)}),Ae({target:"Symbol",stat:!0,forced:!Le},{for:function(e){var t=String(e);if(y(Ca,t))return Ca[t];var n=_a(t);return Ca[t]=n,Ea[n]=t,n},keyFor:function(e){if(!Da(e))throw TypeError(e+" is not a symbol");if(y(Ea,e))return Ea[e]},useSetter:function(){Pa=!0},useSimple:function(){Pa=!1}}),Ae({target:"Object",stat:!0,forced:!Le,sham:!o},{create:function(e,t){return void 0===t?ta(e):La(ta(e),t)},defineProperty:Na,defineProperties:La,getOwnPropertyDescriptor:ja}),Ae({target:"Object",stat:!0,forced:!Le},{getOwnPropertyNames:Ua,getOwnPropertySymbols:Va}),Ae({target:"Object",stat:!0,forced:i(function(){Se.f(1)})},{getOwnPropertySymbols:function(e){return Se.f(De(e))}}),ya){var Fa=!Le||i(function(){var e=_a();return"[null]"!=ya([e])||"{}"!=ya({a:e})||"{}"!=ya(Object(e))});Ae({target:"JSON",stat:!0,forced:Fa},{stringify:function(e,t,n){for(var r,i=[e],o=1;arguments.length>o;)i.push(arguments[o++]);if(r=t,(v(t)||void 0!==e)&&!Da(e))return Oe(t)||(t=function(e,t){if("function"==typeof r&&(t=r.call(this,e,t)),!Da(t))return t}),i[1]=t,ya.apply(null,i)}})}_a.prototype[ha]||x(_a.prototype,ha,_a.prototype.valueOf),la(_a,"Symbol"),z[fa]=!0;var Ga=I.f,Wa=r.Symbol;if(o&&"function"==typeof Wa&&(!("description"in Wa.prototype)||void 0!==Wa().description)){var Ba={},Ha=function(){var e=arguments.length<1||void 0===arguments[0]?void 0:String(arguments[0]),t=this instanceof Ha?new Wa(e):void 0===e?Wa():Wa(e);return""===e&&(Ba[t]=!0),t};ke(Ha,Wa);var za=Ha.prototype=Wa.prototype;za.constructor=Ha;var Ja=za.toString,qa="Symbol(test)"==String(Wa("test")),Qa=/^Symbol\((.*)\)[^)]+$/;Ga(za,"description",{configurable:!0,get:function(){var e=v(this)?this.valueOf():this,t=Ja.call(e);if(y(Ba,e))return"";var n=qa?t.slice(7,-1):t.replace(Qa,"$1");return""===n?void 0:n}}),Ae({global:!0,forced:!0},{Symbol:Ha})}ca("iterator");var Ka=it.forEach,$a=Ln("forEach")?function(e){return Ka(this,e,arguments.length>1?arguments[1]:void 0)}:[].forEach;Ae({target:"Array",proto:!0,forced:[].forEach!=$a},{forEach:$a});var Ya=Fe("unscopables"),Xa=Array.prototype;null==Xa[Ya]&&I.f(Xa,Ya,{configurable:!0,value:ta(null)});var Za,es,ts,ns=function(e){Xa[Ya][e]=!0},rs={},is=Fe("iterator"),os=!1;[].keys&&("next"in(ts=[].keys())?(es=Ar(Ar(ts)))!==Object.prototype&&(Za=es):os=!0),null==Za&&(Za={}),y(Za,is)||x(Za,is,function(){return this});var as={IteratorPrototype:Za,BUGGY_SAFARI_ITERATORS:os},ss=as.IteratorPrototype,cs=function(){return this},us=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,n={};try{(e=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set).call(n,[]),t=n instanceof Array}catch(r){}return function(n,r){return C(n),function(e){if(!v(e)&&null!==e)throw TypeError("Can't set "+String(e)+" as a prototype")}(r),t?e.call(n,r):n.__proto__=r,n}}():void 0),ds=as.IteratorPrototype,ls=as.BUGGY_SAFARI_ITERATORS,ps=Fe("iterator"),fs=function(){return this},hs=function(e,t,n,r,i,o,a){!function(e,t,n){var r=t+" Iterator";e.prototype=ta(ss,{next:u(1,n)}),la(e,r,!1),rs[r]=cs}(n,t,r);var s,c,d,l=function(e){if(e===i&&v)return v;if(!ls&&e in h)return h[e];switch(e){case"keys":case"values":case"entries":return function(){return new n(this,e)}}return function(){return new n(this)}},p=t+" Iterator",f=!1,h=e.prototype,m=h[ps]||h["@@iterator"]||i&&h[i],v=!ls&&m||l(i),g="Array"==t&&h.entries||m;if(g&&(s=Ar(g.call(new e)),ds!==Object.prototype&&s.next&&(Ar(s)!==ds&&(us?us(s,ds):"function"!=typeof s[ps]&&x(s,ps,fs)),la(s,p,!0))),"values"==i&&m&&"values"!==m.name&&(f=!0,v=function(){return m.call(this)}),h[ps]!==v&&x(h,ps,v),rs[t]=v,i)if(c={values:l("values"),keys:o?v:l("keys"),entries:l("entries")},a)for(d in c)!ls&&!f&&d in h||te(h,d,c[d]);else Ae({target:t,proto:!0,forced:ls||f},c);return c},ms=ee.set,vs=ee.getterFor("Array Iterator"),gs=hs(Array,"Array",function(e,t){ms(this,{type:"Array Iterator",target:m(e),index:0,kind:t})},function(){var e=vs(this),t=e.target,n=e.kind,r=e.index++;return!t||r>=t.length?(e.target=void 0,{value:void 0,done:!0}):"keys"==n?{value:r,done:!1}:"values"==n?{value:t[r],done:!1}:{value:[r,t[r]],done:!1}},"values");rs.Arguments=rs.Array,ns("keys"),ns("values"),ns("entries");var _s=!i(function(){return Object.isExtensible(Object.preventExtensions({}))}),ys=t(function(e){var t=I.f,n=W("meta"),r=0,i=Object.isExtensible||function(){return!0},o=function(e){t(e,n,{value:{objectID:"O"+ ++r,weakData:{}}})},a=e.exports={REQUIRED:!1,fastKey:function(e,t){if(!v(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!y(e,n)){if(!i(e))return"F";if(!t)return"E";o(e)}return e[n].objectID},getWeakData:function(e,t){if(!y(e,n)){if(!i(e))return!0;if(!t)return!1;o(e)}return e[n].weakData},onFreeze:function(e){return _s&&a.REQUIRED&&i(e)&&!y(e,n)&&o(e),e}};z[n]=!0}),Ss=(ys.REQUIRED,ys.fastKey,ys.getWeakData,ys.onFreeze,Fe("iterator")),bs=Array.prototype,ks=Fe("iterator"),ws=function(e,t,n,r){try{return r?t(C(n)[0],n[1]):t(n)}catch(o){var i=e.return;throw void 0!==i&&C(i.call(e)),o}},Ts=t(function(e){var t=function(e,t){this.stopped=e,this.result=t};(e.exports=function(e,n,r,i,o){var a,s,c,u,d,l,p,f,h=tt(n,r,i?2:1);if(o)a=e;else{if("function"!=typeof(s=function(e){if(null!=e)return e[ks]||e["@@iterator"]||rs[jr(e)]}(e)))throw TypeError("Target is not iterable");if(void 0!==(f=s)&&(rs.Array===f||bs[Ss]===f)){for(c=0,u=ue(e.length);u>c;c++)if((d=i?h(C(p=e[c])[0],p[1]):h(e[c]))&&d instanceof t)return d;return new t(!1)}a=s.call(e)}for(l=a.next;!(p=l.call(a)).done;)if("object"==typeof(d=ws(a,h,p.value,i))&&d&&d instanceof t)return d;return new t(!1)}).stop=function(e){return new t(!0,e)}}),Rs=function(e,t,n){if(!(e instanceof t))throw TypeError("Incorrect "+(n?n+" ":"")+"invocation");return e},Cs=Fe("iterator"),Es=!1;try{var Is=0,xs={next:function(){return{done:!!Is++}},return:function(){Es=!0}};xs[Cs]=function(){return this},Array.from(xs,function(){throw 2})}catch(Rh){}var Ps=function(e,t){if(!t&&!Es)return!1;var n=!1;try{var r={};r[Cs]=function(){return{next:function(){return{done:n=!0}}}},e(r)}catch(Rh){}return n},As=function(e,t,n){var r,i;return us&&"function"==typeof(r=t.constructor)&&r!==n&&v(i=r.prototype)&&i!==n.prototype&&us(e,i),e},Os=function(e,t,n){for(var r in t)te(e,r,t[r],n);return e},Ds=Fe("species"),Ns=function(e){var t=ie(e),n=I.f;o&&t&&!t[Ds]&&n(t,Ds,{configurable:!0,get:function(){return this}})},Ls=I.f,Ms=ys.fastKey,js=ee.set,Us=ee.getterFor,Vs=(function(e,t,n){var o=-1!==e.indexOf("Map"),a=-1!==e.indexOf("Weak"),s=o?"set":"add",c=r[e],u=c&&c.prototype,d=c,l={},p=function(e){var t=u[e];te(u,e,"add"==e?function(e){return t.call(this,0===e?0:e),this}:"delete"==e?function(e){return!(a&&!v(e))&&t.call(this,0===e?0:e)}:"get"==e?function(e){return a&&!v(e)?void 0:t.call(this,0===e?0:e)}:"has"==e?function(e){return!(a&&!v(e))&&t.call(this,0===e?0:e)}:function(e,n){return t.call(this,0===e?0:e,n),this})};if(xe(e,"function"!=typeof c||!(a||u.forEach&&!i(function(){(new c).entries().next()}))))d=n.getConstructor(t,e,o,s),ys.REQUIRED=!0;else if(xe(e,!0)){var f=new d,h=f[s](a?{}:-0,1)!=f,m=i(function(){f.has(1)}),g=Ps(function(e){new c(e)}),_=!a&&i(function(){for(var e=new c,t=5;t--;)e[s](t,t);return!e.has(-0)});g||((d=t(function(t,n){Rs(t,d,e);var r=As(new c,t,d);return null!=n&&Ts(n,r[s],r,o),r})).prototype=u,u.constructor=d),(m||_)&&(p("delete"),p("has"),o&&p("get")),(_||h)&&p(s),a&&u.clear&&delete u.clear}l[e]=d,Ae({global:!0,forced:d!=c},l),la(d,e),a||n.setStrong(d,e,o)}("Map",function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}},{getConstructor:function(e,t,n,r){var i=e(function(e,a){Rs(e,i,t),js(e,{type:t,index:ta(null),first:void 0,last:void 0,size:0}),o||(e.size=0),null!=a&&Ts(a,e[r],e,n)}),a=Us(t),s=function(e,t,n){var r,i,s=a(e),u=c(e,t);return u?u.value=n:(s.last=u={index:i=Ms(t,!0),key:t,value:n,previous:r=s.last,next:void 0,removed:!1},s.first||(s.first=u),r&&(r.next=u),o?s.size++:e.size++,"F"!==i&&(s.index[i]=u)),e},c=function(e,t){var n,r=a(e),i=Ms(t);if("F"!==i)return r.index[i];for(n=r.first;n;n=n.next)if(n.key==t)return n};return Os(i.prototype,{clear:function(){for(var e=a(this),t=e.index,n=e.first;n;)n.removed=!0,n.previous&&(n.previous=n.previous.next=void 0),delete t[n.index],n=n.next;e.first=e.last=void 0,o?e.size=0:this.size=0},delete:function(e){var t=a(this),n=c(this,e);if(n){var r=n.next,i=n.previous;delete t.index[n.index],n.removed=!0,i&&(i.next=r),r&&(r.previous=i),t.first==n&&(t.first=r),t.last==n&&(t.last=i),o?t.size--:this.size--}return!!n},forEach:function(e){for(var t,n=a(this),r=tt(e,arguments.length>1?arguments[1]:void 0,3);t=t?t.next:n.first;)for(r(t.value,t.key,this);t&&t.removed;)t=t.previous},has:function(e){return!!c(this,e)}}),Os(i.prototype,n?{get:function(e){var t=c(this,e);return t&&t.value},set:function(e,t){return s(this,0===e?0:e,t)}}:{add:function(e){return s(this,e=0===e?0:e,e)}}),o&&Ls(i.prototype,"size",{get:function(){return a(this).size}}),i},setStrong:function(e,t,n){var r=t+" Iterator",i=Us(t),o=Us(r);hs(e,t,function(e,t){js(this,{type:r,target:e,state:i(e),kind:t,last:void 0})},function(){for(var e=o(this),t=e.kind,n=e.last;n&&n.removed;)n=n.previous;return e.target&&(e.last=n=n?n.next:e.state.first)?"keys"==t?{value:n.key,done:!1}:"values"==t?{value:n.value,done:!1}:{value:[n.key,n.value],done:!1}:(e.target=void 0,{value:void 0,done:!0})},n?"entries":"values",!n,!0),Ns(t)}}),"\t\n\v\f\r                　\u2028\u2029\ufeff"),Fs="["+Vs+"]",Gs=RegExp("^"+Fs+Fs+"*"),Ws=RegExp(Fs+Fs+"*$"),Bs=function(e){return function(t){var n=String(h(t));return 1&e&&(n=n.replace(Gs,"")),2&e&&(n=n.replace(Ws,"")),n}},Hs={start:Bs(1),end:Bs(2),trim:Bs(3)},zs=ye.f,Js=R.f,qs=I.f,Qs=Hs.trim,Ks=r.Number,$s=Ks.prototype,Ys="Number"==l(ta($s)),Xs=function(e){var t,n,r,i,o,a,s,c,u=g(e,!1);if("string"==typeof u&&u.length>2)if(43===(t=(u=Qs(u)).charCodeAt(0))||45===t){if(88===(n=u.charCodeAt(2))||120===n)return NaN}else if(48===t){switch(u.charCodeAt(1)){case 66:case 98:r=2,i=49;break;case 79:case 111:r=8,i=55;break;default:return+u}for(a=(o=u.slice(2)).length,s=0;s<a;s++)if((c=o.charCodeAt(s))<48||c>i)return NaN;return parseInt(o,r)}return+u};if(xe("Number",!Ks(" 0o1")||!Ks("0b1")||Ks("+0x1"))){for(var Zs,ec=function(e){var t=arguments.length<1?0:e,n=this;return n instanceof ec&&(Ys?i(function(){$s.valueOf.call(n)}):"Number"!=l(n))?As(new Ks(Xs(t)),n,ec):Xs(t)},tc=o?zs(Ks):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),nc=0;tc.length>nc;nc++)y(Ks,Zs=tc[nc])&&!y(ec,Zs)&&qs(ec,Zs,Js(Ks,Zs));ec.prototype=$s,$s.constructor=ec,te(r,"Number",ec)}var rc=Math.floor;Ae({target:"Number",stat:!0},{isInteger:function(e){return!v(e)&&isFinite(e)&&rc(e)===e}});var ic=Hs.trim,oc=r.parseInt,ac=/^[+-]?0[Xx]/,sc=8!==oc(Vs+"08")||22!==oc(Vs+"0x16")?function(e,t){var n=ic(String(e));return oc(n,t>>>0||(ac.test(n)?16:10))}:oc;Ae({global:!0,forced:parseInt!=sc},{parseInt:sc});var cc,uc,dc,lc=r.Promise,pc=/(iphone|ipod|ipad).*applewebkit/i.test(Be),fc=r.location,hc=r.setImmediate,mc=r.clearImmediate,vc=r.process,gc=r.MessageChannel,_c=r.Dispatch,yc=0,Sc={},bc=function(e){if(Sc.hasOwnProperty(e)){var t=Sc[e];delete Sc[e],t()}},kc=function(e){return function(){bc(e)}},wc=function(e){bc(e.data)},Tc=function(e){r.postMessage(e+"",fc.protocol+"//"+fc.host)};hc&&mc||(hc=function(e){for(var t=[],n=1;arguments.length>n;)t.push(arguments[n++]);return Sc[++yc]=function(){("function"==typeof e?e:Function(e)).apply(void 0,t)},cc(yc),yc},mc=function(e){delete Sc[e]},"process"==l(vc)?cc=function(e){vc.nextTick(kc(e))}:_c&&_c.now?cc=function(e){_c.now(kc(e))}:gc&&!pc?(dc=(uc=new gc).port2,uc.port1.onmessage=wc,cc=tt(dc.postMessage,dc,1)):!r.addEventListener||"function"!=typeof postMessage||r.importScripts||i(Tc)?cc="onreadystatechange"in k("script")?function(e){$o.appendChild(k("script")).onreadystatechange=function(){$o.removeChild(this),bc(e)}}:function(e){setTimeout(kc(e),0)}:(cc=Tc,r.addEventListener("message",wc,!1)));var Rc,Cc,Ec,Ic,xc,Pc,Ac,Oc,Dc={set:hc,clear:mc},Nc=R.f,Lc=Dc.set,Mc=r.MutationObserver||r.WebKitMutationObserver,jc=r.process,Uc=r.Promise,Vc="process"==l(jc),Fc=Nc(r,"queueMicrotask"),Gc=Fc&&Fc.value;Gc||(Rc=function(){var e,t;for(Vc&&(e=jc.domain)&&e.exit();Cc;){t=Cc.fn,Cc=Cc.next;try{t()}catch(Rh){throw Cc?Ic():Ec=void 0,Rh}}Ec=void 0,e&&e.enter()},Vc?Ic=function(){jc.nextTick(Rc)}:Mc&&!pc?(xc=!0,Pc=document.createTextNode(""),new Mc(Rc).observe(Pc,{characterData:!0}),Ic=function(){Pc.data=xc=!xc}):Uc&&Uc.resolve?(Ac=Uc.resolve(void 0),Oc=Ac.then,Ic=function(){Oc.call(Ac,Rc)}):Ic=function(){Lc.call(r,Rc)});var Wc,Bc,Hc,zc,Jc=Gc||function(e){var t={fn:e,next:void 0};Ec&&(Ec.next=t),Cc||(Cc=t,Ic()),Ec=t},qc=function(e){var t,n;this.promise=new e(function(e,r){if(void 0!==t||void 0!==n)throw TypeError("Bad Promise constructor");t=e,n=r}),this.resolve=et(t),this.reject=et(n)},Qc={f:function(e){return new qc(e)}},Kc=function(e,t){if(C(e),v(t)&&t.constructor===e)return t;var n=Qc.f(e);return(0,n.resolve)(t),n.promise},$c=function(e){try{return{error:!1,value:e()}}catch(Rh){return{error:!0,value:Rh}}},Yc=Dc.set,Xc=Fe("species"),Zc="Promise",eu=ee.get,tu=ee.set,nu=ee.getterFor(Zc),ru=lc,iu=r.TypeError,ou=r.document,au=r.process,su=ie("fetch"),cu=Qc.f,uu=cu,du="process"==l(au),lu=!!(ou&&ou.createEvent&&r.dispatchEvent),pu=xe(Zc,function(){if(!(M(ru)!==String(ru))){if(66===qe)return!0;if(!du&&"function"!=typeof PromiseRejectionEvent)return!0}if(qe>=51&&/native code/.test(ru))return!1;var e=ru.resolve(1),t=function(e){e(function(){},function(){})};return(e.constructor={})[Xc]=t,!(e.then(function(){})instanceof t)}),fu=pu||!Ps(function(e){ru.all(e).catch(function(){})}),hu=function(e){var t;return!(!v(e)||"function"!=typeof(t=e.then))&&t},mu=function(e,t,n){if(!t.notified){t.notified=!0;var r=t.reactions;Jc(function(){for(var i=t.value,o=1==t.state,a=0;r.length>a;){var s,c,u,d=r[a++],l=o?d.ok:d.fail,p=d.resolve,f=d.reject,h=d.domain;try{l?(o||(2===t.rejection&&yu(e,t),t.rejection=1),!0===l?s=i:(h&&h.enter(),s=l(i),h&&(h.exit(),u=!0)),s===d.promise?f(iu("Promise-chain cycle")):(c=hu(s))?c.call(s,p,f):p(s)):f(i)}catch(Rh){h&&!u&&h.exit(),f(Rh)}}t.reactions=[],t.notified=!1,n&&!t.rejection&&gu(e,t)})}},vu=function(e,t,n){var i,o;lu?((i=ou.createEvent("Event")).promise=t,i.reason=n,i.initEvent(e,!1,!0),r.dispatchEvent(i)):i={promise:t,reason:n},(o=r["on"+e])?o(i):"unhandledrejection"===e&&function(e,t){var n=r.console;n&&n.error&&(1===arguments.length?n.error(e):n.error(e,t))}("Unhandled promise rejection",n)},gu=function(e,t){Yc.call(r,function(){var n,r=t.value;if(_u(t)&&(n=$c(function(){du?au.emit("unhandledRejection",r,e):vu("unhandledrejection",e,r)}),t.rejection=du||_u(t)?2:1,n.error))throw n.value})},_u=function(e){return 1!==e.rejection&&!e.parent},yu=function(e,t){Yc.call(r,function(){du?au.emit("rejectionHandled",e):vu("rejectionhandled",e,t.value)})},Su=function(e,t,n,r){return function(i){e(t,n,i,r)}},bu=function(e,t,n,r){t.done||(t.done=!0,r&&(t=r),t.value=n,t.state=2,mu(e,t,!0))},ku=function(e,t,n,r){if(!t.done){t.done=!0,r&&(t=r);try{if(e===n)throw iu("Promise can't be resolved itself");var i=hu(n);i?Jc(function(){var r={done:!1};try{i.call(n,Su(ku,e,r,t),Su(bu,e,r,t))}catch(Rh){bu(e,r,Rh,t)}}):(t.value=n,t.state=1,mu(e,t,!1))}catch(Rh){bu(e,{done:!1},Rh,t)}}};pu&&(ru=function(e){Rs(this,ru,Zc),et(e),Wc.call(this);var t=eu(this);try{e(Su(ku,this,t),Su(bu,this,t))}catch(Rh){bu(this,t,Rh)}},(Wc=function(e){tu(this,{type:Zc,done:!1,notified:!1,parent:!1,reactions:[],rejection:!1,state:0,value:void 0})}).prototype=Os(ru.prototype,{then:function(e,t){var n=nu(this),r=cu(si(this,ru));return r.ok="function"!=typeof e||e,r.fail="function"==typeof t&&t,r.domain=du?au.domain:void 0,n.parent=!0,n.reactions.push(r),0!=n.state&&mu(this,n,!1),r.promise},catch:function(e){return this.then(void 0,e)}}),Bc=function(){var e=new Wc,t=eu(e);this.promise=e,this.resolve=Su(ku,e,t),this.reject=Su(bu,e,t)},Qc.f=cu=function(e){return e===ru||e===Hc?new Bc(e):uu(e)},"function"==typeof lc&&(zc=lc.prototype.then,te(lc.prototype,"then",function(e,t){var n=this;return new ru(function(e,t){zc.call(n,e,t)}).then(e,t)},{unsafe:!0}),"function"==typeof su&&Ae({global:!0,enumerable:!0,forced:!0},{fetch:function(e){return Kc(ru,su.apply(r,arguments))}}))),Ae({global:!0,wrap:!0,forced:pu},{Promise:ru}),la(ru,Zc,!1),Ns(Zc),Hc=ie(Zc),Ae({target:Zc,stat:!0,forced:pu},{reject:function(e){var t=cu(this);return t.reject.call(void 0,e),t.promise}}),Ae({target:Zc,stat:!0,forced:pu},{resolve:function(e){return Kc(this,e)}}),Ae({target:Zc,stat:!0,forced:fu},{all:function(e){var t=this,n=cu(t),r=n.resolve,i=n.reject,o=$c(function(){var n=et(t.resolve),o=[],a=0,s=1;Ts(e,function(e){var c=a++,u=!1;o.push(void 0),s++,n.call(t,e).then(function(e){u||(u=!0,o[c]=e,--s||r(o))},i)}),--s||r(o)});return o.error&&i(o.value),n.promise},race:function(e){var t=this,n=cu(t),r=n.reject,i=$c(function(){var i=et(t.resolve);Ts(e,function(e){i.call(t,e).then(n.resolve,r)})});return i.error&&r(i.value),n.promise}});var wu=Kr.charAt,Tu=ee.set,Ru=ee.getterFor("String Iterator");hs(String,"String",function(e){Tu(this,{type:"String Iterator",string:String(e),index:0})},function(){var e,t=Ru(this),n=t.string,r=t.index;return r>=n.length?{value:void 0,done:!0}:(e=wu(n,r),t.index+=e.length,{value:e,done:!1})}),qr("match",1,function(e,t,n){return[function(t){var n=h(this),r=null==t?void 0:t[e];return void 0!==r?r.call(t,n):new RegExp(t)[e](String(n))},function(e){var r=n(t,e,this);if(r.done)return r.value;var i=C(e),o=String(this);if(!i.global)return Xr(i,o);var a=i.unicode;i.lastIndex=0;for(var s,c=[],u=0;null!==(s=Xr(i,o));){var d=String(s[0]);c[u]=d,""===d&&(i.lastIndex=Yr(o,ue(i.lastIndex),a)),u++}return 0===u?null:c}]});var Cu,Eu=function(e){if(oi(e))throw TypeError("The method doesn't accept regular expressions");return e},Iu=Fe("match"),xu=function(e){var t=/./;try{"/./"[e](t)}catch(n){try{return t[Iu]=!1,"/./"[e](t)}catch(r){}}return!1},Pu=R.f,Au="".startsWith,Ou=Math.min,Du=xu("startsWith"),Nu=!(Du||(Cu=Pu(String.prototype,"startsWith"),!Cu||Cu.writable));Ae({target:"String",proto:!0,forced:!Nu&&!Du},{startsWith:function(e){var t=String(h(this));Eu(e);var n=ue(Ou(arguments.length>1?arguments[1]:void 0,t.length)),r=String(e);return Au?Au.call(t,r,n):t.slice(n,n+r.length)===r}});var Lu={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0};for(var Mu in Lu){var ju=r[Mu],Uu=ju&&ju.prototype;if(Uu&&Uu.forEach!==$a)try{x(Uu,"forEach",$a)}catch(Rh){Uu.forEach=$a}}var Vu=Fe("iterator"),Fu=Fe("toStringTag"),Gu=gs.values;for(var Wu in Lu){var Bu=r[Wu],Hu=Bu&&Bu.prototype;if(Hu){if(Hu[Vu]!==Gu)try{x(Hu,Vu,Gu)}catch(Rh){Hu[Vu]=Gu}if(Hu[Fu]||x(Hu,Fu,Wu),Lu[Wu])for(var zu in gs)if(Hu[zu]!==gs[zu])try{x(Hu,zu,gs[zu])}catch(Rh){Hu[zu]=gs[zu]}}}var Ju=i(function(){Qo(1)});Ae({target:"Object",stat:!0,forced:Ju},{keys:function(e){return Qo(De(e))}});var qu=c.f,Qu=function(e){return function(t){for(var n,r=m(t),i=Qo(r),a=i.length,s=0,c=[];a>s;)n=i[s++],o&&!qu.call(r,n)||c.push(e?[n,r[n]]:r[n]);return c}},Ku={entries:Qu(!0),values:Qu(!1)}.values;Ae({target:"Object",stat:!0},{values:function(e){return Ku(e)}});var $u=I.f,Yu=ye.f,Xu=ee.set,Zu=Fe("match"),ed=r.RegExp,td=ed.prototype,nd=/a/g,rd=/a/g,id=new ed(nd)!==nd,od=Zn.UNSUPPORTED_Y;if(o&&xe("RegExp",!id||od||i(function(){return rd[Zu]=!1,ed(nd)!=nd||ed(rd)==rd||"/a/i"!=ed(nd,"i")}))){for(var ad=function(e,t){var n,r=this instanceof ad,i=oi(e),o=void 0===t;if(!r&&i&&e.constructor===ad&&o)return e;id?i&&!o&&(e=e.source):e instanceof ad&&(o&&(t=Yn.call(e)),e=e.source),od&&(n=!!t&&t.indexOf("y")>-1)&&(t=t.replace(/y/g,""));var a=As(id?new ed(e,t):ed(e,t),r?this:td,ad);return od&&n&&Xu(a,{sticky:n}),a},sd=function(e){e in ad||$u(ad,e,{configurable:!0,get:function(){return ed[e]},set:function(t){ed[e]=t}})},cd=Yu(ed),ud=0;cd.length>ud;)sd(cd[ud++]);td.constructor=ad,ad.prototype=td,te(r,"RegExp",ad)}Ns("RegExp");var dd=Object.is||function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t};qr("search",1,function(e,t,n){return[function(t){var n=h(this),r=null==t?void 0:t[e];return void 0!==r?r.call(t,n):new RegExp(t)[e](String(n))},function(e){var r=n(t,e,this);if(r.done)return r.value;var i=C(e),o=String(this),a=i.lastIndex;dd(a,0)||(i.lastIndex=0);var s=Xr(i,o);return dd(i.lastIndex,a)||(i.lastIndex=a),null===s?-1:s.index}]});var ld=Hs.trim,pd=r.parseFloat,fd=1/pd(Vs+"-0")!=-Infinity?function(e){var t=ld(String(e)),n=pd(t);return 0===n&&"-"==t.charAt(0)?-0:n}:pd;Ae({global:!0,forced:parseFloat!=fd},{parseFloat:fd});var hd=window.navigator&&window.navigator.userAgent||"",md=/AppleWebKit\/([\d.]+)/i.exec(hd),vd=(md&&parseFloat(md.pop()),/iPad/i.test(hd)),gd=/iPhone/i.test(hd)&&!vd,_d=/iPod/i.test(hd),yd=gd||vd||_d,Sd=(function(){var e=hd.match(/OS (\d+)_/i);e&&e[1]&&e[1]}(),/Android/i.test(hd)),bd=function(){var e=hd.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(!e)return null;var t=e[1]&&parseFloat(e[1]),n=e[2]&&parseFloat(e[2]);return t&&n?parseFloat(e[1]+"."+e[2]):t||null}(),kd=(Sd&&/webkit/i.test(hd),/Firefox/i.test(hd)),wd=/Edge/i.test(hd),Td=!wd&&/Chrome/i.test(hd),Rd=function(){var e=hd.match(/Chrome\/(\d+)/);return e&&e[1]?parseFloat(e[1]):null}(),Cd=function(){var e=hd.match(/Chrome\/([\d.]+)/);return e&&e[1]?e[1]:null}(),Ed=(/MSIE\s8\.0/.test(hd),function(){var e=/MSIE\s(\d+)\.\d/.exec(hd),t=e&&parseFloat(e[1]);!t&&/Trident\/7.0/i.test(hd)&&/rv:11.0/.test(hd)&&(t=11)}(),/Safari/i.test(hd)&&!Td&&!Sd&&!wd),Id=function(){var e=hd.match(/Version\/([\d.]+)/);return e&&e[1]?e[1]:null}(),xd=/TBS\/\d+/i.test(hd),Pd=(function(){var e=hd.match(/TBS\/(\d+)/i);if(e&&e[1])e[1]}(),!xd&&/MQQBrowser\/\d+/i.test(hd)),Ad=(!xd&&/ QQBrowser\/\d+/i.test(hd),/(micromessenger|webbrowser)/i.test(hd),/Windows/i.test(hd)),Od=/MAC OS X/i.test(hd),Dd=/Linux/i.test(hd),Nd=(/MicroMessenger/i.test(hd),/UCBrowser/i.test(hd)),Ld=/MiuiBrowser/i.test(hd),Md=/HuaweiBrowser/i.test(hd),jd=Td?"Chrome/"+Cd:Ed?"Safari/"+Id:"NotSupportedBrowser",Ud=function(e){var t=window.location.search.match(new RegExp("(\\?|&)"+e+"=([^&]*)(&|$)"));return t?decodeURIComponent(t[2]):""},Vd=function(){return Ud("trtc_env")},Fd=function(e){var t=e,n=Ud("trtc_env");return n&&(t="wss://"+n+".rtc.qq.com:8687"),t};var Gd=function(e){if(!e||"object"!==lt(e)||"[object Object]"!=Object.prototype.toString.call(e))return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;var n=Object.prototype.hasOwnProperty.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&Function.prototype.toString.call(n)===Function.prototype.toString.call(Object)},Wd=Object.prototype.hasOwnProperty;function Bd(e){if(null==e)return!0;if("boolean"==typeof e)return!1;if("number"==typeof e)return 0===e;if("string"==typeof e)return 0===e.length;if("function"==typeof e)return 0===e.length;if(Array.isArray(e))return 0===e.length;if(e instanceof Error)return""===e.message;if(Gd(e))switch(Object.prototype.toString.call(e)){case"[object File]":case"[object Map]":case"[object Set]":return 0===e.size;case"[object Object]":for(var t in e)if(Wd.call(e,t))return!1;return!0}return!1}var Hd,zd=t(function(e){var t=Object.prototype.hasOwnProperty,n="~";function r(){}function i(e,t,n){this.fn=e,this.context=t,this.once=n||!1}function o(e,t,r,o,a){if("function"!=typeof r)throw new TypeError("The listener must be a function");var s=new i(r,o||e,a),c=n?n+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],s]:e._events[c].push(s):(e._events[c]=s,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new r:delete e._events[t]}function s(){this._events=new r,this._eventsCount=0}Object.create&&(r.prototype=Object.create(null),(new r).__proto__||(n=!1)),s.prototype.eventNames=function(){var e,r,i=[];if(0===this._eventsCount)return i;for(r in e=this._events)t.call(e,r)&&i.push(n?r.slice(1):r);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},s.prototype.listeners=function(e){var t=n?n+e:e,r=this._events[t];if(!r)return[];if(r.fn)return[r.fn];for(var i=0,o=r.length,a=new Array(o);i<o;i++)a[i]=r[i].fn;return a},s.prototype.listenerCount=function(e){var t=n?n+e:e,r=this._events[t];return r?r.fn?1:r.length:0},s.prototype.emit=function(e,t,r,i,o,a){var s=n?n+e:e;if(!this._events[s])return!1;var c,u,d=this._events[s],l=arguments.length;if(d.fn){switch(d.once&&this.removeListener(e,d.fn,void 0,!0),l){case 1:return d.fn.call(d.context),!0;case 2:return d.fn.call(d.context,t),!0;case 3:return d.fn.call(d.context,t,r),!0;case 4:return d.fn.call(d.context,t,r,i),!0;case 5:return d.fn.call(d.context,t,r,i,o),!0;case 6:return d.fn.call(d.context,t,r,i,o,a),!0}for(u=1,c=new Array(l-1);u<l;u++)c[u-1]=arguments[u];d.fn.apply(d.context,c)}else{var p,f=d.length;for(u=0;u<f;u++)switch(d[u].once&&this.removeListener(e,d[u].fn,void 0,!0),l){case 1:d[u].fn.call(d[u].context);break;case 2:d[u].fn.call(d[u].context,t);break;case 3:d[u].fn.call(d[u].context,t,r);break;case 4:d[u].fn.call(d[u].context,t,r,i);break;default:if(!c)for(p=1,c=new Array(l-1);p<l;p++)c[p-1]=arguments[p];d[u].fn.apply(d[u].context,c)}}return!0},s.prototype.on=function(e,t,n){return o(this,e,t,n,!1)},s.prototype.once=function(e,t,n){return o(this,e,t,n,!0)},s.prototype.removeListener=function(e,t,r,i){var o=n?n+e:e;if(!this._events[o])return this;if(!t)return a(this,o),this;var s=this._events[o];if(s.fn)s.fn!==t||i&&!s.once||r&&s.context!==r||a(this,o);else{for(var c=0,u=[],d=s.length;c<d;c++)(s[c].fn!==t||i&&!s[c].once||r&&s[c].context!==r)&&u.push(s[c]);u.length?this._events[o]=1===u.length?u[0]:u:a(this,o)}return this},s.prototype.removeAllListeners=function(e){var t;return e?(t=n?n+e:e,this._events[t]&&a(this,t)):(this._events=new r,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=n,s.EventEmitter=s,e.exports=s}),Jd="connection-state-changed",qd="error",Qd={DISCONNECTED:"DISCONNECTED",CONNECTING:"CONNECTING",RECONNECTING:"RECONNECTING",CONNECTED:"CONNECTED"},Kd={NEW_REMOTE_SDP:2,NEW_ICE_CANDIDATE:4,CLINET_BANNDED:8,CHANNEL_SETUP_SUCCESS:19,CHANNEL_SETUP_FAILED:80,CHANNEL_RECONNECT_RESULT:514,JOIN_ROOM_RESULT:20,PEER_JOIN:16,PEER_LEAVE:18,UPDATE_REMOTE_SDP:48,UPDATE_AUDIO_SSRC:50,UPDATE_VIDEO_SSRC:52,UPLINK_NETWORK_STATS:22,UPDATE_REMOTE_MUTE_STAT:23,CLOSE_PEER_ACK:10,SUBSCRIBE_ACK:26},$d={NEW_REMOTE_SDP:"new-remote-sdp",NEW_ICE_CANDIDATE:"new-ice-candidate",CLINET_BANNDED:"client-banned",CHANNEL_SETUP_SUCCESS:"channel-setup-success",CHANNEL_SETUP_FAILED:"channel-setup-failed",CHANNEL_RECONNECT_RESULT:"channel-reconnect-result",JOIN_ROOM_RESULT:"join-room-result",PEER_JOIN:"peer-join",PEER_LEAVE:"peer-leave",UPDATE_REMOTE_SDP:"update-remote-sdp",UPDATE_AUDIO_SSRC:"update-audio-ssrc",UPDATE_VIDEO_SSRC:"update-video-ssrc",UPLINK_NETWORK_STATS:"uplink-network-stats",UPDATE_REMOTE_MUTE_STAT:"update-remote-mute-stat",CLOSE_PEER_ACK:"close-peer-ack",SUBSCRIBE_ACK:"subscribe-ack",REQUEST_REBUILD_SESSION:"request-rebuild-session"},Yd="on_peer_sdp",Xd="on_reexchange_sdp",Zd="on_create_room",el="on_close_peer",tl="on_quality_report",nl="on_rebuild_session",rl="on_change_av_state",il="on_subscribe",ol="on_constraints_config",al="on_test_ws_broken",sl="on_recreate_down_peerc",cl={INVALID_PARAMETER:4096,INVALID_OPERATION:4097,NOT_SUPPORTED:4098,DEVICE_NOT_FOUND:4099,SIGNAL_CHANNEL_SETUP_FAILED:16385,SIGNAL_CHANNEL_ERROR:16386,ICE_TRANSPORT_ERROR:16387,JOIN_ROOM_FAILED:16388,CREATE_OFFER_FAILED:16389,CLIENT_BANNED:16448,SERVER_TIMEOUT:16449,SUBSCRIPTION_TIMEOUT:16450,PLAY_NOT_ALLOWED:16451,UNKNOWN:65535},ul=function(e){return e===cl.INVALID_PARAMETER?"INVALID_PARAMETER":e===cl.INVALID_OPERATION?"INVALID_OPERATION":e===cl.CLIENT_BANNED?"CLIENT_BANNED":e===cl.CREATE_OFFER_FAILED?"CREATE_OFFER_FAILED":e===cl.JOIN_ROOM_FAILED?"JOIN_ROOM_FAILED":e===cl.ICE_TRANSPORT_ERROR?"ICE_TRANSPORT_ERROR":e===cl.PLAY_NOT_ALLOWED?"PLAY_NOT_ALLOWED":e===cl.NOT_SUPPORTED?"NOT_SUPPORTED":e===cl.SIGNAL_CHANNEL_SETUP_FAILED?"SIGNAL_CHANNEL_SETUP_FAILED":e===cl.SIGNAL_CHANNEL_ERROR?"SIGNAL_CHANNEL_ERROR":e===cl.SERVER_TIMEOUT?"SERVER_TIMEOUT":e===cl.SUBSCRIPTION_TIMEOUT?"SUBSCRIPTION_TIMEOUT":"UNKNOWN"},dl=function(e){function t(e){var n,r=e.message,i=e.code,o=void 0===i?cl.UNKNOWN:i,a=e.extraCode,s=void 0===a?0:a;return ht(this,t),(n=Tt(this,St(t).call(this,r+" <".concat(ul(o)," 0x").concat(o.toString(16),"> https://trtc-1252463788.file.myqcloud.com/web/docs/module-ErrorCode.html")))).code_=o,n.extraCode_=s,n.name="RtcError",n.message_=r,n}return yt(t,wt(Error)),vt(t,[{key:"getCode",value:function(){return this.code_}},{key:"getExtraCode",value:function(){return this.extraCode_}}]),t}(),ll={sdkAppId:"",userId:"",version:"",env:"qcloud",browserVersion:"",ua:""},pl=function(e){ll.sdkAppId="".concat(e.sdkAppId),ll.version="".concat(e.version),ll.env=e.env,ll.userId=e.userId,ll.browserVersion=e.browserVersion,ll.ua=e.ua},fl=function(e){var t=JSON.stringify({timestamp:Co(),sdkAppId:ll.sdkAppId,userId:ll.userId,version:ll.version,log:e});wo.post("https://yun.tim.qq.com/v5/AVQualityReportSvc/C2S?sdkappid=1&cmdtype=jssdk_log",t).then(function(e){}).catch(function(e){console.log(e)})},hl=function(e){"qcloud"===ll.env&&function(e){var t="stat-".concat(e.eventType,"-").concat(e.result);"delta-join"!==e.eventType&&"delta-leave"!==e.eventType&&"delta-publish"!==e.eventType||(t="".concat(e.eventType,":").concat(e.delta)),fl(t),"join"===e.eventType&&"failed"===e.result&&(t="stat-".concat(e.eventType,"-").concat(e.result,"-").concat(e.code),fl(t))}(e);var t=_t({},e,ll);void 0===t.code&&(t.code="failed"===t.result?cl.UNKNOWN:0),wo.post("https://yun.tim.qq.com/v5/AVQualityReportSvc/C2S?sdkappid=1&cmdtype=jssdk_event",JSON.stringify(t)).then(function(e){}).catch(function(e){console.log(e)})},ml=32768,vl=32769,gl=32770,_l=32771,yl=32772,Sl=32773,bl=32774,kl=32775,wl=32776,Tl=32777,Rl=32778,Cl=32779,El=32780,Il=32781,xl=32782,Pl=32783,Al=32784,Ol=32785,Dl=32786,Nl=32787,Ll=32788,Ml=32789,jl=32790,Ul=32791,Vl=32792,Fl=32793,Gl=new Map,Wl=function(e,t){var n=Gl.get(e);n||(Gl.set(e,[]),n=Gl.get(e)),n.push(t)},Bl=function(e){var t=Gl.get(e);return t?Gl.delete(e):t=[],t},Hl=3,zl=function(){function e(t){ht(this,e),this.sdkAppId_=t.sdkAppId,this.userId_=t.userId,this.userSig_=t.userSig,this.url_=t.url,this.backupUrl_=t.backupUrl,this.version_=t.version,this.urlWithParam_="".concat(this.url_,"?sdkAppid=").concat(this.sdkAppId_,"&identifier=").concat(this.userId_,"&userSig=").concat(this.userSig_),this.backupUrlWithParam_="".concat(this.backupUrl_,"?sdkAppid=").concat(this.sdkAppId_,"&identifier=").concat(this.userId_,"&userSig=").concat(this.userSig_),this.isConnected_=!1,this.isConnecting_=!1,this.tryCount_=Hl,this.socketInUse_=null,this.socket_=null,this.backupSocket_=null,this.backupTimer_=-1,this.reconnectTimer_=-1,this.signalInfo_={},this.currentState_=Qd.DISCONNECTED,this.emitter_=new zd}return vt(e,[{key:"connect",value:function(){var e=this;sr.info("connect to url: ".concat(this.urlWithParam_)),this.emitter_.emit(Jd,{prevState:this.currentState_,state:Qd.CONNECTING}),this.currentState_=Qd.CONNECTING,this.socket_=new WebSocket(this.urlWithParam_),this.bindSocket(this.socket_),this.backupTimer_=setTimeout(function(){e.isConnected_||(sr.info("trying to connect to backupUrl"),e.tryConnectBackup())},1e3)}},{key:"tryConnectBackup",value:function(){this.backupSocket_||(sr.debug("try to connect to url: ".concat(this.backupUrlWithParam_)),this.backupSocket_=new WebSocket(this.backupUrlWithParam_),this.bindSocket(this.backupSocket_))}},{key:"bindSocket",value:function(e){e.onopen=this.onopen.bind(this),e.onclose=this.onclose.bind(this),e.onerror=this.onerror.bind(this),e.onmessage=this.onmessage.bind(this)}},{key:"unbindSocket",value:function(e){e.onopen=function(){},e.onclose=function(){},e.onerror=function(){},e.onmessage=function(){}}},{key:"unbindAndCloseSocket",value:function(e){if("main"===e){if(this.socket_){this.unbindSocket(this.socket_);try{this.socket_.close(1e3)}catch(t){}this.socket_=null}}else if(this.backupSocket_){this.unbindSocket(this.backupSocket_);try{this.backupSocket_.close(1e3)}catch(t){}this.backupSocket_=null}}},{key:"clearBackupTimer",value:function(){-1!==this.backupTimer_&&(clearTimeout(this.backupTimer_),this.backupTimer_=-1)}},{key:"onopen",value:function(e){if(!this.isConnected_){this.isConnecting_?hl({eventType:"websocketReconnectionState",result:"success"}):hl({eventType:"websocketConnectionState",result:"success"}),this.isConnected_=!0,this.isConnecting_=!1,this.clearBackupTimer(),e.target===this.socket_?(this.unbindAndCloseSocket("backup"),this.socketInUse_=this.socket_):(this.unbindAndCloseSocket("main"),this.socketInUse_=this.backupSocket_);var t=e.target.url;sr.info("[".concat(this.userId_,"] websocket[").concat(t,"] is connected")),this.emitter_.emit(Jd,{prevState:this.currentState_,state:Qd.CONNECTED}),this.currentState_=Qd.CONNECTED}}},{key:"onclose",value:function(e){var t=e.target.url,n=e.target===this.socketInUse_;sr.info("[".concat(this.userId_,"] websocket[").concat(t," InUse: ").concat(n,"] is closed with code: ").concat(e.code)),e.target===this.socketInUse_&&(this.isConnected_=!1,this.addSignalEvent("disconnected"),e.wasClean&&1e3===e.code?(this.emitter_.emit(Jd,{prevState:this.currentState_,state:Qd.DISCONNECTED}),this.currentState_=Qd.DISCONNECTED):(sr.warn("[".concat(this.userId_,"] onclose code:").concat(e.code," reason:").concat(e.reason)),this.isConnecting_&&this.tryCount_<=0?(this.isConnecting_=!1,this.tryCount_=Hl,sr.error("[".concat(this.userId_,"] reconnect failed")),this.emitter_.emit(Jd,{prevState:this.currentState_,state:Qd.DISCONNECTED}),this.currentState_=Qd.DISCONNECTED,hl({eventType:"websocketReconnectionState",result:"failed",code:cl.SIGNAL_CHANNEL_ERROR}),this.emitter_.emit(qd,new dl({code:cl.SIGNAL_CHANNEL_ERROR,message:"WebSocket reconnection failed"}))):(sr.warn("close current websocket and schedule a reconnect timeout"),this.socketInUse_.onclose=function(){},this.socketInUse_.close(4011),this.socket_=this.backupSocket_=this.socketInUse_=null,this.reconnect(t))))}},{key:"onerror",value:function(e){var t=e.target.url;sr.error("[".concat(this.userId_,"] websocket[").concat(t,"] error observed")),this.isConnected_?e.target===this.socketInUse_&&(this.isConnected_=!1,this.unbindAndCloseSocket("main"),this.unbindAndCloseSocket("backup"),this.socketInUse_=null,this.addSignalEvent("disconnected"),this.emitter_.emit(qd,new dl({code:cl.SIGNAL_CHANNEL_ERROR,message:"WebSocket error observed"}))):(e.target==this.socket_?(this.unbindAndCloseSocket("main"),this.isConnecting_||(sr.warn("main socket error observed, try connecting to backup domain"),this.tryConnectBackup())):this.unbindAndCloseSocket("backup"),null===this.socket_&&null===this.backupSocket_&&(this.isConnecting_?hl({eventType:"websocketReconnectionState",result:"failed",code:cl.SIGNAL_CHANNEL_ERROR}):hl({eventType:"websocketConnectionState",result:"failed",code:cl.SIGNAL_CHANNEL_ERROR}),sr.error("SignalChannel setup failed  https://trtc-1252463788.file.myqcloud.com/web/docs/tutorial-10-error-code-tips.html"),this.emitter_.emit(qd,new dl({code:cl.SIGNAL_CHANNEL_ERROR,message:"failed to connect to remote server via websocket"}))))}},{key:"onmessage",value:function(e){var t=this;if(this.isConnected_){var n=JSON.parse(e.data),r=n.cmd,i=n.content,o=Object.values(Kd),a=Object.keys(Kd)[o.indexOf(r)],s=$d[a];if(r!==Kd.UPDATE_REMOTE_MUTE_STAT&&r!==Kd.UPLINK_NETWORK_STATS){var c=e.target==this.socket_?this.url_:this.backupUrl_;if(sr.debug("[".concat(this.userId_,"] websocket[").concat(c,"] received message: ").concat(e.data)),sr.info("[".concat(this.userId_,"] Received event: [ ").concat(s||"unknown cmd: "+r," ]")),(s===$d.UPDATE_REMOTE_SDP||s===$d.UPDATE_AUDIO_SSRC||s===$d.UPDATE_VIDEO_SSRC)&&i.offersdp)try{var u=JSON.parse(i.offersdp),d=u.audiossrc,l=u.videossrc,p=u.rtxssrc;sr.info("[".concat(this.userId_,"] ssrc info in offersdp: [ audiossrc: ").concat(d," videossrc: ").concat(l," rtxssrc: ").concat(p," ]"))}catch(m){}}switch(r){case Kd.CHANNEL_SETUP_SUCCESS:this.signalInfo_.relayIp=i.relayip,this.signalInfo_.relayInnerIp=i.innerip,this.signalInfo_.signalIp=i.signalip,this.signalInfo_.localIp=i.localip,this.signalInfo_.dataPort=i.dataport,this.signalInfo_.stunPort=i.stunport,this.signalInfo_.checkSigSeq=i.checkSigSeq,this.signalInfo_.socketId=i.socketid,this.signalInfo_.tinyId=i.tinyid,this.signalInfo_.openId=i.openid,this.signalInfo_.stunPortList=i.stunportList,!i.stunportList||i.stunportList.length<=0?this.signalInfo_.stunServers="stun:"+i.relayip+":"+i.stunport:(this.signalInfo_.stunServers=[],i.stunportList.forEach(function(e){var n="stun:"+i.relayip+":"+e;t.signalInfo_.stunServers.push(n)})),i.cgiurl&&(this.signalInfo_.logCgiUrl=i.cgiurl),i.svrTime&&function(e){To=e-(new Date).getTime();var t=new Date;t.setTime(e),sr.info("baseTime from server: "+t+" offset: "+To)}(i.svrTime),sr.info("ChannelSetup Success: signalIp:".concat(i.signalip," relayIp:").concat(i.relayip," clientIp:").concat(i.localip," checkSigSeq:").concat(i.checkSigSeq)),1===(i.rc||0)?this.emitter_.emit($d.REQUEST_REBUILD_SESSION,{signalInfo:this.signalInfo_}):this.emitter_.emit(s,{signalInfo:this.signalInfo_}),this.addSignalEvent("connected");break;case Kd.CHANNEL_SETUP_FAILED:var f="sdkAppId invalid",h="";void 0!==i.errorCode&&(f=i.errorCode,h=i.errorMsg),this.emitter_.emit(qd,new dl({code:cl.SIGNAL_CHANNEL_SETUP_FAILED,message:"SignalChannel setup failure: ('errorCode': ".concat(f,", 'errorMsg': ").concat(h," })")}));break;case Kd.CHANNEL_RECONNECT_RESULT:-1===i.result&&(sr.error("SignalChannel reconnect failed for the user may have left the room from the perspective of remote relay server   https://trtc-1252463788.file.myqcloud.com/web/docs/tutorial-10-error-code-tips.html"),hl({eventType:"websocketReconnectionState",result:"failed",code:cl.SIGNAL_CHANNEL_ERROR}),this.addSignalEvent("disconnected"),this.emitter_.emit(qd,new dl({code:cl.SIGNAL_CHANNEL_ERROR,message:"SignalChannel reconnect failed"})));break;default:this.emitter_.emit(s,{data:n})}}}},{key:"addSignalEvent",value:function(e){Wl(this.userId_,"connected"===e?{eventId:Ul,eventDesc:"signal channel is connected",timestamp:Ro(),userId:this.userId_,tinyId:this.signalInfo_.tinyId}:{eventId:jl,eventDesc:"signal channel is disconnected",timestamp:Ro(),userId:this.userId_,tinyId:this.signalInfo_.tinyId})}},{key:"reconnect",value:function(e){var t=this;this.reconnectTimer_=setTimeout(function(){t.isConnecting_=!0,t.tryCount_-=1;var n=e;Bd(t.signalInfo_)||-1!==e.indexOf("&rc=1")||(n=e+"&iip="+t.signalInfo_.relayInnerIp+"&dp="+t.signalInfo_.dataPort+"&oip="+t.signalInfo_.relayIp+"&sp="+t.signalInfo_.stunPort+"&rc=1"),t.emitter_.emit(Jd,{prevState:t.currentState_,state:Qd.RECONNECTING}),t.currentState_=Qd.RECONNECTING,t.socket_=new WebSocket(n),t.bindSocket(t.socket_)},3e3)}},{key:"isConnected",value:function(){return this.isConnected_}},{key:"send",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;if(this.isConnected_){var r=this.createSendMessage(e);r.data=t,void 0!==n&&(r.srctinyid=n),this.socketInUse_.send(JSON.stringify(r))}}},{key:"sendWithReport",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2?arguments[2]:void 0;if(this.isConnected_){var r=this.createSendMessage(e);r.data=t,r.report=n,this.socketInUse_.send(JSON.stringify(r))}}},{key:"createSendMessage",value:function(e){return{tag_key:e,data:"",openid:this.userId_,tinyid:this.signalInfo_.tinyId,version:this.version_,ua:navigator.userAgent}}},{key:"close",value:function(){sr.info("close SignalChannel"),-1!==this.reconnectTimer_&&(clearTimeout(this.reconnectTimer_),this.reconnectTimer_=-1),this.clearBackupTimer(),this.isConnected_=!1,this.socketInUse_=null,this.unbindAndCloseSocket("main"),this.unbindAndCloseSocket("backup")}},{key:"on",value:function(e,t,n){this.emitter_.on(e,t,n)}},{key:"removeListener",value:function(e,t,n){this.emitter_.removeListener(e,t,n)}},{key:"once",value:function(e,t){this.emitter_.once(e,t)}}]),e}();window.addEventListener("message",function(e){e.origin==window.location.origin&&function(e){if("PermissionDeniedError"==e){if(Hd)return Hd("PermissionDeniedError");throw new Error("PermissionDeniedError")}e.sourceId&&Hd&&Hd(e.sourceId,!0===e.canRequestAudioTrack)}(e.data)});window.InstallTrigger;var Jl=!!window.opera||navigator.userAgent.indexOf(" OPR/")>=0,ql=(window.chrome,function(){var e=!1;return["RTCPeerConnection","webkitRTCPeerConnection","RTCIceGatherer"].forEach(function(t){t in window&&(e=!0)}),e}),Ql=function(){var e=ft(regeneratorRuntime.mark(function e(){var t,n;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=new RTCPeerConnection,"",n=!1,e.prev=3,e.next=6,t.createOffer({offerToReceiveAudio:1,offerToReceiveVideo:1});case 6:-1!==e.sent.sdp.toLowerCase().indexOf("h264")&&(n=!0),e.next=12;break;case 10:e.prev=10,e.t0=e.catch(3);case 12:return t.close(),e.abrupt("return",n);case 14:case"end":return e.stop()}},e,null,[[3,10]])}));return function(){return e.apply(this,arguments)}}(),Kl=function(){if("undefined"==typeof RTCRtpTransceiver)return!1;if(!("currentDirection"in RTCRtpTransceiver.prototype))return!1;var e=new RTCPeerConnection,t=!1;try{e.addTransceiver("audio"),t=!0}catch(n){}return e.close(),t},$l=function(){var e=!1,t=Ed;return navigator.mediaDevices&&navigator.mediaDevices.getDisplayMedia&&!t&&(e=!0),e},Yl=!1,Xl=!0,Zl=function(){var e=ft(regeneratorRuntime.mark(function e(){var t,n,r;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!Yl){e.next=2;break}return e.abrupt("return",Xl);case 2:if(!(t=!(kd||Pd||Nd||Ld||Md))){e.next=14;break}if(n=ql(),r=!1,!n){e.next=10;break}return e.next=9,Ql();case 9:r=e.sent;case 10:(t=n&&r)||sr.error("".concat(navigator.userAgent," isWebRTCSupported: ").concat(n," isH264Supported: ").concat(r)),e.next=15;break;case 14:sr.error("blacklist: ".concat(navigator.userAgent));case 15:return Yl=!0,Xl=t,e.abrupt("return",t);case 18:case"end":return e.stop()}},e)}));return function(){return e.apply(this,arguments)}}(),ep=function(){return!Yl||Xl},tp=function(){return"RTCPeerConnection"in window&&"getReceivers"in window.RTCPeerConnection.prototype},np=function(){return"RTCPeerConnection"in window&&"getSenders"in window.RTCPeerConnection.prototype};Zl();var rp=Math.min,ip=[].lastIndexOf,op=!!ip&&1/[1].lastIndexOf(1,-0)<0,ap=Ln("lastIndexOf"),sp=op||ap?function(e){if(op)return ip.apply(this,arguments)||0;var t=m(this),n=ue(t.length),r=n-1;for(arguments.length>1&&(r=rp(r,se(arguments[1]))),r<0&&(r=n+r);r>=0;r--)if(r in t&&t[r]===e)return r||0;return-1}:ip;Ae({target:"Array",proto:!0,forced:sp!==[].lastIndexOf},{lastIndexOf:sp});var cp={Android:function(){return navigator.userAgent.match(/Android/i)},BlackBerry:function(){return navigator.userAgent.match(/BlackBerry|BB10/i)},iOS:function(){return navigator.userAgent.match(/iPhone|iPad|iPod/i)},Opera:function(){return navigator.userAgent.match(/Opera Mini/i)},Windows:function(){return navigator.userAgent.match(/IEMobile/i)},any:function(){return cp.Android()||cp.BlackBerry()||cp.iOS()||cp.Opera()||cp.Windows()},getOsName:function(){var e="Unknown OS";return cp.Android()&&(e="Android"),cp.BlackBerry()&&(e="BlackBerry"),cp.iOS()&&(e="iOS"),cp.Opera()&&(e="Opera Mini"),cp.Windows()&&(e="Windows"),e}};function up(){return cp.any()?{osName:cp.getOsName()}:function(){for(var e,t=navigator.appVersion,n=navigator.userAgent,r="-",i=[{s:"Chrome OS",r:/CrOS/},{s:"Windows 10",r:/(Windows 10.0|Windows NT 10.0)/},{s:"Windows 8.1",r:/(Windows 8.1|Windows NT 6.3)/},{s:"Windows 8",r:/(Windows 8|Windows NT 6.2)/},{s:"Windows 7",r:/(Windows 7|Windows NT 6.1)/},{s:"Windows Vista",r:/Windows NT 6.0/},{s:"Windows Server 2003",r:/Windows NT 5.2/},{s:"Windows XP",r:/(Windows NT 5.1|Windows XP)/},{s:"Windows 2000",r:/(Windows NT 5.0|Windows 2000)/},{s:"Windows ME",r:/(Win 9x 4.90|Windows ME)/},{s:"Windows 98",r:/(Windows 98|Win98)/},{s:"Windows 95",r:/(Windows 95|Win95|Windows_95)/},{s:"Windows NT 4.0",r:/(Windows NT 4.0|WinNT4.0|WinNT|Windows NT)/},{s:"Windows CE",r:/Windows CE/},{s:"Windows 3.11",r:/Win16/},{s:"Android",r:/Android/},{s:"Open BSD",r:/OpenBSD/},{s:"Sun OS",r:/SunOS/},{s:"Linux",r:/(Linux|X11)/},{s:"iOS",r:/(iPhone|iPad|iPod)/},{s:"Mac OS X",r:/Mac OS X/},{s:"Mac OS",r:/(MacPPC|MacIntel|Mac_PowerPC|Macintosh)/},{s:"QNX",r:/QNX/},{s:"UNIX",r:/UNIX/},{s:"BeOS",r:/BeOS/},{s:"OS/2",r:/OS\/2/},{s:"Search Bot",r:/(nuhk|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask Jeeves\/Teoma|ia_archiver)/}],o=0;e=i[o];o++)if(e.r.test(n)){r=e.s;break}var a="-";switch(/Windows/.test(r)&&(/Windows (.*)/.test(r)&&(a=/Windows (.*)/.exec(r)[1]),r="Windows"),r){case"Mac OS X":/Mac OS X (10[._\d]+)/.test(n)&&(a=/Mac OS X (10[._\d]+)/.exec(n)[1]);break;case"Android":/Android ([._\d]+)/.test(n)&&(a=/Android ([._\d]+)/.exec(n)[1]);break;case"iOS":/OS (\d+)_(\d+)_?(\d+)?/.test(n)&&(a=(a=/OS (\d+)_(\d+)_?(\d+)?/.exec(t))[1]+"."+a[2]+"."+(0|a[3]))}return{osName:r+a}}()}/Android|webOS|iPhone|iPad|iPod|BB10|BlackBerry|IEMobile|Opera Mini|Mobile|mobile/i.test(navigator.userAgent||"");var dp=!(-1===navigator.userAgent.indexOf("Edge")||!navigator.msSaveOrOpenBlob&&!navigator.msSaveBlob),lp=!!window.opera||navigator.userAgent.indexOf(" OPR/")>=0,pp=navigator.userAgent.toLowerCase().indexOf("firefox")>-1&&"netscape"in window&&/ rv:/.test(navigator.userAgent),fp=/^((?!chrome|android).)*safari/i.test(navigator.userAgent),hp=!!window.chrome&&!lp,mp="undefined"!=typeof document&&!!document.documentMode&&!dp;function vp(){navigator.appVersion;var e,t,n,r=navigator.userAgent,i=navigator.appName,o=""+parseFloat(navigator.appVersion),a=parseInt(navigator.appVersion,10);if(lp){i="Opera";try{a=(o=navigator.userAgent.split("OPR/")[1].split(" ")[0]).split(".")[0]}catch(s){o="0.0.0.0",a=0}}else mp?((t=r.indexOf("rv:"))>0?o=r.substring(t+3):(t=r.indexOf("MSIE"),o=r.substring(t+5)),i="IE"):hp?(t=r.indexOf("Chrome"),i="Chrome",o=r.substring(t+7)):fp?-1!==r.indexOf("CriOS")?(t=r.indexOf("CriOS"),i="Chrome",o=r.substring(t+6)):-1!==r.indexOf("FxiOS")?(t=r.indexOf("FxiOS"),i="Firefox",o=r.substring(t+6)):(t=r.indexOf("Safari"),i="Safari",o=r.substring(t+7),-1!==(t=r.indexOf("Version"))&&(o=r.substring(t+8)),-1!==navigator.userAgent.indexOf("Version/")&&(o=navigator.userAgent.split("Version/")[1].split(" ")[0])):pp?(t=r.indexOf("Firefox"),i="Firefox",o=r.substring(t+8)):(e=r.lastIndexOf(" ")+1)<(t=r.lastIndexOf("/"))&&(i=r.substring(e,t),o=r.substring(t+1),i.toLowerCase()===i.toUpperCase()&&(i=navigator.appName));return dp&&(i="Edge",o=navigator.userAgent.split("Edge/")[1]),-1!==(n=o.search(/[; )]/))&&(o=o.substring(0,n)),a=parseInt(""+o,10),isNaN(a)&&(o=""+parseFloat(navigator.appVersion),a=parseInt(navigator.appVersion,10)),{fullVersion:o,version:a,name:i}}function gp(){var e="";screen.width&&(e+=(screen.width?screen.width:"")+" x "+(screen.height?screen.height:""));return e}function _p(){var e=!1;return["RTCPeerConnection","webkitRTCPeerConnection","RTCIceGatherer"].forEach(function(t){t in window&&(e=!0)}),e}function yp(){var e=!1;return navigator.getUserMedia?e=!0:navigator.mediaDevices&&navigator.mediaDevices.getUserMedia&&(e=!0),e}function Sp(){return bp.apply(this,arguments)}function bp(){return(bp=ft(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(navigator.mediaDevices){e.next=3;break}return console.log("navigator.mediaDevices is undefined"),e.abrupt("return",[]);case 3:return e.next=5,navigator.mediaDevices.enumerateDevices();case 5:return t=e.sent,e.abrupt("return",t.filter(function(e){return"audioinput"===e.kind}));case 7:case"end":return e.stop()}},e)}))).apply(this,arguments)}function kp(){return wp.apply(this,arguments)}function wp(){return(wp=ft(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(navigator.mediaDevices){e.next=3;break}return console.log("navigator.mediaDevices is undefined"),e.abrupt("return",[]);case 3:return e.next=5,navigator.mediaDevices.enumerateDevices();case 5:return t=e.sent,e.abrupt("return",t.filter(function(e){return"videoinput"===e.kind}));case 7:case"end":return e.stop()}},e)}))).apply(this,arguments)}var Tp=1,Rp=2,Cp=20,Ep=21,Ip="5Y2wZK8nANNAoVw6dSAHVjNxrD1ObBM2kBPV",xp="224d130c-7b5c-415b-aaa2-79c2eb5a6df2",Pp=2,Ap=7,Op=0,Dp="join",Np="publish",Lp="unpublish",Mp="local_stream_initialize",jp="subscribe",Up="unsubscibe",Vp="uplink_connection",Fp="uplink_reconnection",Gp="downlink_connection",Wp="downlink_reconnection",Bp="signal_connection",Hp="signal_reconnection",zp="update_stream",Jp={CONNECTING:"connecting",CONNECTED:"connected",DISCONNECTED:"disconnected"},qp=it.find,Qp=!0;"find"in[]&&Array(1).find(function(){Qp=!1}),Ae({target:"Array",proto:!0,forced:Qp},{find:function(e){return qp(this,e,arguments.length>1?arguments[1]:void 0)}}),ns("find");var Kp=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})},$p="stream-added",Yp="stream-removed",Xp="stream-updated",Zp="stream-published",ef="stream-subscribed",tf="error",nf="connection-state-changed",rf="stream-added",of="stream-removed",af="stream-updated",sf="stream-subscribed",cf="connection-state-changed",uf="peer-join",df="peer-leave",lf="mute-audio",pf="mute-video",ff="unmute-audio",hf="unmute-video",mf="client-banned",vf="network-quality",gf="error",_f="player-state-changed",yf="screen-sharing-stopped",Sf="player-state-changed",bf=1..toFixed,kf=Math.floor,wf=function(e,t,n){return 0===t?n:t%2==1?wf(e,t-1,n*e):wf(e*e,t/2,n)},Tf=bf&&("0.000"!==8e-5.toFixed(3)||"1"!==.9.toFixed(0)||"1.25"!==1.255.toFixed(2)||"1000000000000000128"!==(0xde0b6b3a7640080).toFixed(0))||!i(function(){bf.call({})});Ae({target:"Number",proto:!0,forced:Tf},{toFixed:function(e){var t,n,r,i,o=function(e){if("number"!=typeof e&&"Number"!=l(e))throw TypeError("Incorrect invocation");return+e}(this),a=se(e),s=[0,0,0,0,0,0],c="",u="0",d=function(e,t){for(var n=-1,r=t;++n<6;)r+=e*s[n],s[n]=r%1e7,r=kf(r/1e7)},p=function(e){for(var t=6,n=0;--t>=0;)n+=s[t],s[t]=kf(n/e),n=n%e*1e7},f=function(){for(var e=6,t="";--e>=0;)if(""!==t||0===e||0!==s[e]){var n=String(s[e]);t=""===t?n:t+hr.call("0",7-n.length)+n}return t};if(a<0||a>20)throw RangeError("Incorrect fraction digits");if(o!=o)return"NaN";if(o<=-1e21||o>=1e21)return String(o);if(o<0&&(c="-",o=-o),o>1e-21)if(n=(t=function(e){for(var t=0,n=e;n>=4096;)t+=12,n/=4096;for(;n>=2;)t+=1,n/=2;return t}(o*wf(2,69,1))-69)<0?o*wf(2,-t,1):o/wf(2,t,1),n*=4503599627370496,(t=52-t)>0){for(d(0,n),r=a;r>=7;)d(1e7,0),r-=7;for(d(wf(10,r,1),0),r=t-1;r>=23;)p(1<<23),r-=23;p(1<<r),d(1,1),p(2),u=f()}else d(0,n),d(1<<-t,0),u=f()+hr.call("0",a);return u=a>0?c+((i=u.length)<=a?"0."+hr.call("0",a-i)+u:u.slice(0,i-a)+"."+u.slice(i-a)):c+u}});var Rf=window.AudioContext||window.webkitAudioContext,Cf=null,Ef=function(){function e(){var t=this;ht(this,e),Cf||(Cf=new Rf),this.context_=Cf,this.instant_=0,this.slow_=0,this.clip_=0,this.script_=this.context_.createScriptProcessor(2048,1,1),this.script_.onaudioprocess=function(e){var n,r=e.inputBuffer.getChannelData(0),i=0,o=0;for(n=0;n<r.length;++n)i+=r[n]*r[n],Math.abs(r[n])>.99&&(o+=1);t.instant_=Math.sqrt(i/r.length),t.slow_=.95*t.slow_+.05*t.instant_,t.clip_=o/r.length}}return vt(e,[{key:"connectToSource",value:function(e,t){try{var n=new MediaStream;n.addTrack(e),this.mic_=this.context_.createMediaStreamSource(n),this.mic_.connect(this.script_),this.script_.connect(this.context_.destination),void 0!==t&&t(null)}catch(r){sr.error("soundMeter connectoToSource error: "+r),void 0!==t&&t(r)}}},{key:"stop",value:function(){this.mic_.disconnect(),this.script_.disconnect()}},{key:"getVolume",value:function(){return this.instant_.toFixed(2)}}]),e}(),If=function(){function e(t){ht(this,e),this.stream_=t.stream,this.userId_=t.stream.getUserId(),this.log_=this.stream_.getIDLogger(),this.track_=t.track,this.div_=t.div,this.muted_=t.muted,this.outputDeviceId_=t.outputDeviceId,this.volume_=t.volume,this.element_=null,this.emitter_=new zd,this.state_="NONE",this.soundMeter_=null}var t,n,r;return vt(e,[{key:"play",value:(r=ft(regeneratorRuntime.mark(function e(){var t,n,r;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if((t=new MediaStream).addTrack(this.track_),(n=document.createElement("audio")).srcObject=t,n.muted=this.muted_,n.setAttribute("id","audio_".concat(this.stream_.getId())),n.setAttribute("autoplay","autoplay"),n.setAttribute("playsinline","playsinline"),this.div_.appendChild(n),!this.outputDeviceId_){e.next=12;break}return e.next=12,n.setSinkId(this.outputDeviceId_);case 12:return this.element_=n,this.setVolume(this.volume_),this.handleEvents(),e.prev=15,e.next=18,n.play();case 18:e.next=26;break;case 20:if(e.prev=20,e.t0=e.catch(15),this.log_.warn("<audio> play() error: "+e.t0),!(r=e.t0.toString()+" <audio>").startsWith("NotAllowedError")){e.next=26;break}throw new dl({code:cl.PLAY_NOT_ALLOWED,message:r});case 26:case"end":return e.stop()}},e,this,[[15,20]])})),function(){return r.apply(this,arguments)})},{key:"handleEvents",value:function(){var e=this;this.element_.addEventListener("playing",function(t){e.log_.info("stream - audio player is starting playing"),e.state_="PLAYING",e.emitter_.emit(Sf,{state:e.state_,reason:"playing"})}),this.element_.addEventListener("ended",function(t){e.log_.info("stream - audio player is ended"),"STOPPED"!==e.state_&&(e.state_="STOPPED",e.emitter_.emit(Sf,{state:e.state_,reason:"ended"}))}),this.element_.addEventListener("pause",function(t){e.log_.info("stream - audio player is paused"),e.state_="PAUSED",e.emitter_.emit(Sf,{state:e.state_,reason:"pause"})}),this.track_.addEventListener("ended",function(t){e.log_.info("stream - audio player track is ended"),"STOPPED"!==e.state_&&(e.state_="STOPPED",e.emitter_.emit(Sf,{state:e.state_,reason:"ended"}))}),this.track_.addEventListener("mute",function(t){e.log_.info("stream - audio track is muted"),"PAUSED"!==e.state_&&(e.state_="PAUSED",e.emitter_.emit(Sf,{state:e.state_,reason:"mute"}))}),this.track_.addEventListener("unmute",function(t){e.log_.info("stream - audio track is unmuted"),"PAUSED"===e.state_&&(e.state_="PLAYING",e.emitter_.emit(Sf,{state:e.state_,reason:"unmute"}))})}},{key:"setSinkId",value:(n=ft(regeneratorRuntime.mark(function e(t){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.outputDeviceId_===t){e.next=4;break}return e.next=3,this.element_.setSinkId(t);case 3:this.outputDeviceId_=t;case 4:case"end":return e.stop()}},e,this)})),function(e){return n.apply(this,arguments)})},{key:"setVolume",value:function(e){this.log_.info("stream - audioElement setVolume to : ".concat(e)),this.element_.volume=e}},{key:"getAudioLevel",value:function(){return this.soundMeter_||(this.soundMeter_=new Ef,this.soundMeter_.connectToSource(this.track_)),this.soundMeter_.getVolume()}},{key:"stop",value:function(){this.div_.removeChild(this.element_),this.element_.srcObject=null,this.soundMeter_&&(this.soundMeter_.stop(),this.soundMeter_=null)}},{key:"resume",value:(t=ft(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.element_.play();case 3:e.next=11;break;case 5:if(e.prev=5,e.t0=e.catch(0),this.log_.warn("<audio> play() error: "+e.t0),!(t=e.t0.toString()+" <audio>").startsWith("NotAllowedError")){e.next=11;break}throw new dl({code:cl.PLAY_NOT_ALLOWED,message:t});case 11:case"end":return e.stop()}},e,this,[[0,5]])})),function(){return t.apply(this,arguments)})},{key:"on",value:function(e,t){this.emitter_.on(e,t)}}]),e}(),xf=function(){function e(t){ht(this,e),this.stream_=t.stream,this.userId_=t.stream.getUserId(),this.log_=this.stream_.getIDLogger(),this.track_=t.track,this.div_=t.div,this.muted_=t.muted,this.objectFit_=t.objectFit,this.mirror_=t.mirror,this.element_=null,this.emitter_=new zd,this.state_="NONE"}var t,n;return vt(e,[{key:"play",value:(n=ft(regeneratorRuntime.mark(function e(){var t,n,r,i;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return(t=new MediaStream).addTrack(this.track_),(n=document.createElement("video")).srcObject=t,n.muted=!0,r=0,this.mirror_&&(r="180deg"),n.setAttribute("id","video_".concat(this.stream_.getId())),n.setAttribute("style","width: 100%; height: 100%; position: absolute; transform: rotateY(".concat(r,"); object-fit: ").concat(this.objectFit_)),n.setAttribute("autoplay","autoplay"),n.setAttribute("playsinline","playsinline"),this.div_.appendChild(n),this.element_=n,this.handleEvents(),e.prev=14,e.next=17,n.play();case 17:e.next=25;break;case 19:if(e.prev=19,e.t0=e.catch(14),this.log_.warn("<video> play() error: "+e.t0),!(i=e.t0.toString()+" <video>").startsWith("NotAllowedError")){e.next=25;break}throw new dl({code:cl.PLAY_NOT_ALLOWED,message:i});case 25:case"end":return e.stop()}},e,this,[[14,19]])})),function(){return n.apply(this,arguments)})},{key:"handleEvents",value:function(){var e=this;this.element_.addEventListener("playing",function(t){e.log_.info("stream - video player is starting playing"),e.state_="PLAYING",e.emitter_.emit(Sf,{state:e.state_,reason:"playing"})}),this.element_.addEventListener("ended",function(t){e.log_.info("stream - video player is ended"),"STOPPED"!==e.state_&&(e.state_="STOPPED",e.emitter_.emit(Sf,{state:e.state_,reason:"ended"}))}),this.element_.addEventListener("pause",function(t){e.log_.info("stream - video player is paused"),e.state_="PAUSED",e.emitter_.emit(Sf,{state:e.state_,reason:"pause"})}),this.track_.addEventListener("ended",function(t){e.log_.info("stream - video player track is ended"),"STOPPED"!==e.state_&&(e.state_="STOPPED",e.emitter_.emit(Sf,{state:e.state_,reason:"ended"}))}),this.track_.addEventListener("mute",function(t){e.log_.info("stream - video track is muted"),"PAUSED"!==e.state_&&(e.state_="PAUSED",e.emitter_.emit(Sf,{state:e.state_,reason:"mute"}))}),this.track_.addEventListener("unmute",function(t){e.log_.info("stream - video track is unmuted"),"PAUSED"===e.state_&&(e.state_="PLAYING",e.emitter_.emit(Sf,{state:e.state_,reason:"unmute"}))})}},{key:"stop",value:function(){this.div_.removeChild(this.element_),this.element_.srcObject=null}},{key:"resume",value:(t=ft(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.element_.play();case 3:e.next=11;break;case 5:if(e.prev=5,e.t0=e.catch(0),this.log_.warn("<video> play() error: "+e.t0),!(t=e.t0.toString()+" <video>").startsWith("NotAllowedError")){e.next=11;break}throw new dl({code:cl.PLAY_NOT_ALLOWED,message:t});case 11:case"end":return e.stop()}},e,this,[[0,5]])})),function(){return t.apply(this,arguments)})},{key:"getVideoFrame",value:function(){var e=document.createElement("canvas");return e.width=this.element_.videoWidth,e.height=this.element_.videoHeight,e.getContext("2d").drawImage(this.element_,0,0),e.toDataURL("image/png")}},{key:"on",value:function(e,t){this.emitter_.on(e,t)}}]),e}(),Pf=function(){function e(t){ht(this,e),this.id_=t.id,this.direction_=t.direction,this.type_=t.type,this.directionPrefix_="local"===this.direction_?"":"*"}return vt(e,[{key:"log",value:function(e,t){sr[e]("[".concat(this.directionPrefix_).concat(this.id_,"] ").concat(this.type_," ").concat(t))}},{key:"info",value:function(e){this.log("info",e)}},{key:"debug",value:function(e){this.log("debug",e)}},{key:"warn",value:function(e){this.log("warn",e)}},{key:"error",value:function(e){this.log("error",e)}}]),e}(),Af=function(){function e(t){ht(this,e),this.userId_=t.userId,this.isRemote_=t.isRemote,this.type_=t.type,this.log_=new Pf({id:"s|"+this.userId_,direction:this.isRemote_?"remote":"local",type:this.isRemote_?this.type_:""}),this.mirror_=!1,this.isRemote_||(this.mirror_=!0),void 0!==t.mirror&&(this.mirror_=t.mirror),this.client_=null,void 0!==t.client&&(this.client_=t.client),this.mediaStream_=null,this.div_=null,this.isPlaying_=!1,this.connection_=null,this.hasAudio_=!1,this.hasVideo_=!1,this.audioPlayer_=null,this.videoPlayer_=null,this.muted_=!1,this.objectFit_="cover",this.id_=Kp(),this.audioOutputDeviceId_=0,this.audioVolume_=1,this.emitter_=new zd}var t,n,r,i,o;return vt(e,[{key:"getType",value:function(){return this.type_}},{key:"getIDLogger",value:function(){return this.log_}},{key:"setConnection",value:function(e){this.connection_!==e&&(this.connection_=e)}},{key:"getConnection",value:function(){return this.connection_}},{key:"play",value:(o=ft(regeneratorRuntime.mark(function e(t,n){var r,i;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.isPlaying_){e.next=2;break}throw new dl({code:cl.INVALID_OPERATION,message:"duplicated play() call observed, please stop() firstly"});case 2:if(this.isPlaying_=!0,this.log_.info("stream start to play with options: ".concat(JSON.stringify(n))),(r=document.createElement("div")).setAttribute("id","player_".concat(this.id_)),r.setAttribute("style","width: 100%; height: 100%; position: relative; background-color: black; overflow: hidden;"),i=t,"object"!==lt(t)&&(i=document.getElementById(t)),i.appendChild(r),this.div_=r,this.isRemote_||(this.muted_=!0),n&&void 0!==n.muted&&(this.muted_=n.muted),this.isRemote_&&"auxiliary"===this.getType()&&(this.objectFit_="contain"),n&&void 0!==n.objectFit&&(this.objectFit_=n.objectFit),!this.hasVideo_){e.next=18;break}return e.next=18,this.playVideo();case 18:if(!this.hasAudio_){e.next=21;break}return e.next=21,this.playAudio();case 21:case"end":return e.stop()}},e,this)})),function(e,t){return o.apply(this,arguments)})},{key:"playAudio",value:(i=ft(regeneratorRuntime.mark(function e(){var t,n=this;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=this.getAudioTrack(),this.audioPlayer_||!t){e.next=7;break}return this.log_.info("stream - create AudioPlayer and play"),this.audioPlayer_=new If({stream:this,track:t,div:this.div_,muted:this.muted_,outputDeviceId:this.audioOutputDeviceId_,volume:this.audioVolume_}),this.audioPlayer_.on(Sf,function(e){n.emitter_.emit(_f,{type:"audio",state:e.state,reason:e.reason})}),e.next=7,this.audioPlayer_.play();case 7:case"end":return e.stop()}},e,this)})),function(){return i.apply(this,arguments)})},{key:"playVideo",value:(r=ft(regeneratorRuntime.mark(function e(){var t,n=this;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=this.getVideoTrack(),this.videoPlayer_||!t){e.next=7;break}return this.log_.info("stream - create VideoPlayer and play"),this.videoPlayer_=new xf({stream:this,track:t,div:this.div_,muted:this.muted_,objectFit:this.objectFit_,mirror:this.mirror_}),this.videoPlayer_.on(Sf,function(e){n.emitter_.emit(_f,{type:"video",state:e.state,reason:e.reason})}),e.next=7,this.videoPlayer_.play();case 7:case"end":return e.stop()}},e,this)})),function(){return r.apply(this,arguments)})},{key:"stopAudio",value:function(){this.audioPlayer_&&(this.log_.info("stream - stop AudioPlayer"),this.audioPlayer_.stop(),this.audioPlayer_=null)}},{key:"stopVideo",value:function(){this.videoPlayer_&&(this.log_.info("stream - stop VideoPlayer"),this.videoPlayer_.stop(),this.videoPlayer_=null)}},{key:"restartAudio",value:function(){this.isPlaying_&&(this.stopAudio(),this.playAudio())}},{key:"restartVideo",value:function(){this.isPlaying_&&(this.stopVideo(),this.playVideo())}},{key:"stop",value:function(){this.isPlaying_&&(this.isPlaying_=!1,this.stopAudio(),this.stopVideo(),this.div_.parentNode.removeChild(this.div_))}},{key:"resume",value:(n=ft(regeneratorRuntime.mark(function e(){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.isPlaying_){e.next=2;break}return e.abrupt("return");case 2:if(this.log_.info("stream - resume"),!this.audioPlayer_){e.next=6;break}return e.next=6,this.audioPlayer_.resume();case 6:if(!this.videoPlayer_){e.next=9;break}return e.next=9,this.videoPlayer_.resume();case 9:case"end":return e.stop()}},e,this)})),function(){return n.apply(this,arguments)})},{key:"close",value:function(){this.isPlaying_&&this.stop(),this.mediaStream_&&(this.mediaStream_.preventEvent=1,this.mediaStream_.getTracks().forEach(function(e){e.stop()}),this.mediaStream_=null)}},{key:"muteAudio",value:function(){return this.addRemoteEvent(!0,"audio"),this.doEnableTrack("audio",!1)}},{key:"muteVideo",value:function(){return this.addRemoteEvent(!0,"video"),this.doEnableTrack("video",!1)}},{key:"unmuteAudio",value:function(){return this.addRemoteEvent(!1,"audio"),this.doEnableTrack("audio",!0)}},{key:"unmuteVideo",value:function(){return this.addRemoteEvent(!1,"video"),this.doEnableTrack("video",!0)}},{key:"addRemoteEvent",value:function(e,t){if(this.isRemote_&&this.client_){var n=this.client_.getUserId(),r="".concat(e?"mute":"unmute"," remote ").concat(t);Wl(n,{eventId:"audio"===t?e?Ol:Nl:e?Al:Dl,eventDesc:r,timestamp:(new Date).getTime(),userId:n,tinyId:this.client_.getTinyId(),remoteUserId:this.userId_,remoteTinyId:this.connection_.getTinyId()})}}},{key:"doEnableTrack",value:function(e,t){var n=!1;return"audio"===e?this.mediaStream_.getAudioTracks().forEach(function(e){n=!0,e.enabled=t}):this.mediaStream_.getVideoTracks().forEach(function(e){n=!0,e.enabled=t}),n}},{key:"getId",value:function(){return this.id_}},{key:"getUserId",value:function(){return this.userId_}},{key:"isPlaying",value:function(){return this.isPlaying_}},{key:"setAudioOutput",value:(t=ft(regeneratorRuntime.mark(function e(t){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.audioOutputDeviceId_=t,!this.audioPlayer_){e.next=4;break}return e.next=4,this.audioPlayer_.setSinkId(t);case 4:case"end":return e.stop()}},e,this)})),function(e){return t.apply(this,arguments)})},{key:"setAudioVolume",value:function(e){this.audioVolume_=e,this.log_.info("setAudioVolume to ".concat(e)),this.audioPlayer_&&this.audioPlayer_.setVolume(e)}},{key:"getAudioLevel",value:function(){var e=0;return this.audioPlayer_&&(e=this.audioPlayer_.getAudioLevel()),e}},{key:"hasAudio",value:function(){return this.hasAudio_}},{key:"hasVideo",value:function(){return this.hasVideo_}},{key:"getAudioTrack",value:function(){var e=null;if(this.mediaStream_){var t=this.mediaStream_.getAudioTracks();t.length>0&&(e=t[0])}return e}},{key:"getVideoTrack",value:function(){var e=null;if(this.mediaStream_){var t=this.mediaStream_.getVideoTracks();t.length>0&&(e=t[0])}return e}},{key:"getVideoFrame",value:function(){return this.videoPlayer_?this.videoPlayer_.getVideoFrame():null}},{key:"getMediaStream",value:function(){return this.mediaStream_}},{key:"setMediaStream",value:function(e){e!==this.mediaStream_&&(this.mediaStream_&&this.mediaStream_.getTracks().forEach(function(e){return e.stop()}),this.mediaStream_=e)}},{key:"setHasVideo",value:function(e){this.hasVideo_=e,this.isPlaying_&&(e?(this.log_.info("stream updated, play video"),this.playVideo()):(this.log_.info("stream updated, stop video"),this.stopVideo()))}},{key:"setHasAudio",value:function(e){this.hasAudio_=e,this.isPlaying_&&(e?(this.log_.info("stream updated, play audio"),this.playAudio()):(this.log_.info("stream updated, stop audio"),this.stopAudio()))}},{key:"on",value:function(e,t){this.emitter_.on(e,t)}},{key:"isRemote",value:function(){return this.isRemote_}}]),e}(),Of=function(e){function t(e){var n;ht(this,t);var r=_t({},e,{isRemote:!0,type:e.type});return(n=Tt(this,St(t).call(this,r))).isInSubscriptionCycle_=!1,n}return yt(t,Af),vt(t,[{key:"getType",value:function(){return Rt(St(t.prototype),"getType",this).call(this)}},{key:"setInSubscriptionCycle",value:function(e){this.isInSubscriptionCycle_=e}},{key:"isInSubscriptionCycle",value:function(){return this.isInSubscriptionCycle_}}]),t}(),Df=t(function(e){var t=e.exports={v:[{name:"version",reg:/^(\d*)$/}],o:[{name:"origin",reg:/^(\S*) (\d*) (\d*) (\S*) IP(\d) (\S*)/,names:["username","sessionId","sessionVersion","netType","ipVer","address"],format:"%s %s %d %s IP%d %s"}],s:[{name:"name"}],i:[{name:"description"}],u:[{name:"uri"}],e:[{name:"email"}],p:[{name:"phone"}],z:[{name:"timezones"}],r:[{name:"repeats"}],t:[{name:"timing",reg:/^(\d*) (\d*)/,names:["start","stop"],format:"%d %d"}],c:[{name:"connection",reg:/^IN IP(\d) (\S*)/,names:["version","ip"],format:"IN IP%d %s"}],b:[{push:"bandwidth",reg:/^(TIAS|AS|CT|RR|RS):(\d*)/,names:["type","limit"],format:"%s:%s"}],m:[{reg:/^(\w*) (\d*) ([\w/]*)(?: (.*))?/,names:["type","port","protocol","payloads"],format:"%s %d %s %s"}],a:[{push:"rtp",reg:/^rtpmap:(\d*) ([\w\-.]*)(?:\s*\/(\d*)(?:\s*\/(\S*))?)?/,names:["payload","codec","rate","encoding"],format:function(e){return e.encoding?"rtpmap:%d %s/%s/%s":e.rate?"rtpmap:%d %s/%s":"rtpmap:%d %s"}},{push:"fmtp",reg:/^fmtp:(\d*) ([\S| ]*)/,names:["payload","config"],format:"fmtp:%d %s"},{name:"control",reg:/^control:(.*)/,format:"control:%s"},{name:"rtcp",reg:/^rtcp:(\d*)(?: (\S*) IP(\d) (\S*))?/,names:["port","netType","ipVer","address"],format:function(e){return null!=e.address?"rtcp:%d %s IP%d %s":"rtcp:%d"}},{push:"rtcpFbTrrInt",reg:/^rtcp-fb:(\*|\d*) trr-int (\d*)/,names:["payload","value"],format:"rtcp-fb:%d trr-int %d"},{push:"rtcpFb",reg:/^rtcp-fb:(\*|\d*) ([\w-_]*)(?: ([\w-_]*))?/,names:["payload","type","subtype"],format:function(e){return null!=e.subtype?"rtcp-fb:%s %s %s":"rtcp-fb:%s %s"}},{push:"ext",reg:/^extmap:(\d+)(?:\/(\w+))?(?: (urn:ietf:params:rtp-hdrext:encrypt))? (\S*)(?: (\S*))?/,names:["value","direction","encrypt-uri","uri","config"],format:function(e){return"extmap:%d"+(e.direction?"/%s":"%v")+(e["encrypt-uri"]?" %s":"%v")+" %s"+(e.config?" %s":"")}},{push:"crypto",reg:/^crypto:(\d*) ([\w_]*) (\S*)(?: (\S*))?/,names:["id","suite","config","sessionConfig"],format:function(e){return null!=e.sessionConfig?"crypto:%d %s %s %s":"crypto:%d %s %s"}},{name:"setup",reg:/^setup:(\w*)/,format:"setup:%s"},{name:"mid",reg:/^mid:([^\s]*)/,format:"mid:%s"},{name:"msid",reg:/^msid:(.*)/,format:"msid:%s"},{name:"ptime",reg:/^ptime:(\d*)/,format:"ptime:%d"},{name:"maxptime",reg:/^maxptime:(\d*)/,format:"maxptime:%d"},{name:"direction",reg:/^(sendrecv|recvonly|sendonly|inactive)/},{name:"icelite",reg:/^(ice-lite)/},{name:"iceUfrag",reg:/^ice-ufrag:(\S*)/,format:"ice-ufrag:%s"},{name:"icePwd",reg:/^ice-pwd:(\S*)/,format:"ice-pwd:%s"},{name:"fingerprint",reg:/^fingerprint:(\S*) (\S*)/,names:["type","hash"],format:"fingerprint:%s %s"},{push:"candidates",reg:/^candidate:(\S*) (\d*) (\S*) (\d*) (\S*) (\d*) typ (\S*)(?: raddr (\S*) rport (\d*))?(?: tcptype (\S*))?(?: generation (\d*))?(?: network-id (\d*))?(?: network-cost (\d*))?/,names:["foundation","component","transport","priority","ip","port","type","raddr","rport","tcptype","generation","network-id","network-cost"],format:function(e){var t="candidate:%s %d %s %d %s %d typ %s";return t+=null!=e.raddr?" raddr %s rport %d":"%v%v",t+=null!=e.tcptype?" tcptype %s":"%v",null!=e.generation&&(t+=" generation %d"),t+=null!=e["network-id"]?" network-id %d":"%v",t+=null!=e["network-cost"]?" network-cost %d":"%v"}},{name:"endOfCandidates",reg:/^(end-of-candidates)/},{name:"remoteCandidates",reg:/^remote-candidates:(.*)/,format:"remote-candidates:%s"},{name:"iceOptions",reg:/^ice-options:(\S*)/,format:"ice-options:%s"},{push:"ssrcs",reg:/^ssrc:(\d*) ([\w_-]*)(?::(.*))?/,names:["id","attribute","value"],format:function(e){var t="ssrc:%d";return null!=e.attribute&&(t+=" %s",null!=e.value&&(t+=":%s")),t}},{push:"ssrcGroups",reg:/^ssrc-group:([\x21\x23\x24\x25\x26\x27\x2A\x2B\x2D\x2E\w]*) (.*)/,names:["semantics","ssrcs"],format:"ssrc-group:%s %s"},{name:"msidSemantic",reg:/^msid-semantic:\s?(\w*) (\S*)/,names:["semantic","token"],format:"msid-semantic: %s %s"},{push:"groups",reg:/^group:(\w*) (.*)/,names:["type","mids"],format:"group:%s %s"},{name:"rtcpMux",reg:/^(rtcp-mux)/},{name:"rtcpRsize",reg:/^(rtcp-rsize)/},{name:"sctpmap",reg:/^sctpmap:([\w_/]*) (\S*)(?: (\S*))?/,names:["sctpmapNumber","app","maxMessageSize"],format:function(e){return null!=e.maxMessageSize?"sctpmap:%s %s %s":"sctpmap:%s %s"}},{name:"xGoogleFlag",reg:/^x-google-flag:([^\s]*)/,format:"x-google-flag:%s"},{push:"rids",reg:/^rid:([\d\w]+) (\w+)(?: ([\S| ]*))?/,names:["id","direction","params"],format:function(e){return e.params?"rid:%s %s %s":"rid:%s %s"}},{push:"imageattrs",reg:new RegExp("^imageattr:(\\d+|\\*)[\\s\\t]+(send|recv)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*)(?:[\\s\\t]+(recv|send)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*))?"),names:["pt","dir1","attrs1","dir2","attrs2"],format:function(e){return"imageattr:%s %s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast",reg:new RegExp("^simulcast:(send|recv) ([a-zA-Z0-9\\-_~;,]+)(?:\\s?(send|recv) ([a-zA-Z0-9\\-_~;,]+))?$"),names:["dir1","list1","dir2","list2"],format:function(e){return"simulcast:%s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast_03",reg:/^simulcast:[\s\t]+([\S+\s\t]+)$/,names:["value"],format:"simulcast: %s"},{name:"framerate",reg:/^framerate:(\d+(?:$|\.\d+))/,format:"framerate:%s"},{name:"sourceFilter",reg:/^source-filter: *(excl|incl) (\S*) (IP4|IP6|\*) (\S*) (.*)/,names:["filterMode","netType","addressTypes","destAddress","srcList"],format:"source-filter: %s %s %s %s %s"},{name:"bundleOnly",reg:/^(bundle-only)/},{name:"label",reg:/^label:(.+)/,format:"label:%s"},{name:"sctpPort",reg:/^sctp-port:(\d+)$/,format:"sctp-port:%s"},{name:"maxMessageSize",reg:/^max-message-size:(\d+)$/,format:"max-message-size:%s"},{push:"invalid",names:["value"]}]};Object.keys(t).forEach(function(e){t[e].forEach(function(e){e.reg||(e.reg=/(.*)/),e.format||(e.format="%s")})})}),Nf=(Df.v,Df.o,Df.s,Df.i,Df.u,Df.e,Df.p,Df.z,Df.r,Df.t,Df.c,Df.b,Df.m,Df.a,t(function(e,t){var n=function(e){return String(Number(e))===e?Number(e):e},r=function(e,t,r){var i=e.name&&e.names;e.push&&!t[e.push]?t[e.push]=[]:i&&!t[e.name]&&(t[e.name]={});var o=e.push?{}:i?t[e.name]:t;!function(e,t,r,i){if(i&&!r)t[i]=n(e[1]);else for(var o=0;o<r.length;o+=1)null!=e[o+1]&&(t[r[o]]=n(e[o+1]))}(r.match(e.reg),o,e.names,e.name),e.push&&t[e.push].push(o)},i=RegExp.prototype.test.bind(/^([a-z])=(.*)/);t.parse=function(e){var t={},n=[],o=t;return e.split(/(\r\n|\r|\n)/).filter(i).forEach(function(e){var t=e[0],i=e.slice(2);"m"===t&&(n.push({rtp:[],fmtp:[]}),o=n[n.length-1]);for(var a=0;a<(Df[t]||[]).length;a+=1){var s=Df[t][a];if(s.reg.test(i))return r(s,o,i)}}),t.media=n,t};var o=function(e,t){var r=t.split(/=(.+)/,2);return 2===r.length?e[r[0]]=n(r[1]):1===r.length&&t.length>1&&(e[r[0]]=void 0),e};t.parseParams=function(e){return e.split(/;\s?/).reduce(o,{})},t.parseFmtpConfig=t.parseParams,t.parsePayloads=function(e){return e.toString().split(" ").map(Number)},t.parseRemoteCandidates=function(e){for(var t=[],r=e.split(" ").map(n),i=0;i<r.length;i+=3)t.push({component:r[i],ip:r[i+1],port:r[i+2]});return t},t.parseImageAttributes=function(e){return e.split(" ").map(function(e){return e.substring(1,e.length-1).split(",").reduce(o,{})})},t.parseSimulcastStreamList=function(e){return e.split(";").map(function(e){return e.split(",").map(function(e){var t,r=!1;return"~"!==e[0]?t=n(e):(t=n(e.substring(1,e.length)),r=!0),{scid:t,paused:r}})})}})),Lf=(Nf.parse,Nf.parseParams,Nf.parseFmtpConfig,Nf.parsePayloads,Nf.parseRemoteCandidates,Nf.parseImageAttributes,Nf.parseSimulcastStreamList,/%[sdv%]/g),Mf=function(e){var t=1,n=arguments,r=n.length;return e.replace(Lf,function(e){if(t>=r)return e;var i=n[t];switch(t+=1,e){case"%%":return"%";case"%s":return String(i);case"%d":return Number(i);case"%v":return""}})},jf=function(e,t,n){var r=[e+"="+(t.format instanceof Function?t.format(t.push?n:n[t.name]):t.format)];if(t.names)for(var i=0;i<t.names.length;i+=1){var o=t.names[i];t.name?r.push(n[t.name][o]):r.push(n[t.names[i]])}else r.push(n[t.name]);return Mf.apply(null,r)},Uf=["v","o","s","i","u","e","p","c","b","t","r","z","a"],Vf=["i","c","b","a"],Ff={write:function(e,t){t=t||{},null==e.version&&(e.version=0),null==e.name&&(e.name=" "),e.media.forEach(function(e){null==e.payloads&&(e.payloads="")});var n=t.outerOrder||Uf,r=t.innerOrder||Vf,i=[];return n.forEach(function(t){Df[t].forEach(function(n){n.name in e&&null!=e[n.name]?i.push(jf(t,n,e)):n.push in e&&null!=e[n.push]&&e[n.push].forEach(function(e){i.push(jf(t,n,e))})})}),e.media.forEach(function(e){i.push(jf("m",Df.m[0],e)),r.forEach(function(t){Df[t].forEach(function(n){n.name in e&&null!=e[n.name]?i.push(jf(t,n,e)):n.push in e&&null!=e[n.push]&&e[n.push].forEach(function(e){i.push(jf(t,n,e))})})})}),i.join("\r\n")+"\r\n"},parse:Nf.parse,parseFmtpConfig:Nf.parseFmtpConfig,parseParams:Nf.parseParams,parsePayloads:Nf.parsePayloads,parseRemoteCandidates:Nf.parseRemoteCandidates,parseImageAttributes:Nf.parseImageAttributes,parseSimulcastStreamList:Nf.parseSimulcastStreamList},Gf=function(e){return Ff.parse(e)},Wf=function(e){return e>>16&255},Bf=function(){function e(t){ht(this,e),this.prevReport_={}}var t,n,r,i;return vt(e,[{key:"getSenderStats",value:(i=ft(regeneratorRuntime.mark(function e(t){var n,r;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n={audio:{bytesSent:0,packetsSent:0,audioLevel:0,totalAudioEnergy:0},video:{bytesSent:0,packetsSent:0,framesEncoded:0,frameWidth:0,frameHeight:0,framesSent:0},rtt:0},!(r=t.getPeerConnection())){e.next=13;break}return e.prev=3,e.next=6,r.getStats();case 6:e.sent.forEach(function(e){"outbound-rtp"===e.type?"video"===e.mediaType?(n.video.bytesSent=e.bytesSent,n.video.packetsSent=e.packetsSent,n.video.framesEncoded=e.framesEncoded):"audio"===e.mediaType&&(n.audio.bytesSent=e.bytesSent,n.audio.packetsSent=e.packetsSent):"candidate-pair"===e.type?"number"==typeof e.currentRoundTripTime&&(n.rtt=1e3*e.currentRoundTripTime):"track"===e.type?("undefined"!==e.frameWidth&&(n.video.frameWidth=e.frameWidth,n.video.frameHeight=e.frameHeight,n.video.framesSent=e.framesSent),void 0!==e.audioLevel&&(n.audio.audioLevel=e.audioLevel||0)):"media-source"===e.type&&"audio"===e.kind&&(n.audio.audioLevel=e.audioLevel||0,n.audio.totalAudioEnergy=e.totalAudioEnergy||0)}),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(3),sr.warn("failed to getStats on sender connection");case 13:return e.abrupt("return",n);case 14:case"end":return e.stop()}},e,null,[[3,10]])})),function(e){return i.apply(this,arguments)})},{key:"getReceiverStats",value:(r=ft(regeneratorRuntime.mark(function e(t){var n,r;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n={tinyId:t.getTinyId(),userId:t.getUserId(),hasAudio:!1,hasVideo:!1,audio:{bytesReceived:0,packetsReceived:0,packetsLost:0,jitter:0,audioLevel:0,totalAudioEnergy:0},video:{bytesReceived:0,packetsReceived:0,packetsLost:0,framesDecoded:0,frameWidth:0,frameHeight:0}},!(r=t.getPeerConnection())){e.next=13;break}return e.prev=3,e.next=6,r.getStats();case 6:e.sent.forEach(function(e){"inbound-rtp"===e.type?"audio"===e.mediaType?(n.audio.packetsReceived=e.packetsReceived,n.audio.bytesReceived=e.bytesReceived,n.audio.packetsLost=e.packetsLost,n.audio.jitter=e.jitter,n.hasAudio=!0):"video"===e.mediaType&&(n.video.packetsReceived=e.packetsReceived,n.video.bytesReceived=e.bytesReceived,n.video.packetsLost=e.packetsLost,n.video.framesDecoded=e.framesDecoded,n.hasVideo=!0):"track"===e.type&&(void 0!==e.frameWidth&&(n.video.frameWidth=e.frameWidth,n.video.frameHeight=e.frameHeight),"audio"===e.kind&&(n.audio.audioLevel=e.audioLevel||0,n.audio.totalAudioEnergy=e.totalAudioEnergy||0))}),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(3),sr.warn("failed to getStats on receiver connection");case 13:return e.abrupt("return",n);case 14:case"end":return e.stop()}},e,null,[[3,10]])})),function(e){return r.apply(this,arguments)})},{key:"getStats",value:(n=ft(regeneratorRuntime.mark(function e(t,n){var r,i,o,a,s,c,u,d,l,p;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r={},!t){e.next=5;break}return e.next=4,this.getSenderStats(t);case 4:r=e.sent;case 5:i=[],o=!0,a=!1,s=void 0,e.prev=9,c=n[Symbol.iterator]();case 11:if(o=(u=c.next()).done){e.next=20;break}return(d=Ct(u.value,2))[0],l=d[1],e.next=15,this.getReceiverStats(l);case 15:p=e.sent,i.push(p);case 17:o=!0,e.next=11;break;case 20:e.next=26;break;case 22:e.prev=22,e.t0=e.catch(9),a=!0,s=e.t0;case 26:e.prev=26,e.prev=27,o||null==c.return||c.return();case 29:if(e.prev=29,!a){e.next=32;break}throw s;case 32:return e.finish(29);case 33:return e.finish(26);case 34:return e.abrupt("return",{senderStats:r,receiverStats:i});case 35:case"end":return e.stop()}},e,this,[[9,22,26,34],[27,,29,33]])})),function(e,t){return n.apply(this,arguments)})},{key:"prepareReport",value:function(e,t){Bd(e.senderStats)||(t.uint32_delay=e.senderStats.rtt,t.AudioReportState.sentAudioLevel=e.senderStats.audio.audioLevel,t.AudioReportState.sentAudioEnergy=e.senderStats.audio.totalAudioEnergy,t.AudioReportState.uint32_audio_enc_pkg_br=e.senderStats.audio.bytesSent,t.VideoReportState.uint32_video_snd_br=e.senderStats.video.bytesSent,t.VideoReportState.uint32_send_total_pkg=e.senderStats.video.packetsSent,t.VideoReportState.VideoEncState[0].uint32_enc_width=e.senderStats.video.frameWidth,t.VideoReportState.VideoEncState[0].uint32_enc_height=e.senderStats.video.frameHeight,t.VideoReportState.VideoEncState[0].uint32_enc_fps=e.senderStats.video.framesSent),e.receiverStats.forEach(function(e){e.hasAudio&&(t.AudioReportState.AudioDecState.push({uint32_audio_delay:0,uint32_audio_jitter:e.audio.jitter,uint32_audio_real_recv_pkg:e.audio.packetsReceived,uint32_audio_flow:e.audio.bytesReceived,uint32_audio_real_recv_br:0,uint64_sender_uin:e.tinyId,packetsLost:e.audio.packetsLost,totalPacketsLost:e.audio.packetsLost,audioLevel:e.audio.audioLevel,audioEnergy:e.audio.totalAudioEnergy}),t.AudioReportState.uint32_audio_real_recv_pkg+=e.audio.packetsReceived,t.AudioReportState.uint32_audio_flow+=e.audio.bytesReceived,t.uint32_real_num+=e.audio.packetsReceived),e.hasVideo&&(t.VideoReportState.VideoDecState.push({uint32_video_recv_fps:e.video.framesDecoded,uint32_video_recv_br:e.video.bytesReceived,uint32_video_real_recv_pkg:e.video.packetsReceived,uint32_dec_height:e.video.frameHeight,uint32_dec_width:e.video.frameWidth,uint32_video_jitter:0,uint64_sender_uin:e.tinyId,packetsLost:e.video.packetsLost,totalPacketsLost:e.video.packetsLost}),t.VideoReportState.uint32_video_total_real_recv_pkg+=e.video.packetsReceived,t.VideoReportState.uint32_video_rcv_br+=e.video.bytesReceived)}),t.uint64_end_utime=(new Date).getTime();var n=this.prevReport_;if(this.prevReport_=JSON.parse(JSON.stringify(t)),Bd(n))t.AudioReportState.uint32_audio_enc_pkg_br=8*t.AudioReportState.uint32_audio_enc_pkg_br/2,t.VideoReportState.uint32_video_rcv_br=8*t.VideoReportState.uint32_video_rcv_br/2,t.VideoReportState.uint32_video_snd_br=8*t.VideoReportState.uint32_video_snd_br/2,t.VideoReportState.VideoDecState.forEach(function(e){e.uint32_video_recv_br=8*e.uint32_video_recv_br/2,t.uint32_total_send_bps=t.AudioReportState.uint32_audio_enc_pkg_br+t.VideoReportState.uint32_video_snd_br});else{t.uint64_begine_utime=n.uint64_end_utime,t.uint32_real_num-=n.uint32_real_num,t.uint32_real_num<=0&&(t.uint32_real_num=0),t.AudioReportState.uint32_audio_real_recv_pkg-=n.AudioReportState.uint32_audio_real_recv_pkg,t.AudioReportState.uint32_audio_real_recv_pkg<=0&&(t.AudioReportState.uint32_audio_real_recv_pkg=0),t.AudioReportState.uint32_audio_enc_pkg_br-=n.AudioReportState.uint32_audio_enc_pkg_br,t.AudioReportState.uint32_audio_enc_pkg_br<=0&&(t.AudioReportState.uint32_audio_enc_pkg_br=0),t.AudioReportState.uint32_audio_enc_pkg_br=8*t.AudioReportState.uint32_audio_enc_pkg_br/2,t.VideoReportState.uint32_video_snd_br-=n.VideoReportState.uint32_video_snd_br,t.VideoReportState.uint32_video_snd_br<=0&&(t.VideoReportState.uint32_video_snd_br=0),t.VideoReportState.uint32_video_snd_br=8*t.VideoReportState.uint32_video_snd_br/2,t.AudioReportState.uint32_audio_flow-=n.AudioReportState.uint32_audio_flow,t.AudioReportState.uint32_audio_flow<=0&&(t.AudioReportState.uint32_audio_flow=0),t.VideoReportState.uint32_send_total_pkg-=n.VideoReportState.uint32_send_total_pkg,t.VideoReportState.uint32_send_total_pkg<=0&&(t.VideoReportState.uint32_send_total_pkg=0),t.VideoReportState.uint32_video_rcv_br-=n.VideoReportState.uint32_video_rcv_br,t.VideoReportState.uint32_video_rcv_br<=0&&(t.VideoReportState.uint32_video_rcv_br=0),t.VideoReportState.uint32_video_rcv_br=8*t.VideoReportState.uint32_video_rcv_br/2,t.VideoReportState.uint32_video_total_real_recv_pkg-=n.VideoReportState.uint32_video_total_real_recv_pkg,t.VideoReportState.uint32_video_total_real_recv_pkg<=0&&(t.VideoReportState.uint32_video_total_real_recv_pkg=0),t.VideoReportState.VideoEncState[0].uint32_enc_fps-=n.VideoReportState.VideoEncState[0].uint32_enc_fps,t.VideoReportState.VideoEncState[0].uint32_enc_fps<0&&(t.VideoReportState.VideoEncState[0].uint32_enc_fps=0),t.VideoReportState.VideoEncState[0].uint32_enc_fps=t.VideoReportState.VideoEncState[0].uint32_enc_fps/2;for(var r=t.VideoReportState.VideoDecState.length,i=0;i<r;i++){for(var o=t.VideoReportState.VideoDecState[i],a=o.uint64_sender_uin,s=o.uint32_video_real_recv_pkg,c=o.uint32_video_recv_br,u=o.uint32_video_recv_fps,d=0;d<n.VideoReportState.VideoDecState.length;d++){var l=n.VideoReportState.VideoDecState[d];if(l.uint64_sender_uin===a){o.packetsLost=o.totalPacketsLost-l.totalPacketsLost,(s-=l.uint32_video_real_recv_pkg)<=0&&(s=0),(c-=l.uint32_video_recv_br)<=0&&(c=0),(u-=l.uint32_video_recv_fps)<0&&(u=0);break}}t.VideoReportState.VideoDecState[i].uint32_video_real_recv_pkg=s,t.VideoReportState.VideoDecState[i].uint32_video_recv_br=8*c/2,t.VideoReportState.VideoDecState[i].uint32_video_recv_fps=u/2}r=t.AudioReportState.AudioDecState.length;for(var p=0;p<r;p++){for(var f=t.AudioReportState.AudioDecState[p],h=f.uint32_audio_real_recv_pkg,m=f.uint32_audio_flow,v=f.uint64_sender_uin,g=0;g<n.AudioReportState.AudioDecState.length;g++){var _=n.AudioReportState.AudioDecState[g];if(_.uint64_sender_uin===v){f.packetsLost=f.totalPacketsLost-_.totalPacketsLost,(h-=_.uint32_audio_real_recv_pkg)<=0&&(h=0),(m-=_.uint32_audio_flow)<=0&&(m=0);break}}t.AudioReportState.AudioDecState[p].uint32_audio_real_recv_pkg=h,t.AudioReportState.AudioDecState[p].uint32_audio_flow=m,t.AudioReportState.AudioDecState[p].uint32_audio_real_recv_br=8*m/2}t.AudioReportState.uint32_audio_real_recv_br=8*t.AudioReportState.uint32_audio_flow/2,t.uint32_real_num=t.AudioReportState.uint32_audio_real_recv_pkg+t.VideoReportState.uint32_video_total_real_recv_pkg,t.uint32_total_send_bps=t.AudioReportState.uint32_audio_enc_pkg_br+t.VideoReportState.uint32_video_snd_br,t.uint32_total_recv_bps=t.AudioReportState.uint32_audio_real_recv_br+t.VideoReportState.uint32_video_rcv_br}return t}},{key:"getStatsReport",value:(t=ft(regeneratorRuntime.mark(function e(t,n){var r,i;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r={uint64_begine_utime:(new Date).getTime(),uint64_end_utime:0,uint32_real_num:0,uint32_delay:0,uint32_CPU_curfreq:0,uint32_total_send_bps:0,uint32_total_recv_bps:0,AudioReportState:{uint32_audio_enc_pkg_br:0,uint32_audio_real_recv_pkg:0,uint32_audio_flow:0,uint32_audio_real_recv_br:0,uint32_audio_delay:0,uint32_audio_jitter:0,uint32_microphone_status:1,sentAudioLevel:0,sentAudioEnergy:0,AudioDecState:[]},VideoReportState:{uint32_video_delay:0,uint32_video_snd_br:0,uint32_video_total_real_recv_pkg:0,uint32_video_rcv_br:0,uint32_send_total_pkg:0,VideoEncState:[{uint32_enc_width:0,uint32_enc_height:0,uint32_capture_fps:0,uint32_enc_fps:0}],VideoDecState:[]}},e.next=3,this.getStats(t,n);case 3:return i=e.sent,this.prepareReport(i,r),e.abrupt("return",r);case 6:case"end":return e.stop()}},e,this)})),function(e,n){return t.apply(this,arguments)})}]),e}(),Hf={voiceActivityDetection:!1},zf=function(){function e(t){ht(this,e),this.userId_=t.userId,this.tinyId_=t.tinyId,this.client_=t.client,this.sdpSemantics_=t.client.getSdpSemantics(),this.isUplink_=t.isUplink,this.log_=new Pf({id:"n|"+this.userId_,direction:this.isUplink_?"local":"remote",type:""}),this.signalChannel_=t.signalChannel,this.peerConnection_=null,this.connectTimer_=-1,this.remoteStreams_=new Map,this.localStream_=null,this.waitForUpdatedAnswer_=!1,this.isErrorObserved_=!1,this.mutedState_=0,this.subscribeState_={audio:!0,video:!0,auxiliary:!0},this.pendingSubscription_=[],this.pendingStreams_=[],this.subscriptionTimeout_=-1,this.subscriptionRetryCount_=0,this.isSubscriptionPending_=!1,this.sentSubscriptionAfterConnected_=!1,this.emitter_=new zd,this.startTime_=new Date,this.endTime_=this.startTime_,this.hasValidEndTime_=!1,this.hasVideo_=!1,this.currentState_=Jp.DISCONNECTED}var t,n,r,i,o,a,s;return vt(e,[{key:"initialize",value:function(){var e={iceServers:this.client_.getIceServers(),iceTransportPolicy:"all",sdpSemantics:this.sdpSemantics_,bundlePolicy:"max-bundle",rtcpMuxPolicy:"require",tcpCandidatePolicy:"disable",IceTransportsType:"nohost"};this.peerConnection_=new RTCPeerConnection(e),this.peerConnection_.onconnectionstatechange=this.onConnectionStateChange.bind(this),this.isUplink_||(this.peerConnection_.ontrack=this.onTrack.bind(this)),this.installSignalChannelEvents()}},{key:"publish",value:function(e,t){var n=this;this.localStream_=e;var r=e.getMediaStream();this.log_.info("is publishing stream: ".concat(e.getId())),r.getTracks().forEach(function(e){"video"===e.kind&&n.setVideoStats("start"),n.peerConnection_.addTrack(e,r)}),this.updateMediaSettings(r),this.exchangeOffer(t)}},{key:"updateMediaSettings",value:function(e){var t=this,n={EncVideoCodec:"H264",EncVideoWidth:0,EncVideoHeight:0,EncVideoBr:"0",EncVideoFps:0,EncAudioCodec:"opus",EncAudioFS:0,EncAudioCh:0,EncAudioBr:"0"};"getSettings"in MediaStreamTrack.prototype?e.getTracks().forEach(function(e){var r=e.getSettings();if("audio"===e.kind){var i=1;r.channelCount&&(i=r.channelCount),n.EncAudioCh=i,n.EncAudioBr="".concat(1e3*t.localStream_.getAudioBitrate()),n.EncAudioFS=r.sampleRate}else"video"===e.kind&&(n.EncVideoWidth=r.width,n.EncVideoHeight=r.height,n.EncVideoFps=r.frameRate,n.EncVideoBr="".concat(1e3*t.localStream_.getVideoBitrate()))}):n=this.getMediaSettingsFromProfile(n),this.log_.info("updateMediaSettings: "+JSON.stringify(n)),this.signalChannel_.send(ol,n)}},{key:"getMediaSettingsFromProfile",value:function(e){var t=this.localStream_;if(t){if(t.getAudioTrack()){var n=t.getAudioProfile();e.EncAudioCh=n.channelCount,e.EncAudioBr="".concat(1e3*n.bitrate),e.EncAudioFS=n.sampleRate}if(t.getVideoTrack()){var r=t.getVideoProfile();e.EncVideoWidth=r.width,e.EncVideoHeight=r.height,e.EncVideoFps=r.frameRate,e.EncVideoBr="".concat(1e3*r.bitrate)}}return e}},{key:"replaceStream",value:(s=ft(regeneratorRuntime.mark(function e(t){var n,r,i=this;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.peerConnection_||!np()){e.next=8;break}return n=t.getVideoTracks(),r=t.getAudioTracks(),(this.peerConnection_.getSenders()||[]).forEach(function(e){e.track&&("video"===e.track.kind?e.replaceTrack&&n.length>0?e.replaceTrack(n[0]):(i.peerConnection_.removeTrack(e),n.length>0&&i.peerConnection_.addTrack(n[0],t)):"audio"===e.track.kind&&(e.replaceTrack&&r.length>0?e.replaceTrack(r[0]):(i.peerConnection_.removeTrack(e),r.length>0&&i.peerConnection_.addTrack(r[0],t))))}),this.updateMediaSettings(t),e.next=8,this.updateOffer();case 8:case"end":return e.stop()}},e,this)})),function(e){return s.apply(this,arguments)})},{key:"addTrack",value:(a=ft(regeneratorRuntime.mark(function e(t){var n;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.peerConnection_){e.next=8;break}return this.log_.info("is adding ".concat(t.kind," track to current published local stream")),n=this.localStream_.getMediaStream(),this.peerConnection_.addTrack(t,n),"video"===t.kind&&this.setVideoStats("start"),this.updateMediaSettings(n),e.next=8,this.updateOffer();case 8:case"end":return e.stop()}},e,this)})),function(e){return a.apply(this,arguments)})},{key:"removeTrack",value:(o=ft(regeneratorRuntime.mark(function e(t){var n;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.peerConnection_||!np()){e.next=9;break}if(this.log_.info("is removing ".concat(t.kind," track from current published local stream")),!(n=this.peerConnection_.getSenders().find(function(e){return e.track===t}))){e.next=9;break}return this.peerConnection_.removeTrack(n),"video"===t.kind&&this.setVideoStats("end"),this.updateMediaSettings(this.localStream_.getMediaStream()),e.next=9,this.updateOffer();case 9:case"end":return e.stop()}},e,this)})),function(e){return o.apply(this,arguments)})},{key:"isReplaceTrackAvailable",value:function(){return"RTCRtpSender"in window&&"replaceTrack"in window.RTCRtpSender.prototype}},{key:"replaceTrack",value:(i=ft(regeneratorRuntime.mark(function e(t){var n,r=this;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.isReplaceTrackAvailable()&&np()){e.next=2;break}throw new dl({code:cl.INVALID_OPERATION,message:"replaceTrack is not supported in this browser, please use switchDevice or addTrack instead"});case 2:if(this.peerConnection_){e.next=4;break}throw new dl({code:cl.INVALID_OPERATION,message:"replaceTrack() is only valid after the LocalStream has been published"});case 4:if(0!==(n=this.peerConnection_.getSenders()).length){e.next=7;break}throw new dl({code:cl.INVALID_OPERATION,message:"replaceTrack() is only valid after the LocalStream has been published"});case 7:n.forEach(function(e){e.track&&e.track.kind===t.kind&&(r.log_.info("is replacing ".concat(t.kind," track to current published local stream")),e.replaceTrack(t))});case 8:case"end":return e.stop()}},e,this)})),function(e){return i.apply(this,arguments)})},{key:"close",value:function(){var e=this;this.log_.info("closing connection"),-1!==this.connectTimer_&&(clearTimeout(this.connectTimer_),this.connectTimer_=-1),-1!==this.subscriptionTimeout_&&(clearTimeout(this.subscriptionTimeout_),this.subscriptionTimeout_=-1),this.peerConnection_&&(this.peerConnection_.onconnectionstatechange=function(e){},this.peerConnection_.close(),this.peerConnection_=null),this.isUplink_||(this.remoteStreams_.forEach(function(t,n,r){var i=t;i.setConnection(null),e.emitter_.emit(Yp,{stream:i})}),this.remoteStreams_.clear()),this.uninstallSignalChannelEvents()}},{key:"installSignalChannelEvents",value:function(){this.isUplink_?this.signalChannel_.on($d.NEW_REMOTE_SDP,this.onNewRemoteSdp,this):(this.signalChannel_.on($d.UPDATE_REMOTE_SDP,this.onUpdateRemoteSdp,this),this.signalChannel_.on($d.UPDATE_AUDIO_SSRC,this.onUpdateAudioSSRC,this),this.signalChannel_.on($d.UPDATE_VIDEO_SSRC,this.onUpdateVideoSSRC,this),this.signalChannel_.on($d.SUBSCRIBE_ACK,this.onSubscription,this))}},{key:"uninstallSignalChannelEvents",value:function(){this.isUplink_?this.signalChannel_.removeListener($d.NEW_REMOTE_SDP,this.onNewRemoteSdp,this):(this.signalChannel_.removeListener($d.UPDATE_REMOTE_SDP,this.onUpdateRemoteSdp,this),this.signalChannel_.removeListener($d.UPDATE_AUDIO_SSRC,this.onUpdateAudioSSRC,this),this.signalChannel_.removeListener($d.UPDATE_VIDEO_SSRC,this.onUpdateVideoSSRC,this),this.signalChannel_.removeListener($d.SUBSCRIBE_ACK,this.onSubscription,this))}},{key:"onNewRemoteSdp",value:function(e){if(this.hitTest(e.data.srctinyid)){var t=e.data.content;this.acceptAnswer(t)}}},{key:"onUpdateRemoteSdp",value:function(e){var t=e.data.content;if(this.hitTest(t.srctinyid)){this.log_.info("is updating remote sdp offer");var n=t.newSdp;this.updateRemoteDescription(n)}}},{key:"onUpdateAudioSSRC",value:function(e){var t=e.data.content;if(this.hitTest(t.srctinyid)){this.log_.info("is updating audio ssrc");var n=t.newSdp;this.updateRemoteDescription(n)}}},{key:"onUpdateVideoSSRC",value:function(e){var t=e.data.content;if(this.hitTest(t.srctinyid)){this.log_.info("is updating video ssrc");var n=t.newSdp;this.updateRemoteDescription(n)}}},{key:"onTrack",value:function(e){var t=e.streams[0],n=e.track;if(this.log_.info("ontrack() kind: ".concat(n.kind," id: ").concat(n.id," streamId: ").concat(t.id)),"unified-plan"===this.sdpSemantics_){var r=function(e){var t=Ff.parse(e),n={audio:[],video:[]};return t.media.forEach(function(e){if(e.ssrcs){var t=e.ssrcs[0].id>>16&255;if("audio"===e.type)n.audio.push(Ip);else if("video"==e.type){var r=t===Pp?Ip:xp;n.video.push(r)}}}),n}(this.peerConnection_.remoteDescription.sdp);if("audio"===n.kind){if(0===r.audio.length||t.id!==Ip)return void this.log_.debug("skip this invalid audio track")}else if(-1===r.video.indexOf(t.id))return void this.log_.debug("skip this invalid video track: ".concat(n.id,"  msid: ").concat(t.id))}hl({eventType:"ontrack",kind:n.kind});var i=!1,o=this.remoteStreams_.get(t.id);if(void 0===o){var a=t.id===Ip?"main":"auxiliary";(o=new Of({type:a,userId:this.userId_,client:this.client_})).setConnection(this),this.remoteStreams_.set(t.id,o),i=!0}o.setMediaStream(t),"audio"===n.kind?(o.hasAudio()&&o.isPlaying()&&(this.log_.debug("already has an audio track, restart audio player with the new track"),o.restartAudio()),o.setHasAudio(!0)):(this.setVideoStats("start"),o.hasVideo()&&o.isPlaying()&&(this.log_.debug("already has an video track, restart video player with the new track"),o.restartVideo()),o.setHasVideo(!0)),i?this.emitter_.emit($p,{stream:o}):this.emitter_.emit(Xp,{stream:o})}},{key:"isRtpSenderAvailable",value:function(){return"RTCRtpSender"in window&&"setParameters"in window.RTCRtpSender.prototype&&np()}},{key:"setBandwidth",value:(r=ft(regeneratorRuntime.mark(function e(t,n){var r,i,o=this;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.isUplink_){e.next=2;break}return e.abrupt("return");case 2:void 0===n&&(n="video"),this.isRtpSenderAvailable()&&(r=this.peerConnection_.getSenders().find(function(e){return e.track&&e.track.kind===n}))&&((i=r.getParameters()).encodings||(i.encodings=[{}]),"unlimited"===t?delete i.encodings[0].maxBitrate:i.encodings[0].maxBitrate=1e3*t,r.setParameters(i).then(function(){o.log_.debug(n+" bandwidth was set to "+t+" kbps")}).catch(function(e){return o.log_.error("failed to set bandwidth: "+e)}));case 4:case"end":return e.stop()}},e,this)})),function(e,t){return r.apply(this,arguments)})},{key:"updateVideoBandwidthRestriction",value:function(e,t){if(Number(t)<=300){return e=-1===e.indexOf("b=AS:")?e.replace(/m=video (.*)\r\nc=IN (.*)\r\n/,"m=video $1\r\nc=IN $2\r\nb=AS:"+t+"\r\n"):e.replace(new RegExp("b=AS:.*\r\n"),"b=AS:"+t+"\r\n")}var n=e.split("\r\n");return n.forEach(function(e,r){if(/^a=fmtp:\d*/.test(e)){var i=["".concat(e,";x-google-max-bitrate=").concat(2*t,";"),"x-google-min-bitrate=".concat(t,";"),"x-google-start-bitrate=".concat(t)];n[r]=i.join("")}}),n.join("\r\n")}},{key:"updateAudioBandwidthRestriction",value:function(e,t){return e=e.replace(/m=audio (.*)\r\nc=IN (.*)\r\n/,"m=audio $1\r\nc=IN $2\r\nb=AS:"+t+"\r\n")}},{key:"removeBandwidthRestriction",value:function(e){return e.replace(/b=AS:.*\r\n/,"").replace(/b=TIAS:.*\r\n/,"")}},{key:"removeVideoOrientation",value:function(e){return e.replace(/urn:3gpp:video-orientation/,"")}},{key:"exchangeOffer",value:function(e){var t=this;this.peerConnection_.createOffer(Hf).then(function(e){return t.peerConnection_.setLocalDescription(e)}).then(function(){t.log_.info("createOffer success, sending offer to remote server");var e=t.peerConnection_.localDescription,n={type:e.type,sdp:t.removeVideoOrientation(e.sdp)};t.log_.debug("sending sdp offer: "+n.sdp),t.signalChannel_.send(Yd,n,0),hl({eventType:"setLocalDescription",kind:"offer",result:"success"})}).catch(function(n){hl({eventType:"setLocalDescription",kind:"offer",result:"failed",code:cl.CREATE_OFFER_FAILED}),t.log_.error("failed to create offer"),e(new dl({code:cl.CREATE_OFFER_FAILED,message:"failed to create offer"}))})}},{key:"updateOffer",value:(n=ft(regeneratorRuntime.mark(function e(){var t,n;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t="",e.prev=1,e.next=4,this.peerConnection_.createOffer(Hf);case 4:return t=e.sent,e.next=7,this.peerConnection_.setLocalDescription(t);case 7:n=this.peerConnection_.localDescription,this.log_.info("createOffer success, sending updated offer to remote server"),this.log_.debug("updatedOffer: "+n.sdp),this.signalChannel_.send(Xd,n,0),this.waitForUpdatedAnswer_=!0,hl({eventType:"setLocalDescription",kind:"offer",result:"success"}),e.next=20;break;case 15:throw e.prev=15,e.t0=e.catch(1),hl({eventType:"setLocalDescription",kind:"offer",result:"failed",code:cl.CREATE_OFFER_FAILED}),this.log_.error("failed to create updated sdp offer"),new dl({code:cl.CREATE_OFFER_FAILED,message:"failed to create updated sdp offer"});case 20:case"end":return e.stop()}},e,this,[[1,15]])})),function(){return n.apply(this,arguments)})},{key:"acceptAnswer",value:function(e){var t=this,n=this.localStream_.getVideoBitrate(),r=this.updateVideoBandwidthRestriction(e.sdp,n);r=this.removeVideoOrientation(r);var i=this.localStream_.getAudioBitrate();r=this.updateAudioBandwidthRestriction(r,i);var o={type:e.type,sdp:r};this.peerConnection_.setRemoteDescription(o).then(function(){t.log_.debug("accepted anwswer: "+r),t.waitForUpdatedAnswer_||(t.log_.info("accepted remote answer, fired stream-published event"),t.emitter_.emit(Zp,{stream:t.localStream_})),t.waitForUpdatedAnswer_&&(t.waitForUpdatedAnswer_=!1),hl({eventType:"setRemoteDescription",kind:"answer",result:"success"})},function(e){hl({eventType:"setRemoteDescription",kind:"answer",result:"failed",code:e instanceof dl?e.getExtraCode()||e.getCode():cl.UNKNOWN}),t.log_.error("failed to accept remote answer "+e)})}},{key:"failIceConnectionForTest",value:function(){this.log_.warn("failIceConnectionForTest()"),this.emitter_.emit(tf,new dl({message:"fake ICE failure fired",code:cl.ICE_TRANSPORT_ERROR}))}},{key:"getDTLSTransportState",value:function(){if(!this.peerConnection_)return"unknown";var e=null;if(this.isUplink_){if(!np()||0===this.peerConnection_.getSenders().length)return"unknown";e=this.peerConnection_.getSenders()[0].transport}else{if(!tp()||0===this.peerConnection_.getReceivers().length)return"unknown";e=this.peerConnection_.getReceivers()[0].transport}return e?e.state:"unknown"}},{key:"onConnectionStateChange",value:function(e){var t=this,n=this.peerConnection_.iceConnectionState,r=this.getDTLSTransportState();if(this.log_.info("onConnectionStateChange() connectionState: "+e.target.connectionState),this.log_.info("ICE Transport state: ".concat(n,", DTLS Transport state: ").concat(r)),"connecting"===e.target.connectionState){this.connectTimer_=setTimeout(function(){var e="".concat(t.isUplink_?"uplink":"downlink"," DTLS Transport connection timeout (").concat(10,"s). ICE Transport state: ").concat(n,", DTLS Transport state: ").concat(r);hl({eventType:"iceConnectionState",result:"failed",code:cl.ICE_TRANSPORT_ERROR}),t.log_.error(e+" https://trtc-1252463788.file.myqcloud.com/web/docs/tutorial-10-error-code-tips.html"),t.addTransportEvent("disconnected"),t.emitter_.emit(nf,{prevState:t.currentState_,state:Jp.DISCONNECTED}),t.currentState_=Jp.DISCONNECTED,t.isErrorObserved_=!0,t.emitter_.emit(tf,new dl({message:e,code:cl.ICE_TRANSPORT_ERROR}))},1e4),this.emitter_.emit(nf,{prevState:this.currentState_,state:Jp.CONNECTING}),this.currentState_=Jp.CONNECTING}else clearTimeout(this.connectTimer_),this.connectTimer_=-1;if("failed"===e.target.connectionState||"closed"===e.target.connectionState){var i="".concat(this.isUplink_?"uplink":"downlink"," ICE/DTLS Transport connection ").concat(e.target.connectionState,". ICE Transport state: ").concat(n,", DTLS Transport state: ").concat(r);hl({eventType:"iceConnectionState",result:"failed",code:cl.ICE_TRANSPORT_ERROR}),this.log_.error(i+" https://trtc-1252463788.file.myqcloud.com/web/docs/tutorial-10-error-code-tips.html"),this.emitter_.emit(nf,{prevState:this.currentState_,state:Jp.DISCONNECTED}),this.currentState_=Jp.DISCONNECTED,this.isErrorObserved_||(this.addTransportEvent("disconnected"),this.emitter_.emit(tf,new dl({message:i,code:cl.ICE_TRANSPORT_ERROR})))}if("connected"===e.target.connectionState||"completed"===e.target.connectionState){if(hl({eventType:"iceConnectionState",result:"success"}),this.addTransportEvent("connected"),this.emitter_.emit(nf,{prevState:this.currentState_,state:Jp.CONNECTED}),this.currentState_=Jp.CONNECTED,!this.sentSubscriptionAfterConnected_&&this.pendingSubscription_.length>0){this.log_.info("send pending subscription after RTCPeerConnection is connected");var o=this.pendingSubscription_[0];this.doSendSubscription(o.data,o.stream),this.sentSubscriptionAfterConnected_=!0}}else"disconnected"===e.target.connectionState&&(this.emitter_.emit(nf,{prevState:this.currentState_,state:Jp.DISCONNECTED}),this.currentState_=Jp.DISCONNECTED,this.addTransportEvent("disconnected"))}},{key:"hitTest",value:function(e){return(0===e||"0"===e)&&this.isUplink_||e===this.tinyId_}},{key:"updateRemoteDescription",value:function(e){var t=this;this.log_.debug("updateRemoteDescription() ".concat(e.sdp));var n=this.peerConnection_.remoteDescription.sdp;this.peerConnection_.setRemoteDescription(e).then(function(){if(hl({eventType:"setRemoteDescription",kind:"offer",result:"success"}),t.log_.info("updateRemoteDescription success"),"unified-plan"===t.sdpSemantics_){var r=function(e,t){var n=Ff.parse(e).media,r=Ff.parse(t).media;return{added:r.filter(function(e){var t=n.find(function(t){return t.mid===e.mid});return(void 0===t||void 0===t.ssrcs)&&e.ssrcs}),removed:n.filter(function(e){var t=r.find(function(t){return t.mid===e.mid});return(void 0===t||void 0===t.ssrcs)&&e.ssrcs})}}(n,e.sdp);t.addRemoteTrack(r.added),t.removeRemoteTrack(r.removed)}else t.removeRemoteTrackLegacy(n,e.sdp);var i=t.pendingStreams_.shift();i&&(t.log_.debug("mark ".concat(i.getType()," stream exit subscription cycle")),i.setInSubscriptionCycle(!1))}).catch(function(e){hl({eventType:"setRemoteDescription",kind:"offer",result:"failed",code:e instanceof dl?e.getExtraCode()||e.getCode():cl.UNKNOWN}),t.log_.error("updateRemoteDescription failed ".concat(e))})}},{key:"addRemoteTrack",value:function(e){var t=this;this.peerConnection_&&tp()&&e.forEach(function(e){var n=e.mid,r=Wf(e.ssrcs[0].id)===Ap?xp:Ip,i=t.peerConnection_.getReceivers()[n];if(i&&i.track){t.log_.info("add ".concat(i.track.kind," track ").concat(i.track.id));var o=t.remoteStreams_.get(r);if(o)o.getMediaStream().addTrack(i.track),"audio"===i.track.kind?o.setHasAudio(!0):(o.setHasVideo(!0),t.setVideoStats("start")),t.emitter_.emit(Xp,{stream:o});else{t.log_.debug("remoteStream for msid:".concat(r," not exist"));var a=new MediaStream;a.addTrack(i.track),(o=new Of({type:r===Ip?"main":"auxiliary",userId:t.userId_,client:t.client_})).setMediaStream(a),o.setConnection(t),t.remoteStreams_.set(r,o),"audio"===i.track.kind?o.setHasAudio(!0):o.setHasVideo(!0),t.emitter_.emit("stream-added",{stream:o})}}})}},{key:"removeRemoteTrack",value:function(e){var t=this;e.forEach(function(e){var n=Wf(e.ssrcs[0].id),r=n===Pp?"video":n===Ap?"auxVideo":"audio";t.log_.info("[".concat(r,"] track was removed"));var i="auxVideo"===r?xp:Ip,o=t.remoteStreams_.get(i);o&&(i!==xp&&0!==o.getMediaStream().getTracks().length||o.isInSubscriptionCycle()?("audio"===r?o.setHasAudio(!1):(o.setHasVideo(!1),t.setVideoStats("end")),t.emitter_.emit(Xp,{stream:o})):(t.log_.info("remote stream ".concat(i," removed")),t.remoteStreams_.delete(i),t.emitter_.emit(Yp,{stream:o})))})}},{key:"removeRemoteTrackLegacy",value:function(e,t){var n=this,r=Gf(e).media,i=Gf(t).media;r.filter(function(e){var t,r=i.find(function(t){return t.type===e.type}),o=Pp;if("audio"===e.type)void 0!==r&&void 0!==r.ssrcs||!e.ssrcs||(n.log_.info("[audio] track was removed"),(t=n.remoteStreams_.get(o))&&(0===t.getMediaStream().getTracks().length?t.isInSubscriptionCycle()||(n.log_.info("remote stream ".concat(o," removed")),n.remoteStreams_.delete(o),n.emitter_.emit(Yp,{stream:t})):(t.setHasAudio(!1),n.emitter_.emit(Xp,{stream:t}))));else if((void 0===r||void 0===r.ssrcs)&&e.ssrcs||r.ssrcGroups&&e.ssrcGroups&&r.ssrcGroups.length<e.ssrcGroups.length){var a=!1,s=!1;void 0===r.ssrcs&&2===e.ssrcGroups.length?(n.log_.info("[main & aux video] tracks were removed"),a=!0,s=!0):void 0===r.ssrcs&&1===e.ssrcGroups.length?(n.log_.info("[main video] track was removed"),a=!0):e.ssrcGroups.length>r.ssrcGroups.length&&(n.log_.info("[auxiliary video] track was removed"),s=!0),s&&(o=xp,n.log_.info("remote stream ".concat(o," removed")),(t=n.remoteStreams_.get(o))&&!t.isInSubscriptionCycle()&&(n.remoteStreams_.delete(o),n.emitter_.emit(Yp,{stream:t}))),a&&(o=Ip,(t=n.remoteStreams_.get(o))&&(0===t.getMediaStream().getTracks().length?t.isInSubscriptionCycle()||(n.log_.info("remote stream ".concat(o," removed")),n.remoteStreams_.delete(o),n.emitter_.emit(Yp,{stream:t})):(n.setVideoStats("end"),t.setHasVideo(!1),n.emitter_.emit(Xp,{stream:t}))))}})}},{key:"setRemoteOffer",value:function(e){var t=this;this.peerConnection_.setRemoteDescription(e).then(function(){return hl({eventType:"setRemoteDescription",kind:"offer",result:"success"}),t.peerConnection_.createAnswer()}).then(function(e){return t.peerConnection_.setLocalDescription(e)}).then(function(){var e=t.peerConnection_.localDescription;t.signalChannel_.send(Yd,e,t.tinyId_),t.log_.info("accepted remote offer and acknowledged answer")}).catch(function(e){hl({eventType:"setRemoteDescription",kind:"offer",result:"failed",code:e instanceof dl?e.getExtraCode()||e.getCode():cl.UNKNOWN}),t.log_.error("failed to accept remote offer "+e)})}},{key:"setMutedState",value:function(e,t){if(this.isUplink_){"audio"===e?t?this.mutedState_|=4:this.mutedState_&=-5:t?this.mutedState_|=1:this.mutedState_&=-2;var n={srctinyid:0,userid:this.userId_,flag:this.mutedState_};this.log_.info("set ".concat(e," muted state: [").concat(t?"mute":"unmute","]")),this.signalChannel_.send(rl,n)}}},{key:"onSubscription",value:function(e){var t=e.data.content,n=t.srctinyid;if(this.hitTest(n)){var r=0===t.errCode,i=this.pendingSubscription_.shift();if(void 0!==i){this.subscriptionRetryCount_=0,this.isSubscriptionPending_=!1,-1!==this.subscriptionTimeout_&&(clearTimeout(this.subscriptionTimeout_),this.subscriptionTimeout_=-1);var o=i.stream;this.log_.info("".concat(o.getType()," stream ").concat(i.type," result: ").concat(r?"success":"failure"," errCode: ").concat(t.errCode)),i.callback(r),hl({eventType:i.type,result:0===t.errCode?"success":"failed"}),r&&"subscribe"===i.type&&this.emitter_.emit(ef,{stream:o})}if(this.pendingSubscription_.length>0){var a=this.pendingSubscription_[0];this.log_.info("schedule a pending subscription"),this.doSendSubscription(a.data,a.stream)}}}},{key:"subscribe",value:function(e,t){var n=this;return new Promise(function(r,i){if(void 0===t||"main"===e.getType()&&void 0!==t.audio&&void 0!==t.video&&t.audio===n.subscribeState_.audio&&t.video===n.subscribeState_.video||"auxiliary"===e.getType()&&void 0!==t.video&&n.subscribeState_.auxiliary===t.video)return n.emitter_.emit(ef,{stream:e,result:!0}),r();var o,a;"main"===e.getType()?(void 0!==t.audio&&(n.subscribeState_.audio=t.audio),void 0!==t.video&&(n.subscribeState_.video=t.video),n.subscribeState_.audio?(o=Tl,a="subscribe audio"):(o=Cl,a="unsubscribe audio"),n.addEventInternal(o,a),n.subscribeState_.video?(o=wl,a="subscribe video"):(o=Rl,a="unsubscribe video"),n.addEventInternal(o,a)):void 0!==t.video&&(n.subscribeState_.auxiliary=t.video);n.log_.info("subscribe ".concat(e.getType()," stream with options ").concat(JSON.stringify(t)," current state: ").concat(JSON.stringify(n.subscribeState_))),n.sendSubscription(e,"subscribe",function(e){if(e)r();else{var t="failed to subscribe remote stream";n.log_.error(t),i(new dl({message:t}))}})})}},{key:"unsubscribe",value:function(e){var t=this;return new Promise(function(n,r){"main"===e.getType()?(t.subscribeState_.audio=!1,t.subscribeState_.video=!1):t.subscribeState_.auxiliary=!1,t.log_.info("unsubscribe ".concat(e.getType()," stream with ").concat(JSON.stringify(t.subscribeState_))),t.sendSubscription(e,"unsubscribe",function(e){if(e)n();else{var i="failed to unsubscribe remote stream";t.log_.error(i),r(new dl({message:i}))}}),t.addEventInternal(Cl,"unsubscribe audio"),t.addEventInternal(Rl,"unsubscribe video")})}},{key:"addEventInternal",value:function(e,t){var n=this.client_.getUserId(),r={eventId:e,eventDesc:t,timestamp:Ro(),userId:n,tinyId:this.client_.getTinyId()};this.isUplink_||(r.remoteUserId=this.userId_,r.remoteTinyId=this.tinyId_),Wl(n,r)}},{key:"addTransportEvent",value:function(e){"connected"===e?this.addEventInternal(Fl,"ice transport is connected"):this.addEventInternal(Vl,"ice transport is disconnected")}},{key:"sendSubscription",value:function(e,t,n){var r={srctinyid:this.tinyId_,userid:this.userId_,audio:this.subscribeState_.audio,bigVideo:this.subscribeState_.video,auxVideo:this.subscribeState_.auxiliary};this.pendingSubscription_.length>0?this.log_.debug("queue the subscription for later handling"):this.doSendSubscription(r,e),this.pendingSubscription_.push({stream:e,type:t,data:r,callback:n}),e.setInSubscriptionCycle(!0)}},{key:"doSendSubscription",value:function(e,t){var n=this;"connected"===this.peerConnection_.connectionState||"completed"===this.peerConnection_.connectionState?(t&&this.pendingStreams_.push(t),this.log_.debug("doSendSubscription() send SUBSCRIBE command with data: ".concat(JSON.stringify(e))),this.signalChannel_.send(il,e),this.isSubscriptionPending_=!0,this.subscriptionTimeout_=setTimeout(function(){if(n.isSubscriptionPending_)if(n.log_.debug("subscription timeout"),n.subscriptionRetryCount_+=1,n.subscriptionRetryCount_<=3){n.log_.debug("resend subscription");var e=n.pendingSubscription_[0].data;n.doSendSubscription(e)}else n.log_.error("remote server not response to subscription"),n.pendingSubscription_.shift(),n.pendingStreams_.shift(),n.isSubscriptionPending_=!1,n.subscriptionRetryCount_=0,n.emitter_.emit(tf,new dl({code:cl.SUBSCRIPTION_TIMEOUT,message:"remote server not response to subscription"}))},5e3)):this.log_.debug("try to send subscription when connectionState ".concat(this.peerConnection_.connectionState))}},{key:"getPeerConnection",value:function(){return this.peerConnection_}},{key:"getUserId",value:function(){return this.userId_}},{key:"getTinyId",value:function(){return this.tinyId_}},{key:"setVideoStats",value:function(e){"start"===e?(this.hasVideo_=!0,this.startTime_=new Date,this.hasValidEndTime_=!1):(this.hasVideo_=!1,this.endTime_=new Date,this.hasValidEndTime_=!0)}},{key:"getVideoHealthStats",value:(t=ft(regeneratorRuntime.mark(function e(){var t,n,r,i,o,a;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.hasValidEndTime_||(this.endTime_=new Date),t=(this.endTime_-this.startTime_)/1e3,n=!1,r=0,i=0,o=0,!this.hasVideo_){e.next=21;break}if(n=!0,!this.isUplink_){e.next=15;break}return e.next=11,(new Bf).getSenderStats(this);case 11:a=e.sent,r=a.video.framesSent/t,e.next=19;break;case 15:return e.next=17,(new Bf).getReceiverStats(this);case 17:a=e.sent,r=a.video.framesDecoded/t;case 19:i=a.video.frameWidth,o=a.video.frameHeight;case 21:return e.abrupt("return",{valid:n,framerate:r,duration:t,width:i,height:o});case 22:case"end":return e.stop()}},e,this)})),function(){return t.apply(this,arguments)})},{key:"on",value:function(e,t){this.emitter_.on(e,t)}}]),e}(),Jf=function(){function e(t){var n=t.signalChannel,r=t.connnections,i=t.userId;ht(this,e),this.signalChannel_=n,this.connnections_=r,this.log_=new Pf({id:"q|"+i,direction:"local",type:""}),this.uplinkConnection_=null,this.uplinkNetworkQuality_=Op,this.uplinkRTT_=0,this.uplinkLoss_=0,this.downlinkNetworkQuality_=Op,this.downlinkPrevStatMap_=new Map,this.downlinkLossAndRTTMap_=new Map,this.interval_=-1,this.emitter_=new zd,this.initilize()}var t,n;return vt(e,[{key:"initilize",value:function(){var e=this;this.signalChannel_.on($d.UPLINK_NETWORK_STATS,function(t){e.handleUplinkNetworkQuality(t)}),this.signalChannel_.on(Jd,this.handleSignalConnectionStateChange.bind(this)),this.start()}},{key:"handleUplinkNetworkQuality",value:function(e){if(!this.uplinkConnection_)return this.uplinkNetworkQuality=Op,this.uplinkLoss_=0,void(this.uplinkRTT_=0);var t=this.uplinkConnection_.getPeerConnection();if(t&&this.isPeerConnectionDisconnected(t))return this.uplinkNetworkQuality=6,this.uplinkLoss_=0,void(this.uplinkRTT_=0);var n=e.data.content;if(0===n.result){var r=n.expectAudPkg+n.expectVidPkg,i=n.recvAudPkg+n.recvVidPkg,o=r-i;if(0===r&&0===i)return;this.uplinkLoss_=o<=0?0:Math.round(o/r*100),this.uplinkRTT_=n.rtt,this.uplinkNetworkQuality=this.getNetworkQuality(this.uplinkLoss_,this.uplinkRTT_)}}},{key:"handleDownlinkNetworkQuality",value:(n=ft(regeneratorRuntime.mark(function e(){var t,n,r,i,o,a,s,c,u,d,l,p,f,h,m=this;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.connnections_&&0!==this.connnections_.size){e.next=3;break}return this.downlinkNetworkQuality=Op,e.abrupt("return");case 3:if((t=Et(this.connnections_.values()).map(function(e){return e.getPeerConnection()}).filter(function(e){return!!e})).filter(this.isPeerConnectionDisconnected).length!==t.length){e.next=8;break}return this.downlinkNetworkQuality=6,e.abrupt("return");case 8:n=t.filter(function(e){return"connected"===e.connectionState}),r=0;case 10:if(!(r<n.length)){e.next=30;break}return e.next=13,this.getStat(n[r]);case 13:if(i=e.sent,o=i.rtt,a=i.totalPacketsLost,s=i.totalPacketsReceived,this.downlinkPrevStatMap_.has(n[r])){e.next=20;break}return this.downlinkPrevStatMap_.set(n[r],{totalPacketsLost:a,totalPacketsReceived:s}),e.abrupt("continue",27);case 20:c=0,u=this.downlinkPrevStatMap_.get(n[r]),d=a-u.totalPacketsLost,l=s-u.totalPacketsReceived,c=d<=0||l<0?0:Math.round(d/(d+l)*100),this.downlinkPrevStatMap_.set(n[r],{totalPacketsLost:a,totalPacketsReceived:s}),this.downlinkLossAndRTTMap_.set(n[r],{rtt:o,loss:c});case 27:r++,e.next=10;break;case 30:if(Et(this.downlinkPrevStatMap_.keys()).forEach(function(e){m.isPeerConnectionDisconnected(e)&&(m.downlinkPrevStatMap_.delete(e),m.downlinkLossAndRTTMap_.delete(e))}),0!==this.downlinkLossAndRTTMap_.size){e.next=33;break}return e.abrupt("return");case 33:p=this.getAverageLossAndRTT(Et(this.downlinkLossAndRTTMap_.values())),f=p.rtt,h=p.loss,this.downlinkNetworkQuality=this.getNetworkQuality(h,f);case 35:case"end":return e.stop()}},e,this)})),function(){return n.apply(this,arguments)})},{key:"getStat",value:(t=ft(regeneratorRuntime.mark(function e(t){var n,r,i,o;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n={rtt:0,totalPacketsLost:0,totalPacketsReceived:0},t&&tp()){e.next=3;break}return e.abrupt("return",n);case 3:r=t.getReceivers(),e.prev=4,i=0;case 6:if(!(i<r.length)){e.next=15;break}return o=r[i],e.next=10,o.getStats();case 10:e.sent.forEach(function(e){"candidate-pair"===e.type&&"number"==typeof e.currentRoundTripTime&&(n.rtt=Math.round(1e3*e.currentRoundTripTime)),"inbound-rtp"!==e.type||"audio"!==e.mediaType&&"video"!==e.mediaType||(n.totalPacketsLost+=e.packetsLost,n.totalPacketsReceived+=e.packetsReceived)});case 12:i++,e.next=6;break;case 15:return e.abrupt("return",n);case 18:return e.prev=18,e.t0=e.catch(4),e.abrupt("return",n);case 21:case"end":return e.stop()}},e,null,[[4,18]])})),function(e){return t.apply(this,arguments)})},{key:"getAverageLossAndRTT",value:function(e){var t={rtt:0,loss:0};return Array.isArray(e)&&e.length>0&&(e.forEach(function(e){t.rtt+=e.rtt,t.loss+=e.loss}),Object.keys(t).forEach(function(n){t[n]=Math.round(t[n]/e.length)})),t}},{key:"getNetworkQuality",value:function(e,t){return e>50||t>500?5:e>30||t>350?4:e>20||t>200?3:e>10||t>100?2:e>=0||t>=0?1:Op}},{key:"handleSignalConnectionStateChange",value:function(e){e.state===Qd.DISCONNECTED?(this.uplinkRTT_=0,this.uplinkLoss_=0,this.uplinkNetworkQuality=6):e.state===Qd.CONNECTED&&6===this.uplinkNetworkQuality&&(this.uplinkNetworkQuality=5)}},{key:"handleUplinkConnectionStateChange",value:function(e){var t=e.state;t===Jp.DISCONNECTED?(this.uplinkLoss_=0,this.uplinkRTT_=0,this.uplinkNetworkQuality=6):t===Jp.CONNECTED&&6===this.uplinkNetworkQuality&&(this.uplinkNetworkQuality=5)}},{key:"isPeerConnectionDisconnected",value:function(e){return!(!e||"disconnected"!==e.connectionState&&"failed"!==e.connectionState&&"closed"!==e.connectionState)}},{key:"setUplinkConnection",value:function(e){this.uplinkConnection_=e,this.uplinkConnection_?this.uplinkConnection_.on(nf,this.handleUplinkConnectionStateChange.bind(this)):(this.uplinkNetworkQuality=Op,this.uplinkRTT_=0,this.uplinkLoss_=0)}},{key:"start",value:function(){var e=this;-1===this.interval_?(this.log_.info("start network quality calculating"),this.interval_=setInterval(function(){e.handleDownlinkNetworkQuality(),e.emitter_.emit(vf,{uplinkNetworkQuality:e.uplinkNetworkQuality,downlinkNetworkQuality:e.downlinkNetworkQuality})},2e3)):this.log_.info("network quality calculating is already started")}},{key:"stop",value:function(){this.log_.info("stop network quality calculating"),-1!==this.interval_&&(clearInterval(this.interval_),this.interval_=-1)}},{key:"on",value:function(e,t){this.emitter_.on(e,t)}},{key:"uplinkNetworkQuality",get:function(){return this.uplinkNetworkQuality_},set:function(e){e!==this.uplinkNetworkQuality_&&this.log_.info("uplink network quality change ".concat(this.uplinkNetworkQuality," -> ").concat(e,", rtt: ").concat(this.uplinkRTT_,", loss: ").concat(this.uplinkLoss_)),this.uplinkNetworkQuality_=e}},{key:"downlinkNetworkQuality",get:function(){return this.downlinkNetworkQuality_},set:function(e){if(e!==this.downlinkNetworkQuality_){var t=this.getAverageLossAndRTT(Et(this.downlinkLossAndRTTMap_.values())),n=t.rtt,r=t.loss;this.log_.info("downlink network quality change ".concat(this.downlinkNetworkQuality," -> ").concat(e,", rtt: ").concat(n,", loss: ").concat(r))}this.downlinkNetworkQuality_=e}}]),e}(),qf=it.findIndex,Qf=!0;"findIndex"in[]&&Array(1).findIndex(function(){Qf=!1}),Ae({target:"Array",proto:!0,forced:Qf},{findIndex:function(e){return qf(this,e,arguments.length>1?arguments[1]:void 0)}}),ns("findIndex");var Kf=function(){function e(){ht(this,e),this.log_=sr,this.localStream_=null,this.prevDevices_=[],this.timer=-1,this.initialize()}var t,n,r;return vt(e,[{key:"initialize",value:(r=ft(regeneratorRuntime.mark(function e(){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:navigator.mediaDevices.addEventListener("devicechange",this.onDeviceChange.bind(this));case 1:case"end":return e.stop()}},e,this)})),function(){return r.apply(this,arguments)})},{key:"onDeviceChange",value:(n=ft(regeneratorRuntime.mark(function e(){var t,n,r,i=this;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.localStream_&&this.localStream_.getMediaStream()&&!this.localStream_.getScreen()){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,Th.getDevices();case 4:t=e.sent,n=t.filter(function(e){return i.prevDevices_.findIndex(function(t){var n=t.deviceId;return e.deviceId===n})<0}),r=this.prevDevices_.filter(function(e){return t.findIndex(function(t){var n=t.deviceId;return e.deviceId===n})<0}),n.length>0&&this.handleDeviceAdded(this.prevDevices_,n),r.length>0&&this.handleDeviceRemoved(t,r),this.prevDevices_=t;case 10:case"end":return e.stop()}},e,this)})),function(){return n.apply(this,arguments)})},{key:"setLocalStream",value:(t=ft(regeneratorRuntime.mark(function e(t){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!t){e.next=4;break}return e.next=3,Th.getDevices();case 3:this.prevDevices_=e.sent;case 4:this.localStream_=t;case 5:case"end":return e.stop()}},e,this)})),function(e){return t.apply(this,arguments)})},{key:"handleDeviceAdded",value:function(e,t){this.log_.warn("devicesAdded: ".concat(JSON.stringify(t))),this.localStream_.updateDeviceIdInUse();var n=t.filter(function(e){return"videoinput"===e.kind}),r=t.filter(function(e){return"audioinput"===e.kind}),i=e.filter(function(e){return"videoinput"===e.kind}),o=e.filter(function(e){return"audioinput"===e.kind}),a=n.length>0&&0===i.length&&this.localStream_.getVideo(),s=r.length>0&&0===o.length&&this.localStream_.getAudio();if(s&&a)return this.log_.info("new microphone and camera detected, but there was no device before."),void this.updateStreamDebounced({cameraId:n[0].deviceId,microphoneId:r[0].deviceId});a&&(this.log_.info("new camera detected, but there was no camera before."),this.updateStreamDebounced({cameraId:n[0].deviceId,microphoneId:this.localStream_.getMicrophoneId()})),s&&(this.log_.info("new microphone detected, but there was no microphone before."),this.updateStreamDebounced({microphoneId:r[0].deviceId,cameraId:this.localStream_.getCameraId()}))}},{key:"handleDeviceRemoved",value:function(e,t){this.log_.warn("devicesRemoved: ".concat(JSON.stringify(t))),this.localStream_.updateDeviceIdInUse();var n=!1,r=!1,i=this.localStream_.getCameraId(),o=this.localStream_.getMicrophoneId();if("default"===o){var a=this.localStream_.getMicrophoneGroupId(),s=e.filter(function(e){return"default"===e.deviceId&&"audioinput"===e.kind})[0];s&&s.groupId!==a&&(r=!0)}if(t.forEach(function(e){var t=e.deviceId;i.length>0&&t===i?n=!0:o.length>0&&t===o&&(r=!0)}),n&&r)return this.log_.warn("current camera and microphone in use is lost, cameraId: ".concat(i,", microphoneId: ").concat(o)),void((this.localStream_.getAudio()||this.localStream_.getVideo())&&this.updateStreamDebounced());n&&(this.log_.warn("current camera in use is lost, deviceId: ".concat(i)),this.localStream_.getVideo()&&this.updateStreamDebounced({microphoneId:o})),r&&(this.log_.warn("current microphone in use is lost, deviceId: ".concat(o)),this.localStream_.getAudio()&&this.updateStreamDebounced({cameraId:i}))}},{key:"updateStreamDebounced",value:function(e){var t=this;this.timer&&(clearTimeout(this.timer),this.timer=-1),this.timer=setTimeout(function(){t.localStream_.updateStream(e),t.timer=-1},500)}}]),e}();function $f(e){return e=window.localStorage?localStorage.getItem(e)||sessionStorage.getItem(e):(e=document.cookie.match(new RegExp("(?:^|;\\s)"+e+"=(.*?)(?:;\\s|$)")))?e[1]:""}function Yf(e,t,n){if(window.localStorage)try{n?localStorage.setItem(e,t):sessionStorage.setItem(e,t)}catch(r){}else document.cookie=e+"="+t+";path=/;domain="+function(){var e=window.location.host,t=e.split(".");return 2<t.length&&(e=({"com.cn":1,"js.cn":1,"net.cn":1,"gov.cn":1,"com.hk":1,"co.nz":1}[t.slice(-2).join(".")]?t.slice(-3):t.slice(-2)).join(".")),e}()+(n?";expires="+n:"")}function Xf(e,t){var n,r={};if(void 0===t)var i=window.location,o=i.host,a=i.pathname,s=i.search.substr(1),c=i.hash;else o=(i=t.match(/\w+:\/\/((?:[\w-]+\.)+\w+)(?::\d+)?(\/[^\?\\"'\|:<>]*)?(?:\?([^'"\\<>#]*))?(?:#(\w+))?/i)||[])[1],a=i[2],s=i[3],c=i[4];return void 0!==c&&(c=c.replace(/"|'|<|>/gi,"M")),s&&function(){for(var e=s.split("&"),t=0,n=e.length;t<n;t++)if(-1!=e[t].indexOf("=")){var i=e[t].indexOf("="),o=e[t].slice(0,i);i=e[t].slice(i+1),r[o]=i}}(),s=function(){if(void 0===s)return s;for(var t=s.split("&"),n=[],r=0,i=t.length;r<i;r++)if(-1!=t[r].indexOf("=")){var o=t[r].indexOf("="),a=t[r].slice(0,o);o=t[r].slice(o+1),e.ignoreParams&&-1!=e.ignoreParams.indexOf(a)||n.push(a+"="+o)}return n.join("&")}(),c&&function(){for(var e=0==c.indexOf("#")?c.substr(1).split("&"):c.split("&"),t=0,r=e.length;t<r;t++)if(-1!=e[t].indexOf("=")){var i=e[t].indexOf("="),o=e[t].slice(0,i);if(i=e[t].slice(i+1),"adtag"===o.toLowerCase()){n=i;break}}}(),{host:o,path:a,search:s,hash:c,param:r,adtag:n}}function Zf(e){var t,n=Xf(e),r={dm:n.host,pvi:"",si:"",url:n.path,arg:encodeURIComponent(n.search||"").substr(0,512),ty:0};return r.pvi=function(){var t=new Date((new Date).getTime()+63072e6).toGMTString();if(e.userReport){var n=$f("pgv_uid");n&&n==e.user.user_id||(r.ty=1,Yf("pgv_uid",e.user.user_id,t)),n=e.user.user_id}else(n=$f("pgv_pvi"))||(r.ty=1,Yf("pgv_pvi",n=eh(),t));return n}(),r.si=((t=$f("pgv_si"))||Yf("pgv_si",t=eh("s")),t),r.url=function(){var t=n.path;return e.senseQuery&&(t+=n.search?"?"+encodeURIComponent(n.search||"").substr(0,512):""),e.senseHash&&(t+=n.hash?encodeURIComponent(n.hash):""),t}(),r}function eh(e){for(var t=[0,1,2,3,4,5,6,7,8,9],n=10;1<n;n--){var r=Math.floor(10*Math.random()),i=t[r];t[r]=t[n-1],t[n-1]=i}for(n=r=0;5>n;n++)r=10*r+t[n];return(e||"")+(r+"")+ +new Date}function th(e){return{r2:e.sid}}function nh(e){var t={};if(e){var n,r=[];for(n in e)e.hasOwnProperty(n)&&r.push(encodeURIComponent(n)+"="+encodeURIComponent(e[n]));e=r.join(";"),t.ext=e}return t}function rh(e){var t=Xf(e,document.referrer);return e=Xf(e),{rdm:t.host,rurl:t.path,rarg:encodeURIComponent(t.search||"").substr(0,512),adt:e.param.ADTAG||e.param.adtag||e.param.CKTAG||e.param.cktag||e.param.PTAG||e.param.ptag||e.adtag}}function ih(){try{var e=navigator,t=screen||{width:"",height:"",colorDepth:""},n={scr:t.width+"x"+t.height,scl:t.colorDepth+"-bit",lg:(e.language||e.userLanguage).toLowerCase(),tz:(new Date).getTimezoneOffset()/60}}catch(r){return{}}return n}var oh={conf:{},version:"2.0.19",init:function(e){var t={sid:0,cid:0,autoReport:0,senseHash:0,senseQuery:0,userReport:0,performanceMonitor:0,ignoreParams:[]};if(e)for(var n in e)e.hasOwnProperty(n)&&t.hasOwnProperty(n)&&(t[n]=e[n]);this.conf=t,this.conf.autoReport&&this.pgv()},pgv:function(){var e=this.conf,t=[],n=this.version;if(e.sid)if(!e.userReport||e.user&&e.user.user_id&&!parseInt(e.user.user_id,10)!==conf.user.user_id){for(var r=0,i=[Zf(e),rh(e),th(e),ih(),nh({version:n}),{random:+new Date}],o=i.length;r<o;r++)for(var a in i[r])i[r].hasOwnProperty(a)&&t.push(a+"="+(void 0===i[r][a]?"":i[r][a]));var s=function(e){e="https://pingtas.qq.com/webview/pingd?"+e.join("&").toLowerCase();var t=new Image;t.onload=t.onerror=t.onabort=function(){t=t.onload=t.onerror=t.onabort=null},t.src=e};s(t),e.performanceMonitor&&(t=function(){for(var t=function(){if(window.performance){var e=window.performance.timing,t={value:e.domainLookupEnd-e.domainLookupStart},n={value:e.connectEnd-e.connectStart},r={value:e.responseStart-(e.requestStart||e.responseStart+1)},i=e.responseEnd-e.responseStart;e.domContentLoadedEventStart?0>i&&(i=0):i=-1,e={domainLookupTime:t,connectTime:n,requestTime:r,resourcesLoadedTime:{value:i},domParsingTime:{value:e.domContentLoadedEventStart?e.domInteractive-e.domLoading:-1},domContentLoadedTime:{value:e.domContentLoadedEventStart?e.domContentLoadedEventStart-e.fetchStart:-1}}}else e="";return e}(),r=[],i=[],o=0,a=[Zf(e),{r2:e.cid},ih(),{random:+new Date}],c=a.length;o<c;o++)for(var u in a[o])a[o].hasOwnProperty(u)&&i.push(u+"="+(void 0===a[o][u]?"":a[o][u]));for(u in t)t.hasOwnProperty(u)&&("domContentLoadedTime"==u?i.push("r3="+t[u].value):r.push(t[u].value));t=nh({pfm:r.join("_"),version:n}),i.push("ext="+t.ext),s(i)},void 0!==window.performance&&void 0!==window.performance.timing&&0!=window.performance.timing.loadEventEnd?t():window.attachEvent?window.attachEvent("onload",t):window.addEventListener&&window.addEventListener("load",t,!1))}else console.log("MTA H5分析错误提示：您选择了用户统计uv，请设置业务的user_id，需为int类型");else console.log("MTA H5分析错误提示：您没有设置sid")},clickStat:function(e,t){var n=this.conf,r=[],i=Zf(n),o=th(n);if(n.cid){i.dm="taclick",i.url=e,o.r2=n.cid,o.r5=function(e){e=void 0===e?{}:e;var t,n=[];for(t in e)e.hasOwnProperty(t)&&n.push(t+"="+encodeURIComponent(e[t]));return n.join(";")}(t);var a=0;for(i=(n=[i,rh(n),o,ih(),nh({version:this.version}),{random:+new Date}]).length;a<i;a++)for(var s in n[a])n[a].hasOwnProperty(s)&&r.push(s+"="+(void 0===n[a][s]?"":n[a][s]));r="https://pingtas.qq.com/webview/pingd?"+r.join("&");var c=new Image;c.onload=c.onerror=c.onabort=function(){c=c.onload=c.onerror=c.onabort=null},c.src=r}else console.log("MTA H5分析错误提示：您没有设置cid,请到管理台开通自定义事件并更新统计代码")},clickShare:function(e){var t,n,r=this.conf,i=Xf(r),o=void 0===(i=i.param.CKTAG||i.param.ckatg)?[]:i.split(".");if(r.cid){i=[];var a=Zf(r),s=th(r);for(a.dm="taclick_share",a.url="mtah5-share-"+e,s.r2=r.cid,s.r5=(n=[],2===(t=o).length&&"mtah5_share"==t[0]&&n.push(t[0]+"="+t[1]),n.join(";")),e=0,a=(r=[a,rh(r),s,ih(),nh({version:this.version}),{random:+new Date}]).length;e<a;e++)for(var c in r[e])r[e].hasOwnProperty(c)&&i.push(c+"="+(void 0===r[e][c]?"":r[e][c]));c="https://pingtas.qq.com/webview/pingd?"+i.join("&");var u=new Image;u.onload=u.onerror=u.onabort=function(){u=u.onload=u.onerror=u.onabort=null},u.src=c}else console.log("MTA H5分析错误提示：您没有设置cid,请到管理台开通自定义事件并更新统计代码")}},ah=new(function(){function e(){ht(this,e),this.init()}return vt(e,[{key:"report",value:function(e,t){try{oh.clickStat(e,t)}catch(Rh){}}},{key:"stat",value:function(){try{oh.pgv()}catch(Rh){}}},{key:"init",value:function(){try{oh.init({sid:"500699039",cid:"500699088",autoReport:1,senseHash:0,senseQuery:0,performanceMonitor:0,ignoreParams:[]})}catch(Rh){}}}]),e}()),sh=function(){function e(t){if(ht(this,e),this.mode_=t.mode,this.sdpSemantics_="plan-b",void 0!==t.sdpSemantics?this.sdpSemantics_=t.sdpSemantics:Kl()&&(this.sdpSemantics_="unified-plan"),this.sdkAppId_=t.sdkAppId,this.userId_=t.userId,this.log_=new Pf({id:"c|"+this.userId_,direction:"local",type:""}),this.userSig_=t.userSig,this.roomId_=0,this.useStringRoomId_=t.useStringRoomId,this.recordId_=null,this.pureAudioPushMode_=null,this.version_=t.version,this.log_.info("using sdpSemantics: "+this.sdpSemantics_),sr.setConfig({sdkAppId:this.sdkAppId_,userId:this.userId_,version:this.version_}),void 0!==t.recordId){if(!Number.isInteger(Number(t.recordId)))throw new dl({code:cl.INVALID_PARAMETER,message:"recordId must be an integer number"});this.recordId_=t.recordId}var n,r;if(void 0!==t.pureAudioPushMode){if(!Number.isInteger(Number(t.pureAudioPushMode)))throw new dl({code:cl.INVALID_PARAMETER,message:"pureAudioPushMode must be an integer number"});this.pureAudioPushMode_=t.pureAudioPushMode,n&&n.Str_uc_params?n.Str_uc_params.pure_audio_push_mod=this.pureAudioPushMode_:n={Str_uc_params:{pure_audio_push_mod:this.pureAudioPushMode_}}}if(this.bussinessInfo_=t.bussinessInfo,void 0!==t.streamId){if(!("string"==typeof t.streamId&&String(t.streamId)&&String(t.streamId).length<=64))throw new dl({code:cl.INVALID_PARAMETER,message:"streamId must be a sting literal within 64 bytes, and not be empty"});n={Str_uc_params:{userdefine_streamid_main:t.streamId}}}if(void 0!==t.userDefineRecordId){if(null===t.userDefineRecordId.match(/^[A-Za-z0-9_-]{1,64}$/gi))throw new dl({code:cl.INVALID_PARAMETER,message:"userDefineRecordId must be a sting literal contains (a-zA-Z),(0-9), underline and hyphen, within 64 bytes, and not be empty"});n?n.Str_uc_params.userdefine_record_id=t.userDefineRecordId:n={Str_uc_params:{userdefine_record_id:t.userDefineRecordId}}}if(void 0!==t.userDefinePushArgs){if(!("string"==typeof t.userDefinePushArgs&&String(t.userDefinePushArgs)&&String(t.userDefinePushArgs).length<=256))throw new dl({code:cl.INVALID_PARAMETER,message:"userDefinePushArgs must be a sting literal within 256 bytes, and not be empty"});n?n.Str_uc_params.userdefine_push_args=t.userDefinePushArgs:n={Str_uc_params:{userdefine_push_args:t.userDefinePushArgs}}}n&&(this.bussinessInfo_=JSON.stringify(n)),this.disableReceiver_=!1,this.signalChannel_=null,this.isScreenShareOnly_=0,void 0!==t.isScreenShareOnly&&(this.isScreenShareOnly_=t.isScreenShareOnly?1:0),this.role_="anchor",this.privateMapKey_="",this.tinyId_=0,this.proxy_=null,this.turnServer_=null,this.connections_=new Map,this.connectionsRefreshCount_=new Map,this.pendingRefresh_=new Map,this.mutedStates_=new Map,this.localStream_=null,this.isPublishing_=!1,this.uplinkConnection_=null,this.emitter_=new zd,this.signalInfo_={},this.isInitialized_=!1,this.isJoined_=!1,this.heartbeat_=-1,this.stats_=new Bf,this.joinTimeout_=-1,this.publishTimeout_=-1,this.unpublishTimeout_=-1,this.publishRetryCount_=0,this.networkQuality_=null,this.startJoinTimestamp_=new Date,this.joinedTimestamp_=new Date,this.downlinkVideoHealthStats_={},this.uplinkVideoHealthStats_={},this.basis_={browser:vp().name+"/"+vp().fullVersion,os:up().osName,displayResolution:gp(),isScreenShareSupported:$l(),isWebRTCSupported:_p(),isGetUserMediaSupported:yp(),isWebAudioSupported:(r={isSupported:!1,isCreateMediaStreamSourceSupported:!1},["AudioContext","webkitAudioContext","mozAudioContext","msAudioContext"].forEach(function(e){r.isSupported||e in window&&(r.isSupported=!0,window[e]&&"createMediaStreamSource"in window[e].prototype&&(r.isCreateMediaStreamSourceSupported=!0))}),r.isSupported),isWebSocketsSupported:"WebSocket"in window&&2===window.WebSocket.CLOSING}}var t,n,r,i,o,a,s,c,u,d,l,p,f;return vt(e,[{key:"setProxyServer",value:function(e){if(!e.startsWith("wss://"))throw new dl({code:cl.INVALID_PARAMETER,message:'proxy server url shall be started with "wss://"'});this.proxy_=e}},{key:"getUrl",value:function(e){var t=Fd(e);return!Vd()&&this.proxy_&&(t=this.proxy_),t}},{key:"getBackupUrl",value:function(){return this.proxy_?this.proxy_:Fd("wss://bk.rtc.qq.com:8687")}},{key:"getUserId",value:function(){return this.userId_}},{key:"getTinyId",value:function(){return this.tinyId_}},{key:"setTurnServer",value:function(e){this.turnServer_={},this.turnServer_.urls="turn:"+e.url,void 0!==e.username&&void 0!==e.credential&&(this.turnServer_.username=e.username,this.turnServer_.credential=e.credential,this.turnServer_.credentialType="password",void 0!==e.credentialType&&(this.turnServer_.credentialType=e.credentialType))}},{key:"initialize",value:function(){var e=this;return new Promise(function(t,n){e.log_.info("setup signal channel"),e.signalChannel_=new zl({sdkAppId:e.sdkAppId_,userId:e.userId_,userSig:e.userSig_,url:e.getUrl("wss://qcloud.rtc.qq.com:8687"),backupUrl:e.getUrl("wss://bk.rtc.qq.com:8687"),version:e.version_}),e.networkQuality_||(e.networkQuality_=new Jf({connnections:e.connections_,signalChannel:e.signalChannel_,userId:e.userId_}),e.networkQuality_.on(vf,function(t){e.emitter_.emit(vf,t)})),e.deviceDetector_||(e.deviceDetector_=new Kf),e.signalChannel_.on(Jd,function(t){switch(e.log_.info("SignalChannel state changed from ".concat(t.prevState," to ").concat(t.state)),t.state){case Qd.CONNECTING:ah.report(Bp,{value:"pending"});break;case Qd.CONNECTED:t.prevState===Qd.RECONNECTING?ah.report(Hp,{value:"success"}):t.prevState===Qd.CONNECTING&&ah.report(Bp,{value:"success"});break;case Qd.RECONNECTING:t.prevState===Qd.CONNECTED&&ah.report(Hp,{value:"pending"})}e.emitter_.emit(cf,t)}),e.signalChannel_.on(qd,function(t){e.isInitialized_?(e.closeUplink(),e.closeConnections(),e.emitter_.emit(gf,t)):n(t)}),e.signalChannel_.on($d.CHANNEL_SETUP_FAILED,function(t){e.log_.error("signal channel setup failed"),n(t)}),e.signalChannel_.on($d.CHANNEL_SETUP_SUCCESS,function(n){e.signalInfo_=n.signalInfo,e.tinyId_=e.signalInfo_.tinyId,e.isInitialized_=!0,t()}),e.signalChannel_.on($d.PEER_JOIN,function(t){e.disableReceiver_||e.onPeerJoin(t.data)}),e.signalChannel_.on($d.PEER_LEAVE,function(t){e.onPeerLeave(t.data)}),e.signalChannel_.on($d.UPDATE_REMOTE_MUTE_STAT,function(t){e.onUpdateRemoteMuteStat(t.data)}),e.signalChannel_.on($d.CLINET_BANNDED,function(t){var n=t.data.content;if(e.closeUplink(),e.closeConnections(),"banned"===n.type){var r="you got banned by account admin";e.log_.error("user was banned because of "+r),e.onClientBanned(r)}else if("kick"===n.type){var i="duplicated userId joining the room";e.log_.error("user was banned because of "+i),e.onClientBanned(i)}else e.log_.error("Relay server timeout observed"),e.emitter_.emit(gf,new dl({code:cl.SERVER_TIMEOUT,message:"Relay server timeout observed"}))}),e.signalChannel_.on($d.REQUEST_REBUILD_SESSION,function(t){e.signalInfo_=t.signalInfo;var n=[];e.connections_&&n.push(0);var r=[],i=!0,o=!1,a=void 0;try{for(var s,c=e.connections_[Symbol.iterator]();!(i=(s=c.next()).done);i=!0){var u=Ct(s.value,2),d=u[0],l=u[1];n.push(d);var p=l.getPeerConnection().remoteDescription;p&&r.push(p.sdp)}}catch(h){o=!0,a=h}finally{try{i||null==c.return||c.return()}finally{if(o)throw a}}var f={socketid:e.signalInfo_.socketId,tinyid:e.tinyId_,appid:e.sdkAppId_,openid:e.userId_,sessionid:String(e.roomId_),sids:n,relayInfo:e.signalInfo_.relayInnerIp,remotesdp:r};try{e.log_.info("reconnect - rebuild session with data: ".concat(JSON.stringify(f))),e.signalChannel_.send(nl,f)}catch(Rh){e.log_.error("reconnect failed because rebuild session failed")}}),e.signalChannel_.connect()})}},{key:"join",value:(f=ft(regeneratorRuntime.mark(function e(t){var n,r,i,o,a,s;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(ep()){e.next=2;break}throw new dl({code:cl.NOT_SUPPORTED,message:"the browser does NOT support TRTC!"});case 2:return ah.report(Dp,{value:"pending"}),this.startJoinTimestamp_=new Date,(n=Vd())||(n="qcloud",this.proxy_&&this.proxy_.startsWith("wss://trtc.rtc.qq.com")&&(n="trtc")),r=vp(),pl({env:n,sdkAppId:this.sdkAppId_,userId:this.userId_,version:this.version_,browserVersion:r.name+r.version,ua:navigator.userAgent}),Wl(this.userId_,{eventId:Ll,eventDesc:"joining room",timestamp:Ro(),userId:this.userId_,tinyId:this.tinyId_}),e.prev=9,e.next=12,this.initialize();case 12:e.next=19;break;case 14:throw e.prev=14,e.t0=e.catch(9),i=e.t0.getCode(),hl({eventType:"join",result:"failed",code:i}),e.t0;case 19:return e.prev=19,e.next=22,this.doJoin(t);case 22:e.next=30;break;case 24:throw e.prev=24,e.t1=e.catch(19),o=e.t1.getExtraCode(),a=0!==o?o:e.t1.getCode(),hl({eventType:"join",result:"failed",code:a}),e.t1;case 30:ah.report(Dp,{value:"success"}),this.joinedTimestamp_=new Date,s=this.joinedTimestamp_-this.startJoinTimestamp_,hl({eventType:"delta-join",result:"success",delta:s}),hl({eventType:"join",result:"success"}),this.uploadTrtcStats();case 36:case"end":return e.stop()}},e,this,[[9,14],[19,24]])})),function(e){return f.apply(this,arguments)})},{key:"uploadTrtcStats",value:(p=ft(regeneratorRuntime.mark(function e(){var t,n,r,i,o,a,s;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t="undefined",n="undefined",e.prev=2,e.next=5,Sp();case 5:r=e.sent,t=r&&r.length,e.next=12;break;case 9:e.prev=9,e.t0=e.catch(2),t="undefined";case 12:return e.prev=12,e.next=15,kp();case 15:i=e.sent,n=i&&i.length,e.next=22;break;case 19:e.prev=19,e.t1=e.catch(12),n="undefined";case 22:o={microphone:t,camera:n},a={webRTC:this.basis_.isWebRTCSupported,getUserMedia:this.basis_.isGetUserMediaSupported,webSocket:this.basis_.isWebSocketsSupported,screenShare:this.basis_.isScreenShareSupported,webAudio:this.basis_.isWebAudioSupported},s={browser:this.basis_.browser,os:this.basis_.os,trtc:a,devices:o},fl("trtcstats-"+JSON.stringify(s)),this.log_.info("TrtcStats-"+JSON.stringify(s));case 27:case"end":return e.stop()}},e,this,[[2,9],[12,19]])})),function(){return p.apply(this,arguments)})},{key:"getVersion",value:function(){var e=this.version_.split(".");return 1e3*parseInt(e[0])+100*parseInt(e[1])+parseInt(e[2])}},{key:"doJoin",value:function(e){var t=this;return new Promise(function(n,r){if(!t.isInitialized_)throw new dl({code:cl.INVALID_OPERATION,message:"SignalChannel is not ready yet"});if(t.isJoined_)throw new dl({code:cl.INVALID_OPERATION,message:"duplicate join() called"});t.roomId_=e.roomId,void 0!==e.role&&(t.role_=e.role);var i="";void 0!==e.privateMapKey&&(i=e.privateMapKey),t.privateMapKey_=i,t.log_.info("Join() => joining room: ".concat(e.roomId," mode: ").concat(t.mode_," role: ").concat(t.role_));var o,a=t.signalInfo_,s={openid:a.openId,tinyid:a.tinyId,peerconnectionport:"",useStrRoomid:!!t.useStringRoomId_&&1,roomid:String(e.roomId),sdkAppID:String(t.sdkAppId_),socketid:a.socketId,userSig:t.userSig_,privMapEncrypt:i,privMap:"",relayip:a.relayInnerIp,dataport:a.dataPort,stunport:a.stunPort,checkSigSeq:a.checkSigSeq,pstnBizType:0,pstnPhoneNumber:null,recordId:t.recordId_,role:"user",jsSdkVersion:String(t.getVersion()),sdpSemantics:t.sdpSemantics_,browserVersion:jd,closeLocalMedia:!0,trtcscene:"live"===t.mode_?Rp:Tp,trtcrole:"anchor"===t.role_?Cp:Ep,bussinessInfo:t.bussinessInfo_,isAuxUser:t.isScreenShareOnly_};t.joinTimeout_=setTimeout(function(){t.log_.error("join room timeout observed  https://trtc-1252463788.file.myqcloud.com/web/docs/tutorial-10-error-code-tips.html"),r(new dl({code:cl.JOIN_ROOM_FAILED,message:"join room timeout"}))},5e3),t.signalChannel_.sendWithReport(Zd,s,(o=0,navigator&&navigator.connection&&navigator.connection.effectiveType&&(o="4g"===navigator.connection.effectiveType?1:4),navigator&&navigator.connection&&navigator.connection.type&&("wifi"===navigator.connection.type?o=1:"cellular"===navigator.connection.type&&(o=4)),{AbilityOption:{GeneralLimit:{CPULimit:{uint32_CPU_num:String(navigator.hardwareConcurrency||0),str_CPU_name:String(navigator.platform),uint32_CPU_maxfreq:String(0),model:"",uint32_total_memory:String(0)},uint32_terminal_type:String(Sd?4:gd?2:vd?3:Od?12:Ad?5:Dd?13:1),uint32_device_type:String(0),str_os_verion:Sd?"Android":gd?"iPhone":vd?"iPad":Od?"Mac":Ad?"Windows":Dd?"Linux":"unknown",uint32_link_type:String(1),str_client_version:"4.6.4",uint32_net_type:String(o),ua:navigator.userAgent,version:""}}})),t.signalChannel_.once($d.JOIN_ROOM_RESULT,function(e){clearTimeout(t.joinTimeout_),t.joinTimeout_=-1;var i=e.data.content.ret;i?(t.log_.error("Join room failed result: "+i+" error: "+e.data.content.error),r(new dl({code:cl.JOIN_ROOM_FAILED,extraCode:i,message:"Failed to join room - "+e.data.content.error}))):(t.isJoined_=!0,t.log_.info("Join room success, start heartbeat"),t.startHeartbeat(),n())})})}},{key:"leave",value:(l=ft(regeneratorRuntime.mark(function e(){var t,n,r,i,o,a,s,c,u,d,l,p;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return Wl(this.userId_,{eventId:Ml,eventDesc:"leaving room",timestamp:Ro(),userId:this.userId_,tinyId:this.tinyId_}),e.prev=1,e.next=4,this.doHeartbeat();case 4:e.next=8;break;case 6:e.prev=6,e.t0=e.catch(1);case 8:t=!1,n=!0,r=!1,i=void 0,e.prev=12,o=this.connections_[Symbol.iterator]();case 14:if(n=(a=o.next()).done){e.next=24;break}if((s=Ct(a.value,2))[0],c=s[1],t){e.next=21;break}return e.next=19,c.getVideoHealthStats();case 19:(u=e.sent).valid&&(t=!0,this.downlinkVideoHealthStats_=u);case 21:n=!0,e.next=14;break;case 24:e.next=30;break;case 26:e.prev=26,e.t1=e.catch(12),r=!0,i=e.t1;case 30:e.prev=30,e.prev=31,n||null==o.return||o.return();case 33:if(e.prev=33,!r){e.next=36;break}throw i;case 36:return e.finish(33);case 37:return e.finish(30);case 38:return d={userAgent:navigator.userAgent},l={uplink:this.uplinkVideoHealthStats_,downlink:this.downlinkVideoHealthStats_},fl("healthstats-"+JSON.stringify(d)),fl("healthstats-"+JSON.stringify(l)),this.log_.info("HealthStats-"+JSON.stringify(l)),e.prev=43,e.next=46,this.doLeave();case 46:e.next=50;break;case 48:e.prev=48,e.t2=e.catch(43);case 50:this.isJoined_=!1,this.destroy(),hl({eventType:"leave",result:"success"}),p=Math.floor((new Date-this.joinedTimestamp_)/1e3),hl({eventType:"delta-leave",result:"success",delta:p});case 55:case"end":return e.stop()}},e,this,[[1,6],[12,26,30,38],[31,,33,37],[43,48]])})),function(){return l.apply(this,arguments)})},{key:"doLeave",value:function(){var e=this;return new Promise(function(){var t=ft(regeneratorRuntime.mark(function t(n,r){return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(e.isJoined_){t.next=2;break}return t.abrupt("return",n());case 2:return e.log_.info("leave() => leaving room"),e.stopHeartbeat(),e.closeConnections(),e.mutedStates_.clear(),e.clearPublishTimeout(),e.clearUnpublishTimeout(),e.clearNetworkQuality(),e.pendingRefresh_.clear(),t.next=12,e.closeUplink();case 12:return t.abrupt("return",n());case 13:case"end":return t.stop()}},t)}));return function(e,n){return t.apply(this,arguments)}}())}},{key:"clearPublishTimeout",value:function(){-1!==this.publishTimeout_&&(clearTimeout(this.publishTimeout_),this.publishTimeout_=-1)}},{key:"clearUnpublishTimeout",value:function(){this.unpublishTimeout_&&(clearTimeout(this.unpublishTimeout_),this.unpublishTimeout_=-1)}},{key:"clearNetworkQuality",value:function(){this.networkQuality_&&(this.networkQuality_.stop(),this.networkQuality_=null)}},{key:"closeConnections",value:function(){this.connections_.forEach(function(e){e.close()}),this.connections_.clear(),this.connectionsRefreshCount_.clear()}},{key:"destroy",value:function(){if(this.isJoined_)throw this.log_.warn("please call leave() before destroy() client"),new dl({code:cl.INVALID_OPERATION,message:"Please call leave() before destory() the client"});this.log_.info("destroying SignalChannel"),this.signalChannel_&&(this.signalChannel_.close(),this.signalChannel_=null)}},{key:"publish",value:function(e){var t=this;return new Promise(function(n,r){if(!ep())throw new dl({code:cl.NOT_SUPPORTED,message:"the browser does NOT support TRTC!"});if(!t.isJoined_)throw new dl({code:cl.INVALID_OPERATION,message:"please call join() before publish()"});if("live"===t.mode_&&"audience"===t.role_)throw new dl({code:cl.INVALID_OPERATION,message:'no permission to publish() under live/audience, please call swithRole("anchor") firstly before publish()'});if(t.localStream_)throw new dl({code:cl.INVALID_OPERATION,message:"duplicate publishing, please unpublish and then re-publish  https://trtc-1252463788.file.myqcloud.com/web/docs/tutorial-10-error-code-tips.html"});if(t.isPublishing_)throw new dl({code:cl.INVALID_OPERATION,message:"previous publishing is ongoing, please avoid re-publishing  https://trtc-1252463788.file.myqcloud.com/web/docs/tutorial-10-error-code-tips.html"});ah.report(Np,{value:"pending"}),t.isPublishing_=!0;var i=new Date,o=t.publishRetryCount_>0;t.log_.info("publish() => publishing local stream");var a=new zf({userId:t.userId_,tinyId:t.tinyId_,client:t,isUplink:!0,signalChannel:t.signalChannel_});o||ah.report(Vp,{value:"pending"}),a.initialize(),t.publishTimeout_=setTimeout(function(){t.isPublishing_=!1,a.close(),t.log_.error("failed to publish because of timeout  https://trtc-1252463788.file.myqcloud.com/web/docs/tutorial-10-error-code-tips.html"),hl({eventType:"publish",result:"failed",code:cl.UNKNOWN}),r(new dl({code:cl.UNKNOWN,message:"publish timeout"}))},1e4),a.publish(e,function(e){t.clearPublishTimeout(),t.isPublishing_=!1,a.close(),t.log_.error("failed to publish stream"),hl({eventType:"publish",result:"failed",code:e instanceof dl?e.getExtraCode()||e.getCode():cl.UNKNOWN}),r(e)}),a.on(Zp,function(r){ah.report(Np,{value:"success"}),t.clearPublishTimeout(),t.isPublishing_=!1,t.localStream_=r.stream,t.localStream_.setConnection(a),t.uplinkConnection_=a,hl({eventType:"publish",result:"success"});var o=new Date;hl({eventType:"delta-publish",result:"success",delta:o-i}),e.hasAudio()&&Wl(t.userId_,{eventId:vl,eventDesc:"publish audio track",timestamp:Ro(),userId:t.userId_,tinyId:t.tinyId_}),e.hasVideo()&&Wl(t.userId_,{eventId:ml,eventDesc:"publish video track",timestamp:Ro(),userId:t.userId_,tinyId:t.tinyId_}),t.networkQuality_&&t.networkQuality_.setUplinkConnection(t.uplinkConnection_),t.deviceDetector_&&t.deviceDetector_.setLocalStream(t.localStream_),n()}),a.on(nf,function(e){e.state===Jp.CONNECTED&&(e.prevState===Jp.CONNECTING&&ah.report(o?Fp:Vp,{value:"success"}),t.publishRetryCount_&&(t.publishRetryCount_=0,t.log_.info("auto re-publish success")))}),a.on(tf,function(n){if(n.getCode()===cl.ICE_TRANSPORT_ERROR&&t.publishRetryCount_<3&&t.localStream_===e)return ah.report(Fp,{value:"pending"}),t.publishRetryCount_+=1,t.log_.warn("try to re-plublish the stream due to ice failure observed"),void t.unpublish(e).then(function(){t.log_.warn("trying re-publish"),t.publish(e).then(function(){t.log_.info("auto re-publish is ongoing")}).catch(function(e){t.log_.warn("auto re-publish failure"),t.publishRetryCount_=0,t.emitter_.emit(gf,n)})}).catch(function(e){t.log_.error("re-plublish/unpublish failure observed"),t.publishRetryCount_=0,t.emitter_.emit(gf,n)});t.emitter_.emit(gf,n)})})}},{key:"unpublish",value:function(e){var t=this;return new Promise(function(n,r){if(t.isPublishing_)throw new dl({code:cl.INVALID_OPERATION,message:"unpublish() is being called during publish() is ongoing"});if(e!==t.localStream_)throw new dl({code:cl.INVALID_PARAMETER,message:"stream has not been published yet"});ah.report(Lp,{value:"pending"}),t.log_.info("unpublish() => unpublishing local stream"),t.doUnpublish().then(function(){ah.report(Lp,{value:"success"}),hl({eventType:"unpublish",result:"success"}),n()}).catch(function(e){hl({eventType:"unpublish",result:"failed",code:e instanceof dl?e.getExtraCode()||e.getCode():cl.UNKNOWN}),r()})})}},{key:"doUnpublish",value:function(){var e=this;return new Promise(function(t,n){e.signalChannel_.send(el),e.unpublishTimeout_=setTimeout(ft(regeneratorRuntime.mark(function t(){return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e.log_.warn("unpublish() timeout observed"),t.next=3,e.closeUplink();case 3:return t.abrupt("return",n());case 4:case"end":return t.stop()}},t)})),5e3),e.signalChannel_.once($d.CLOSE_PEER_ACK,function(){var n=ft(regeneratorRuntime.mark(function n(r){return regeneratorRuntime.wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return e.clearUnpublishTimeout(),e.log_.info("received CLOSE_PEER_ACK, close uplink connection"),n.next=4,e.closeUplink();case 4:return n.abrupt("return",t());case 5:case"end":return n.stop()}},n)}));return function(e){return n.apply(this,arguments)}}())})}},{key:"closeUplink",value:(d=ft(regeneratorRuntime.mark(function e(){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.uplinkConnection_){e.next=12;break}return e.next=3,this.uplinkConnection_.getVideoHealthStats();case 3:this.uplinkVideoHealthStats_=e.sent,this.uplinkConnection_.close(),this.uplinkConnection_=null,this.networkQuality_&&this.networkQuality_.setUplinkConnection(null),this.localStream_.hasAudio()&&Wl(this.userId_,{eventId:_l,eventDesc:"unpublish audio track",timestamp:Ro(),userId:this.userId_,tinyId:this.tinyId_}),this.localStream_.hasVideo()&&Wl(this.userId_,{eventId:gl,eventDesc:"unpublish video track",timestamp:Ro(),userId:this.userId_,tinyId:this.tinyId_}),this.localStream_.setConnection(null),this.localStream_=null,this.deviceDetector_&&this.deviceDetector_.setLocalStream(null);case 12:case"end":return e.stop()}},e,this)})),function(){return d.apply(this,arguments)})},{key:"subscribe",value:(u=ft(regeneratorRuntime.mark(function e(t,n){var r;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=2;break}throw new dl({code:cl.INVALID_PARAMETER,message:"stream is undefined or null"});case 2:if(t.isRemote()){e.next=4;break}throw new dl({code:cl.INVALID_PARAMETER,message:"try to subscribe a local stream"});case 4:return ah.report(jp,{value:"pending"}),this.log_.info("subscribe() => subscribe to [".concat(t.getUserId(),"] ").concat(t.getType()," stream with options: ").concat(JSON.stringify(n))),r=t.getConnection(),e.next=9,r.subscribe(t,n);case 9:ah.report(jp,{value:"success"});case 10:case"end":return e.stop()}},e,this)})),function(e,t){return u.apply(this,arguments)})},{key:"unsubscribe",value:(c=ft(regeneratorRuntime.mark(function e(t){var n;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=2;break}throw new dl({code:cl.INVALID_PARAMETER,message:"stream is undefined or null"});case 2:if(t.isRemote()){e.next=4;break}throw new dl({code:cl.INVALID_PARAMETER,message:"try to unsubscribe a local stream"});case 4:return this.log_.info("unsubscribe() => unsubscribe to [".concat(t.getUserId(),"] ").concat(t.getType()," stream")),ah.report(Up,{value:"pending"}),n=t.getConnection(),e.next=9,n.unsubscribe(t);case 9:ah.report(Up,{value:"success"});case 10:case"end":return e.stop()}},e,this)})),function(e){return c.apply(this,arguments)})},{key:"switchRole",value:(s=ft(regeneratorRuntime.mark(function e(t){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if("live"===this.mode_){e.next=2;break}throw new dl({code:cl.INVALID_PARAMETER,message:"role is only valid in live mode"});case 2:if("anchor"===t||"audience"===t){e.next=4;break}throw new dl({code:cl.INVALID_PARAMETER,message:"role could only be set to a value as anchor or audience"});case 4:if(this.role_===t){e.next=13;break}if(this.role_=t,this.log_.info("switchRole() => switch role to: "+t),this.isJoined_){e.next=9;break}return e.abrupt("return");case 9:return e.next=11,this.leave();case 11:return e.next=13,this.join({role:t,roomId:this.roomId_,privateMapKey:this.privateMapKey_});case 13:case"end":return e.stop()}},e,this)})),function(e){return s.apply(this,arguments)})},{key:"on",value:function(e,t){this.emitter_.on(e,t)}},{key:"off",value:function(e,t){"*"===e?this.emitter_.removeAllListeners():this.emitter_.off(e,t)}},{key:"getRemoteMutedState",value:function(){var e=this,t=[];return this.mutedStates_.forEach(function(n,r,i){var o=e.connections_.get(r);o&&t.push(_t({userId:o.getUserId()},n))}),t}},{key:"setDefaultMuteRemoteStreams",value:function(e){this.log_.info("setDefaultMuteRemoteStreams muted: "+e),this.disableReceiver_=e}},{key:"getTransportStats",value:(a=ft(regeneratorRuntime.mark(function e(){var t,n;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t={rtt:0},!this.uplinkConnection_){e.next=6;break}return e.next=4,this.stats_.getSenderStats(this.uplinkConnection_);case 4:n=e.sent,t.rtt=n.rtt;case 6:return e.abrupt("return",t);case 7:case"end":return e.stop()}},e,this)})),function(){return a.apply(this,arguments)})},{key:"getLocalAudioStats",value:(o=ft(regeneratorRuntime.mark(function e(){var t,n;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if((t={})[this.userId_]={bytesSent:0,packetsSent:0},!this.uplinkConnection_){e.next=7;break}return e.next=5,this.stats_.getSenderStats(this.uplinkConnection_);case 5:n=e.sent,t[this.userId_]={bytesSent:n.audio.bytesSent,packetsSent:n.audio.packetsSent};case 7:return e.abrupt("return",t);case 8:case"end":return e.stop()}},e,this)})),function(){return o.apply(this,arguments)})},{key:"getLocalVideoStats",value:(i=ft(regeneratorRuntime.mark(function e(){var t,n;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if((t={})[this.userId_]={bytesSent:0,packetsSent:0,framesEncoded:0,framesSent:0,frameWidth:0,frameHeight:0},!this.uplinkConnection_){e.next=7;break}return e.next=5,this.stats_.getSenderStats(this.uplinkConnection_);case 5:n=e.sent,t[this.userId_]={bytesSent:n.video.bytesSent,packetsSent:n.video.packetsSent,framesEncoded:n.video.framesEncoded,framesSent:n.video.framesSent,frameWidth:n.video.frameWidth,frameHeight:n.video.frameHeight};case 7:return e.abrupt("return",t);case 8:case"end":return e.stop()}},e,this)})),function(){return i.apply(this,arguments)})},{key:"getRemoteAudioStats",value:(r=ft(regeneratorRuntime.mark(function e(){var t,n,r,i,o,a,s,c,u;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:t={},n=!0,r=!1,i=void 0,e.prev=4,o=this.connections_[Symbol.iterator]();case 6:if(n=(a=o.next()).done){e.next=15;break}return(s=Ct(a.value,2))[0],c=s[1],e.next=10,this.stats_.getReceiverStats(c);case 10:(u=e.sent).hasAudio&&(t[u.userId]={bytesReceived:u.audio.bytesReceived,packetsReceived:u.audio.packetsReceived,packetsLost:u.audio.packetsLost});case 12:n=!0,e.next=6;break;case 15:e.next=21;break;case 17:e.prev=17,e.t0=e.catch(4),r=!0,i=e.t0;case 21:e.prev=21,e.prev=22,n||null==o.return||o.return();case 24:if(e.prev=24,!r){e.next=27;break}throw i;case 27:return e.finish(24);case 28:return e.finish(21);case 29:return e.abrupt("return",t);case 30:case"end":return e.stop()}},e,this,[[4,17,21,29],[22,,24,28]])})),function(){return r.apply(this,arguments)})},{key:"getRemoteVideoStats",value:(n=ft(regeneratorRuntime.mark(function e(){var t,n,r,i,o,a,s,c,u;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:t={},n=!0,r=!1,i=void 0,e.prev=4,o=this.connections_[Symbol.iterator]();case 6:if(n=(a=o.next()).done){e.next=15;break}return(s=Ct(a.value,2))[0],c=s[1],e.next=10,this.stats_.getReceiverStats(c);case 10:(u=e.sent).hasVideo&&(t[u.userId]={bytesReceived:u.video.bytesReceived,packetsReceived:u.video.packetsReceived,packetsLost:u.video.packetsLost,framesDecoded:u.video.framesDecoded,frameWidth:u.video.frameWidth,frameHeight:u.video.frameHeight});case 12:n=!0,e.next=6;break;case 15:e.next=21;break;case 17:e.prev=17,e.t0=e.catch(4),r=!0,i=e.t0;case 21:e.prev=21,e.prev=22,n||null==o.return||o.return();case 24:if(e.prev=24,!r){e.next=27;break}throw i;case 27:return e.finish(24);case 28:return e.finish(21);case 29:return e.abrupt("return",t);case 30:case"end":return e.stop()}},e,this,[[4,17,21,29],[22,,24,28]])})),function(){return n.apply(this,arguments)})},{key:"getSdpSemantics",value:function(){return this.sdpSemantics_}},{key:"getIceServers",value:function(){var e=[];return this.turnServer_&&e.push(this.turnServer_),e}},{key:"startHeartbeat",value:function(){if(-1===this.heartbeat_){this.log_.info("startHeartbeat..."),this.heartbeat_=setInterval(this.doHeartbeat.bind(this),2e3)}}},{key:"stopHeartbeat",value:function(){-1!==this.heartbeat_&&(this.log_.info("stopHeartbeat"),clearInterval(this.heartbeat_),this.heartbeat_=-1)}},{key:"doHeartbeat",value:(t=ft(regeneratorRuntime.mark(function e(){var t,n,r,i,o;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.stats_.getStatsReport(this.uplinkConnection_,this.connections_);case 2:t=e.sent,n=this.signalChannel_.isConnected()?Bl(this.userId_):[],r={WebRTCQualityReq:t,eventList:n,sdkAppId:this.sdkAppId_,tinyid:this.tinyId_,roomid:this.roomId_,socketid:this.signalInfo_.socketId,clientip:this.signalInfo_.localIp,serverip:this.signalInfo_.relayIp,cpunumber:navigator.hardwareConcurrency||0,cpudevice:navigator.platform,devicename:navigator.platform,ostype:navigator.platform,mode:this.localStream_?this.localStream_.getScreen()?"screen":"video":""},i=0,this.localStream_&&this.localStream_.getMediaStream()&&(o=this.localStream_.getMediaStream().getAudioTracks(),i=o.length>0&&o[0].muted?3:1),r.WebRTCQualityReq.AudioReportState.uint32_microphone_status=i,this.signalChannel_.send(tl,r);case 9:case"end":return e.stop()}},e,this)})),function(){return t.apply(this,arguments)})},{key:"onPeerJoin",value:function(e){var t=this,n=e.content,r=n.srcopenid,i=n.srctinyid,o=n.remoteoffer,a=this.pendingRefresh_.has(i),s=this.connections_.get(i);s&&(this.log_.warn("duplicated peer-join observed, rebuild the connection"),this.log_.info("remote peer [".concat(r,"] is leaving")),this.emitter_.emit(df,{userId:r}),s.close(),this.connections_.delete(i));var c=this.pendingRefresh_.get(i);c&&(clearTimeout(c),this.pendingRefresh_.delete(i)),this.log_.info("remote peer [".concat(r,"] is online")),this.emitter_.emit(uf,{userId:r});var u=new zf({userId:r,tinyId:i,client:this,isUplink:!1,signalChannel:this.signalChannel_});this.connections_.set(i,u),a||ah.report(Gp,{value:"pending"}),u.initialize(),u.setRemoteOffer(o),u.on($p,function(e){t.emitter_.emit(rf,{stream:e.stream})}),u.on(Yp,function(e){t.emitter_.emit(of,{stream:e.stream})}),u.on(Xp,function(e){t.emitter_.emit(af,{stream:e.stream})}),u.on(ef,function(e){t.emitter_.emit(sf,{stream:e.stream})}),u.on(tf,function(e){var n=e.getCode(),o=t.connectionsRefreshCount_.get(i);if(n===cl.ICE_TRANSPORT_ERROR&&o<5){o+=1,t.connectionsRefreshCount_.set(i,o),t.log_.warn("try to refresh remote peer [".concat(r,"]"));var a={srctinyid:i};ah.report(Wp,{value:"pending"}),t.signalChannel_.send(sl,a);var s=t.pendingRefresh_.get(i);s&&clearTimeout(s);var c=t;return s=setTimeout(function(){t.log_.warn("refresh remote peer timeout observed"),c.emitter_.emit(gf,e)},1e4),void t.pendingRefresh_.set(i,s)}t.emitter_.emit(gf,e)}),u.on(nf,function(e){e.state===Jp.CONNECTED&&(t.connectionsRefreshCount_.set(i,0),e.prevState===Jp.CONNECTING&&ah.report(a?Wp:Gp,{value:"success"}))})}},{key:"onPeerLeave",value:function(e){var t=e.content,n=t.srctinyid,r=t.srcopenid,i=this.connections_.get(n);i&&(void 0===r&&(r=i.getUserId()),this.log_.info("remote peer [".concat(r,"] is leaving")),this.emitter_.emit(df,{userId:r}),i.close(),this.connections_.delete(n),this.mutedStates_.delete(n),this.connectionsRefreshCount_.delete(n))}},{key:"onUpdateRemoteMuteStat",value:function(e){var t=this,n=e.content;n.userlist.forEach(function(e){var n=e.srctinyid,r=e.userid;if(0!==n&&n!==t.tinyId_)if(t.connections_.get(n)){var i=!!(1&e.flag),o=!!(8&e.flag),a=!!(64&e.flag),s=!!(16&e.flag),c=t.mutedStates_.get(n);if(void 0===c)return t.mutedStates_.set(n,{hasAudio:o,hasVideo:i,audioMuted:a,videoMuted:s}),i?s?t.emitter_.emit(pf,{userId:r}):t.emitter_.emit(hf,{userId:r}):t.emitter_.emit(pf,{userId:r}),void(o?a?t.emitter_.emit(lf,{userId:r}):t.emitter_.emit(ff,{userId:r}):t.emitter_.emit(lf,{userId:r}));var u=!a&&o;(!c.audioMuted&&c.hasAudio)!==u&&(u?t.emitter_.emit(ff,{userId:r}):t.emitter_.emit(lf,{userId:r}));var d=!s&&i;(!c.videoMuted&&c.hasVideo)!==d&&(d?t.emitter_.emit(hf,{userId:r}):t.emitter_.emit(pf,{userId:r})),t.mutedStates_.set(n,{hasAudio:o,hasVideo:i,audioMuted:a,videoMuted:s})}else t.mutedStates_.delete(n)})}},{key:"onClientBanned",value:function(e){this.emitter_.emit(mf,new dl({code:cl.CLIENT_BANNED,message:"client was banned because of "+e}))}},{key:"triggerReconnectForTest",value:function(e){var t={exitcode:e};this.log_.warn("triggerReconnectForTest exitcode: "+e),this.signalChannel_.send(al,t)}},{key:"setBandwidthForTest",value:function(e){this.uplinkConnection_&&this.uplinkConnection_.setBandwidth(e)}}]),e}(),ch=he.includes;Ae({target:"Array",proto:!0},{includes:function(e){return ch(this,e,arguments.length>1?arguments[1]:void 0)}}),ns("includes"),Ae({target:"String",proto:!0,forced:!xu("includes")},{includes:function(e){return!!~String(h(this)).indexOf(Eu(e),arguments.length>1?arguments[1]:void 0)}});var uh=function(){var e=ft(regeneratorRuntime.mark(function e(t){var n,r,i;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(navigator.mediaDevices){e.next=3;break}return sr.error("navigator.mediaDevices is undefined"),e.abrupt("return");case 3:return e.next=5,dh(t);case 5:if(n=e.sent,sr.info("getUserMedia with constraints: "+JSON.stringify(n)),!n.audio){e.next=14;break}return e.next=10,Th.getMicrophones();case 10:if(r=e.sent,sr.info("microphones: ".concat(JSON.stringify(r))),0!==r.length){e.next=14;break}throw new dl({code:cl.DEVICE_NOT_FOUND,message:"no microphone detected, but you are trying to get audio stream, please check your microphone and the configeration on TRTC.createStream."});case 14:if(!n.video){e.next=21;break}return e.next=17,Th.getCameras();case 17:if(i=e.sent,sr.info("cameras: ".concat(JSON.stringify(i))),0!==i.length){e.next=21;break}throw new dl({code:cl.DEVICE_NOT_FOUND,message:"no camera detected, but you are trying to get video stream, please check your camera and the configeration on TRTC.createStream."});case 21:return e.next=23,navigator.mediaDevices.getUserMedia(n);case 23:return e.abrupt("return",e.sent);case 24:case"end":return e.stop()}},e)}));return function(t){return e.apply(this,arguments)}}();function dh(e){return lh.apply(this,arguments)}function lh(){return(lh=ft(regeneratorRuntime.mark(function e(t){var n,r,i,o;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n={echoCancellation:!0,autoGainControl:!0,noiseSuppression:!0},t.audio){e.next=5;break}n=!1,e.next=15;break;case 5:if(Bd(t.microphoneId)){e.next=9;break}n=_t({deviceId:{exact:t.microphoneId},sampleRate:t.sampleRate,channelCount:t.channelCount},n),e.next=15;break;case 9:return n=_t({sampleRate:t.sampleRate,channelCount:t.channelCount},n),e.next=12,Th.getMicrophones();case 12:r=e.sent,(i=r.filter(function(e){var t=e.deviceId;return t.length>0&&"default"!==t})).length>0&&(n.deviceId={exact:i[0].deviceId});case 15:return o={},o=void 0!==t.facingMode&&t.video?{facingMode:t.facingMode,width:t.width,height:t.height,frameRate:t.frameRate}:!Bd(t.cameraId)&&t.video?{deviceId:{exact:t.cameraId},width:t.width,height:t.height,frameRate:t.frameRate}:!!t.video&&(void 0===t.width||{width:t.width,height:t.height,frameRate:t.frameRate}),e.abrupt("return",{audio:n,video:o});case 18:case"end":return e.stop()}},e)}))).apply(this,arguments)}var ph=function(){var e=ft(regeneratorRuntime.mark(function e(t){var n,r,i,o,a,s,c,u,d;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(navigator.mediaDevices){e.next=3;break}return sr.error("navigator.mediaDevices is undefined"),e.abrupt("return");case 3:if(n=null,!(Rd<74)){e.next=28;break}return r=hh(t),sr.info("getDisplayMedia with contraints: "+JSON.stringify(r)),e.next=9,navigator.mediaDevices.getDisplayMedia(r);case 9:if(i=e.sent,!t.screenAudio){e.next=15;break}return sr.warn("chrome version is too low to support capture system audio"),e.abrupt("return",i);case 15:if(!t.audio){e.next=25;break}return o=fh(t),sr.info("getUserMedia with constraints: "+JSON.stringify(o)),e.next=20,navigator.mediaDevices.getUserMedia(o);case 20:return n=e.sent,i.addTrack(n.getAudioTracks()[0]),e.abrupt("return",i);case 25:return e.abrupt("return",i);case 26:e.next=54;break;case 28:if(!t.screenAudio){e.next=38;break}return t.audioConstraints={echoCancellation:!0,noiseSuppression:!0,sampleRate:44100},a=hh(t),sr.info("getDisplayMedia with contraints: "+JSON.stringify(a)),e.next=34,navigator.mediaDevices.getDisplayMedia(a);case 34:return s=e.sent,e.abrupt("return",s);case 38:return c=hh(t),sr.info("getDisplayMedia with contraints: "+JSON.stringify(c)),e.next=42,navigator.mediaDevices.getDisplayMedia(c);case 42:if(u=e.sent,!t.audio){e.next=53;break}return d=fh(t),sr.info("getUserMedia with constraints: "+JSON.stringify(d)),e.next=48,navigator.mediaDevices.getUserMedia(d);case 48:return n=e.sent,u.addTrack(n.getAudioTracks()[0]),e.abrupt("return",u);case 53:return e.abrupt("return",u);case 54:case"end":return e.stop()}},e)}));return function(t){return e.apply(this,arguments)}}();function fh(e){return{audio:void 0!==e.microphoneId?{deviceId:{exact:e.microphoneId},sampleRate:e.sampleRate,channelCount:e.channelCount}:{sampleRate:e.sampleRate,channelCount:e.channelCount},video:!1}}function hh(e){var t={},n={width:e.width,height:e.height,frameRate:e.frameRate};return void 0!==e.screenSource&&(n.displaySurface=e.screenSource),t.video=n,void 0!==e.audioConstraints&&(t.audio=e.audioConstraints),t}var mh=new Map;mh.set("120p",{width:160,height:120,frameRate:15,bitrate:200}),mh.set("180p",{width:320,height:180,frameRate:15,bitrate:350}),mh.set("240p",{width:320,height:240,frameRate:15,bitrate:400}),mh.set("360p",{width:640,height:360,frameRate:15,bitrate:800}),mh.set("480p",{width:640,height:480,frameRate:15,bitrate:900}),mh.set("720p",{width:1280,height:720,frameRate:15,bitrate:1500}),mh.set("1080p",{width:1920,height:1080,frameRate:15,bitrate:2e3}),mh.set("1440p",{width:2560,height:1440,frameRate:30,bitrate:4860}),mh.set("4K",{width:3840,height:2160,frameRate:30,bitrate:9e3});var vh=new Map;vh.set("480p",{width:640,height:480,frameRate:5,bitrate:900}),vh.set("480p_2",{width:640,height:480,frameRate:30,bitrate:1e3}),vh.set("720p",{width:1280,height:720,frameRate:5,bitrate:1200}),vh.set("720p_2",{width:1280,height:720,frameRate:30,bitrate:3e3}),vh.set("1080p",{width:1920,height:1080,frameRate:5,bitrate:1600}),mh.set("1080p_2",{width:1920,height:1080,frameRate:30,bitrate:4e3});var gh=new Map;gh.set("standard",{sampleRate:48e3,channelCount:1,bitrate:40}),gh.set("high",{sampleRate:48e3,channelCount:1,bitrate:128});var _h=function(e){function t(e){var n;ht(this,t);var r=_t({},e,{isRemote:!1,type:"local"});return e.screen&&(r.mirror=!1),(n=Tt(this,St(t).call(this,r))).video_=e.video,n.audio_=e.audio,n.cameraId_=e.cameraId,n.cameraGroupId_="",n.facingMode_=e.facingMode,n.microphoneId_=e.microphoneId,n.microphoneGroupId_="",n.videoSource_=e.videoSource,n.audioSource_=e.audioSource,n.screen_=e.screen,n.screenSource_=e.screenSource,n.screenAudio_=e.screenAudio,n.audioProfile_={sampleRate:48e3,channelCount:1,bitrate:40},n.videoProfile_={width:640,height:480,frameRate:15,bitrate:900},n.screenProfile_={width:1920,height:1080,frameRate:5,bitrate:1600},n.videoBitrate_=n.screen_?1600:900,n}var n,r,i,o,a,s;return yt(t,Af),vt(t,[{key:"initialize",value:function(){var e=this;return new Promise(function(t,n){if(ah.report(Mp,{value:"pending"}),void 0===e.audio_){var r=new MediaStream;return void 0!==e.audioSource_&&(r.addTrack(e.audioSource_),e.setHasAudio(!0)),void 0!==e.videoSource_&&(r.addTrack(e.videoSource_),e.setHasVideo(!0)),e.setMediaStream(r),hl({eventType:"stream-initialize",kind:"custom",result:"success"}),ah.report(Mp,{value:"success"}),t()}e.screen_?(e.log_.info("initialize stream audio: "+e.audio_+" screenAudio: "+e.screenAudio_+" screen: "+e.screen_),ph({audio:e.audio_,screenAudio:e.screenAudio_,microphoneId:e.microphoneId_,screenSource:e.screenSource_,width:e.screenProfile_.width,height:e.screenProfile_.height,frameRate:e.screenProfile_.frameRate,sampleRate:e.audioProfile_.sampleRate,channelCount:e.audioProfile_.channelCount}).then(function(n){return e.setMediaStream(n),e.setHasAudio(e.audio_||e.screenAudio_),e.setHasVideo(!0),e.listenForScreenSharingStopped(e.getVideoTrack()),e.setVideoContentHint("detail"),e.updateDeviceIdInUse(),hl({eventType:"stream-initialize",kind:"getDisplayMedia",result:"success"}),ah.report(Mp,{value:"success"}),t()}).catch(function(t){hl({eventType:"stream-initialize",kind:"getDisplayMedia",result:"failed",code:t instanceof dl?t.getExtraCode()||t.getCode():cl.UNKNOWN}),e.log_.error("getDisplayMedia error observed "+t),n(t)})):(e.log_.info("initialize stream audio: "+e.audio_+" video: "+e.video_),uh({audio:e.audio_,video:e.video_,facingMode:e.facingMode_,cameraId:e.cameraId_,microphoneId:e.microphoneId_,width:e.videoProfile_.width,height:e.videoProfile_.height,frameRate:e.videoProfile_.frameRate,sampleRate:e.audioProfile_.sampleRate,channelCount:e.audioProfile_.channelCount}).then(function(n){return e.setMediaStream(n),e.setHasAudio(e.audio_),e.setHasVideo(e.video_),e.updateDeviceIdInUse(),e.log_.debug("gotStream hasAudio: "+e.hasAudio_+" hasVideo: "+e.hasVideo_),hl({eventType:"stream-initialize",kind:"getUserMedia",result:"success"}),ah.report(Mp,{value:"success"}),t()}).catch(function(t){hl({eventType:"stream-initialize",kind:"getUserMedia",result:"failed",code:t instanceof dl?t.getExtraCode()||t.getCode():cl.UNKNOWN}),e.log_.error("getUserMedia error observed "+t+" https://trtc-1252463788.file.myqcloud.com/web/docs/tutorial-10-error-code-tips.html"),n(t)}))})}},{key:"listenForScreenSharingStopped",value:function(e){var t=this;e.addEventListener("ended",function(e){t.log_.info("screensharing was stopped because the video track is ended"),t.emitter_.emit(yf)})}},{key:"muteAudio",value:function(){var e=Rt(St(t.prototype),"muteAudio",this).call(this);if(e){this.log_.info("localStream mute audio");var n=this.getConnection();if(n){n.setMutedState("audio",!0);var r=n.getUserId(),i=n.getTinyId();Wl(r,{eventId:yl,eventDesc:"mute local audio track",timestamp:Ro(),userId:r,tinyId:i})}}return e}},{key:"muteVideo",value:function(){this.log_.info("localStream mute video");var e=Rt(St(t.prototype),"muteVideo",this).call(this);if(e){var n=this.getConnection();if(n){n.setMutedState("video",!0);var r=n.getUserId(),i=n.getTinyId();Wl(r,{eventId:Sl,eventDesc:"mute local video track",timestamp:Ro(),userId:r,tinyId:i})}}return e}},{key:"unmuteAudio",value:function(){var e=Rt(St(t.prototype),"unmuteAudio",this).call(this);if(e){this.log_.info("localStream unmute audio");var n=this.getConnection();if(n){n.setMutedState("audio",!1);var r=n.getUserId(),i=n.getTinyId();Wl(r,{eventId:bl,eventDesc:"unmute local audio track",timestamp:Ro(),userId:r,tinyId:i})}}return e}},{key:"unmuteVideo",value:function(){this.log_.info("localStream unmute video");var e=Rt(St(t.prototype),"unmuteVideo",this).call(this);if(e){var n=this.getConnection();if(n){n.setMutedState("video",!1);var r=n.getUserId(),i=n.getTinyId();Wl(r,{eventId:kl,eventDesc:"unmute local video track",timestamp:Ro(),userId:r,tinyId:i})}}return e}},{key:"setAudioProfile",value:function(e){var t;"object"===lt(e)?t=e:void 0===(t=gh.get(e))&&(t=gh.get("standard")),this.log_.info("setAudioProfile: "+JSON.stringify(t)),this.audioProfile_=t}},{key:"setVideoProfile",value:function(e){var t;"object"===lt(e)?t=e:void 0===(t=mh.get(e))&&(t=mh.get("480p")),this.log_.info("setVideoProfile "+JSON.stringify(t)),this.videoProfile_=t,this.videoBitrate_=t.bitrate}},{key:"getVideoBitrate",value:function(){return this.videoBitrate_}},{key:"getAudioBitrate",value:function(){return this.audioProfile_.bitrate}},{key:"setScreenProfile",value:function(e){var t=e;"object"!==lt(e)&&void 0===(t=vh.get(e))&&(t=vh.get("1080p")),this.log_.info("setScreenProfile "+JSON.stringify(e)),this.screenProfile_=t,this.videoBitrate_=t.bitrate}},{key:"getVideoProfile",value:function(){return this.screen_?this.screenProfile_:this.videoProfile_}},{key:"getAudioProfile",value:function(){return this.audioProfile_}},{key:"setVideoContentHint",value:function(e){var t=this.getVideoTrack();t&&"contentHint"in t&&(this.log_.info("set video track contentHint to: "+e),t.contentHint=e,t.contentHint!==e&&this.log_.warn("Invalid video track contentHint: "+e))}},{key:"switchDevice",value:(s=ft(regeneratorRuntime.mark(function e(n,r){var i,o,a,s,c,u,d;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.screen_){e.next=2;break}throw new dl({code:cl.INVALID_OPERATION,message:"switch device is not supported in screen sharing"});case 2:if(!("audio"===n&&this.microphoneId_===r||"video"===n&&this.cameraId_===r||this.audioSource_||this.videoSource_)){e.next=4;break}return e.abrupt("return");case 4:if("audio"===n&&(this.microphoneId_=r,this.audio_||(this.audio_=!0)),"video"===n&&("user"===r||"environment"===r?this.facingMode_=r:this.cameraId_=r,this.video_||(this.video_=!0)),this.getMediaStream()){e.next=8;break}return e.abrupt("return");case 8:return this.log_.info("switchDevice "+n+" to: "+r),"video"===n&&(Sd||yd)&&(i=this.getVideoTrack())&&i.stop(),e.next=12,uh({audio:this.audio_,video:this.video_,facingMode:this.facingMode_,cameraId:this.cameraId_,microphoneId:this.microphoneId_,width:this.videoProfile_.width,height:this.videoProfile_.height,frameRate:this.videoProfile_.frameRate,sampleRate:this.audioProfile_.sampleRate,channelCount:this.audioProfile_.channelCount});case 12:if(o=e.sent,this.setMediaStream(o),this.updateDeviceIdInUse(),!(a=this.getConnection())){e.next=25;break}return e.next=19,a.replaceStream(o);case 19:s=a.getUserId(),c=a.getTinyId(),u=El,d="switch camera","audio"===n&&(u=Il,d="switch microphone"),Wl(s,{eventId:u,eventDesc:d,timestamp:Ro(),userId:s,tinyId:c});case 25:return this.log_.info("switchDevice - restart audio/video player"),Rt(St(t.prototype),"restartAudio",this).call(this),Rt(St(t.prototype),"restartVideo",this).call(this),e.abrupt("return");case 29:case"end":return e.stop()}},e,this)})),function(e,t){return s.apply(this,arguments)})},{key:"addTrack",value:(a=ft(regeneratorRuntime.mark(function e(t){var n,r,i,o,a,s,c;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=this.getMediaStream()){e.next=3;break}throw new dl({code:cl.INVALID_OPERATION,message:"the local stream is not initialized yet"});case 3:if(!("audio"===t.kind&&n.getAudioTracks().length>0||"video"===t.kind&&n.getVideoTracks().length>0)){e.next=5;break}throw new dl({code:cl.INVALID_OPERATION,message:"A Stream has at most one audio track and one video track"});case 5:if("video"!==t.kind){e.next=10;break}if(!("getSettings"in MediaStreamTrack.prototype)){e.next=10;break}if((r=t.getSettings()).width===this.videoProfile_.width&&r.height===this.videoProfile_.height){e.next=10;break}throw new dl({code:cl.INVALID_PARAMETER,message:"video resolution of the track (".concat(r.width," x ").concat(r.height,") shall be kept the same as the one defined via setVideoProfile(): ").concat(this.videoProfile_.width," x ").concat(this.videoProfile_.height)});case 10:if(n.addTrack(t),"audio"===t.kind?(this.audio_=!0,this.setHasAudio(!0)):(this.video_=!0,this.setHasVideo(!0)),!(i=this.getConnection())){e.next=22;break}return e.next=16,i.addTrack(t);case 16:o=i.getUserId(),a=i.getTinyId(),s=ml,c="add video track to current published stream","audio"===t.kind&&(s=vl,c="add audio track to current published stream"),Wl(o,{eventId:s,eventDesc:c,timestamp:Ro(),userId:o,tinyId:a});case 22:case"end":return e.stop()}},e,this)})),function(e){return a.apply(this,arguments)})},{key:"removeTrack",value:(o=ft(regeneratorRuntime.mark(function e(t){var n,r,i,o,a,s;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if("audio"!==t.kind){e.next=2;break}throw new dl({code:cl.INVALID_PARAMETER,message:"remove audio track is not supported"});case 2:if(n=this.getMediaStream()){e.next=5;break}throw new dl({code:cl.INVALID_OPERATION,message:"the local stream is not initialized yet"});case 5:if(-1!==n.getTracks().indexOf(t)){e.next=7;break}throw new dl({code:cl.INVALID_PARAMETER,message:"the track to be removed is not being publishing"});case 7:if(n.removeTrack(t),"audio"===t.kind?(this.audio_=!1,this.setHasAudio(!1)):(this.video_=!1,this.setHasVideo(!1)),!(r=this.getConnection())){e.next=19;break}return e.next=13,r.removeTrack(t);case 13:i=r.getUserId(),o=r.getTinyId(),a=gl,s="remove video track from current published stream","audio"===t.kind&&(a=_l,s="remove audio track from current published stream"),Wl(i,{eventId:a,eventDesc:s,timestamp:Ro(),userId:i,tinyId:o});case 19:case"end":return e.stop()}},e,this)})),function(e){return o.apply(this,arguments)})},{key:"replaceTrack",value:(i=ft(regeneratorRuntime.mark(function e(n){var r,i,o,a,s,c,u;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=this.getMediaStream()){e.next=3;break}throw new dl({code:cl.INVALID_OPERATION,message:"the local stream is not initialized yet"});case 3:if(!("audio"===n.kind&&r.getAudioTracks().length<=0||"video"===n.kind&&r.getVideoTracks().length<=0)){e.next=5;break}throw new dl({code:cl.INVALID_PARAMETER,message:"try to replace ".concat(n.kind," track but there's no previous ").concat(n.kind," being published")});case 5:if("video"!==n.kind){e.next=10;break}if(!("getSettings"in MediaStreamTrack.prototype)){e.next=10;break}if((i=n.getSettings()).width===this.videoProfile_.width&&i.height===this.videoProfile_.height){e.next=10;break}throw new dl({code:cl.INVALID_PARAMETER,message:"video resolution of the track (".concat(i.width," x ").concat(i.height,") shall be kept the same as the one defined via setVideoProfile(): ").concat(this.videoProfile_.width," x ").concat(this.videoProfile_.height)});case 10:if("audio"===n.kind?(r.removeTrack(r.getAudioTracks()[0]),r.addTrack(n),Rt(St(t.prototype),"restartAudio",this).call(this)):(r.removeTrack(r.getVideoTracks()[0]),r.addTrack(n),Rt(St(t.prototype),"restartVideo",this).call(this)),!(o=this.getConnection())){e.next=21;break}return e.next=15,o.replaceTrack(n);case 15:a=o.getUserId(),s=o.getTinyId(),c=xl,u="replace video track from current published stream","audio"===n.kind&&(c=Pl,u="replace audio track from current published stream"),Wl(a,{eventId:c,eventDesc:u,timestamp:Ro(),userId:a,tinyId:s});case 21:case"end":return e.stop()}},e,this)})),function(e){return i.apply(this,arguments)})},{key:"updateStream",value:(r=ft(regeneratorRuntime.mark(function e(t){var n,r,i,o,a,s,c,u,d,l,p;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.mediaStream_){e.next=2;break}return e.abrupt("return");case 2:return this.log_.info("updateStream() try to recover local stream"),e.prev=3,e.next=6,Th.getCameras();case 6:return n=e.sent,e.next=9,Th.getMicrophones();case 9:if(r=e.sent,i=this.audio_,(o=this.video_)&&0===n.length&&(o=!1,this.log_.info("updateStream() video flag is true, but no camera detected, set video to false")),i&&0===r.length&&(i=!1,this.log_.info("updateStream() audio flag is true, but no microphone detected, set audio to false")),!1!==i||!1!==o){e.next=17;break}return this.log_.info("updateStream() both audio and video are false, recover stream aborted"),e.abrupt("return");case 17:return ah.report(zp,{value:"pending"}),a=this.mediaStream_.getAudioTracks().map(function(e){return e.enabled}).includes(!1),s=this.mediaStream_.getVideoTracks().map(function(e){return e.enabled}).includes(!1),c=t&&n.findIndex(function(e){return e.deviceId===t.cameraId})>=0,u=t&&r.findIndex(function(e){return e.deviceId===t.microphoneId})>=0,e.next=24,uh({audio:i,video:o,cameraId:c?t.cameraId:void 0,microphoneId:u?t.microphoneId:void 0,facingMode:this.facingMode_,width:this.videoProfile_.width,height:this.videoProfile_.height,frameRate:this.videoProfile_.frameRate,sampleRate:this.audioProfile_.sampleRate,channelCount:this.audioProfile_.channelCount});case 24:d=e.sent,l=d.getTracks(),p=0;case 27:if(!(p<l.length)){e.next=33;break}return e.next=30,this.replaceTrack_(l[p]);case 30:p++,e.next=27;break;case 33:a&&(this.log_.info("updateStream() prev audio tracks is muted, keep current audio tracks muted"),this.doEnableTrack("audio",!1)),s&&(this.log_.info("updateStream() prev video tracks is muted, keep current video tracks muted"),this.doEnableTrack("video",!1)),this.updateDeviceIdInUse(),ah.report(zp,{value:"success"}),this.log_.info("updateStream() recover local stream successfully"),e.next=43;break;case 40:e.prev=40,e.t0=e.catch(3),this.log_.error("updateStream() failed to recover local stream, "+e.t0);case 43:case"end":return e.stop()}},e,this,[[3,40]])})),function(e){return r.apply(this,arguments)})},{key:"replaceTrack_",value:(n=ft(regeneratorRuntime.mark(function e(n){var r,i,o,a;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=this.mediaStream_.getAudioTracks(),i=this.mediaStream_.getVideoTracks(),!("audio"===n.kind&&r.length<=0||"video"===n.kind&&i.length<=0)){e.next=5;break}return this.log_.info("there is no previous ".concat(n.kind," track, replacement ignored")),e.abrupt("return");case 5:if("audio"===n.kind?(this.mediaStream_.removeTrack(r[0]),this.mediaStream_.addTrack(n),Rt(St(t.prototype),"restartAudio",this).call(this)):("getSettings"in MediaStreamTrack.prototype&&((o=n.getSettings()).width===this.videoProfile_.width&&o.height===this.videoProfile_.height||this.log_.warn("the resolution of video track to be replaced (".concat(o.width," x ").concat(o.height,") is different from the videoProfile (").concat(this.videoProfile_.width," x ").concat(this.videoProfile_.height,"). It may cause a cloud recording exception"))),this.mediaStream_.removeTrack(i[0]),this.mediaStream_.addTrack(n),Rt(St(t.prototype),"restartVideo",this).call(this)),!(a=this.getConnection())){e.next=10;break}return e.next=10,a.replaceTrack(n);case 10:case"end":return e.stop()}},e,this)})),function(e){return n.apply(this,arguments)})},{key:"updateDeviceIdInUse",value:function(){var e=this;if(!this.mediaStream_)return this.cameraId_="",this.cameraGroupId_="",this.microphoneId_="",void(this.microphoneGroupId_="");"getSettings"in MediaStreamTrack.prototype&&this.mediaStream_.getTracks().forEach(function(t){var n=t.getSettings(),r=n.deviceId,i=n.groupId;r&&("audio"===t.kind?(e.microphoneId_=r,e.microphoneGroupId_=i):"video"!==t.kind||e.screen_||(e.cameraId_=r,e.cameraGroupId_=i))});var t=this.mediaStream_.getAudioTracks(),n=this.mediaStream_.getVideoTracks();t&&0===t.length&&(this.microphoneId_="",this.microphoneGroupId_=""),n&&0===n.length&&(this.cameraId_="",this.cameraGroupId_="")}},{key:"getScreen",value:function(){return this.screen_}},{key:"getVideo",value:function(){return this.video_}},{key:"getAudio",value:function(){return this.audio_}},{key:"getCameraId",value:function(){return this.cameraId_}},{key:"getMicrophoneId",value:function(){return this.microphoneId_}},{key:"getMicrophoneGroupId",value:function(){return this.microphoneGroupId_}},{key:"setAudioVolume",value:function(e){Rt(St(t.prototype),"setAudioVolume",this).call(this,e)}}]),t}();console.log("WebRTC-adapter ".concat(Nn.browserDetails.browser,"/").concat(Nn.browserDetails.version));var yh,Sh,bh,kh,wh,Th={VERSION:"4.6.4",Logger:{LogLevel:{TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,NONE:5},setLogLevel:function(e){sr.setLogLevel(e)},enableUploadLog:function(){sr.enableUploadLog()},disableUploadLog:function(){sr.disableUploadLog()}},checkSystemRequirements:(wh=ft(regeneratorRuntime.mark(function e(){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Zl();case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)})),function(){return wh.apply(this,arguments)}),isScreenShareSupported:function(){return $l()},getDevices:(kh=ft(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(navigator.mediaDevices){e.next=3;break}return sr.error("navigator.mediaDevices is undefined"),e.abrupt("return",[]);case 3:return e.next=5,navigator.mediaDevices.enumerateDevices();case 5:return t=e.sent,e.abrupt("return",t.map(function(e,t){var n=e.label;e.label||(n=e.kind+"_"+t);var r={label:n,deviceId:e.deviceId,kind:e.kind};return e.groupId&&(r.groupId=e.groupId),r}));case 7:case"end":return e.stop()}},e)})),function(){return kh.apply(this,arguments)}),getCameras:(bh=ft(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(navigator.mediaDevices){e.next=3;break}return sr.error("navigator.mediaDevices is undefined"),e.abrupt("return",[]);case 3:return e.next=5,navigator.mediaDevices.enumerateDevices();case 5:return t=e.sent,e.abrupt("return",t.filter(function(e){return"videoinput"===e.kind}).map(function(e,t){var n=e.label;e.label||(n="camera_"+t);var r={label:n,deviceId:e.deviceId,kind:e.kind};return e.groupId&&(r.groupId=e.groupId),r}));case 7:case"end":return e.stop()}},e)})),function(){return bh.apply(this,arguments)}),getMicrophones:(Sh=ft(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(navigator.mediaDevices){e.next=3;break}return sr.error("navigator.mediaDevices is undefined"),e.abrupt("return",[]);case 3:return e.next=5,navigator.mediaDevices.enumerateDevices();case 5:return t=e.sent,e.abrupt("return",t.filter(function(e){return"audioinput"===e.kind}).map(function(e,t){var n=e.label;e.label||(n="microphone_"+t);var r={label:n,deviceId:e.deviceId,kind:e.kind};return e.groupId&&(r.groupId=e.groupId),r}));case 7:case"end":return e.stop()}},e)})),function(){return Sh.apply(this,arguments)}),getSpeakers:(yh=ft(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,navigator.mediaDevices.enumerateDevices();case 2:return t=e.sent,e.abrupt("return",t.filter(function(e){return"audiooutput"===e.kind}).map(function(e,t){var n=e.label;e.label||(n="speaker_"+t);var r={label:n,deviceId:e.deviceId,kind:e.kind};return e.groupId&&(r.groupId=e.groupId),r}));case 4:case"end":return e.stop()}},e)})),function(){return yh.apply(this,arguments)}),createClient:function(e){ah.report("sdkAppID",{value:e.sdkAppId}),ah.report("version",{value:this.VERSION});var t={version:this.VERSION};return new sh(_t({},t,e))},createStream:function(e){if(void 0!==e.screen&&e.screen&&void 0===e.audio&&(e.audio=!1),!(void 0===e.audio&&void 0===e.video||void 0===e.audioSource&&void 0===e.videoSource))throw new dl({code:cl.INVALID_PARAMETER,message:"LocalStream must be created by createStream() with either audio/video or audioSource/videoSourcebut can not be mixed with audio/video and audioSource/videoSource"});if(void 0!==e.screen&&!0===e.screen&&!0===e.video)throw new dl({code:cl.INVALID_PARAMETER,message:"screen/video options are mutually exclusive, they can not be both true"});if(e.audio&&e.screenAudio)throw new dl({code:cl.INVALID_PARAMETER,message:"audio/screenAudio options are mutually exclusive, they can not be both true"});if(!0!==e.screen&&!0===e.screenAudio)throw new dl({code:cl.INVALID_PARAMETER,message:"screenAudio options are configurable while screen options is true"});if(void 0!==e.screen&&!0===e.screen&&!this.isScreenShareSupported())throw new dl({code:cl.INVALID_OPERATION,message:"screen capture is not supported, please use the latest chrome browser"});return new _h(e)}};return console.info("******************************************************************************"),console.info("*   欢迎使用 TRTC Web SDK - 腾讯云实时音视频通信"),console.info("*   API 文档：https://trtc-1252463788.file.myqcloud.com/web/docs/index.html"),console.info("*   版本更新日志：https://cloud.tencent.com/document/product/647/38958"),console.info("*   反馈问题：https://github.com/tencentyun/TRTCSDK/issues"),console.info("******************************************************************************"),sr.info("TRTC Web SDK Version: "+Th.VERSION),sr.info("UserAgent: "+navigator.userAgent),sr.info("URL of current page: "+location.href),Th});
