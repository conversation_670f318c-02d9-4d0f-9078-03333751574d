// uno.config.ts
import {
  defineConfig,
  presetUno,
  presetAttributify,
  presetIcons,
  transformerDirectives,
  transformerVariantGroup,
} from 'unocss'
import fs from 'node:fs/promises'
import ant from '@iconify-json/ant-design/icons.json'
import { FileSystemIconLoader } from '@iconify/utils/lib/loader/node-loaders'

export default defineConfig({
  presets: [
    presetAttributify({}),
    presetUno(),
    presetIcons({
      collections: {
        mid: () => import('@iconify-json/mdi/icons.json').then((i) => i.default),
        // hancode: {
        //   logo: () => fs.readFile('./public/logo.svg', 'utf8')
        // }
        a: () => import('@iconify-json/ant-design/icons.json').then((i) => i.default),
        custom: FileSystemIconLoader('./assets/icons', (svg) => svg.replace(/^<svg /, '<svg fill="currentColor" ')),
      },
      extraProperties: {
        display: 'inline-block',
        width: '1em',
        height: '1em',
      },
    }),
  ],
  shortcuts: [
    [
      'btn-primary',
      [
        'bg-primary',
        'color-white',
        'font-size-14',
        'text-center',
        'transition-all',
        'hover:bg-#e6f4ff',
        'hover:color-primary',
        'cursor-pointer',
      ],
    ],

    ['filter', ['flex']],
    ['filter__title', ['title-text', 'w-90', 'flex', 'items-center', 'justify-center', 'bg-secondary']],
    ['filter__content', ['relative', 'p-y-6', 'p-x-8', 'flex-1', 'flex', 'gap-5', 'flex-wrap']],
    ['filter__option--disabled', ['color-#00000040', 'cursor-not-allowed', 'hover:b-transparent']],
    [
      'filter__image-option',
      [
        'relative',
        'inline-block',
        'p-y-2',
        'p-x-12',
        'b-2',
        'b-solid',
        'b-transparent',
        'text-center',
        'cursor-pointer',
        'hover:b-primary',
        'transition-all',
      ],
    ],
    ['filter__image-option--selected', ['b-primary']],
    ['filter__image-option--disabled', ['cursor-not-allowed', 'hover:b-#fff']],
    ['filter__sub-title', ['title-text', 'w-90', 'h-full', 'inline-block', 'font-size-12']],

    ['card', ['relative', 'bg-white', 'rounded-2', 'card-shadow']],
    ['card--reactive', ['card', 'cursor-pointer', 'transition-transform', 'hover:card-shadow-hover']],
    ['text', ['font-size-14', 'color-#333']],
    ['sub-text', ['font-size-12', 'color-#999']],
    ['title-text', ['font-size-14', 'color-#000', 'font-bold']],
    ['flex-center', ['flex', 'justify-center', 'items-center']],
    [
      'card-list',
      ['grid', 'gap-20', 'grid-cols-1', 'sm-grid-cols-2', 'md-grid-cols-3', 'lg-grid-cols-4', 'xl-grid-cols-5'],
    ],
    ['btn-disabled', ['bg-[rgba(0, 0, 0, 0.25)]', 'cursor-not-allowed', 'text-14', 'text-white', 'text-center']],
    ['link', ['text-primary', 'cursor-pointer']],
    ['content-width', 'max-w-1440 mx-auto w-full'],
    ['hover-link', 'hover:text-primary cursor-pointer active:text-secondary'],
    ['button-link', 'color-primary hover:text-primaryText transition-colors cursor-pointer'],
  ],
  rules: [
    ['card-shadow', { 'box-shadow': '0 3px 25px #1c344f1a' }],
    ['card-shadow-hover', { 'box-shadow': '0 0 8px 0 rgba(0, 0, 0, 0.3)' }],
    ['image-disabled', { filter: 'grayscale(100%) blur(2px)' }],
  ],
  theme: {
    colors: {
      primary: 'var(--primary-color)',
      secondary: 'var(--secondary-color)',
      midnight: 'var(--midnight-blue)',
      powder: 'var(--powder-blue)',
      primaryBg: '#2A2A35',
    },
  },
  variants: [
    (matcher) => {
      const regex = /^(nth-child\(\w+\)):(.*)/
      const match = matcher.match(regex)
      if (match) {
        return {
          matcher: match[2],
          selector: (s) => `${s}:${match[1]}`,
        }
      } else {
        return matcher
      }
    },
  ],
  transformers: [transformerDirectives(), transformerVariantGroup()],
  safelist: Object.keys(ant.icons).map((key) => `i-a-${key}`),
})
