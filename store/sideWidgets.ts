import { defineStore } from 'pinia'

export const sideWidgetsStore = defineStore('sideWidgetsStore', () => {
  // const ansPriceCount = ref<Number>(1)
  const feedbackPopVisible = ref<Boolean>(false)

  // function setAnsPriceCount(count:Number){
  //   ansPriceCount.value = count;
  // }

  function setFeedbackPopVisible(visible: boolean) {
    feedbackPopVisible.value = visible
  }

  let loadAns = false
  const _ansPriceCount = ref(0)
  const ansPriceCount = computed(() => {
    if (!loadAns) {
      loadAns = true
      updateAnsCount()
    }
    return _ansPriceCount.value
  })

  async function updateAnsCount() {
    const res = await http<number>('/mall/p/personal-inquiry/count')
    useMall(res, () => {
      _ansPriceCount.value = res.data
    })
  }

  return { setFeedbackPopVisible, feedbackPopVisible, ansPriceCount, updateAnsCount }
})
