import st from 'store2'

export const COMPAREKEY = 'comparekey'
export type SpecItem = {
  specCode: string
  specName: string
  specValueCode: string
  specValue: string
}

export type Compare = {
  partModel: string
  partId: string
  specs: SpecItem[]
}

export const useCompareStore = defineStore('compare', () => {
  const compareList = ref<Compare[]>([])

  const compareKeys = computed(() => compareList.value.reduce((set, item) => {
    set.set(item.partModel, item)
    return set
  }, new Map() as Map<string, Compare>))

  const add = (compare: Compare) => {
    if (compareKeys.value.has(compare.partModel)) return
    compareList.value.push(compare)
    st.set(COMPAREKEY, compareList.value)
  }

  const remove = (model: string) => {
    const idx = compareList.value.findIndex(item => item.partModel == model)
    if (idx > -1) {
      compareList.value.splice(idx, 1)
      st.set(COMPAREKEY, compareList.value)
    }
  }
  return {
    compareList,
    compareKeys,
    add,
    remove
  }
})