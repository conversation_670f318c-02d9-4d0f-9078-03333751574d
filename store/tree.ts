import { define<PERSON>tore } from 'pinia';
import { Category } from '~/types/category';

export const treeStore = defineStore('treeStore', () => {
  const tree: {
    categoryCode: string;
    breadcrumb: string[];
    data: any[];
  } = reactive({
    categoryCode: '',
    breadcrumb: [],
    data: [],
  });

  const active = ref('')

  function setTree(_tree: any[]) {
    tree.data = _tree;

    // if (tree.categoryCode) {
    //   setCategoryCode(tree.categoryCode);
    // }
  }

  const treeMap = computed(() => {
    const map: Obj = {}
    const dfs = (list) => {
      list.forEach(item => {
        map[item.id] = item
        if (item.children?.length) {
          dfs(item.children)
        }
      })
    }
    dfs(tree.data)
    return map
  })

  const navList = computed(() => {
    const list: Obj[] = []
    if (active.value) {
      let node = treeMap.value[active.value]
      if (node) {
        list.push(node)
      }
      while (node && node.parentId) {
        const parent = treeMap.value[node.parentId]
        if (parent && parent.parentId) {
          list.unshift(parent)
        }
        node = parent
      }
    }
    return list as Category[]
  })

  function setCategoryCode(_categoryCode: string) {
    tree.categoryCode = _categoryCode;
    calBreadcrumb();
  }

  function calBreadcrumb() {
    function dfs(node: any) {
      if (node.categoryCode === tree.categoryCode) {
        setBreadcrumb(node.categoryPath, node.categoryName)
        return true;
      }
      if (node.children) {
        for (let i = 0; i < node.children.length; i++) {
          dfs(node.children[i]);
        }
      }
    }
  
    if (tree.categoryCode && tree.data.length) {
      const list = tree.data.reduce((res, item) => {
        if (item.children?.length) {
          res.concat(item.children)
        }
        return res
      }, [])
      for (let i = 0; i < list; i++) {
        if (dfs(list[i])) {
          break;
        }
      }
    }
  }

  function clearTree() {
    tree.data = [];
  }

  function setBreadcrumb(categoryPath: string, categoryName: string) {
    tree.breadcrumb = categoryPath ? [
      ...(categoryPath.split('>') || []),
      categoryName,
    ] : [categoryName];
  }

  const setActive = (id) => {
    active.value = id
  }

  return { setTree, setCategoryCode, setBreadcrumb, clearTree, tree, treeMap, navList, setActive };
});
