import { defineStore } from 'pinia'
import { getOneByCond } from '~/api/mall-platform'
import { userStore } from './user'

export type Company = {
  shopCompanyId: string | number
  type: number
} & obj

export const companyStore = defineStore('companyStore', () => {
  const company = ref({} as Company)

  const _company_id = useCookie('_company', {
    domain: useRuntimeConfig().public.baseUrl.VITE_DOMAIN,
    default: () => '',
  })

  function setCompany(obj: any = {}) {
    if (!obj.shopCompanyId) {
      obj = {
        shopCompanyId: 0,
      }
    }
    company.value = obj
    _company_id.value = obj.shopCompanyId
  }

  function clear() {
    adminMap.value = {}
    company.value = {} as Company
    _company_id.value = ''
  }

  const adminMap = ref({})

  const isAdmin = computed(() => {
    const companyId = company.value.shopCompanyId
    return !!adminMap.value[companyId]
  })

  async function setAdminInfo() {
    const companyId = company.value.shopCompanyId
    if (companyId in adminMap.value) return
    const [err, res] = await _try(() =>
      getOneByCond({
        merchantId: companyId,
        userMobile: userStore().user.userMobile,
      }),
    )
    if (!err) {
      const ret = parseJson<{ isAdmin?: number }>(res.data, {})
      adminMap.value[companyId] = ret.isAdmin == 1
    }
  }

  const isPersonal = computed(() => company.value.shopCompanyId == 0)

  return { setCompany, company, clear, isAdmin, setAdminInfo, isPersonal }
})
