export const useSearch = defineStore('search', () => {
  const types = [
    { type: 'skuCode', name: '找型号' },
    { type: 'keyword', name: '找名称' },
  ] as const
  const current = useCookie<number>('_search_type', {
    default: () => 0,
  })
  const changeType = () => {
    if (current.value + 1 < types.length) {
      current.value++
    } else {
      current.value = 0
    }
  }

  const currentType = computed(() => types[current.value])
  return {
    currentType,
    changeType,
  }
})
