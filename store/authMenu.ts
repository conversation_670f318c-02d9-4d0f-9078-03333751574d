import { omit } from 'lodash-es'
import { defineStore } from 'pinia'
import { companyStore } from './company'

// 兼容旧详情菜单
const extra = {
  // 'procure-order': ['订单详情'],
  // return: ['退款详情'],
  // po: ['po-detail'],
  // 'return-manage': ['return-detail'],
  // inv: ['inv-detail'],
  // pay: ['pay-detail'],
  // 'ans-price': ['rfq-detail'],
}

const whiteList = ['notify-center', 'customer']

export const authMenuStore = defineStore('authMenuStore', () => {
  const authMenu = ref<Obj>({})

  const getMenuKey = (key: string) => `workSpace-company-space-${key}`

  const commonMenuList = ref<obj[]>([])
  const settingMenuList = ref<obj[]>([])
  const menuSet = ref(new Map())
  const company = companyStore()

  function setAuthMenu(obj: any) {
    console.log('%c Line:28 🥛 obj', 'color:#1D88CB', obj)
    authMenu.value = obj
    setMenuMap(obj.menuList || [])
  }

  function setMenuMap(list: obj[]) {
    // 企业空间内的菜单Key
    const map = new Map()
    const _menu: obj[] = []
    const _smenu: obj[] = []
    const _set = new Map()
    const dfs = (list, type = 0) => {
      list.forEach((item) => {
        if (item.type == 0) {
          // if (company.isPersonal && !item.personalSpaceDisplay) return
          let menuItem = omit(item, 'list')
          menuItem.key = item.url
          map.set(item.menuId, menuItem)
        }
        if (item.type == 1) {
          const menuItem = map.get(item.parentId)
          if (!menuItem.children) {
            menuItem.children = []
          }

          if (item.hidden != 1) {
            item.key = item.url
            menuItem.children.push(item)
          }

          if (company.isPersonal) {
            if (item.personalSpaceDisplay) {
              _set.set(getMenuKey(item.url), type)
            }
          } else {
            _set.set(getMenuKey(item.url), type)
          }
          if (extra[item.url]) {
            extra[item.url].forEach((i) => {
              _set.set(getMenuKey(i), type)
            })
          }
        }
        if (item.list?.length) {
          dfs(item.list, item.menuGroup)
        }
        if (item.type == 0) {
          const menuItem = map.get(item.menuId)
          if (menuItem.children?.length) {
            if (item.menuGroup == 0) {
              _menu.push(menuItem)
            } else if (item.menuGroup == 1) {
              _smenu.push(menuItem)
            }
          }
        }
      })
    }
    dfs(list)
    if (_menu.length) {
      whiteList.forEach((item) => {
        _set.set(getMenuKey(item), 0)
      })
    }
    commonMenuList.value = _menu
    settingMenuList.value = _smenu
    menuSet.value = _set
  }

  function findFirst(type) {
    let list: obj[] = []
    if (type == 0) {
      list = commonMenuList.value
    }
    if (type == 1) {
      list = settingMenuList.value
    }
    for (let i = 0; i < list.length; i++) {
      const item = list[i]
      if (!item.children) {
        continue
      }
      for (let j = 0; j < item.children.length; j++) {
        const find = item.children[j]
        if (find) {
          if (company.isPersonal) {
            if (find.personalSpaceDisplay) {
              return getMenuKey(find.url)
            }
          } else {
            return getMenuKey(find.url)
          }
        }
      }
    }
  }

  const permissions = computed(() => new Set(authMenu.value.authorities || []))

  return { setAuthMenu, authMenu, menuSet, commonMenuList, settingMenuList, findFirst, getMenuKey, permissions }
})
