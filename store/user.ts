import { defineStore } from 'pinia'
import st from 'store2'
import { authMenuStore } from './authMenu'
import { companyStore } from './company'

export enum ApplyStatus {
  NONE,
  PROCESSING,
  FINISH,
}

export enum MemberLevel {
  /**
   * 普通会员
   */
  NORMAL,
  /**
   *认证会员
   */
  AUTHENTICATION,
  /**
   * 推广会员
   */
  GENERALIZATION,
  /**
   * 专业会员
   */
  PROFESSIONAL,
}

export type User = {
  userId: string
  balance: number
  growth: number
  levelType: number
  mobile: string
  nickName: string
  pic: string
  userMemo: number
  score: number
  sex: string
  status: number
  unionId: string
  userMobile: string
  username: string
  userMail: string
  birthDate: string
  position: string
  personalProfile: string
  isSuperAdmin: number
  wxUnionId: string
  tuName: string
  realName: string
  identity: string
  company: string
  uniqueKey: string
  workPermit: string
  level: MemberLevel
  upgradeStatus: ApplyStatus
}
export const userStore = defineStore('userStore', () => {
  const user = ref({} as User)

  function setUser(obj: any) {
    user.value = obj
  }

  function clearUser() {
    const company = companyStore()
    company.clear()
    user.value = {} as User
    authMenuStore().setAuthMenu({})

    useCookie(BBC_TOKEN, {
      domain: useRuntimeConfig().public.baseUrl.VITE_DOMAIN,
    }).value = undefined
  }
  return { setUser, clearUser, user }
})
