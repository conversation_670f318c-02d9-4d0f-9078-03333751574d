import { defineStore } from 'pinia'
import { userStore } from '~/store/user'
import { getUnreadCount } from '~/api/message'

export const usemessageStore = defineStore('message', () => {
  const _count = ref(0)
  let load = false

  const updatemsgCount = async () => {
    const user = userStore()
    if (!user.user.userId) return

    try {
      const res = await getUnreadCount(user.user.userId)
      if (res.success) {
        _count.value = res.data
        load = true
      }
    } catch (error) {
      console.error('获取未读消息数量失败:', error)
    }
  }
  const msgCount = computed(() => {
    const user = userStore()

    if (!load && user.user.userId) {
      updatemsgCount()
    }
    return _count.value
  })

  return {
    updatemsgCount,
    msgCount,
  }
})

export const messageStore = usemessageStore
