### 项目背景
+ 这是一个nuxt项目，使用vue3+typescript+ant-design-vue+unocss+pinia+vue-router+vite
+ 样式优先使用unocss, 注意html跟字体是4px
+ 优先使用script setup语法
+ vue中的属性不需要引入

### 项目结构
components: 组件 自动引入
utils: 工具函数 自动引入
pages: 页面
composables: 组合式api 自动引入
stores: pinia 自动引入

### try_http使用示例

```js
const [err, res] = await try_http('apiurl', {
  method: 'get',
  params: {},
  body: {}
})
if (err) return
consle.log(res.data)
```
