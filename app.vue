<template>
  <a-config-provider
    :locale="locale"
    :theme="{
      token: {
        borderRadius: 2,

        colorPrimary: '#f94c30',
        colorInfo: '#f94c30',
        colorLink: '#F94C30',
        colorLinkActive: '#d4301e',
        colorLinkHover: '#ff7559',
      },
      components: {
        Slider: {
          colorPrimaryBorder: '#f94c30',
          colorPrimaryBorderHover: '#e86b52',
        },
        Menu: {
          colorItemTextHoverHorizontal: '#1677ff',
          controlItemBgActive: '#2b1513',
          colorPrimaryBorder: '#59251c',
          colorErrorBg: '#2c1618',
        },
      },
    }"
  >
    <template #renderEmpty>
      <hm-empty />
    </template>
    <a-extract-style>
      <nuxt-layout>
        <NuxtPage></NuxtPage>
      </nuxt-layout>
    </a-extract-style>
  </a-config-provider>
</template>

<script setup>
import st from 'store2'
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import { useScreen } from '~/store/screen'
import { COMPAREKEY, useCompareStore } from './store/compare'
import { getCategoryTree } from '~/api/search'
import { treeStore } from '~/store/tree'
import { useRegister } from './store/showRegister'
import is32BitBrowser from './utils/is32Bit'
import { companyStore } from './store/company'
import { userStore } from './store/user'
dayjs.locale('zh-cn')
const route = useRoute()
const treeStoreObj = treeStore()
const locale = ref(zhCN)
const store = useScreen()
const compareStore = useCompareStore()
const registerStore = useRegister()
const resize = () => {
  store.setWidth(window.innerWidth)
}

useHead({
  meta: [{ name: 'referrer', content: 'strict-origin-when-cross-origin' }],
  script: [
    {
      src: 'https://hm.baidu.com/hm.js?128fe021fa58ec7f39f0a2245caaeb97',
    },
    {
      src: '/meiqia.js',
      body: true,
    },
  ],
})
watch(route, () => {
  if (_hmt) {
    _hmt.push(['_trackPageview', route.path])
  }
})

onMounted(() => {
  const is32 = is32BitBrowser()
  if (is32) {
    alert('检测到您正在使用32位浏览器，请使用64位浏览器打开')
  }
  // 获取整个类目 2023-04-26
  getCategoryTree()
    .then((res) => {
      if (res.code === 'ok') {
        treeStoreObj.setTree(res.data)
      }
    })
    .finally(() => {})

  window.addEventListener('resize', resize)
  resize()

  compareStore.compareList = st.get(COMPAREKEY) || []

  const _time = localStorage.getItem('_show_register_time')
  if (_time) {
    registerStore.setTime(Number(_time))
  }
})
onBeforeUnmount(() => {
  window.removeEventListener('resize', resize)
})

const user = userStore()
const company = companyStore()
const isLogin = useLoginState()
useEventListener('visibilitychange', async () => {
  if (!isLogin.value) return
  if (document.hidden) return
  const [err, res] = await try_http('/mall/shop/userSpace/getOneByCond', {
    params: {
      userMobile: user.user.userMobile,
    },
  })
  if (err) return
  if (company.company.shopCompanyId != res.data.merchantId) {
    message.warn('检测到您切换了企业空间，正在重新加载')
    setTimeout(() => {
      window.location.reload()
    }, 1000)
  }
})
</script>
