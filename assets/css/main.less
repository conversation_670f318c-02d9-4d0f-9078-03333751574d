@import '@unocss/reset/normalize.css';
:root {
  --primary-color: #f94c30;
  --secondary-color: #e86e6b33;
  --midnight-blue: #1c344f;
  --powder-blue: #eff2f7;
}

*,
:before,
:after {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
}

html {
  font-size: 4px;
}

body {
  background-color: var(--powder-blue);
}

.register-m {
  .ant-modal-content {
    border-radius: 8px;
    overflow: hidden;
    padding: 0 !important;
  }
  .ant-modal-body {
    position: relative;
  }
}

.text-ellipsis {
  vertical-align: middle;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

a {
  color: inherit;
  text-decoration: none;
  &:active,
  &:visited {
    color: inherit;
  }
}

.ant-input-number .ant-input-number-input {
  background: inherit !important;
  color: inherit !important;
}
