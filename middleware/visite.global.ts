// 2024-07-15 统计访问量

async function getIPAddress() {
  const response = await fetch('https://api.ipify.org/?format=json')
  const data = await response.json()
  const ipAddress = data.ip
  return ipAddress
}

const getDeviceInfo = () => {
  // 先判断是不是微信端打开的
  if (/(micromessenger)/i.test(navigator.userAgent)) {
    return '微信浏览器'
  } else {
    // return "普通浏览器"
    // 判断h5还是pc true就是h5
    let client = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    if (client) {
      return 'H5'
    } else {
      return 'PC'
    }
  }
}

export default defineNuxtRouteMiddleware(async (to) => {
  if (import.meta.server) return
  console.log('统计访问量...visite...')
  // let IP = sessionStorage.getItem('CUR_IP')
  // if (!IP) {
  //   try {
  //     IP = await getIPAddress()
  //   } catch(error) {
  //     IP = '未知IP'
  //   }
  //   sessionStorage.setItem('CUR_IP', IP)
  // }
  // const device = getDeviceInfo()
  const inviteCode = sessionStorage.getItem('invite_register_code')
  if (!!inviteCode) {
    console.log('统计访问量 - 邀请链接')
    http('/mall/promotionLog', {
      method: 'post',
      params: {
        inviteCode
      }
    })
  } else {
    console.log('统计访问量 - 其他')
  }
})
