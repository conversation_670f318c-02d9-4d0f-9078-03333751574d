// 2024-07-08 处理邀请码问题 邀请码在页面间跳转不丢失 注册完成之后 删除注册码
import { userStore } from '~/store/user'

export default defineNuxtRouteMiddleware(async (to) => {
  if (import.meta.server) return
  const user = userStore()
  // 仅在未登录是生效
  if (!user.user.userId) {
    console.log('未登录...invite...')
    const query = to.query || {}
    if (query.invite) {
      sessionStorage.setItem('invite_register_code', query.invite)
    }
    if (!query.invite) {
      const inviteCode = sessionStorage.getItem('invite_register_code')
      if (!!inviteCode) {
        return navigateTo({
          path: to.path,
          query: {
            ...to.query,
            invite: inviteCode
          }
        })
      }
    }
  } else {
    console.log('已登录...invite...')
    sessionStorage.removeItem('invite_register_code')
  }
})
