import { MemberLevel, userStore } from '~/store/user'
import { companyStore } from '~/store/company'
import { authMenuStore } from '~/store/authMenu'
import { getMerchantByMerchantId } from '~/api/mall-manage/index'
import { getCompany as getCompanyInfo } from '~/api/common'

const authRoutes = [
  'my-info',
  'my-security',
  'my-favourite',
  'my-invite',
  'my-score',
  'my-member',
  'message-system',
  'message-interaction',
  'message-customer-service',
  'ans-price',
  '询价详情',
]

// TODO 普通会员没有的菜单权限 跳转404
const normalMemberMenu = ['/workSpace']

const getCompany = async (userMobile) => {
  const res = await http<any>('/mall/shop/userSpace/getOneByCond', {
    method: 'get',
    params: {
      userMobile,
    },
  })
  return res
}

const getAuthMenu = async () => {
  const res = await http<any>(`/mall/p/sys/enterpriseMenu/auth`, {
    method: 'get',
  })
  return res
}

export default defineNuxtRouteMiddleware(async (to) => {
  // if (import.meta.server) return
  const token = useCookie(BBC_TOKEN).value
  const user = userStore()
  const company = companyStore()
  const authMenu = authMenuStore()
  if (token) {
    if (!user.user.userId) {
      const [err, res] = await try_http('/mall/p/user/userInfo')
      if (!err) {
        user.setUser(res.data)
      } else {
        user.clearUser()
      }
    }
    if (user.user.userMobile && company.company.shopCompanyId === undefined) {
      // 非普通会员登录检查是否在企业空间
      const [err, res] = await _try(() => getCompany(user.user.userMobile))
      if (!err) {
        if (res.data.merchantId) {
          const [companyErr, companyRes] = await _try(() =>
            getMerchantByMerchantId({ merchantId: res.data.merchantId }),
          )
          if (companyErr) return
          const ret = parseJson(companyRes.data, {})
          company.setCompany(ret)
        } else {
          company.setCompany({
            shopCompanyId: 0, // 个人空间
          })
        }
      }
    }
    if (!authMenu.authMenu.loadedMenu) {
      const [err, res] = await _try(() => getAuthMenu())
      if (!err) {
        authMenu.setAuthMenu({
          ...res.data,
          loadedMenu: true,
        })
      }
    }

    if (user.user.userId) {
      if (to.path.includes('/login')) {
        return navigateTo({ path: '/' })
      }
    }
  }
  if (authRoutes.includes(to.name as string) || to.path.startsWith('/workSpace')) {
    if (!token || !user.user.userId) {
      if (import.meta.client) {
        message.error('登录状态已过期,跳转到登录页面...')
      }

      const config = useRuntimeConfig().public.baseUrl
      const yanxuan = config.VITE_YANXUAN
      const home = config.VITE_HOME

      return navigateTo(
        {
          path: yanxuan + '/login',
          query: {
            redirect: home + to.fullPath,
          },
        },
        {
          external: true,
        },
      )
    }
  }

  if (to.name == 'brand-join-settle') {
    const isLogin = useLoginState()
    if (!isLogin.value) {
      return navigateTo('/')
    } else {
      const isEdit = to.query.type == 'edit'
      const companyId = to.query.companyId
      if (isEdit || !companyId) return
      const companyInfo = await getCompanyInfo(companyId)
      if (!has(companyInfo?.businessLicense)) {
        useSSR({
          type: 'Modal',
          title: '提示',
          content: '缺少营业执照信息，请先在工商信息上传您的营业执照信息',
        })
        return navigateTo('/workSpace/company-space/commercial-info')
      }
    }
  }
})
