import { companyStore } from '~/store/company'
import { authMenuStore } from '~/store/authMenu'

export default defineNuxtRouteMiddleware(async (to) => {
  const company = companyStore()
  const { type } = company.company
  if (!company.isPersonal && ![1, 3].includes(type)) {
    return navigateTo({ path: '/' })
  }
  if (company.company.shopCompanyId) {
    await company.setAdminInfo()
  }

  if (to.name == 'workSpace' || to.name == 'workSpace-my-space') {
    const firstMenu = authMenuStore().findFirst(0)
    if (firstMenu) {
      return navigateTo({
        name: firstMenu,
      })
    }
  }
  if (to.name == 'workSpace-settings') {
    const firstMenu = authMenuStore().findFirst(1)
    if (firstMenu) {
      return navigateTo({
        name: firstMenu,
      })
    } else {
      return navigateTo({
        name: 'no-permission',
      })
    }
  }

  const find = authMenuStore().menuSet.has(to.name)
  if (!find) {
    return navigateTo({
      name: 'no-permission',
    })
  }
})
