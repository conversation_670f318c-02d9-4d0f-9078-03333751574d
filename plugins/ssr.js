export default defineNuxtPlugin((nuxtApp) => {
  const list = useCookie('message-list')
  nuxtApp.hook('page:loading:end', () => {
    while (list.value?.length) {
      const item = list.value.pop()
      if (item) {
        const { type, title, content } = item
        if (type == 'Modal') {
          Modal.info({
            content,
            title,
          })
        } else if (type == 'message') {
          message.destroy()
          message.error(content)
        }
        // setTimeout(() => {
        // }, 1000)
      }
    }
  })
})
