import { LoadingOutlined } from '@ant-design/icons-vue'
import { render } from 'vue'
export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.directive('LoadingClick', {
    mounted(el, binding) {
      el.dataset.loading = 0
      el.addEventListener('click', async () => {
        if (el.dataset.loading == 1) return
        el.dataset.loading = 1
        let instance = document.createElement('span')
        const style = {
          marginRight: '2px',
        }
        Object.assign(instance.style, style)
        const _opacity = el.style.opacity
        el.style.opacity = 0.65
        render(h(LoadingOutlined), instance)
        el.prepend(instance)
        await _try(() => binding.value())
        el.style.opacity = _opacity
        el.removeChild(instance)
        el.dataset.loading = 0
        instance = null
      })
    },
  })
})
