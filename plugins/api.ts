// import * as cookie from 'js-cookie'
import crypto from 'crypto-js'
import { userStore } from '~/store/user'

const API_PREFIX = '/mall-platform'

// 平台端配置访问密钥
const appId = '1001'
const appSecret = 'fe675fe7aaee830b6fed09b64e034f84dcbdaeb429d9cccd4ebb90e15af8dd711718853233208'
const userId = '1'

const sha256 = (str) => {
  return crypto.SHA256(str).toString(crypto.enc.Hex)
}

const sort = (obj) => {
  const sortedObj = Object.fromEntries(Object.entries(obj).sort((a, b) => a[0].localeCompare(b[0])))
  return sortedObj
}

const getStringData = (obj) => {
  for (let k in obj) {
    obj[k] = [null, undefined].includes(obj[k]) ? '' : obj[k].toString()
    if (obj[k] == '') {
      delete obj[k]
    }
  }
  return obj
}

const formatData = (data: any = {}, method) => {
  const _data =
    method == 'get'
      ? {
          userId: userId,
          timestamp: new Date().getTime().toString(),
          ...getStringData(data),
        }
      : {
          timestamp: new Date().getTime().toString(),
          data: {
            ...getStringData(data),
            userId: userId,
          },
        }
  _data.sign = sha256(
    JSON.stringify(
      sort({
        ..._data,
        appSecret: appSecret,
      }),
    ),
  )
  _data.appId = appId
  return _data
}

export default defineNuxtPlugin((nuxtApp) => {
  const customApi = $fetch.create({
    baseURL: nuxtApp.$config.public.baseUrl.VITE_API_BASE,
    onRequest({ options }) {
      const token = useCookie(BBC_TOKEN).value

      if (token) {
        const headers = (options.headers ||= {})
        // @ts-ignore
        headers.authorization = `${token}`
      }
    },
    onResponse(ctx) {
      const _data = ctx.response._data
      if (_data instanceof Blob) {
        return Promise.resolve(_data)
      }
      if (_data?.code != '00000' && _data?.code != 'ok') {
        if (import.meta.client) {
          message.error(_data.msg || _data.message)
        }
        if (_data?.code == 'A00004') {
          userStore().clearUser()
          return Promise.reject('err')
        }
        return Promise.reject(_data)
      }
      return Promise.resolve(_data)
    },
    async onResponseError({ response }) {
      if (response.status === 401) {
        await nuxtApp.runWithContext(() => navigateTo('/login'))
      }
    },
  })

  const platApi = $fetch.create({
    baseURL: nuxtApp.$config.public.baseUrl.VITE_API_BASE + API_PREFIX,

    onRequest({ options }) {
      if (!options) {
        options = {}
      }
      if (!options.method) {
        options.method = 'get'
      }
      const method = options.method
      if (method == 'get') {
        options.params = formatData(options.params, method)
      } else {
        options.body = formatData(options.body, method)
      }
      if (!options.headers) {
        options.headers = {}
      }
      // @ts-ignore
      options.headers.grantType = 'sign'
    },

    async onResponseError({ response }) {
      if (response.status === 401) {
        await nuxtApp.runWithContext(() => navigateTo('/login'))
      }
    },
  })

  // Expose to useNuxtApp().$api
  return {
    provide: {
      customApi,
      platApi,
    },
  }
})
