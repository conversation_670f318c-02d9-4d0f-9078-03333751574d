import Http from '~/utils/request'
import crypto from 'crypto-js'

const API_PREFIX = '/mall-platform'

// 平台端配置访问密钥
const appId = '1001'
const appSecret =
  'fe675fe7aaee830b6fed09b64e034f84dcbdaeb429d9cccd4ebb90e15af8dd711718853233208'
const userId = '1'

const sha256 = (str) => {
  return crypto.SHA256(str).toString(crypto.enc.Hex)
}

const sort = (obj) => {
  const sortedObj = Object.fromEntries(
    Object.entries(obj).sort((a, b) => a[0].localeCompare(b[0])),
  )
  return sortedObj
}

const getStringData = (obj) => {
  for (let k in obj) {
    obj[k] = [null, undefined].includes(obj[k]) ? '' : obj[k].toString()
    if (obj[k] == '') {
      delete obj[k]
    }
  }
  return obj
}

export const formatData = (data, method) => {
  const _data =
    method == 'get'
      ? {
          userId: userId,
          timestamp: new Date().getTime().toString(),
          ...getStringData(data),
        }
      : {
          timestamp: new Date().getTime().toString(),
          data: {
            ...getStringData(data),
            userId: userId,
          },
        }
  _data.sign = sha256(
    JSON.stringify(
      sort({
        ..._data,
        appSecret: appSecret,
      }),
    ),
  )
  _data.appId = appId
  return _data
}

export const getHeader = (method) => {
  return method == 'get'
    ? {
        grantType: 'sign',
      }
    : {
        grantType: 'sign',
        'Content-Type': 'application/json',
      }
}

export const _http = {
  // get: (url, data = {}) => http(API_PREFIX + url, {
  //   headers,
  //   method: 'get',
  //   params: formatData(data, 'get')
  // })
}

const methods = ['get', 'put', 'post', 'delete']
methods.forEach((method) => {
  _http[method] = (url, data = {}) => {
    url = API_PREFIX + url
    const headers = { grantType: 'sign' }
    const _data = formatData(data, method)
    const params = ['get'].includes(method) ? _data : {}
    const body = !['get'].includes(method) ? _data : {}
    const options = {
      method,
      headers,
    }
    if (['get'].includes(method)) {
      options.params = params
    } else {
      options.body = body
    }
    return http(url, options)
  }
})

// 根据社会信用号查询审批记录
export const getShopCompanyByCreditCode = (params) => {
  return _http.get(`/shop/merchantAudit/getShopCompanyByCreditCode`, params)
}

// 根据 shopCompanyId 查询 企业信息
export const getShopCompanyByShopCompanyId = (shopCompanyId) => {
  return _http.get(`/shop/merchantAudit/getShopCompany/${shopCompanyId}`)
}

// 根据 merchantId 查询 店铺
export const getShopsByCompanyId = (params) => {
  return _http.get(`/platform/shopDetail/searchShops`, params)
}

// 根据条件查询企业用户信息
export const getOneByCond = (params) => {
  return _http.get(`/shop/merchantUser/getOneByCond`, params)
}

// 企业升级服务
export const companyUpgradation = (data) => {
  return _http.post(`/shop/merchantAudit`, data)
}

// 查询角色列表
export const getRole = (params) => {
  return _http.get(`/sys/enterpriseRole/page`, params)
}

// 新增角色
export const addRole = (data) => {
  return _http.post(`/sys/enterpriseRole`, data)
}

// 修改角色
export const editRole = (data) => {
  return _http.put(`/sys/enterpriseRole`, data)
}

// 删除角色
export const delRole = (data) => {
  return _http.delete(`/sys/enterpriseRole`, data)
}

// 获取验证图片  以及token
export const reqGet = (data) => {
  return _http.post('/captcha/get', data)
}

// 滑动或者点选验证
export const reqCheck = (data) => {
  return _http.post('/captcha/check', data)
}

// 获取个人积分订单
export const getScoreOrderList = (params) => {
  return _http.get('/score/order/page', params)
}
