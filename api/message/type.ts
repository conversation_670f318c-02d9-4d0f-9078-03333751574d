// 消息类型枚举
export interface MessageType {
  value: string
  label: string
  color: string
}

// 消息项数据结构
export interface MessageItem {
  logId: number
  nickName: string
  remindId: string
  merchantId: number
  shopId?: number
  supplierId?: number
  shopName?: string
  remindType: number
  sendType: number
  userMobile: string
  templateId: number
  message: string
  status: number // 0-未读, 1-已读
  jobStatus: number
  createTime: string
  orderNumber: string
  prodName?: string
  startTime?: string
  endTime?: string
  activityName?: string
  paramContent: string
  templateCode?: string
  mpCode?: string
  templateName?: string
  msgType?: string
}

// 解析后的消息内容
export interface ParsedMessageContent {
  title: string
  message: string
  menuModule: string
  detailPageUrl: string
}

// 分页响应数据结构
export interface MessagePageResponse {
  records: MessageItem[]
  total: number
  size: number
  current: number
  pages: number
}

// 消息列表响应
export interface MessageListResponse {
  page: MessagePageResponse
  unReadCount: number
}

// 消息查询参数
export interface MessageQueryParams {
  userId: string
  current?: number
  size?: number
  type?: string
  readStatus?: boolean
  startTime?: string
  endTime?: string
}

// 用于页面显示的消息项（转换后的格式）
export interface DisplayMessageItem {
  id: number
  type: string
  title: string
  content: string
  read: boolean
  timestamp: string
  detailUrl?: string
  orderNumber?: string
}
