import { http } from '~/utils/http'
import { type MessageListResponse, type MessageQueryParams } from './type'

/**
 * 获取消息未读数量
 * @param userId 用户ID
 * @returns 未读消息数量
 */
export const getUnreadCount = (userId: string) => {
  return http<number>('/mall/p/myNotifyLog/equipmentSupplier/unReadCount', {
    params: { userId },
  })
}

/**
 * 获取消息列表（包含未读数量）
 * @param params 查询参数
 * @returns 消息列表和未读数量
 */
export const getMessageList = (params: MessageQueryParams) => {
  return http<MessageListResponse>('/mall/p/myNotifyLog/equipmentSupplier/unReadCountList', {
    params,
  })
}

/**
 * 标记消息为已读（使用批量已读接口传递单个ID）
 * @param logId 消息ID
 * @param userId 用户ID
 * @returns 操作结果
 */
export const markMessageAsRead = (logId: number, userId: string) => {
  return http(`/mall/p/myNotifyLog/equipmentSupplier/batchRead?userId=${userId}`, {
    method: 'POST',
    body: { logIds: [logId] },
  })
}

/**
 * 删除消息
 * @param logId 消息ID
 * @returns 操作结果
 */
export const deleteMessage = (logId: number) => {
  return http(`/mall/p/myNotifyLog/${logId}`, {
    method: 'DELETE',
  })
}

/**
 * 批量标记已读
 * @param logIds 消息ID列表
 * @param userId 用户ID
 * @returns 操作结果
 */
export const batchMarkAsRead = (logIds: number[], userId: string) => {
  return http(`/mall/p/myNotifyLog/equipmentSupplier/batchRead?userId=${userId}`, {
    method: 'POST',
    body: { logIds },
  })
}

/**
 * 全部标记已读
 * @param userId 用户ID
 * @returns 操作结果
 */
export const markAllAsRead = (userId: string) => {
  return http(`/mall/p/myNotifyLog/equipmentSupplier/batchRead?userId=${userId}`, {
    method: 'POST',
    body: {},
  })
}

/**
 * 清空已读消息
 * @returns 操作结果
 */
export const clearReadMessages = () => {
  return http('/mall/p/myNotifyLog/equipmentSupplier/batchDelete', {
    method: 'POST',
    body: {},
  })
}
