export type Select = {
  from: string
  isFinished: boolean
  partNumber: string
  selectionParameters: string
  seriesCode: string
  seriesCodes: string
  specValues: SelectItem[]
}

export type SelectItem = {
  disabled: boolean
  max: string
  min: string
  step: string
  isHidden: boolean
  options: { name: string; value: string; visible: boolean }[]
  order?: number
  specCode: string
  specCodeDisplayName: string
  specValue: string
  type: 'Select' | 'Ranged'
  unit?: string
}
