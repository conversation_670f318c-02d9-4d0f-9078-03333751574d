export type Model = {
  prjPath: string
  brandCode: string
  brandName: string
  partNo: string
  productName: string
}

interface Base {
  Unit: string
  sort: number
  buttonstate: 'onevalue' | ''
  current: string
  resolvedVal: string
  desc: string
  attr: string
  /**
   * 主要变量
   */
  vargroups: ['MAIN' | 'SECONDARAY']
}

export interface FixedItem extends Base {
  status: 'fixed'
}

export interface StaticItem extends Base {
  status: 'static'
  values: {
    name: string
    value: string
    visible: boolean
  }[]
}

export interface LinkItem extends Base {
  status: ''
  ExecDocument: string
}

export interface ValueRangeItem extends Base {
  status: ''
  isValueRange: true
  values: {
    name: string
    value: string
    visible: boolean
  }[]
}
export interface RangeItem extends Base {
  status: 'ranged'
  max: number
  min: number
  step: number
}

export type LineItem = FixedItem | StaticItem | RangeItem | ValueRangeItem | LinkItem

export type Cane = {
  index: {
    line: {
      nr: string,
      values: Record<string, LineItem>
    }
    NB: string
    mident: string
  }
}