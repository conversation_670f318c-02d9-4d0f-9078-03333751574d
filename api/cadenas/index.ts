import http from "~/utils/request";
import { Cane, Model } from "./type";
export * from "./type";

export const fetchParams = (pageUrl) =>
  http.post<Model>(`/cadenas-prj/findByPageUrl?pageUrl=${pageUrl}`);

export const fetchForm = (params: { path?: string; mident?: string, changevar?: string, changeval?: string }) => {
  const { path, mident, changeval, changevar } = params;
  const query: Obj = {};
  if (path) {
    query.path = path;
  }
  if (mident) {
    query.mident = mident;
    query.includecrossref = true
  }
  if (changevar && changeval) {
    query.changevar = changevar
    query.changeval = changeval
  }
  return $fetch<Cane>(
    "https://yidaocloud-embedded.partcommunity.com/service/table",
    {
      params: {
        language: "chinese",
        eol: 1,
        plm: true,
        includeunclassifiedbycountry: false,
        enablePreviewPerLine: true,
        includecrossref: true,
        apikey: "5328c21db46647f19056a270357069d7",
        server_type: 'SUPPLIER_EXTERNAL_yidaocloud-embedded"',
        ...query,
        _t: new Date().getTime(),
      },
    },
  );
};

export const fetchToken = () => http.get('/cadenas-prj/download')
