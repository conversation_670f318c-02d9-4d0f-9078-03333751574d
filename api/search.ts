import Http from '~/utils/request'
import {
  Category,
  CompareResult,
  Condition,
  Pagination,
  Part,
  SelectionItem,
  TypeSelection,
} from './search/type'
export * from './search/type'

/**
 * 获取完整的瀚码标准类目树
 * @returns
 */
export const getCategoryTree = () => {
  return Http.get('/selection/hancode-category/tree')
}

/**
 * 获取类目的搜索结果
 * @param params
 * @returns
 */
export const getCategoryList = (params: any) => {
  params.keyword = encodeURIComponent(params.keyword)
  return Http.get('/selection/category-search', params)
}

/**
 * 获取过滤项目的搜索结果
 * @param params
 * @returns
 */
export const getFilterList = (params: any) => {
  params.keyword = encodeURIComponent(params.keyword)
  return Http.get('/selection/main-condition-search', params)
}

/**
 * 获取零部件的搜索结果
 * @param data
 * @returns
 */
export const getSelectionList = (data: any) => {
  return Http.post<{ BY_KEYWORD: Pagination<SelectionItem> }>(
    '/selection/part-search',
    data,
  )
}

export const getSelect = (condition: 'kw' | 'type', data) => {
  if (condition == 'kw') {
    return Http.post('/selection/search-by-keyword', data)
  } else {
    return Http.post('/selection/search-by-type', data)
  }
}

/**
 * 获取类目页的过滤条件
 * @param params
 * @returns
 */
export const getConditionList = (categoryCode: string) => {
  return Http.get<Condition>('/selection/part-condition/' + categoryCode)
}

/**
 * 获取零部件详情
 * @param params
 * @returns
 */
export const getPartDetail = (params: any) => {
  return Http.get<Part>('/selection/part-details', params)
}

/**
 * 获取零部件选型号码
 * @param data
 * @returns
 */
export const getPartTypeList = (data: any) => {
  return Http.post<TypeSelection[]>('/selection/part-type-selections', data)
}

/**
 * 获取搜索建议
 * @param data
 * @returns
 */
export function getSearchSuggestion(data: any) {
  return Http.post('/selection/search-suggestion', data)
}

/**
 * 获取推荐类目
 * @param id
 * @param max
 * @returns
 */
export function getRecommendFn(id: string, max = 12) {
  return Http.get<Category[]>(
    `/selection/part-recommend?id=${id}&number=${max}`,
  )
}
/**
 * 获取平替类目
 * @param id
 * @param max
 * @returns
 */
export function getRecommendFnV2(id: string, max = 12) {
  return Http.get<Category[]>(
    `/selection/part-recommend/v2?partId=${id}&number=${max}`,
  )
}
/**
 * 获取供应商商品
 * @param id
 * @param max
 * @returns
 */
export function getRecommendFnV3(id: string, max = 12) {
  return Http.get<Category[]>(
    `/selection/supplier-recommend?partId=${id}&number=${max}`,
  )
}
/**
 * 对比
 * @param data
 * @returns
 */
export function partCompare(data) {
  return Http.post<CompareResult[]>('/selection/parts-specification', data)
}
