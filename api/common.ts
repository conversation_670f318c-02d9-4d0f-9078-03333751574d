import Http from '~/utils/request'
import { getMerchantByMerchantId } from './mall-manage'

/**
 * 单文件上传
 * @param {*} file
 * @param {*} resourceType Available values : CategoryLogo, CategoryExcel
 * @returns
 */
export async function uploadFile(file: File, resourceType: string) {
  return await Http.upload('/file/upload?resourceType=' + resourceType, file)
}

export const getCompany = async (companyId) => {
  try {
    const result = await getMerchantByMerchantId({ merchantId: companyId })
    const company = JSON.parse(result.data)
    return company
  } catch (err) {
    return {}
  }
}
