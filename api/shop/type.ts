export type ShopItem = {
  intro: string
  firstLetter: string
  fansCount: number
  mainProduct: string
  merechanName: string
  merchanShortName: string
  shopLogo: string
  shopName: string
  shopId: number
  type: number
  visits: number
  shopStatus: number
  shopCompany?: any
  firmName?: string
}

export type Pagination<T = any> = {
  current: number
  pages: number
  records: T[]
  size: number
  total: number
}

