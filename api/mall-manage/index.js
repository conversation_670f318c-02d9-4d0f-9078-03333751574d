import Http from '~/utils/request'
import crypto from 'crypto-js'

// 平台端配置访问密钥
const appId = '1002'
const appSecret = 'b281bc2c616cb3c3a097215fdc9397ae87e6e06b156cc34e656be7a1a9ce88391718853257970'
const userId = '1'
const shopId = '1'

const sha256 = (str) => {
  return crypto.SHA256(str).toString(crypto.enc.Hex)
}

const sort = (obj) => {
  const sortedObj = Object.fromEntries(Object.entries(obj).sort((a, b) => a[0].localeCompare(b[0])))
  return sortedObj
}

const getStringData = (obj) => {
  for (let k in obj) {
    obj[k] = [null, undefined].includes(obj[k]) ? '' : obj[k].toString()
    if (obj[k] == '') {
      delete obj[k]
    }
  }
  return obj
}

export const formatData = (data, method) => {
  const _data =
    method == 'get'
      ? {
          userId: userId,
          shopId: shopId,
          timestamp: new Date().getTime().toString(),
          ...getStringData(data),
        }
      : {
          timestamp: new Date().getTime().toString(),
          data: {
            ...getStringData(data),
            userId: userId,
            shopId: shopId,
          },
        }
  _data.sign = sha256(
    JSON.stringify(
      sort({
        ..._data,
        appSecret: appSecret,
      })
    )
  )
  _data.appId = appId
  return _data
}

export const getHeader = (method) => {
  return method == 'get'
    ? {
        grantType: 'sign',
      }
    : {
        grantType: 'sign',
        'Content-Type': 'application/json',
      }
}

const API_PREFIX = '/mall-manage'

const _http = {
  // get: (url, data = {}) => http(API_PREFIX + url, {
  //   headers,
  //   method: 'get',
  //   params: formatData(data, 'get')
  // })
}

const methods = ['get', 'put', 'post', 'delete']
methods.forEach((method) => {
  _http[method] = (url, data = {}) => {
    url = API_PREFIX + url
    const headers = { grantType: 'sign' }
    const _data = formatData(data, method)
    const params = ['get', 'delete'].includes(method) ? _data : {}
    const body = !['get', 'delete'].includes(method) ? _data : {}
    const options = {
      method,
      headers,
    }
    if (['get'].includes(method)) {
      options.params = params
    } else {
      options.body = body
    }
    return http(url, options)
  }
})

export const mallLogin = (data) => {
  return Http.post('/shopLogin', data, {
    headers: {
      accessMode: 'YIDAO',
    },
    VITE_API_PREFIX: API_PREFIX,
  })
}

export const saveMember = (data) => {
  return Http.post('/shop/shopCompany/storage', formatData(data, 'post'), {
    headers: getHeader('post'),
    VITE_API_PREFIX: API_PREFIX,
  })
}

export const getMerchantByUser = (data) => {
  return Http.get('/shop/shopCompany/getMerchantByUser', formatData(data, 'get'), {
    headers: getHeader('get'),
    VITE_API_PREFIX: API_PREFIX,
  })
}

// 修改工商信息 企业信息
export const putShopCompany = (data) => {
  return http('/mall/p/shopCompany', {
    method: 'put',
    body: data,
  })
}

// 根据商城ID查询企业
export const getMerchantByMerchantId = (params) => {
  return _http.get('/shop/shopCompany/getMerchantByMerchantId', params)
}

// 获取验证图片  以及token
export const reqGet = (data) => {
  return _http.post('/captcha/get', data)
}

// 滑动或者点选验证
export const reqCheck = (data) => {
  return _http.post('/captcha/check', data)
}
