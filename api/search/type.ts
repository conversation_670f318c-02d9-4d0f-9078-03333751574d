type Extra = {
  selected?: boolean
  disabled?: boolean
}

export const enum SelectType {
  NORMAL,
  NEW,
}

export type Part = {
  category?: PartCategory
  id: string
  imageUrl: string
  pageUrl: string
  productName: string
  productBrand: string
  selectionParameters: SelectParameter[]
  website: Website
  specDetailHtml: string
  specPdfUrl?: string
  from: string
  has3DModel: boolean
  prjPath: string
  renderModel: SelectType
  modelFile?: string
  modelParameterType: number
  modelParameterFileId?: string
  lowestPrice?: number
  lowestDiscountedPrice?: number
  lowestTradeTerm?: number
  packingUnit: ''
  canInquiry: boolean
}

type PartCategory = {
  categoryCode: string
  categoryName: string
  categoryPath: string
  categoryUrl?: string
  id: string
}

export type SelectParameter = {
  spec_style: 'input' | 'select'
  spec_code: string
  spec_name: string
  spec_options: SpecOption[]
  value_range: [Range]
}

type SpecOption = {
  spec_option_code: string
  spec_option_img_url?: string
  spec_option_name: string
  spec_option_value: string
}

type Range = {
  min_value: string
  max_value: string
  step_value: string
}

export type Website = {
  id: number
  websiteCode: string
  websiteImgUrl: string
  websiteName: string
  websiteUrl: string
} & Extra

export type TypeSpecOption = {
  disabled: boolean
  executeResourceUrl?: string
  isHidden: boolean
  max: number
  min: number
  options: { name: string; value: string; visible: boolean }[]
  order?: number
  specCodeDisplayName?: string
  specValueDisplayName: string
  step: number
  type: string
  unit: string
  specCode: string
  specValue: string
  specValueDisp: string
}

export type TypeSelection = {
  isFinished: boolean
  from: string
  extra?: string
  selectionParameters?: unknown
  seriesCode?: string
  seriesCodes?: string[]
  skuTemplateId: string
  viewUrl?: string
  partNumber: string
  specValues: TypeSpecOption[]
  typeCode: string
  doc: string
  modelFilePath: string
  image: string
}

export type CompareResult = {
  categoryName: string
  partBrand: string
  partLogo: string
  partName: string
  websiteLogo: string
  specs: SpecResultItem[]
  partModel: string
}

export type SpecResultItem = {
  platform: boolean
  specCode: string
  specName: string
  options: SpecResultOption[]
}

export type SpecResultOption = {
  optionImage?: string
  optionName: string
  originSpec?: OriginOption[]
}

type OriginOption = {
  specCode: string
  specName: string
  specValue: string
  specValueCode: string
}

export type Category = {
  id: string
  website: Website
  productBrand: string
  productName: string
  pageUrl: string
  imageUrl: string
  specPdfUrl?: string
  partCode?: string
  typeCode?: string
  highLights?: string

  shopName?: string
  shopLogo?: string
  shopAddress?: string
  intro?: string
}

export type FilterOption = {
  optionName: string
  optionValueImgUrl: string
  optionValueSiteCodes: string
  originConditions: []
} & Extra
export type FilterCondition = {
  conditionCommonNo: string
  conditionName: string
  options: FilterOption[]
}
export type Condition = {
  filterConditions: FilterCondition[]
  hancodeCategory: PartCategory
  websites: Website[]
}

export type Pagination<T> = {
  pageNo: number
  pageSize: number
  totalCount: number
  totalPage: number
  data: T[]
}

export type SelectionItem = {
  has3DModel: boolean
  highLights: string
  id: string
  imageUrl: string
  pageUrl: string
  partCode?: string
  productBrand: string
  productName: string
  specPdfUrl: string
  typeCode?: string
  website: Website
}
