export enum ParamType {
  text = 1,
  input = 2,
  range = 3,
  radio = 4,
}

type Sel = {
  Text: string
  Value: string
}

type Base = {
  Area: string
  Name: string
  Key: string
  Sel: Sel
  Unit?: string
  Show: boolean
  Used: boolean
  Disabled: boolean
}

export type TextItem = {
  Type: ParamType.text
} & Base

export type RangeItem = {
  Type: ParamType.range
  Min: number
  Max: number
  Step: number
} & Base

export type InputItem = {
  Type: ParamType.input
} & Base

export type SelectItem = {
  Type: ParamType.radio
  Values: Sel[]
} & Base

export type ParamItem = SelectItem | TextItem | InputItem | RangeItem

export type BeData = {
  prodParams: ParamItem[]
  isSelected: boolean
  skuName: string
  stepName: string
  modelFileName: string
  prodName: string
}
