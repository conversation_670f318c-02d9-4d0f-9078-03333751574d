export type TrackNumItem = {
  beginTime: string
  createTime: string
  createdBy: string
  endTime: string
  enginerId: string
  id: number
  inquiryTime: string
  merchantId: number
  orderNo: string
  prodIds: null
  prodNames: string
  purchaseId: null
  sort: null
  status: number
  updateTime: string
  updatedBy: null
}

export type Sku = {
  id?: string
  brandCode: string
  brandName: string
  buyerRemark: string
  invalidFlag: number
  validStatus: number
  number: number
  price: number
  prodId: number
  prodName: string
  sellerRemark: string
  skuCode: string
  skuId: number
  totalPrice: number
  tradeTerm: number
  alternatives?: Sku[]
  source: number
}

export type ProdInfo = {
  createdTime: string
  createdBy: string
  enginerId: string
  enginerName: string
  id: number
  freightFee: number
  inquiryTime: string
  orderNo: string
  purchaseId: string
  purchaseName: string
  skuLists: Sku[]
  status: number
  updatedTime: string
  updatedBy: string
  tradeTerm: number
  totalPrice: number
  reInquiryFlag: boolean
}

export type PriceWithCount = {
  categoryDiscount: string
  discountName: string
  memberDiscount: string
  memberDiscountName: string
  oriPrice: number
  skuId: number
  skuPrice: number
  tradeTerm: number
}
