function is32BitBrowser() {
  if (process.client) {
    const userAgent = window.navigator.userAgent
    console.log('%c Line:3 🥛 userAgent', 'color:#A93FD3', userAgent)

    // 检查是否包含 "WOW64" 或 "Win64; x64" 字符串，这些通常表示 64 位系统
    const is64Bit =
      userAgent.includes('x64') ||
      userAgent.includes('Mac OS') ||
      userAgent.includes('x86_64') ||
      userAgent.includes('Android') ||
      userAgent.includes('iPhone')

    // 如果不包含上述字符串，可能是 32 位浏览器
    return !is64Bit
  }
}

export default is32BitBrowser
