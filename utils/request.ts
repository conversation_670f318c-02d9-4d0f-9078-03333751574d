import { message } from 'ant-design-vue'
import { companyStore } from '~/store/company'
import { userStore } from '~/store/user'

export type Response<T = any> = {
  code: string
  data: T
  message: string
}

// 处理错误状态码
const handleError = (status: number): void => {
  // const ErrorStatusMap = {
  //   400: '请求失败！请您稍后重试',
  //   401: '登录失效！请您重新登录',
  //   403: '当前账号无权限访问！',
  //   404: '你所访问的资源不存在！',
  //   405: '请求方式错误！请您稍后重试',
  //   408: '请求超时！请您稍后重试',
  //   500: '服务异常！',
  //   502: '网关错误！',
  //   503: '服务不可用！',
  //   504: '网关超时！',
  // };
  // message.error(ErrorStatusMap[status as keyof typeof ErrorStatusMap] || '请求失败！');
  if (status === 401) {
    localStorage.removeItem('access_token')

    // 如果当前在用户中心，跳转到登录页
    if (window.location.pathname.startsWith('/my/info')) {
      message.error('登录状态已过期，即将跳转到登录页面...')
      setTimeout(() => {
        window.location.href = '/login?type=LOGIN&redirect=' + window.location.href
      }, 1000)
    }
  }
}

const fetch = async <T = any>(url: string, args?: any, config?: any): Promise<Response<T>> => {
  const nuxtApp = useNuxtApp()
  await nextTick()
  const _config = await nuxtApp.runWithContext(() => useRuntimeConfig())
  let { VITE_API_HOST, VITE_API_PREFIX } = _config.public.baseUrl
  if (config && config.VITE_API_PREFIX) {
    VITE_API_PREFIX = config.VITE_API_PREFIX
  }

  const extraHeader = config && config.headers ? config.headers : {}
  // const auth = getAuthorization(VITE_API_PREFIX)
  const headers = {
    // headers信息
    authorization: await nuxtApp.runWithContext(() => useCookie(BBC_TOKEN).value),
    ...extraHeader,
  }

  const reqUrl = VITE_API_HOST + VITE_API_PREFIX + url

  return new Promise((resolve, reject) => {
    nuxtApp.runWithContext(() => {
      useFetch(reqUrl, {
        ...args,
        timeout: 10 * 1000,
        onRequest({ request, options }) {
          options.headers = headers
        },
        onResponseError({ request, response, options }) {
          process.client && handleError(response.status)
          reject(response)
        },
        watch: false,
      })
        .then(({ data }: any) => {
          const value = data.value
          if (!data._rawValue) {
            // 这里处理错误回调
            reject(value)
          } else if (!['ok', '00000'].includes(data._rawValue.code)) {
            if (data._rawValue.message == '请先登录') {
              userStore().clearUser()
              companyStore().setCompany({})
              nextTick(() => {
                return navigateTo({ path: '/login' })
              })
            }
            if (process.client) {
              message.destroy()
              message.error(data._rawValue.message || data._rawValue.msg)
            }
            resolve(data._rawValue)
          } else {
            resolve(data._rawValue)
          }
        })
        .catch((err: any) => {
          reject(err)
        })
    })
  })
}

export default new (class Http {
  get<T = any>(url: string, params?: any, config?: any) {
    return fetch<T>(url, { method: 'get', params }, config)
  }

  post<T = any>(url: string, body?: any, config?: any) {
    return fetch<T>(url, { method: 'post', body }, config)
  }

  put<T = any>(url: string, body?: any, config?: any) {
    return fetch<T>(url, { method: 'put', body }, config)
  }

  delete<T = any>(url: string, params?: any, config?: any) {
    return fetch<T>(url, { method: 'delete', params }, config)
  }

  upload<T = any>(url: string, file: File) {
    const formData = new FormData()
    formData.append('file', file)
    return fetch(url, {
      method: 'post',
      body: formData,
      headers: {
        'Content-Type': 'multipart/form-data', // 设置 Content-Type
      },
    })
  }
})()

export const downloadStream = (url: string, options = {}) => {
  const config = useRuntimeConfig()
  url = config.public.baseUrl.VITE_API_PREFIX + url
  return $fetch<Blob>(url, {
    responseType: 'blob',
    ...options,
  })
}
