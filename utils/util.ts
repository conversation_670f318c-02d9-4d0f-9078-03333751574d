import { formatData } from '~/api/mall-manage'

/**
 * 判断一个url是否为能够正常访问的图片
 * @param url
 * @returns
 */
export const isImageValid = (url: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const img = new Image()
    img.onload = function () {
      resolve(true)
    }
    img.onerror = function () {
      resolve(false)
    }
    img.src = url
  })
}

/**
 * 压缩图片
 * @param file
 * @param maxWidth
 * @param maxHeight
 * @param quality
 * @returns
 */
export const compressImage = (file: File, maxWidth: number, maxHeight: number, quality: number): Promise<File> => {
  return new Promise<File>((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)

    reader.onload = (event) => {
      const image = new Image()
      image.src = event.target!.result as string

      image.onload = () => {
        let width = image.width
        let height = image.height

        if (width > maxWidth || height > maxHeight) {
          // 计算新的宽度和高度
          if (width > height) {
            height = Math.round((height * maxWidth) / width)
            width = maxWidth
          } else {
            width = Math.round((width * maxHeight) / height)
            height = maxHeight
          }
        }

        const canvas = document.createElement('canvas')
        canvas.width = width
        canvas.height = height

        const ctx = canvas.getContext('2d')
        ctx!.drawImage(image, 0, 0, width, height)

        canvas.toBlob(
          (blob) => {
            const compressedFile = new File([blob!], file.name, {
              type: file.type, // 使用原始文件的类型
              lastModified: Date.now(),
            })
            resolve(compressedFile)
          },
          file.type, // 使用原始文件的类型
          quality, // 压缩质量，取值范围为 0 到 1
        )
      }
    }

    reader.onerror = (error) => {
      reject(error)
    }
  })
}

export const useCount = (count: number) => {
  if (count < 10000) return count
  return (count / 10000).toFixed(1) + '万'
}

type ExtractKeys<T, K extends keyof T> = K
export const pick = <T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: ExtractKeys<T, K>[],
): { [P in K]: T[P] } => {
  return keys.reduce(
    (acc, key) => {
      acc[key] = obj[key]
      return acc
    },
    {} as { [P in K]: T[P] },
  )
}

export const arr_to_tree = <T, K extends keyof T>(arr: T[], cid: K, pid: K) => {
  type Item = T & {
    children?: Item
  }
  const map: any = {}
  arr.forEach((item) => {
    map[item[cid]] = item
  })
  const tree: Item[] = []
  arr.forEach((item) => {
    const parent = map[item[pid]]
    if (parent) {
      if (!parent.children) {
        parent.children = []
      }
      parent.children.push(item)
    } else {
      // @ts-ignore
      tree.push(item)
    }
  })
  tree.map = map
  return tree
}

export const has = <T>(val: T) => {
  if (typeof val == 'number') return true
  if (val == null || val == undefined || val == '') return false
  return true
}

export const sleep = (time: number) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true)
    }, time)
  })
}

export const parseJson = <T = any>(str: string, cb: T) => {
  if (!str) return cb
  try {
    const res = JSON.parse(str)
    return res as T
  } catch {
    return cb
  }
}

export function filterTree(tree, filterFunc) {
  return tree.reduce((acc, node) => {
    // 判断当前节点是否符合条件
    const isMatch = filterFunc(node)

    // 递归过滤子节点
    const filteredChildren = filterTree(node.children || [], filterFunc)

    // 如果节点匹配或有匹配的子节点，加入结果中
    if (isMatch) {
      acc.push({
        ...node,
        children: filteredChildren, // 仅保留匹配的子节点
      })
    }

    return acc
  }, [])
}

export const genUUID = () => {
  const s: any = []
  const hexDigits = '0123456789abcdef'
  for (let i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
  }
  s[14] = '4' // bits 12-15 of the time_hi_and_version field to 0010
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1) // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23] = '-'

  return s.join('')
}

export function previewDoc(url: string) {
  if (url.includes('[')) {
    const list = parseJson<Obj[]>(url, [])
    if (list.length) {
      const url = list[0].url
      window.open(url, '_blank')
    }
  } else {
    window.open(url)
  }
}

export const getshopsign = formatData

export const eq = (value, ...args) => {
  return args.some((item) => item == value)
}

export const colResize = (w, col) => {
  col.width = w
}
