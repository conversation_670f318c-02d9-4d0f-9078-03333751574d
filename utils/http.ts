import { NitroFetchOptions } from 'nitropack'
import { _http } from '~/api/mall-platform'
import { type Response } from './request'

export type MallResponse<T = any> = {
  code: string
  data: T
  msg: string
  sign: string
  success: boolean
}
export const http = async <T>(url: string, options: NitroFetchOptions<any, any> = {}) => {
  return useNuxtApp().$customApi<T>(url, options) as Promise<MallResponse<T>>
}

export const plat_http = _http as Record<'get' | 'post' | 'put' | 'delete', (...args) => Promise<MallResponse>>

type TryResult<T> = [null, T] | [Error, null]

interface TryHttp {
  <T = any>(url: `/mall${string}`, opts?: obj): Promise<TryResult<MallResponse<T>>>
  <T = any>(url: string, opts?: obj): Promise<TryResult<Response<T>>>
}

export const try_http: TryHttp = async <T = any>(url: string, opts: obj = {}) => {
  try {
    const res = await http<T>(url, opts)
    return [null, res] as any
  } catch (err) {
    return [err, null] as any
  }
}
