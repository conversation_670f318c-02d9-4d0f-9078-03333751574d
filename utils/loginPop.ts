import { createApp, createVNode, render } from 'vue'
import LoginPopupVue from '~/components/LoginPopup.vue'

const instance = ref<InstanceType<typeof LoginPopupVue>>()
class LoginPop {
  constructor() {}
  static show(callback?: Function) {
    if (!instance.value) {
      let ctx = useNuxtApp().vueApp._context
      const Comp = defineComponent({
        setup() {
          return () => h(LoginPopupVue, { ref: instance })
        },
      })
      const style = {
        position: 'fixed',
        left: 0,
        top: 0,
        width: '100%',
        height: '100%',
      }
      const el = document.createElement('div')
      // Object.assign(el.style, style)
      const vnode = createVNode(Comp)
      vnode.appContext = ctx
      render(vnode, el)
      document.body.appendChild(el)
    }
    instance.value?.showLoginPopup(callback)
  }
}

export default LoginPop
