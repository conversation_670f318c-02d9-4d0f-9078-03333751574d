export const storageWithExpire = {
  setStorage: (key, value, expire) => {
    if (isNaN(expire) || expire < 1) {
      console.error('有效期应为一个有效数值');
      return;
    }
    const obj = {
      data: value, // 存储值
      time: Date.now(), // 存值时间戳
      expire: expire // 过期时间
    };
    if (localStorage) {
      localStorage.setItem(key, JSON.stringify(obj));
    }
  },

  getStorage: (key) => {
    let val = null;
    if (localStorage) {
      val = localStorage.getItem(key);
    }
    if (!val) return 'invalid';
    val = JSON.parse(val);
    if (Date.now() > val.time + val.expire) {
      localStorage.removeItem(key);
      return 'invalid';
    }
    return val.data;
  }
}
