import { createVNode, render } from 'vue'
import Chat from '~/pages/brand/components/chat.vue'

const map: Record<string, Ref<InstanceType<typeof Chat> | undefined>> = {}

class chatPop {
  static show(shopId: string, tag?: obj) {
    if (!map[shopId]) {
      map[shopId] = ref<InstanceType<typeof Chat>>()
      const ctx = useNuxtApp().vueApp._context
      const Comp = defineComponent({
        setup() {
          return () =>
            h(Chat, {
              ref: map[shopId],
            })
        },
      })
      const el = document.createElement('el')
      const vnode = createVNode(Comp)
      vnode.appContext = ctx
      render(vnode, el)
      document.body.appendChild(el)
    }
    map[shopId].value?.show(shopId, tag)
  }
}

export default chatPop
