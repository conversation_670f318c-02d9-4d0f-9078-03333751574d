// 防抖 防止表单重复提交
export const Debounce = (fn, t) => {
  const delay = t || 1000
  let timer
  return function () {
    const args = arguments
    if (timer) {
      clearTimeout(timer)
    }

    const callNow = !timer

    timer = setTimeout(() => {
      timer = null
    }, delay)

    if (callNow) fn.apply(this, args)
  }
}

 
export function debounce(fn, times = 500, imediate = false) {
	let timer = null;
	// 定义全局判断上一次是否是立即执行
	let isVoke = false;
 
	const _debounce = function (...args) {
		if (timer) {
			clearTimeout(timer); // 取消上一次定时器
			timer = null; // 清除定时器缓存
		}
		if (imediate && !isVoke) {
			//!isVoke=true
			fn.apply(this, args);
			// imediate=false //立即执行完成之后改成false   分析问题 当下一次重新输入时  不会立即执行=>延迟执行
			isVoke = true; //这里赋值为true 当上一次执行完毕后赋值为true //让其延迟执行
		} else {
			//延迟执行
			timer = setTimeout(() => {
				//延迟执行
				fn.apply(this, args); //外部传入的函数 
				isVoke = false; //abcd=> 当abcd执行完后重启false   在判断是否立即执行
			}, times);
		}
	};
	return _debounce;
}