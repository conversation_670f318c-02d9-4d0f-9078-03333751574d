import MemberGuide from '@/components/MemberGuide/index.vue'
import { createVNode, render } from 'vue'
const instance = ref<InstanceType<typeof MemberGuide>>()
export class Guide {
  static show(text?: string) {
    if (!instance.value) {
      const el = document.createElement('div')
      const ctx = useNuxtApp().vueApp._context
      const Comp = defineComponent({
        setup() {
          return () =>
            h(MemberGuide, {
              ref: instance,
              onUpgrade() {
                const yanxuan = useRuntimeConfig().public.baseUrl.VITE_YANXUAN
                window.open(`${yanxuan}/enterpriseCenter/create`)
              },
            })
        },
      })
      const node = createVNode(Comp)
      node.appContext = ctx
      render(node, el)
      document.body.appendChild(el)
    }
    nextTick(() => {
      instance.value?.show(text)
    })
  }
}
