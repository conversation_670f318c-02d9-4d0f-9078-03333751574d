{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"dev": "nuxt dev", "dev:prod": "nuxt dev --mode devprod", "debugger": "nuxt dev --mode debugger", "build:dev": "nuxt build --mode webstage", "build:prod": "nuxt build --mode webprod", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "upgrade": "nuxi upgrade --force"}, "devDependencies": {"@ant-design-vue/nuxt": "^1.4.6", "@iconify-json/ant-design": "^1.1.17", "@iconify-json/mdi": "^1.1.67", "@iconify/utils": "^2.3.0", "@nuxt/devtools": "latest", "@nuxtjs/sitemap": "^6.0.0-beta.1", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "@types/lodash-es": "^4.17.12", "@unocss/nuxt": "^0.58.5", "js-cookie": "^3.0.5", "less": "^4.2.0", "mammoth": "^1.7.2", "nuxt": "^3.11.2", "overlayscrollbars": "^2.11.0", "overlayscrollbars-vue": "^0.5.9", "vite-plugin-compression": "^0.5.1"}, "dependencies": {"@microsoft/fetch-event-source": "^2.0.1", "@pinia/nuxt": "^0.5.1", "@vueuse/nuxt": "^10.9.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "ant-design-vue": "^4.2.6", "big.js": "^7.0.1", "crypto-js": "^4.2.0", "exceljs": "^4.4.0", "html2canvas": "^1.4.1", "iframe-resizer": "^4.3.11", "js-file-download": "^0.4.12", "lodash": "^4.17.21", "magic-string": "^0.30.7", "markdown-it": "^14.1.0", "md5": "^2.3.0", "mitt": "^3.0.1", "nuxt-icons": "^3.2.1", "pdf-vue3": "^1.0.12", "pinia": "^2.1.7", "sass": "^1.81.0", "store2": "^2.14.3", "swiper": "^11.0.6", "vue-countup-v3": "^1.4.0", "vue-qrcode": "^2.2.2", "vxe-table": "^4.9.5"}}