import { aesEncode } from '~/utils/aes'

export default defineEventHandler(async (event) => {
  const body = (await readBody(event)) as { username: string; password: string }
  console.log('%c Line:2 🥛 body', 'color:#7A9C72', body)
  const { username, password } = body

  const res = await $fetch('/bussiness', {
    method: 'post',
    body: {
      userName: username,
      passWord: aesEncode(password),
    },
  })
  return res
})

