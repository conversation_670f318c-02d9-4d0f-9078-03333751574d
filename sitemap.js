const baseUrl = 'https://www.yanxuan.cloud'

const fetchCategory = async () => {
  const ret = await fetch(baseUrl + '/api/selection/hancode-category/tree')
  const res = await ret.json()
  const urls = []
  const dfs = (list) => {
    for (const item of list) {
      if (item.type == 'LEAF') {
        urls.push({
          loc: `${baseUrl}/category?id=${item.categoryCode}`,
          changefreq: 'daily',
          priority: 0.9,
        })
      }
      if (item.children?.length) {
        dfs(item.children)
      }
    }
  }
  dfs(res.data)
  return urls
}

const sitemap = {
  hostname: baseUrl,
  path: '/sitemap.xml',
  cacheTime: 1000 * 60 * 60 * 24,
  gzip: true,
  generate: false,
  excludeAppSources: true,
  defaults: {
    changefred: 'always',
    lastmod: new Date(),
    priority: 0.8,
  },
  urls: async () => {
    const staticPage = ['', '/find', '/promotion', '/buy']
    const base = staticPage.map((item) => {
      return {
        loc: baseUrl + item,
        changefreq: 'daily',
        priority: 1,
      }
    })

    const categories = await fetchCategory()
    const url = [...base, ...categories]

    return url
  },
}

export default sitemap
